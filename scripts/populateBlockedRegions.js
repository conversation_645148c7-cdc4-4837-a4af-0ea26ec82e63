require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const mongoClient = require('../src/mongoClient');
const BlockedRegions = require('../src/models/fraud/blockedRegions.model');
const {
  BLOCKED_REGION_TYPES,
} = require('../src/constants/blockedRegions');

const RUSSIA_TIMEZONES = [
  'Europe/Kaliningrad',
  'Europe/Moscow',
  'Europe/Volgograd',
  'Europe/Samara',
  'Europe/Saratov',
  'Europe/Kirov',
  'Asia/Yekaterinburg',
  'Asia/Omsk',
  'Asia/Novosibirsk',
  'Asia/Novokuznetsk',
  'Asia/Krasnoyarsk',
  'Asia/Irkutsk',
  'Asia/Vladivostok',
  'Asia/Srednekolymsk',
  'Asia/Khandyga',
  'Asia/Ust-Nera',
  'Asia/Yakutsk',
];

const BLOCKED_COUNTRIES = ['russia', 'russian federation'];

const start = async () => {
  try {
    await mongoClient.connect();

    const blockedRegions = [];

    // Add blocked countries
    BLOCKED_COUNTRIES.forEach((country) => {
      blockedRegions.push({
        type: BLOCKED_REGION_TYPES.COUNTRY,
        value: country.toLowerCase(),
        reason: "Product request to not support russia and it's regions",
        createdBy: '<EMAIL>',
      });
    });

    // Add blocked timezones
    RUSSIA_TIMEZONES.forEach((timezone) => {
      blockedRegions.push({
        type: BLOCKED_REGION_TYPES.TIMEZONE,
        value: timezone.toLowerCase(),
        reason: "Product request to not support russia and it's regions",
        createdBy: '<EMAIL>',
      });
    });

    // Insert all blocked regions
    const result = await BlockedRegions.insertMany(blockedRegions);

    console.log(`Successfully inserted ${result.length} blocked regions:`);
    console.log(`- ${BLOCKED_COUNTRIES.length} countries`);
    console.log(`- ${RUSSIA_TIMEZONES.length} timezones`);
  } catch (error) {
    console.error(`Error inserting blocked regions: ${error.message}`);
  } finally {
    process.exit(0);
  }
};

start();
