/* eslint-disable prefer-const */
/* eslint-disable no-await-in-loop */
require('module-alias/register');
require('dotenv').config({
  path: '.env',
});
const fs = require('fs');
const mongoClient = require('../src/mongoClient');
const { inviteService } = require('../src/services/membership');
const User = require('../src/models/users.model');
const Learner = require('../src/models/learners.model');

// ############ UPDATE THESE VALUES ############
// DEV
// const OUTPUT_FILE = `./scripts/output/bulkInviteMembers-dev.output.txt`;
// const COMMUNITY_ID = '68494c120479fba4ab39608e'; // dev-nas.io/member-import-test
// const CSV_FILE_PATH = './scripts/data/email-import-list-dev.csv'; // Path to your CSV file
// const USER_EMAIL = '<EMAIL>'; // Just provide the email
// const BATCH_SIZE = 3;
// Prod
// const OUTPUT_FILE = `./scripts/output/bulkInviteMembers-prod.output.txt`;
// const COMMUNITY_ID = '6491c83261936750a998520d'; // nas.io/nasdailynewsletter
// const CSV_FILE_PATH = './scripts/data/ai-readiness-9k.csv'; // Path to your CSV file
// const USER_EMAIL = '<EMAIL>'; // Just provide the email
// const BATCH_SIZE = 100;

const OUTPUT_FILE = `./scripts/output/bulkInviteMembers.output.txt`;
const COMMUNITY_ID = '';
const CSV_FILE_PATH = ''; // Path to your CSV file
const USER_EMAIL = ''; // Just provide the email, usually the owner of the communiy.
const BATCH_SIZE = 100;

const writeToFile = (data) => {
  fs.writeFileSync(OUTPUT_FILE, data + '\n', {
    flag: 'a',
  });
  console.log(data);
};

/**
 * Build user object from email by looking up user and learner collections
 */
const buildUserObject = async (email) => {
  try {
    const user = await User.findOne({
      email: email.toLowerCase(),
      isActive: true,
    }).lean();
    if (!user) {
      throw new Error(`User not found for email: ${email}`);
    }

    const learner = await Learner.findById(user.learner).lean();
    if (!learner || !learner.isActive) {
      throw new Error(`Learner not found for user: ${email}`);
    }

    return {
      _id: user._id,
      email: user.email,
      learner: {
        _id: learner._id,
      },
    };
  } catch (error) {
    throw new Error(`Failed to build user object: ${error.message}`);
  }
};

/**
 * Read CSV file and extract emails
 */
const readEmailsFromCSV = (csvPath) => {
  try {
    const csvContent = fs.readFileSync(csvPath, 'utf8');
    const lines = csvContent.split('\n').filter((line) => line.trim());

    if (lines.length === 0) return [];

    // Get headers from first line
    const headers = lines[0]
      .split(',')
      .map((h) => h.trim().toLowerCase().replace(/"/g, ''));

    // Find the email column
    const emailColumnIndex = headers.findIndex(
      (header) => header === 'email'
    );

    if (emailColumnIndex === -1) {
      console.error('No email column found in CSV');
      return [];
    }

    // Extract emails from data rows
    const emails = lines
      .slice(1)
      .map((line) => {
        const columns = line
          .split(',')
          .map((col) => col.trim().replace(/"/g, ''));
        return columns[emailColumnIndex];
      })
      .filter((email) => email && email.trim())
      .map((email) => email.toLowerCase());

    console.log(`Found ${emails.length} emails in CSV`);
    return emails;
  } catch (error) {
    console.error('Error reading CSV:', error);
    return [];
  }
};

/**
 * Create batches from array
 */
const createBatches = (array, batchSize) => {
  const batches = [];
  for (let i = 0; i < array.length; i += batchSize) {
    batches.push(array.slice(i, i + batchSize));
  }
  return batches;
};

const bulkInviteMembers = async () => {
  // eslint-disable-next-line no-undef
  const scriptStartTime = performance.now();

  try {
    const currDateTime = new Date().toISOString();
    writeToFile(`\n\nStarting bulk invite process at ${currDateTime}`);

    await mongoClient.connect();

    // Build user object from email
    writeToFile(`Building user object for: ${USER_EMAIL}`);
    const USER_OBJECT = await buildUserObject(USER_EMAIL);
    writeToFile(`User object built successfully for: ${USER_EMAIL}`);

    // Read emails from CSV
    const emails = readEmailsFromCSV(CSV_FILE_PATH);
    if (emails.length === 0) {
      writeToFile('No emails found in CSV file');
      return;
    }

    // Create batches
    const batches = createBatches(emails, BATCH_SIZE);
    writeToFile(
      `Total emails: ${emails.length}, Total batches: ${batches.length}`
    );

    // Initialize cumulative results
    let cumulativeResults = {
      rowsReceived: 0,
      newlyInvitedUsersCount: 0,
      invitesSentCount: 0,
      existingMembersCount: 0,
      alreadyInvitedUsersCount: 0,
    };

    const errors = [];

    // Process each batch
    for (let i = 0; i < batches.length; i++) {
      // eslint-disable-next-line no-undef
      const batchStartTime = performance.now();
      const batch = batches[i];
      const startRow = i * BATCH_SIZE + 1;
      const endRow = startRow + batch.length - 1;

      writeToFile(`\nStart: Batch ${i + 1}. Rows ${startRow} - ${endRow}`);
      writeToFile(`Emails: ${batch.join(', ')}`);

      try {
        const data = batch.map((email) => ({ email }));

        const result = await inviteService.inviteMembers({
          communityId: COMMUNITY_ID,
          user: USER_OBJECT,
          data,
          sendEmail: false,
          bypassFraudCheck: true,
        });

        // Update cumulative results
        cumulativeResults.rowsReceived += result.rowsReceived;
        cumulativeResults.newlyInvitedUsersCount +=
          result.newlyInvitedUsersCount;
        cumulativeResults.invitesSentCount += result.invitesSentCount;
        cumulativeResults.existingMembersCount +=
          result.existingMembersCount;
        cumulativeResults.alreadyInvitedUsersCount +=
          result.alreadyInvitedUsersCount;

        writeToFile(
          `Success: Batch ${i + 1}. New: ${
            result.newlyInvitedUsersCount
          }, Existing: ${result.existingMembersCount}`
        );

        // eslint-disable-next-line no-undef
        const batchDuration = performance.now() - batchStartTime;
        writeToFile(
          `End: Batch ${
            i + 1
          }. Rows ${startRow} - ${endRow}. Time: ${batchDuration.toFixed(
            2
          )}ms`
        );
      } catch (error) {
        const errorInfo = {
          batchNumber: i + 1,
          startRow,
          endRow,
          emails: batch,
          error: error.message,
          timestamp: new Date().toISOString(),
        };

        errors.push(errorInfo);
        writeToFile(`Error: Batch ${i + 1}. ${error.message}`);

        // eslint-disable-next-line no-undef
        const batchDuration = performance.now() - batchStartTime;
        writeToFile(
          `End: Batch ${
            i + 1
          }. Rows ${startRow} - ${endRow} (FAILED). Time: ${batchDuration.toFixed(
            2
          )}ms`
        );
      }

      // Small delay between batches
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    // Final results
    writeToFile('\n=== FINAL RESULTS ===');
    writeToFile(`Total rows processed: ${cumulativeResults.rowsReceived}`);
    writeToFile(
      `Newly invited users: ${cumulativeResults.newlyInvitedUsersCount}`
    );
    writeToFile(`Invites sent: ${cumulativeResults.invitesSentCount}`);
    writeToFile(
      `Existing members: ${cumulativeResults.existingMembersCount}`
    );
    writeToFile(
      `Already invited users: ${cumulativeResults.alreadyInvitedUsersCount}`
    );
    writeToFile(`Total errors: ${errors.length}`);

    // Write errors to separate file if any
    if (errors.length > 0) {
      const errorFile = `./scripts/output/bulkInviteMembers.errors.json`;
      fs.writeFileSync(errorFile, JSON.stringify(errors, null, 2));
      writeToFile(`Errors written to: ${errorFile}`);
    }

    // eslint-disable-next-line no-undef
    const totalDuration = performance.now() - scriptStartTime;
    writeToFile(
      `Total script execution time: ${(totalDuration / 1000).toFixed(2)}s`
    );
    writeToFile('END');
  } catch (e) {
    writeToFile('Fatal Error: ' + e.message);
  } finally {
    process.exit(0);
  }
};

bulkInviteMembers();
