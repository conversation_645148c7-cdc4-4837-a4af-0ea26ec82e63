/* eslint-disable prefer-const */
/* eslint-disable no-await-in-loop */
require('dotenv').config({
  path: '.env',
});
const fs = require('fs');
const mongoClient = require('../src/mongoClient');
const EventService = require('../src/services/event');
const CommunityEventsModel = require('../src/communitiesAPI/models/communityEvents.model');
const EventAttendeesModel = require('../src/communitiesAPI/models/eventAttendees.model');
const { EVENT_MAIL_TYPES } = require('../src/services/mail/constants');
const EventUtils = require('../src/utils/event.util');
const {
  COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES,
} = require('../src/communitiesAPI/constants');
const { EVENT_TYPES } = require('../src/constants/common');

const OUTPUT_FILE = `./scripts/output/sendEventRsvpEmailToAllAttendees.output.txt`;

// ############ UPADTE EVENT ID ############
const EVENT_ID = '673ca88de1ad0442924cb49c'; // eg: '679b5402432a014efdd6c424';

const writeToFile = (data) => {
  fs.writeFileSync(OUTPUT_FILE, data + '\n', {
    flag: 'a',
  });
  console.log(data);
};

const sendEventRsvpEmailToAllAttendees = async () => {
  try {
    const currDateTime = new Date().toISOString();
    writeToFile(
      `Sending email to all going attendees of event: ${EVENT_ID} on ${currDateTime}`
    );

    // constants
    const mailType = EVENT_MAIL_TYPES.MEMBER_COMMUNITY_EVENT_RSVP;
    await mongoClient.connect();

    // get event
    const event = await CommunityEventsModel.findById(EVENT_ID).lean();
    writeToFile('Event: ' + JSON.stringify(event));

    // get all attendees going to the event
    const eventAttendeesFilter = {
      eventObjectId: EVENT_ID,
      status: COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING,
    };
    const eventAttendees = await EventAttendeesModel.find(
      eventAttendeesFilter
    ).lean();
    writeToFile('Total eventAttendees: ' + eventAttendees.length);

    // Send email to each attendee sequentially
    for (const eventAttendee of eventAttendees) {
      let {
        _id,
        email,
        learnerObjectId,
        purchaseType,
        ticketReference,
        ticketReferences,
        quantity,
      } = eventAttendee;

      const isQrCodeMissingInAnyTicRef = ticketReferences.some(
        (existingTicketReference) => !existingTicketReference.qrCodeSrc
      );

      const updateFilter = {};
      if (isQrCodeMissingInAnyTicRef) {
        const communityObjectId = event.communities[0];
        if (event.type === EVENT_TYPES.INPERSON) {
          const updatedTicketReferences =
            await EventUtils.addQrCodeToTicketReferences({
              ticketReferences,
              eventObjectId: event._id,
              communityObjectId,
              learnerObjectId,
            });
          updateFilter.ticketReferences = updatedTicketReferences;
          ticketReferences = updatedTicketReferences;
        }
      }

      await EventAttendeesModel.updateOne(
        {
          _id,
        },
        {
          $set: updateFilter,
        }
      );

      // eslint-disable-next-line no-await-in-loop
      await EventService.sendEventEmail({
        mailType,
        event,
        learnerObjectId,
        eventAttendeeObjectId: _id,
        eventAttendeePurchaseType: purchaseType,
        ticketReference,
        ticketReferences,
        quantity,
      });

      writeToFile(`Sent email to ${email}`);
    }

    writeToFile('END');
  } catch (e) {
    writeToFile('Error: ' + e.message);
  } finally {
    process.exit(0);
  }
};

sendEventRsvpEmailToAllAttendees();
