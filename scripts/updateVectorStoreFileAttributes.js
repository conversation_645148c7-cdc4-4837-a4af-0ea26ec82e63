require('module-alias/register');
require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const fs = require('fs').promises;

const mongoClient = require('../src/mongoClient');
const aiClient = require('../src/services/aiCofounder/ai/client');
const { PLATFORM } = require('../src/services/aiCofounder/ai/constants');

const start = async () => {
  await mongoClient.connect();

  const currentDate = new Date();

  const vectorStoreId = 'vs_68556954c7fc819191bcfe5bc2f4fa28';

  const data = [];

  let result = await aiClient.vectorStores.files.list(vectorStoreId);
  data.push(...result.data);

  while (result.has_more) {
    const lastFileId = result.body.last_id;
    if (!lastFileId) {
      break;
    }

    // eslint-disable-next-line no-await-in-loop
    result = await aiClient.vectorStores.files.list(vectorStoreId, {
      after: lastFileId,
    });

    data.push(...result.data);
  }

  for await (const file of data) {
    const fileResult = await aiClient.files.retrieve(file.id);

    const words = fileResult.filename
      .toLowerCase()
      .split('.txt')[0]
      .split(' ');
    const currentFilePlatform = file.attributes?.platform;

    let platform;
    if (words.includes('mobile')) {
      platform = PLATFORM.APP;
    } else if (words.includes('web')) {
      platform = PLATFORM.WEB;
    } else {
      platform = PLATFORM.SHARED;
    }

    if (currentFilePlatform === platform) {
      console.log(
        `File ${fileResult.id} ${fileResult.filename} already has platform ${platform}. Skipping.`
      );
      // eslint-disable-next-line no-continue
      continue;
    }

    console.log(
      `Updating file ${fileResult.id}: ${fileResult.filename} to platform ${platform}`
    );

    await aiClient.vectorStores.files.update(fileResult.id, {
      vector_store_id: vectorStoreId,
      attributes: {
        platform,
      },
    });

    await fs.appendFile(
      'logs/updateVectorStoreFileAttributes.log',
      `${currentDate.toISOString()},${vectorStoreId},${fileResult.id},${
        fileResult.filename
      },${platform}\n`
    );
  }

  console.log('Completed');
  process.exit(0);
};

start();
