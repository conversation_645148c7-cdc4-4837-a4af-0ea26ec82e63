require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const fs = require('fs');
const path = require('path');
const mongoClient = require('../src/mongoClient');
const CommunityPlanOrderModel = require('../src/models/plan/communityPlanOrder.model');
const CommunityPlanTransactionModel = require('../src/models/plan/communityPlanTransaction.model');
const CommunityModel = require('../src/communitiesAPI/models/community.model');

// Currency conversion rates to USD (approximate rates)
const CURRENCY_TO_USD_RATES = {
  USD: 1,
  EUR: 1.10,
  GBP: 1.27,
  CAD: 0.74,
  AUD: 0.66,
  SGD: 0.74,
  MXN: 0.059,     // 1 MXN = ~0.059 USD
  INR: 0.012,     // 1 INR = ~0.012 USD
  COP: 0.00025,   // 1 COP = ~0.00025 USD
  ARS: 0.0011,    // 1 ARS = ~0.0011 USD
  BRL: 0.20,      // 1 BRL = ~0.20 USD
  PEN: 0.27,      // 1 PEN = ~0.27 USD
  CLP: 0.0011,    // 1 CLP = ~0.0011 USD
  PHP: 0.018,     // 1 PHP = ~0.018 USD
  MYR: 0.22,      // 1 MYR = ~0.22 USD
  IDR: 0.000067,  // 1 IDR = ~0.000067 USD
  VND: 0.00004,   // 1 VND = ~0.00004 USD
  ILS: 0.27,      // 1 ILS = ~0.27 USD
  AED: 0.27,      // 1 AED = ~0.27 USD
  JPY: 0.0067,    // 1 JPY = ~0.0067 USD  
  HUF: 0.0027,    // 1 HUF = ~0.0027 USD
};

// Maximum amount in USD cents (< $5 = < 500 cents)
const MAX_USD_CENTS = 500;

// Expected regular pricing by country for PRO/PLATINUM plans (in USD)
const EXPECTED_REGULAR_PRICING = {
  // Tier 1 countries (developed markets)
  'United States': { 
    PRO: { monthly: 29, annual: 279 }, 
    PLATINUM: { monthly: 99, annual: 799 } 
  },
  'Canada': { 
    PRO: { monthly: 29, annual: 279 }, 
    PLATINUM: { monthly: 99, annual: 799 } 
  },
  'United Kingdom': { 
    PRO: { monthly: 29, annual: 279 }, 
    PLATINUM: { monthly: 99, annual: 799 } 
  },
  'Australia': { 
    PRO: { monthly: 29, annual: 279 }, 
    PLATINUM: { monthly: 99, annual: 799 } 
  },
  'Germany': { 
    PRO: { monthly: 29, annual: 279 }, 
    PLATINUM: { monthly: 99, annual: 799 } 
  },
  'France': { 
    PRO: { monthly: 29, annual: 279 }, 
    PLATINUM: { monthly: 99, annual: 799 } 
  },
  'Netherlands': { 
    PRO: { monthly: 29, annual: 279 }, 
    PLATINUM: { monthly: 99, annual: 799 } 
  },
  'Switzerland': { 
    PRO: { monthly: 29, annual: 279 }, 
    PLATINUM: { monthly: 99, annual: 799 } 
  },
  'Norway': { 
    PRO: { monthly: 29, annual: 279 }, 
    PLATINUM: { monthly: 99, annual: 799 } 
  },
  'Denmark': { 
    PRO: { monthly: 29, annual: 279 }, 
    PLATINUM: { monthly: 99, annual: 799 } 
  },
  'Sweden': { 
    PRO: { monthly: 29, annual: 279 }, 
    PLATINUM: { monthly: 99, annual: 799 } 
  },
  'Finland': { 
    PRO: { monthly: 29, annual: 279 }, 
    PLATINUM: { monthly: 99, annual: 799 } 
  },
  'Singapore': { 
    PRO: { monthly: 29, annual: 279 }, 
    PLATINUM: { monthly: 99, annual: 799 } 
  },
  'Japan': { 
    PRO: { monthly: 29, annual: 279 }, 
    PLATINUM: { monthly: 99, annual: 799 } 
  },
  
  // Tier 2 countries (emerging markets) - estimated annual pricing with discount
  'Chile': { 
    PRO: { monthly: 27, annual: 259 }, 
    PLATINUM: { monthly: 54, annual: 519 } 
  },
  'Argentina': { 
    PRO: { monthly: 27, annual: 259 }, 
    PLATINUM: { monthly: 54, annual: 519 } 
  },
  'Brazil': { 
    PRO: { monthly: 27, annual: 259 }, 
    PLATINUM: { monthly: 54, annual: 519 } 
  },
  'Mexico': { 
    PRO: { monthly: 27, annual: 259 }, 
    PLATINUM: { monthly: 54, annual: 519 } 
  },
  'Colombia': { 
    PRO: { monthly: 27, annual: 259 }, 
    PLATINUM: { monthly: 54, annual: 519 } 
  },
  'Peru': { 
    PRO: { monthly: 27, annual: 259 }, 
    PLATINUM: { monthly: 54, annual: 519 } 
  },
  'Malaysia': { 
    PRO: { monthly: 27, annual: 259 }, 
    PLATINUM: { monthly: 54, annual: 519 } 
  },
  'Thailand': { 
    PRO: { monthly: 27, annual: 259 }, 
    PLATINUM: { monthly: 54, annual: 519 } 
  },
  'Philippines': { 
    PRO: { monthly: 27, annual: 259 }, 
    PLATINUM: { monthly: 54, annual: 519 } 
  },
  'Indonesia': { 
    PRO: { monthly: 27, annual: 259 }, 
    PLATINUM: { monthly: 54, annual: 519 } 
  },
  'Vietnam': { 
    PRO: { monthly: 27, annual: 259 }, 
    PLATINUM: { monthly: 54, annual: 519 } 
  },
  'India': { 
    PRO: { monthly: 27, annual: 259 }, 
    PLATINUM: { monthly: 54, annual: 519 } 
  },
  'Turkey': { 
    PRO: { monthly: 27, annual: 259 }, 
    PLATINUM: { monthly: 54, annual: 519 } 
  },
  'Poland': { 
    PRO: { monthly: 27, annual: 259 }, 
    PLATINUM: { monthly: 54, annual: 519 } 
  },
  'Czech Republic': { 
    PRO: { monthly: 27, annual: 259 }, 
    PLATINUM: { monthly: 54, annual: 519 } 
  },
  'Hungary': { 
    PRO: { monthly: 27, annual: 259 }, 
    PLATINUM: { monthly: 54, annual: 519 } 
  },
  'South Africa': { 
    PRO: { monthly: 27, annual: 259 }, 
    PLATINUM: { monthly: 54, annual: 519 } 
  },
  
  // Default fallback
  'DEFAULT': { 
    PRO: { monthly: 29, annual: 279 }, 
    PLATINUM: { monthly: 99, annual: 799 } 
  }
};

/**
 * Detect if a subscription is annual based on next billing date
 * @param {Date} subscribeTime - When subscription started
 * @param {Date} nextBillingDate - Next billing date
 * @returns {string} 'annual' or 'monthly'
 */
const detectBillingInterval = (subscribeTime, nextBillingDate) => {
  if (!subscribeTime || !nextBillingDate) return 'monthly';
  
  const daysDiff = Math.ceil((new Date(nextBillingDate) - new Date(subscribeTime)) / (1000 * 60 * 60 * 24));
  
  // If next billing is more than 300 days away, likely annual
  return daysDiff > 300 ? 'annual' : 'monthly';
};

/**
 * Get expected regular price for a plan in a specific country
 * @param {string} planType - Plan type (PRO or PLATINUM)
 * @param {string} country - Country name
 * @param {string} billingInterval - Billing interval (monthly or annual)
 * @returns {number} Expected price in USD
 */
const getExpectedRegularPrice = (planType, country, billingInterval = 'monthly') => {
  const countryPricing = EXPECTED_REGULAR_PRICING[country] || EXPECTED_REGULAR_PRICING['DEFAULT'];
  const planPricing = countryPricing[planType] || countryPricing['PRO'];
  
  // Default to monthly if interval not specified or not recognized
  if (billingInterval === 'annual' || billingInterval === 'year') {
    return planPricing.annual;
  }
  return planPricing.monthly;
};

/**
 * Write results to CSV
 * @param {Array} results - Analysis results
 * @param {string} outputPath - Output CSV path
 */
const writeResultsToCSV = (results, outputPath) => {
  try {
    const headers = [
      'communityId',
      'communityCode',
      'communityName',
      'communityLink',
      'ownerEmail',
      'subscriberEmail',
      'planType',
      'planStatus',
      'country',
      'nextBillingDate',
      'minChargedUSD',
      'maxChargedUSD',
      'expectedRegularPriceUSD',
      'revenueLossPerRenewal',
      'totalRevenueLoss',
      'totalPaidUSD',
      'transactionCount',
      'monthlyChargesUSD',
      'renewalCyclesStuckAtTrial',
      'issueType',
    ];

    const rows = results.map((result) => [
      result.communityObjectId || '',
      result.communityCode || '',
      result.communityName || '',
      result.communityLink || '',
      result.communityOwnerEmail || '',
      result.subscriberEmail || '',
      result.planType || '',
      result.planStatus || '',
      result.checkoutCountry || '',
      result.nextBillingDate ? new Date(result.nextBillingDate).toISOString().split('T')[0] : '',
      result.minChargedUSD || '',
      result.maxChargedUSD || '',
      result.expectedRegularPriceUSD || '',
      result.revenueLossPerRenewal ? `$${result.revenueLossPerRenewal.toFixed(2)}` : '$0.00',
      result.totalRevenueLoss ? `$${result.totalRevenueLoss.toFixed(2)}` : '$0.00',
      result.totalPaidUSD || '',
      result.transactionCount || 0,
      result.monthlyChargesUSD || '',
      result.renewalCyclesStuckAtTrial || 0,
      result.issueType || '',
    ]);

    const csvContent = [headers, ...rows]
      .map((row) => row.map((field) => `"${field}"`).join(','))
      .join('\n');

    fs.writeFileSync(outputPath, csvContent);
    console.log(`✅ Results written to ${outputPath}`);
  } catch (error) {
    console.error('Error writing results to CSV:', error);
  }
};

/**
 * Format amount based on currency
 * @param {number} amount - Amount in cents
 * @param {string} currency - Currency code
 * @returns {string} Formatted amount
 */
const formatAmount = (amount, currency) => {
  if (!amount || amount === 0) return `${currency} 0`;
  
  // Convert cents to main currency unit
  const value = amount / 100;
  
  // For currencies like CLP that don't typically show decimal places
  const decimalPlaces = ['CLP', 'JPY', 'KRW', 'VND', 'IDR'].includes(currency) ? 0 : 2;
  
  return `${currency} ${value.toLocaleString('en-US', {
    minimumFractionDigits: decimalPlaces,
    maximumFractionDigits: decimalPlaces,
  })}`;
};

/**
 * Convert currency amount to USD cents
 * @param {number} amount - Amount in local currency cents
 * @param {string} currency - Currency code
 * @returns {number} Amount in USD cents
 */
const convertToUSDCents = (amount, currency) => {
  const rate = CURRENCY_TO_USD_RATES[currency] || 1;
  return Math.round(amount * rate);
};

/**
 * Check if amount is under $5 USD equivalent
 * @param {number} amount - Amount in local currency cents
 * @param {string} currency - Currency code
 * @returns {boolean}
 */
const isUnder5USD = (amount, currency) => {
  const usdCents = convertToUSDCents(amount, currency);
  return usdCents <= MAX_USD_CENTS;
};

/**
 * Format amount as USD equivalent
 * @param {number} amount - Amount in local currency cents
 * @param {string} currency - Currency code
 * @returns {string} Formatted USD equivalent
 */
const formatAsUSD = (amount, currency) => {
  if (!amount || amount === 0) return '$0.00';
  const usdCents = convertToUSDCents(amount, currency);
  return `$${(usdCents / 100).toFixed(2)}`;
};

/**
 * Format monthly charges array
 * @param {Array} transactionDetails - Array of transaction details
 * @param {string} currency - Currency code
 * @returns {string} Formatted monthly charges
 */
const formatMonthlyChargesUSD = (transactionDetails) => {
  if (!transactionDetails || transactionDetails.length === 0) return '';
  
  const charges = transactionDetails
    .sort((a, b) => new Date(a.date) - new Date(b.date))
    .map(tx => {
      const date = new Date(tx.date);
      const monthYear = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      const amountUSD = `$${(tx.amountUSD / 100).toFixed(2)}`;
      const type = tx.isRenewal ? 'R' : 'I'; // R = Renewal, I = Initial
      const cycle = tx.billingCycle || '?';
      return `${monthYear}: ${amountUSD} (${type}${cycle})`;
    });
  
  return charges.join(' | ');
};

/**
 * Find communities with trial pricing issues
 */
const findTrialPricingIssues = async () => {
  const results = [];
  
  try {
    // 1. Find communities still on trial that should have transitioned
    console.log('🔍 Finding communities still marked as on trial...');
    
    const overdueTrials = await CommunityPlanOrderModel.aggregate([
      {
        $match: {
          status: 'current',
          entityType: { $in: ['PRO', 'PLATINUM'] },
          isOnTrial: true,
          nextBillingDate: { $lt: new Date() }
        }
      },
      {
        $lookup: {
          from: 'communities',
          localField: 'communityObjectId',
          foreignField: '_id',
          as: 'community'
        }
      },
      {
        $unwind: {
          path: '$community',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $lookup: {
          from: 'learners',
          localField: 'learnerObjectId',
          foreignField: '_id',
          as: 'learner'
        }
      },
      {
        $unwind: {
          path: '$learner',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $project: {
          communityObjectId: 1,
          communityCode: '$community.code',
          communityName: '$community.name',
          communityLink: { $concat: ['https://nas.io/', '$community.code'] },
          communityOwnerEmail: '$community.createdBy',
          subscriberEmail: '$learner.email',
          planType: '$entityType',
          checkoutCountry: '$country.name',
          subscribeTime: '$createdAt',
          nextRenewalTime: '$nextBillingDate',
          currentBillingAmount: '$amountInLocalCurrency',
          status: 1,
          currency: '$localCurrency',
          trialAmount: '$amountInLocalCurrency',
          expectedPriceId: '$metadata.nextBillingPriceId',
          billingCycle: 1,
          isOnTrial: 1,
          lastPaymentStatus: '$paymentDetails.status',
          lastPaymentDate: '$paymentDetails.latestUpdatedTime',
          nextBillingDate: 1,
          createdAt: 1
        }
      }
    ]);

    console.log(`Found ${overdueTrials.length} communities with overdue trials`);

    // Process overdue trials - filter for communities paying less than $5 USD
    for (const order of overdueTrials) {
      // Only include if current billing amount is under $5 USD equivalent
      if (!isUnder5USD(order.currentBillingAmount, order.currency)) {
        continue;
      }

      const daysSinceTrialEnd = Math.floor(
        (new Date() - new Date(order.nextBillingDate)) / (1000 * 60 * 60 * 24)
      );

      results.push({
        communityCode: order.communityCode,
        communityName: order.communityName,
        communityLink: order.communityLink,
        communityOwnerEmail: order.communityOwnerEmail,
        subscriberEmail: order.subscriberEmail,
        communityObjectId: order.communityObjectId?.toString(),
        planType: order.planType,
        planStatus: order.status,
        checkoutCountry: order.checkoutCountry,
        subscribeTime: order.subscribeTime,
        nextRenewalTime: order.nextRenewalTime,
        nextBillingDate: order.nextRenewalTime,
        currentBillingAmount: order.currentBillingAmount,
        minChargedUSD: formatAsUSD(order.trialAmount, order.currency),
        maxChargedUSD: formatAsUSD(order.trialAmount, order.currency),
        expectedRegularPriceUSD: `$${getExpectedRegularPrice(order.planType, order.checkoutCountry, detectBillingInterval(order.subscribeTime, order.nextRenewalTime)).toFixed(2)}`,
        revenueLossPerRenewal: 0, // No renewals yet for overdue trials
        totalRevenueLoss: 0,
        totalPaidUSD: formatAsUSD(order.trialAmount, order.currency),
        transactionCount: 1,
        monthlyChargesUSD: '', // No transaction details available for overdue trials
        renewalCyclesStuckAtTrial: order.billingCycle || 0,
        isOnTrial: order.isOnTrial,
        lastPaymentStatus: order.lastPaymentStatus,
        lastPaymentDate: order.lastPaymentDate,
        nextBillingDate: order.nextBillingDate,
        daysSinceTrialEnd: daysSinceTrialEnd,
        issueType: 'OVERDUE_TRIAL'
      });
    }

    // 2. Find communities with low pricing in any transactions
    console.log('🔍 Finding communities with low prices in transactions...');

    const suspiciousTransactions = await CommunityPlanTransactionModel.aggregate([
      {
        $match: {
          entityType: { $in: ['PRO', 'PLATINUM'] },
          status: 'Success',
          createdAt: { $gte: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000) } // Last 180 days
        }
      },
      {
        $group: {
          _id: {
            communityObjectId: '$communityObjectId',
            currency: '$paidCurrency'
          },
          maxCycle: { $max: { $ifNull: ['$metadata.billingCycle', 1] } },
          transactionCount: { $sum: 1 },
          totalAmountPaid: { $sum: '$paidAmount' },
          totalAmountPaidUSD: { $sum: '$paidAmountInUsd' },
          latestTransaction: { $max: '$createdAt' },
          firstTransaction: { $min: '$createdAt' },
          uniqueAmounts: { $addToSet: '$paidAmount' },
          uniqueAmountsUSD: { $addToSet: '$paidAmountInUsd' },
          minAmountUSD: { $min: '$paidAmountInUsd' },
          maxAmountUSD: { $max: '$paidAmountInUsd' },
          planOrderObjectId: { $first: '$planOrderObjectId' },
          planType: { $first: '$entityType' },
          transactionIds: { $push: '$_id' },
          transactionDetails: { 
            $push: {
              amount: '$paidAmount',
              amountUSD: '$paidAmountInUsd',
              date: '$createdAt',
              billingCycle: '$metadata.billingCycle',
              isRenewal: '$metadata.isRenewalPayment',
              transactionType: '$transactionType'
            }
          }
        }
      },
      {
        $lookup: {
          from: 'community_plan_orders',
          localField: 'planOrderObjectId',
          foreignField: '_id',
          as: 'order'
        }
      },
      {
        $unwind: {
          path: '$order',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $lookup: {
          from: 'communities',
          localField: '_id.communityObjectId',
          foreignField: '_id',
          as: 'community'
        }
      },
      {
        $unwind: {
          path: '$community',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $lookup: {
          from: 'learners',
          localField: 'order.learnerObjectId',
          foreignField: '_id',
          as: 'learner'
        }
      },
      {
        $unwind: {
          path: '$learner',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $project: {
          communityObjectId: '$_id.communityObjectId',
          communityCode: '$community.code',
          communityName: '$community.name',
          communityLink: { $concat: ['https://nas.io/', '$community.code'] },
          communityOwnerEmail: '$community.createdBy',
          subscriberEmail: '$learner.email',
          planType: 1,
          checkoutCountry: '$order.country.name',
          subscribeTime: '$order.createdAt',
          nextRenewalTime: '$order.nextBillingDate',
          currentBillingAmount: '$order.amountInLocalCurrency',
          currency: '$_id.currency',
          uniqueAmounts: 1,
          uniqueAmountsUSD: 1,
          minAmountUSD: 1,
          maxAmountUSD: 1,
          maxCycle: 1,
          transactionCount: 1,
          totalAmountPaid: 1,
          totalAmountPaidUSD: 1,
          latestTransaction: 1,
          firstTransaction: 1,
          orderStatus: '$order.status',
          isOnTrial: '$order.isOnTrial',
          nextBillingPriceId: '$order.metadata.nextBillingPriceId',
          transactionIds: 1,
          transactionDetails: 1
        }
      }
    ]);

    // Filter for communities with actual billing transition problems
    const lowPriceIssues = suspiciousTransactions.filter(tx => {
      if (tx.orderStatus !== 'current') return false;
      
      // Skip if only 1 transaction (could be just trial, no renewals yet)
      if (tx.transactionCount <= 1) return false;
      
      // Get renewal transactions (exclude initial subscription)
      const renewalTransactions = tx.transactionDetails.filter(detail => detail.isRenewal);
      
      // Skip if no renewal transactions
      if (renewalTransactions.length === 0) return false;
      
      // Flag if ANY renewal transaction is under $5 USD (failed transition)
      const hasLowRenewalPrice = renewalTransactions.some(renewal => renewal.amountUSD <= 500);
      
      return hasLowRenewalPrice;
    });

    console.log(`Found ${lowPriceIssues.length} communities with low price issues`);
    console.log(`Total suspicious transactions analyzed: ${suspiciousTransactions.length}`);

    // Add to results
    for (const issue of lowPriceIssues) {
      // Get renewal transactions only
      const renewalTransactions = issue.transactionDetails.filter(detail => detail.isRenewal);
      
      // Count how many renewal transactions are at trial price (under $5 USD)
      const trialPriceRenewals = renewalTransactions.filter(renewal => renewal.amountUSD <= 500);
      
      // Detect billing interval
      const billingInterval = detectBillingInterval(issue.subscribeTime, issue.nextRenewalTime);
      
      // Calculate actual revenue loss for each stuck renewal
      const expectedPrice = getExpectedRegularPrice(issue.planType, issue.checkoutCountry, billingInterval);
      const expectedPriceCents = expectedPrice * 100; // Convert to cents
      
      let totalRevenueLoss = 0;
      trialPriceRenewals.forEach(renewal => {
        const loss = (expectedPriceCents - renewal.amountUSD) / 100; // Convert back to dollars
        totalRevenueLoss += loss;
      });
      
      const avgRevenueLossPerStuckRenewal = trialPriceRenewals.length > 0 ? totalRevenueLoss / trialPriceRenewals.length : 0;
      
      // Check if latest renewal is still at trial price
      const latestRenewal = renewalTransactions.sort((a, b) => new Date(b.date) - new Date(a.date))[0];
      const isCurrentlyStuck = latestRenewal && latestRenewal.amountUSD <= 500;
      
      // Determine issue type
      const issueType = isCurrentlyStuck ? 'CURRENTLY_STUCK_AT_TRIAL_PRICE' : 'PREVIOUSLY_STUCK_NOW_CORRECTED';
      
      results.push({
        communityCode: issue.communityCode,
        communityName: issue.communityName,
        communityLink: issue.communityLink,
        communityOwnerEmail: issue.communityOwnerEmail,
        subscriberEmail: issue.subscriberEmail,
        communityObjectId: issue.communityObjectId?.toString(),
        planType: issue.planType,
        planStatus: issue.orderStatus,
        checkoutCountry: issue.checkoutCountry,
        subscribeTime: issue.subscribeTime,
        nextRenewalTime: issue.nextRenewalTime,
        nextBillingDate: issue.nextRenewalTime,
        minChargedUSD: `$${(issue.minAmountUSD / 100).toFixed(2)}`,
        maxChargedUSD: `$${(issue.maxAmountUSD / 100).toFixed(2)}`,
        expectedRegularPriceUSD: `$${expectedPrice.toFixed(2)}`,
        revenueLossPerRenewal: avgRevenueLossPerStuckRenewal,
        totalRevenueLoss: totalRevenueLoss,
        totalPaidUSD: `$${(issue.totalAmountPaidUSD / 100).toFixed(2)}`,
        transactionCount: issue.transactionCount,
        monthlyChargesUSD: formatMonthlyChargesUSD(issue.transactionDetails),
        renewalCyclesStuckAtTrial: trialPriceRenewals.length, // Only count actual trial price renewals
        isOnTrial: issue.isOnTrial,
        lastPaymentStatus: 'success',
        lastPaymentDate: issue.latestTransaction,
        firstPaymentDate: issue.firstTransaction,
        nextBillingDate: issue.nextRenewalTime,
        daysSinceTrialEnd: '',
        issueType: issueType
      });
    }

    // 3. Find communities with failed payments trying to charge regular price
    console.log('🔍 Finding communities with failed transition payments...');

    const failedTransitions = await CommunityPlanOrderModel.find({
      status: 'current',
      entityType: { $in: ['PRO', 'PLATINUM'] },
      isOnTrial: true,
      'paymentDetails.status': 'failed',
      'paymentDetails.latestUpdatedTime': { 
        $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) 
      }
    })
    .populate('communityObjectId', 'code name createdBy')
    .populate('learnerObjectId', 'email')
    .lean();

    console.log(`Found ${failedTransitions.length} communities with failed transitions`);

    for (const order of failedTransitions) {
      // Only include if current billing amount is under $5 USD equivalent
      if (!isUnder5USD(order.amountInLocalCurrency, order.localCurrency)) {
        continue;
      }

      results.push({
        communityCode: order.communityObjectId?.code,
        communityName: order.communityObjectId?.name,
        communityLink: order.communityObjectId?.code ? `https://nas.io/${order.communityObjectId.code}` : '',
        communityOwnerEmail: order.communityObjectId?.createdBy,
        subscriberEmail: order.learnerObjectId?.email,
        communityObjectId: order.communityObjectId?._id?.toString(),
        planType: order.entityType,
        planStatus: order.status,
        checkoutCountry: order.country?.name,
        subscribeTime: order.createdAt,
        nextRenewalTime: order.nextBillingDate,
        currentBillingAmount: order.amountInLocalCurrency,
        minChargedUSD: formatAsUSD(order.amountInLocalCurrency, order.localCurrency),
        maxChargedUSD: formatAsUSD(order.amountInLocalCurrency, order.localCurrency),
        expectedRegularPriceUSD: `$${getExpectedRegularPrice(order.entityType, order.country?.name, detectBillingInterval(order.createdAt, order.nextBillingDate)).toFixed(2)}`,
        revenueLossPerRenewal: 0, // Failed transitions haven't generated renewals
        totalRevenueLoss: 0,
        totalPaidUSD: formatAsUSD(order.amountInLocalCurrency, order.localCurrency),
        transactionCount: 1,
        monthlyChargesUSD: '', // No transaction details available for failed transitions
        renewalCyclesStuckAtTrial: order.billingCycle || 0,
        isOnTrial: order.isOnTrial,
        lastPaymentStatus: order.paymentDetails?.status,
        lastPaymentDate: order.paymentDetails?.latestUpdatedTime,
        nextBillingDate: order.nextBillingDate,
        daysSinceTrialEnd: order.nextBillingDate ? 
          Math.floor((new Date() - new Date(order.nextBillingDate)) / (1000 * 60 * 60 * 24)) : 0,
        issueType: 'FAILED_TRANSITION'
      });
    }

    return results;
  } catch (error) {
    console.error('Error finding trial pricing issues:', error);
    throw error;
  }
};

/**
 * Generate summary statistics
 * @param {Array} results - Analysis results
 */
const generateSummary = (results) => {
  const summary = {
    total: results.length,
    byIssueType: {},
    byCurrency: {},
    byPlanType: {},
    totalRevenueLoss: 0,
    revenueLossByType: {},
    transactionStats: {
      totalTransactions: 0,
      totalAmountPaid: {},
      avgTransactionsPerCommunity: 0,
      avgRenewalCyclesStuckAtTrial: 0
    }
  };

  let totalTransactions = 0;
  let totalMonths = 0;
  let communitiesWithTransactionData = 0;

  results.forEach(result => {
    // Count by issue type
    summary.byIssueType[result.issueType] = (summary.byIssueType[result.issueType] || 0) + 1;
    
    // Count by currency
    summary.byCurrency[result.currency] = (summary.byCurrency[result.currency] || 0) + 1;
    
    // Count by plan type
    summary.byPlanType[result.planType] = (summary.byPlanType[result.planType] || 0) + 1;

    // Revenue loss calculations
    if (result.totalRevenueLoss && result.totalRevenueLoss > 0) {
      summary.totalRevenueLoss += result.totalRevenueLoss;
      
      // Track revenue loss by issue type
      if (!summary.revenueLossByType[result.issueType]) {
        summary.revenueLossByType[result.issueType] = 0;
      }
      summary.revenueLossByType[result.issueType] += result.totalRevenueLoss;
    }

    // Transaction statistics
    if (result.transactionCount && result.transactionCount > 0) {
      totalTransactions += result.transactionCount;
      communitiesWithTransactionData++;
      
      // Sum total amounts by currency
      if (!summary.transactionStats.totalAmountPaid[result.currency]) {
        summary.transactionStats.totalAmountPaid[result.currency] = 0;
      }
      summary.transactionStats.totalAmountPaid[result.currency] += result.totalAmountPaid || 0;
    }

    if (result.renewalCyclesStuckAtTrial && result.renewalCyclesStuckAtTrial > 0) {
      totalMonths += result.renewalCyclesStuckAtTrial;
    }
  });

  summary.transactionStats.totalTransactions = totalTransactions;
  summary.transactionStats.avgTransactionsPerCommunity = communitiesWithTransactionData > 0 
    ? Math.round(totalTransactions / communitiesWithTransactionData * 100) / 100 
    : 0;
  summary.transactionStats.avgRenewalCyclesStuckAtTrial = communitiesWithTransactionData > 0 
    ? Math.round(totalMonths / communitiesWithTransactionData * 100) / 100 
    : 0;

  return summary;
};

const start = async () => {
  try {
    console.log('🚀 Starting trial pricing issues analysis...');
    
    await mongoClient.connect();
    
    // Create output directory if it doesn't exist
    const outputDir = path.join(__dirname, 'output');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // Find issues
    const results = await findTrialPricingIssues();
    
    // Generate summary
    const summary = generateSummary(results);
    
    console.log('\n📊 Analysis Summary:');
    console.log(`Total issues found: ${summary.total}`);
    console.log(`\n💰 TOTAL REVENUE LOSS: $${summary.totalRevenueLoss.toFixed(2)}`);
    console.log('\nRevenue Loss by Issue Type:');
    Object.entries(summary.revenueLossByType).forEach(([type, loss]) => {
      console.log(`  ${type}: $${loss.toFixed(2)}`);
    });
    console.log('\nBy Issue Type:');
    Object.entries(summary.byIssueType).forEach(([type, count]) => {
      console.log(`  ${type}: ${count}`);
    });
    console.log('\nBy Currency:');
    Object.entries(summary.byCurrency).forEach(([currency, count]) => {
      console.log(`  ${currency}: ${count}`);
    });
    console.log('\nBy Plan Type:');
    Object.entries(summary.byPlanType).forEach(([plan, count]) => {
      console.log(`  ${plan}: ${count}`);
    });
    
    // Write results to CSV
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const outputPath = path.join(outputDir, `trial_pricing_issues_${timestamp}.csv`);
    writeResultsToCSV(results, outputPath);
    
    // Also write summary to JSON
    const summaryPath = path.join(outputDir, `trial_pricing_summary_${timestamp}.json`);
    fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
    console.log(`\n📄 Summary written to ${summaryPath}`);
    
  } catch (error) {
    console.error('❌ Error in analysis:', error);
  } finally {
    process.exit(0);
  }
};

// Run the script
start();