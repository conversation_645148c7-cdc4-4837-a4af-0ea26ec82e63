#!/usr/bin/env node

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {};

// Parse arguments
for (let i = 0; i < args.length; i++) {
  switch (args[i]) {
    case '--items':
    case '-i':
      i++;
      try {
        options.items = JSON.parse(args[i]);
      } catch (e) {
        console.error('Invalid JSON for items:', e.message);
        process.exit(1);
      }
      break;
    case '--community-code':
    case '-c':
      i++;
      options.communityCode = args[i];
      break;
    case '--community-id':
      i++;
      options.communityId = args[i];
      break;
    case '--payment-provider':
    case '-p':
      i++;
      options.paymentProvider = args[i];
      break;
    case '--help':
    case '-h':
      showHelp();
      process.exit(0);
      break;
    case '--ip':
      i++;
      options.ipAddress = args[i];
      break;
    case '--email':
      i++;
      options.userEmail = args[i];
      break;
    case '--password':
      i++;
      options.userPassword = args[i];
      break;
    case '--api-url':
      i++;
      options.apiUrl = args[i];
      break;
    case '--card-number':
      i++;
      options.cardNumber = args[i];
      break;
    case '--card-name':
      i++;
      options.cardName = args[i];
      break;
    case '--card-expiry':
      i++;
      options.cardExpiry = args[i];
      break;
    case '--card-cvv':
      i++;
      options.cardCvv = args[i];
      break;
    default:
      console.error(`Unknown argument: ${args[i]}`);
      showHelp();
      process.exit(1);
  }
}

function showHelp() {
  console.log(`
Community Signup Test Script - Fully Automated & Dynamic
========================================================

This script takes just the items and community info and automatically:
1. Calls signup API to get signup token and community ID
2. Generates EBANX payment token
3. Calls confirm API to complete payment

Usage: node test-community-signup-full-auto.js [options]

Required Options:
  --items, -i <json>           Items array as JSON
  --community-code, -c <code>  Community code

Optional Options:
  --community-id <id>         Community ID (auto-detected if not provided)
  --payment-provider, -p <provider>  Payment provider (default: ebanx)
  --ip <address>              IP address for geo-location (default: ***********)
  --email <email>             User email for authentication (default: <EMAIL>)
  --password <password>       User password for authentication (default: Admin123)
  --api-url <url>             API base URL (default: http://localhost:3003)
  --card-number <number>      Test card number (default: ****************)
  --card-name <name>          Cardholder name (default: Test User)
  --card-expiry <expiry>      Card expiry MM/YY (default: 12/25)
  --card-cvv <cvv>           Card CVV (default: 123)
  --help, -h                  Show this help message

Environment Variables:
  SIGNATURE_SECRET            Secret key for payload signature (required)
  BYPASS_SIGNATURE           Set to 'true' to bypass signature validation
  EBANX_INTEGRATION_KEY      EBANX integration key for token generation

Examples:
  # Test challenge signup in GRANDFATHER_2 community
  SIGNATURE_SECRET="secret123" node test-community-signup-full-auto.js \\
    --items '[{"type":"CHALLENGE","entityId":"687b28dcf6fc39ef65669a02"}]' \\
    --community-code "GRANDFATHER_2"

  # Test subscription signup in different community
  SIGNATURE_SECRET="secret123" node test-community-signup-full-auto.js \\
    --items '[{"type":"SUBSCRIPTION","priceId":"price_123"}]' \\
    --community-code "MY_COMMUNITY" \\
    --community-id "685a6691e41947de10aabc6e"

  # Test event signup with custom card
  SIGNATURE_SECRET="secret123" node test-community-signup-full-auto.js \\
    --items '[{"type":"EVENT","entityId":"event_123"}]' \\
    --community-code "EVENT_COMMUNITY" \\
    --card-number "****************" \\
    --card-name "John Doe"

  # Test multiple items
  SIGNATURE_SECRET="secret123" node test-community-signup-full-auto.js \\
    --items '[{"type":"SUBSCRIPTION","priceId":"price_123"},{"type":"CHALLENGE","entityId":"challenge_456"}]' \\
    --community-code "PREMIUM_COMMUNITY"
`);
}

// Validate required parameters
if (!options.items) {
  console.error('❌ Error: --items is required');
  console.error(
    'Example: --items \'[{"type":"CHALLENGE","entityId":"123"}]\''
  );
  process.exit(1);
}

if (!options.communityCode) {
  console.error('❌ Error: --community-code is required');
  console.error('Example: --community-code "GRANDFATHER_2"');
  process.exit(1);
}

// Configuration with defaults
const API_BASE_URL =
  options.apiUrl || process.env.API_URL || 'http://localhost:3003';
const AUTH_API_URL = process.env.AUTH_API_URL || 'https://api.dev-nas.io';
const USER_EMAIL =
  options.userEmail || process.env.USER_EMAIL || '<EMAIL>';
const USER_PASSWORD =
  options.userPassword || process.env.USER_PASSWORD || 'Admin123';
const SIGNATURE_SECRET = process.env.SIGNATURE_SECRET || '';
const BYPASS_SIGNATURE = process.env.BYPASS_SIGNATURE === 'true';
const EBANX_INTEGRATION_KEY = process.env.EBANX_INTEGRATION_KEY || '';
const IP_ADDRESS = options.ipAddress || '***********';

// Dynamic values from user input
const COMMUNITY_CODE = options.communityCode;
const COMMUNITY_ID = options.communityId; // Optional, will be auto-detected
const PAYMENT_PROVIDER = options.paymentProvider || 'ebanx';
const ITEMS = options.items;

// Card details for token generation
const CARD_DETAILS = {
  number: options.cardNumber || '****************',
  name: options.cardName || 'Test User',
  expiry: options.cardExpiry || '12/25',
  cvv: options.cardCvv || '123',
};

// Helper function to generate payload signature
function generateSignature(payload, secretKey) {
  const requestBody = JSON.stringify(payload);
  const nonce = crypto.randomBytes(16).toString('hex');
  const timestamp = Date.now();

  const message = `${requestBody}:${nonce}:${timestamp}`;
  const signature = crypto
    .createHmac('sha256', secretKey)
    .update(message)
    .digest('hex');

  console.log('Signature Debug:');
  console.log('- Nonce:', nonce);
  console.log('- Timestamp:', timestamp);
  console.log('- Signature:', signature);

  return { signature, nonce, timestamp };
}

// Helper function to log responses
function logResponse(step, response) {
  console.log(`\n=== ${step} ===`);
  console.log('Status:', response.status);
  console.log('Data:', JSON.stringify(response.data, null, 2));

  // Save response to file
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `response-${step
    .toLowerCase()
    .replace(/\s+/g, '-')}-${timestamp}.json`;
  fs.writeFileSync(
    path.join(__dirname, filename),
    JSON.stringify(
      {
        step,
        timestamp: new Date().toISOString(),
        status: response.status,
        headers: response.headers,
        data: response.data,
      },
      null,
      2
    )
  );
  console.log(`Response saved to: ${filename}`);
}

// Function to get auth token
async function getAuthToken() {
  console.log('\n--- Getting Auth Token ---');
  console.log('Email:', USER_EMAIL);

  if (!USER_PASSWORD) {
    throw new Error(
      'USER_PASSWORD environment variable or --password argument is required'
    );
  }

  try {
    const loginResponse = await axios.post(
      `${AUTH_API_URL}/api/v1/log-in`,
      {
        email: USER_EMAIL,
        password: USER_PASSWORD,
        communityMandatory: true,
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    const { token, refresh_token } = loginResponse.data || {};

    if (!token) {
      throw new Error('No access token received from login');
    }

    console.log('✅ Successfully authenticated');
    console.log('Access Token:', token.substring(0, 20) + '...');

    return { accessToken: token, refreshToken: refresh_token };
  } catch (error) {
    console.error(
      '❌ Authentication failed:',
      error.response?.data || error.message
    );
    throw error;
  }
}

// Function to get checkout token
async function getCheckoutToken(accessToken) {
  console.log('\n--- Getting Checkout Token ---');

  try {
    const checkoutTokenResponse = await axios.post(
      `${API_BASE_URL}/api/v1/auth/checkout-token`,
      {},
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    const checkoutToken =
      checkoutTokenResponse.data.data?.token ||
      checkoutTokenResponse.data.token;

    if (!checkoutToken) {
      console.log('No checkout token endpoint found, using access token');
      return accessToken;
    }

    console.log('✅ Got checkout token');
    return checkoutToken;
  } catch (error) {
    console.log(
      'Checkout token endpoint not available, using access token'
    );
    return accessToken;
  }
}

// Function to extract community ID from signup response
function extractCommunityId(signupResponse) {
  const responseData = signupResponse.data.data || {};

  // Try to extract from various possible locations
  if (responseData.communityId) {
    return responseData.communityId;
  }

  if (responseData.community?._id) {
    return responseData.community._id;
  }

  if (responseData.community?.id) {
    return responseData.community.id;
  }

  // Try to decode from signup token
  try {
    const { signupToken } = responseData;
    if (signupToken) {
      const base64Payload = signupToken.split('.')[1];
      const payload = JSON.parse(
        Buffer.from(base64Payload, 'base64').toString()
      );
      if (payload.communityObjectId) {
        return payload.communityObjectId;
      }
    }
  } catch (e) {
    console.log('Could not decode signup token to extract community ID');
  }

  // Fallback to provided community ID or default
  return COMMUNITY_ID || '685a6691e41947de10aabc6e';
}

// Function to generate EBANX payment token
async function generateEbanxPaymentToken(cardDetails, amount = 100.0) {
  console.log('\n--- Generating EBANX Payment Token ---');
  console.log('Card:', cardDetails.number.substr(0, 4) + '************');

  // EBANX test API endpoint
  const ebanxApiUrl = 'https://sandbox.ebanx.com/ws/direct';

  try {
    const ebanxPayload = {
      integration_key: EBANX_INTEGRATION_KEY || 'test_ik_12345', // Use test key if not provided
      operation: 'request',
      mode: 'full',
      payment: {
        merchant_payment_code: `test_${Date.now()}`,
        amount_total: amount,
        currency_code: 'MXN',
        name: cardDetails.name,
        email: USER_EMAIL,
        payment_type_code: 'visa',
        creditcard: {
          card_number: cardDetails.number,
          card_name: cardDetails.name,
          card_due_date: cardDetails.expiry,
          card_cvv: cardDetails.cvv,
        },
      },
    };

    console.log('Calling EBANX API...');
    const response = await axios.post(ebanxApiUrl, ebanxPayload);

    if (response.data.status === 'SUCCESS') {
      const paymentHash = response.data.payment.hash;
      console.log(
        '✅ EBANX token generated:',
        paymentHash.substring(0, 20) + '...'
      );

      return {
        token: paymentHash,
        masked_card_number:
          cardDetails.number.substr(0, 4) +
          'XXXXXXXX' +
          cardDetails.number.substr(-4),
        payment_type_code: 'visa',
        card_unique_id: null,
      };
    } else {
      throw new Error(`EBANX API error: ${response.data.status_message}`);
    }
  } catch (error) {
    console.log('⚠️  EBANX API call failed, using mock token for testing');
    console.log('Error:', error.response?.data || error.message);

    // Return mock token for testing when EBANX API is not available
    return {
      token:
        '039c2931b43049a2b6d4887e9adb18622b0fad3a28c6a4d40f9c45d8b09d66bba850c37b490d6b6142cc097e0932abf35218f28f605d45181ffee1e928a31415',
      masked_card_number:
        cardDetails.number.substr(0, 4) +
        'XXXXXXXX' +
        cardDetails.number.substr(-4),
      payment_type_code: 'visa',
      card_unique_id: null,
    };
  }
}

// Main function to test signup flow
async function testFullSignupFlow() {
  try {
    console.log(
      'Community Signup Test Script - Fully Automated & Dynamic'
    );
    console.log(
      '========================================================'
    );
    console.log('API URL:', API_BASE_URL);
    console.log('Community Code:', COMMUNITY_CODE);
    console.log(
      'Community ID:',
      COMMUNITY_ID || 'Auto-detect from response'
    );
    console.log('Payment Provider:', PAYMENT_PROVIDER);
    console.log('Items:', JSON.stringify(ITEMS, null, 2));
    console.log('IP Address:', IP_ADDRESS);

    // Step 1: Get authentication token
    const { accessToken } = await getAuthToken();

    // Step 2: Get checkout token (if needed)
    const checkoutToken = await getCheckoutToken(accessToken);

    // Step 3: Build and send signup payload
    console.log('\n--- Step 3: Building and sending signup payload ---');

    const signupPayload = {
      communityCode: COMMUNITY_CODE,
      timezone: 'Asia/Singapore',
      trackingData: {
        source: 'organic',
        _ga: 'GA1.1.820189556.**********',
        _ga_SID: 'GS2.1.s1752936594$o8$g1$t1752936598$j56$l0$h0',
        _fbc: '',
        _fbp: 'fb.1.**********337.740577413731671185',
        country: 'MX',
        referralCode: null,
        productCard: null,
      },
      requestor: 'signupRequestor',
      memberInfo: {
        email: USER_EMAIL,
        firstName: CARD_DETAILS.name.split(' ')[0] || 'Test',
        lastName: CARD_DETAILS.name.split(' ')[1] || 'User',
        languagePreference: 'en',
      },
      paymentProvider: PAYMENT_PROVIDER,
      items: ITEMS,
    };

    console.log('Signup Payload:', JSON.stringify(signupPayload, null, 2));

    // Prepare headers
    const headers = {
      Authorization: `Bearer ${checkoutToken}`,
      'Content-Type': 'application/json',
      'x-forwarded-for': IP_ADDRESS,
      'User-Agent': 'test-script/1.0',
    };

    // Add signature headers if not bypassing
    if (!BYPASS_SIGNATURE && SIGNATURE_SECRET) {
      const { signature, nonce, timestamp } = generateSignature(
        signupPayload,
        SIGNATURE_SECRET
      );
      headers.signature = signature;
      headers.nonce = nonce;
      headers.timestamp = timestamp.toString();
      console.log('Added signature headers');
    } else if (BYPASS_SIGNATURE) {
      console.log('Bypassing signature validation');
    } else {
      console.log('⚠️  No signature secret provided, may fail validation');
    }

    const signupResponse = await axios.post(
      `${API_BASE_URL}/api/v1/communities/signup`,
      signupPayload,
      { headers }
    );

    logResponse('Signup Response', signupResponse);

    // Extract important data from signup response
    const responseData = signupResponse.data.data || {};
    const { signupToken, paymentStatus, entityStatus } = responseData;

    // Extract or use provided community ID
    const detectedCommunityId = extractCommunityId(signupResponse);
    console.log('\n--- Extracted Community Information ---');
    console.log('Detected Community ID:', detectedCommunityId);

    if (!signupToken) {
      console.log(
        '\n⚠️  No signup token received - payment may not be required'
      );
      console.log(
        'Payment Required:',
        paymentStatus?.requirePayment || false
      );
      console.log('Already Joined:', entityStatus?.alreadyJoined || false);

      if (!paymentStatus?.requirePayment) {
        console.log('\n✅ No payment required - user already has access');
        return;
      }

      throw new Error('No signup token received but payment is required');
    }

    console.log('\n--- Important Values from Signup ---');
    console.log('Signup Token:', signupToken.substring(0, 50) + '...');
    console.log('Payment Required:', paymentStatus?.requirePayment);
    console.log('Currency:', paymentStatus?.currency);
    console.log('Amount:', paymentStatus?.paymentMetadata?.amount);

    // Step 4: Generate payment token
    const estimatedAmount =
      paymentStatus?.paymentMetadata?.amount || 100.0;
    const paymentTokenDetails = await generateEbanxPaymentToken(
      CARD_DETAILS,
      estimatedAmount
    );

    // Step 5: Build and send confirm payload
    console.log('\n--- Step 5: Building and sending confirm payload ---');

    const confirmType =
      entityStatus?.type ||
      paymentStatus?.type ||
      ITEMS[0]?.type ||
      'CHALLENGE';

    const confirmPayload = {
      signupToken,
      confirmType,
      metadata: {
        redirectUrl: `https://dev-nas.io/checkout-global?communityId=${detectedCommunityId}&communityCode=${COMMUNITY_CODE}&requestor=signupRequestor`,
        paymentTypeCode: paymentTokenDetails.payment_type_code,
        paymentTokenDetails,
        savePaymentMethod: true,
      },
      paymentMethodId: paymentTokenDetails.token,
    };

    console.log(
      'Confirm Payload:',
      JSON.stringify(confirmPayload, null, 2)
    );

    // Prepare confirm headers
    const confirmHeaders = {
      Authorization: `Bearer ${checkoutToken}`,
      'Content-Type': 'application/json',
      'x-forwarded-for': IP_ADDRESS,
      'User-Agent': 'test-script/1.0',
    };

    // Add signature headers for confirm request
    if (!BYPASS_SIGNATURE && SIGNATURE_SECRET) {
      const { signature, nonce, timestamp } = generateSignature(
        confirmPayload,
        SIGNATURE_SECRET
      );
      confirmHeaders.signature = signature;
      confirmHeaders.nonce = nonce;
      confirmHeaders.timestamp = timestamp.toString();
    }

    const confirmResponse = await axios.post(
      `${API_BASE_URL}/api/v1/communities/signup/confirm`,
      confirmPayload,
      { headers: confirmHeaders }
    );

    logResponse('Confirm Response', confirmResponse);

    console.log('\n✅ Complete signup flow finished successfully!');
    console.log('Final Status:', confirmResponse.data.data?.status);
    console.log(
      'Community:',
      `${COMMUNITY_CODE} (${detectedCommunityId})`
    );
    console.log('Items Processed:', ITEMS.length);
  } catch (error) {
    console.error('\n❌ Error during signup flow:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error(
        'Error Data:',
        JSON.stringify(error.response.data, null, 2)
      );

      // Save error response
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `error-${timestamp}.json`;
      fs.writeFileSync(
        path.join(__dirname, filename),
        JSON.stringify(
          {
            timestamp: new Date().toISOString(),
            status: error.response.status,
            headers: error.response.headers,
            data: error.response.data,
            config: {
              url: error.config.url,
              method: error.config.method,
              headers: error.config.headers,
              data: error.config.data,
            },
          },
          null,
          2
        )
      );
      console.error(`Error details saved to: ${filename}`);
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Run the test
if (require.main === module) {
  testFullSignupFlow();
}

module.exports = { testFullSignupFlow };

