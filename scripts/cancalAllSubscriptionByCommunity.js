require('module-alias/register');

require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
  override: true,
});
const fs = require('fs').promises;
const mongoClient = require('../src/mongoClient');
const {
  COMMUNITY_SUBSCRIPTION_STATUSES,
} = require('../src/constants/common');
const SubscriptionModel = require('../src/communitiesAPI/models/communitySubscriptions.model');
const {
  cancelSubscriptionService,
} = require('../src/services/communitySubscription');

const BATCH_SIZE = 1000;

const cancelSubscription = async (
  communityCode,
  emails = [],
  cancelForAll = false
) => {
  const query = {
    communityCode,
    status: 'Current',
  };
  if (!cancelForAll) {
    query.email = { $in: emails };
  }

  const subscriptions = await SubscriptionModel.find(query).lean();

  if (subscriptions.length === 0) {
    return;
  }

  const batches = [];

  for (let i = 0; i < subscriptions.length; i += BATCH_SIZE) {
    const batch = subscriptions.slice(i, i + BATCH_SIZE);
    batches.push(batch);
  }

  for await (const batch of batches) {
    const timestamp = Date.now();

    const cancellationReason = 'Requested by community manager';

    await Promise.all(
      batch.map(async (subscription) => {
        const message = `${subscription.email},${subscription.status},${subscription.amount},${subscription.interval},${subscription.intervalCount},${subscription.billingCycle},${subscription.paymentProvider},${subscription.stripeSubscriptionId}`;
        console.log(message);

        if (subscription.stripeSubscriptionId) {
          await cancelSubscriptionService.cancelPaidSubscription(
            subscription,
            cancellationReason,
            '**************'
          );
        } else {
          await SubscriptionModel.updateOne(
            { _id: subscription._id },
            {
              $set: {
                status: COMMUNITY_SUBSCRIPTION_STATUSES.CANCELLED,
                cancellationReason,
                cancelledAt: timestamp,
                unsubscribedAt: timestamp,
              },
            }
          ).lean();
        }

        await fs.appendFile(
          `logs/cancel_all_subscription-${timestamp}.log`,
          `${message}\n`
        );
      })
    );
  }
};

const start = async () => {
  try {
    await mongoClient.connect();

    const communityCodes = [];

    for await (const communityCode of communityCodes) {
      await cancelSubscription(communityCode, [], true);
      console.log(`Canceled ALL Subscriptions for ${communityCode}`);
    }

    console.log(`Canceled ALL subscriptions`);
  } catch (error) {
    console.log(error);
    console.log('Error running for cancel subscription script');
  } finally {
    process.exit(0);
  }
};

start();
