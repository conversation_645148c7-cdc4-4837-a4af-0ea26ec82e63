require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const fs = require('fs');
const path = require('path');
const mongoClient = require('../src/mongoClient');
const CommunityModel = require('../src/communitiesAPI/models/community.model');

/**
 * Fuzzy match names similar to "Nusier Yassin" using proper plan orders
 */
const findNusierYassinCommunitiesWithPlanOrders = async () => {
  try {
    console.log('🔍 Searching for communities similar to "Nusier Yassin" with proper plan orders...');
    
    // Use aggregation to get communities with proper plan order data
    const pipeline = [
      // Match communities that match our patterns
      {
        $match: {
          $or: [
            { name: /nas.*yassin/i },
            { name: /nusier/i },
            { name: /nuseir/i },
            { name: /nusair/i },
            { name: /nasir.*yassin/i },
            { name: /nas.*daily/i },
            { name: /nasdaily/i },
            { code: /nas.*daily/i },
            { code: /nasdaily/i },
            { createdBy: /nas.*yassin/i },
            { createdBy: /nusier/i },
            { createdBy: /nuseir/i },
            { createdBy: /contentteam@nasdaily/i },
          ]
        }
      },
      // Lookup user information
      {
        $lookup: {
          from: 'users',
          localField: 'createdBy',
          foreignField: 'email',
          as: 'ownerUser'
        }
      },
      // Unwind user (if exists)
      {
        $unwind: {
          path: '$ownerUser',
          preserveNullAndEmptyArrays: true
        }
      },
      // Lookup learner information
      {
        $lookup: {
          from: 'learners',
          localField: 'ownerUser.learner',
          foreignField: '_id',
          as: 'ownerLearner'
        }
      },
      // Unwind learner (if exists)
      {
        $unwind: {
          path: '$ownerLearner',
          preserveNullAndEmptyArrays: true
        }
      },
      // Lookup country information based on learner's countryId
      {
        $lookup: {
          from: 'country_currency_mapping',
          localField: 'ownerLearner.countryId',
          foreignField: 'countryId',
          as: 'ownerCountryInfo'
        }
      },
      // Unwind country info (if exists)
      {
        $unwind: {
          path: '$ownerCountryInfo',
          preserveNullAndEmptyArrays: true
        }
      },
      // Lookup plan orders for this community
      {
        $lookup: {
          from: 'community_plan_orders',
          localField: '_id',
          foreignField: 'communityObjectId',
          as: 'planOrders'
        }
      },
      // Get the latest plan order
      {
        $addFields: {
          latestPlanOrder: {
            $arrayElemAt: [
              {
                $sortArray: {
                  input: '$planOrders',
                  sortBy: { createdAt: -1 }
                }
              },
              0
            ]
          }
        }
      },
      // Lookup plan details if we have a plan order
      {
        $lookup: {
          from: 'community_plans',
          localField: 'latestPlanOrder.planObjectId',
          foreignField: '_id',
          as: 'planDetails'
        }
      },
      // Unwind plan details (if exists)
      {
        $unwind: {
          path: '$planDetails',
          preserveNullAndEmptyArrays: true
        }
      },
      // Project the fields we need
      {
        $project: {
          _id: 1,
          code: 1,
          name: 1,
          createdBy: 1,
          createdAt: 1,
          countryCreatedIn: 1,
          By: 1,
          isActive: 1,
          isDraft: 1,
          isDemo: 1,
          isFreeCommunity: 1,
          isPaidCommunity: 1,
          // Plan information from plan orders
          planType: '$planDetails.entityType',
          paymentInterval: '$latestPlanOrder.interval',
          paymentIntervalCount: '$latestPlanOrder.intervalCount',
          planOrderStatus: '$latestPlanOrder.status',
          billingCycle: '$latestPlanOrder.billingCycle',
          // Owner information
          ownerName: '$ownerUser.name',
          ownerCountryCode: '$ownerCountryInfo.countryCode',
          ownerCountry: '$ownerCountryInfo.country',
          learnerCountryId: '$ownerLearner.countryId'
        }
      },
      // Sort by creation date (newest first)
      {
        $sort: { createdAt: -1 }
      }
    ];
    
    const communitiesWithPlanOrders = await CommunityModel.aggregate(pipeline);
    
    console.log(`Found ${communitiesWithPlanOrders.length} communities...`);
    
    // Also search for users with matching names and get their communities
    console.log('Searching for additional users with matching names...');
    
    const ownerNamePipeline = [
      // Match users with matching names
      {
        $match: {
          $or: [
            { name: /nas.*yassin/i },
            { name: /nusier/i },
            { name: /nuseir/i },
            { name: /nusair/i },
            { name: /nasir.*yassin/i },
            { name: /nas.*daily/i },
            { name: /nasdaily/i },
            { name: /yassin/i }
          ]
        }
      },
      // Lookup learner information
      {
        $lookup: {
          from: 'learners',
          localField: 'learner',
          foreignField: '_id',
          as: 'learnerInfo'
        }
      },
      // Unwind learner
      {
        $unwind: {
          path: '$learnerInfo',
          preserveNullAndEmptyArrays: true
        }
      },
      // Lookup country information
      {
        $lookup: {
          from: 'country_currency_mapping',
          localField: 'learnerInfo.countryId',
          foreignField: 'countryId',
          as: 'countryInfo'
        }
      },
      // Unwind country info
      {
        $unwind: {
          path: '$countryInfo',
          preserveNullAndEmptyArrays: true
        }
      },
      // Lookup communities owned by this user
      {
        $lookup: {
          from: 'communities',
          localField: 'email',
          foreignField: 'createdBy',
          as: 'ownedCommunities'
        }
      },
      // Unwind communities
      {
        $unwind: {
          path: '$ownedCommunities',
          preserveNullAndEmptyArrays: false
        }
      },
      // Lookup plan orders for each community
      {
        $lookup: {
          from: 'community_plan_orders',
          localField: 'ownedCommunities._id',
          foreignField: 'communityObjectId',
          as: 'planOrders'
        }
      },
      // Get the latest plan order
      {
        $addFields: {
          latestPlanOrder: {
            $arrayElemAt: [
              {
                $sortArray: {
                  input: '$planOrders',
                  sortBy: { createdAt: -1 }
                }
              },
              0
            ]
          }
        }
      },
      // Lookup plan details
      {
        $lookup: {
          from: 'community_plans',
          localField: 'latestPlanOrder.planObjectId',
          foreignField: '_id',
          as: 'planDetails'
        }
      },
      // Unwind plan details
      {
        $unwind: {
          path: '$planDetails',
          preserveNullAndEmptyArrays: true
        }
      },
      // Project the fields we need
      {
        $project: {
          _id: '$ownedCommunities._id',
          code: '$ownedCommunities.code',
          name: '$ownedCommunities.name',
          createdBy: '$email',
          createdAt: '$ownedCommunities.createdAt',
          countryCreatedIn: '$ownedCommunities.countryCreatedIn',
          By: '$ownedCommunities.By',
          isActive: '$ownedCommunities.isActive',
          isDraft: '$ownedCommunities.isDraft',
          isDemo: '$ownedCommunities.isDemo',
          isFreeCommunity: '$ownedCommunities.isFreeCommunity',
          isPaidCommunity: '$ownedCommunities.isPaidCommunity',
          // Plan information from plan orders
          planType: '$planDetails.entityType',
          paymentInterval: '$latestPlanOrder.interval',
          paymentIntervalCount: '$latestPlanOrder.intervalCount',
          planOrderStatus: '$latestPlanOrder.status',
          billingCycle: '$latestPlanOrder.billingCycle',
          // Owner information
          ownerName: '$name',
          ownerCountryCode: '$countryInfo.countryCode',
          ownerCountry: '$countryInfo.country',
          learnerCountryId: '$learnerInfo.countryId'
        }
      },
      // Sort by creation date (newest first)
      {
        $sort: { createdAt: -1 }
      }
    ];
    
    const additionalCommunitiesFromMatchingUsers = await CommunityModel.db.collection('users').aggregate(ownerNamePipeline).toArray();
    
    console.log(`Found ${additionalCommunitiesFromMatchingUsers.length} additional communities from matching users...`);
    
    // Combine results and remove duplicates
    const allMatches = [...communitiesWithPlanOrders];
    const existingIds = new Set(communitiesWithPlanOrders.map(c => c._id.toString()));
    
    additionalCommunitiesFromMatchingUsers.forEach(community => {
      if (!existingIds.has(community._id.toString())) {
        allMatches.push(community);
      }
    });
    
    // Format results
    const results = allMatches.map(community => {
      // Try multiple sources for owner name
      let ownerName = community.ownerName || community.By || 'Unknown';
      
      // Determine community status
      let status = 'Unknown';
      if (community.isDraft) {
        status = 'Draft';
      } else if (!community.isActive) {
        status = 'Inactive';
      } else if (community.isDemo) {
        status = 'Demo';
      } else {
        status = 'Active';
      }
      
      // Determine community type
      let communityType = 'Unknown';
      if (community.isFreeCommunity) {
        communityType = 'Free';
      } else if (community.isPaidCommunity) {
        communityType = 'Paid';
      } else {
        communityType = 'Not specified';
      }
      
      // Determine actual plan type based on active plan orders
      let actualPlanType = 'FREE';
      if (community.planType && community.planOrderStatus === 'current') {
        actualPlanType = community.planType;
      }
      
      // Format payment interval
      let paymentInterval = '';
      if (community.paymentInterval && community.paymentIntervalCount && community.planOrderStatus === 'current') {
        if (community.paymentInterval === 'month' && community.paymentIntervalCount === 1) {
          paymentInterval = 'Monthly';
        } else if (community.paymentInterval === 'year' && community.paymentIntervalCount === 1) {
          paymentInterval = 'Yearly';
        } else if (community.paymentInterval === 'day') {
          paymentInterval = `${community.paymentIntervalCount} day${community.paymentIntervalCount > 1 ? 's' : ''}`;
        } else {
          paymentInterval = `${community.paymentIntervalCount} ${community.paymentInterval}${community.paymentIntervalCount > 1 ? 's' : ''}`;
        }
      }
      
      return {
        communityCode: community.code,
        communityName: community.name,
        communityLink: `https://nas.io/${community.code}`,
        createdTime: community.createdAt,
        ownerName: ownerName,
        ownerEmail: community.createdBy || '',
        communityCountry: community.countryCreatedIn || '',
        ownerCountry: community.ownerCountry || community.ownerCountryCode || '',
        communityStatus: status,
        communityType: communityType,
        planType: actualPlanType,
        paymentInterval: paymentInterval
      };
    });
    
    // Sort by created time (newest first)
    results.sort((a, b) => new Date(b.createdTime) - new Date(a.createdTime));
    
    console.log(`\n✅ Found ${results.length} total communities with potential matches`);
    
    return results;
  } catch (error) {
    console.error('❌ Error finding Nusier Yassin communities:', error);
    throw error;
  }
};

/**
 * Write results to CSV
 */
const writeResultsToCSV = (results, outputPath) => {
  try {
    const headers = [
      'community code',
      'community name',
      'community link',
      'created time',
      'owner\'s name',
      'owner\'s email',
      'community country',
      'owner\'s country',
      'community status',
      'community type',
      'plan type',
      'payment interval'
    ];

    const rows = results.map((result) => [
      result.communityCode || '',
      result.communityName || '',
      result.communityLink || '',
      result.createdTime ? new Date(result.createdTime).toISOString() : '',
      result.ownerName || '',
      result.ownerEmail || '',
      result.communityCountry || '',
      result.ownerCountry || '',
      result.communityStatus || '',
      result.communityType || '',
      result.planType || '',
      result.paymentInterval || ''
    ]);

    const csvContent = [headers, ...rows]
      .map((row) => row.map((field) => `"${field}"`).join(','))
      .join('\n');

    fs.writeFileSync(outputPath, csvContent);
    console.log(`✅ Results written to ${outputPath}`);
  } catch (error) {
    console.error('Error writing results to CSV:', error);
  }
};

const start = async () => {
  try {
    console.log('🚀 Starting Nusier Yassin similarity search with plan orders...');
    
    await mongoClient.connect();
    
    // Create output directory if it doesn't exist
    const outputDir = path.join(__dirname, 'output');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // Find matches
    const results = await findNusierYassinCommunitiesWithPlanOrders();
    
    // Display top matches
    console.log('\n🎯 Top 20 Recent Communities:');
    results.slice(0, 20).forEach((match, index) => {
      console.log(`${index + 1}. ${match.communityCode} - "${match.communityName}"`);
      console.log(`   Owner: ${match.ownerName} (${match.ownerEmail})`);
      console.log(`   Community Country: ${match.communityCountry || 'Not specified'}`);
      console.log(`   Owner Country: ${match.ownerCountry || 'Not specified'}`);
      console.log(`   Status: ${match.communityStatus}, Type: ${match.communityType}`);
      console.log(`   Plan: ${match.planType}, Payment: ${match.paymentInterval || 'No payment'}`);
      console.log(`   Created: ${new Date(match.createdTime).toDateString()}`);
      console.log('');
    });
    
    // Write results to CSV
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const outputPath = path.join(outputDir, `nusier_yassin_with_plan_orders_${timestamp}.csv`);
    writeResultsToCSV(results, outputPath);
    
  } catch (error) {
    console.error('❌ Error in analysis:', error);
  } finally {
    process.exit(0);
  }
};

// Run the script
start();