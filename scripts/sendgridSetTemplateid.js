require('dotenv').config();
const mongoClient = require('../src/mongoClient');
const mailContentModel = require('../src/models/notificationBackend/mailContent.model');

// Constants - update these values as needed
const mailType = 'COMMUNITY_MANAGERS_REVOKE';
const templateId = 'd-1ddeb28998e040c4b1d3bd89eafe1a6d';

/**
 * <PERSON><PERSON>t to update mail content from AWS to SENDGRID with template ID
 */
const updateMailContentToSendgrid = async () => {
  try {
    await mongoClient.connect();
    console.log('Connected to MongoDB');

    console.log(`Finding mail content for mailType: ${mailType}`);

    // First, find the document
    const existingDocument = await mailContentModel
      .findOne({
        mailType,
        recordType: 'general',
        mailContentSource: 'AWS',
      })
      .lean();

    if (!existingDocument) {
      console.log('No document found matching the criteria');
      return;
    }

    console.log(`Found document with ID: ${existingDocument._id}`);
    console.log(
      `Current mailContentSource: ${existingDocument.mailContentSource}`
    );

    // Then update the document using _id
    const result = await mailContentModel.updateOne(
      { _id: existingDocument._id },
      {
        $set: {
          mailContentSource: 'SENDGRID',
          template: templateId,
          isInternalEmail: true,
        },
      }
    );

    console.log(`Matched ${result.matchedCount} documents`);
    console.log(`Modified ${result.modifiedCount} documents`);

    if (result.modifiedCount === 0) {
      console.log(
        'Document found but no changes were made (possibly already updated)'
      );
    } else {
      console.log('Update completed successfully');
    }
  } catch (error) {
    console.error('Error updating mail content:', error);
  } finally {
    process.exit(0);
  }
};

updateMailContentToSendgrid();
