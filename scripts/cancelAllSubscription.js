require('module-alias/register');

require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const fs = require('fs');
const path = require('path');
const mongoClient = require('../src/mongoClient');
const {
  COMMUNITY_SUBSCRIPTION_STATUSES,
} = require('../src/constants/common');
const SubscriptionModel = require('../src/communitiesAPI/models/communitySubscriptions.model');
const {
  cancelSubscriptionService,
} = require('../src/services/communitySubscription');
const CommunityModel = require('../src/communitiesAPI/models/community.model');

const parseArguments = () => {
  const args = process.argv.slice(2);

  if (args.includes('--help') || args.includes('-h')) {
    // Help text with all feature IDs
    console.log('communityCode should be the first argument');
    console.log('use --csv {fileName} to run csv file');
    console.log('use --all to delete all subscriptions');
    process.exit(1);
  }

  if (
    args.length < 2 ||
    (!args.includes('--all') && !args.includes('--csv'))
  ) {
    console.error('❌ Error: missing argument. use -h for more details');
    process.exit(1);
  }

  if (args.includes('--all') && args.includes('--csv')) {
    console.error(
      '❌ Error: Cannot have choose both --all and --csv option. Use -h for more details'
    );
    process.exit(1);
  }

  const communityCode = args[0];

  let csvFilename;
  if (args.includes('--csv')) {
    const index = args.indexOf('--csv');
    if (args.length <= index + 1) {
      console.error('❌ Error: missing csv filename');
      process.exit(1);
    }
    csvFilename = args[index + 1];
  }

  // Validation logic
  return { communityCode, csvFilename };
};

/**
 * Read CSV file and parse community object IDs
 * @param {string} csvPath - Path to the CSV file
 * @returns {Array} Array of community object IDs
 */
const readCommunityIdsFromCSV = (csvPath) => {
  try {
    const csvContent = fs.readFileSync(csvPath, 'utf8');
    const lines = csvContent.split('\n').filter((line) => line.trim());

    if (lines.length === 0) return [];

    // Get headers from first line
    const headers = lines[0]
      .split(',')
      .map((h) => h.trim().toLowerCase().replace(/"/g, ''));

    // Find the column with community ID (flexible naming)
    const idColumnIndex = headers.findIndex(
      (header) =>
        header.includes('EMAIL') ||
        header.includes('email') ||
        header.includes('Email')
    );

    if (idColumnIndex === -1) {
      console.error(
        'No email column found. Expected columns: EMAIL, email, or Email'
      );
      return [];
    }

    // Extract IDs from data rows
    const emails = lines
      .slice(1)
      .map((line) => {
        const columns = line
          .split(',')
          .map((col) => col.trim().replace(/"/g, ''));
        return columns[idColumnIndex];
      })
      .filter((id) => id && id.trim());

    console.log(`Found ${emails.length} emails in CSV`);
    return emails;
  } catch (error) {
    console.error('Error reading CSV:', error);
    return [];
  }
};

const cancelSubscription = async (
  communityCode,
  emails = [],
  cancelForAll = false
) => {
  const query = {
    communityCode,
    status: 'Current',
  };
  if (!cancelForAll) {
    query.email = { $in: emails };
  }

  const subscriptions = await SubscriptionModel.find(query).lean();

  const timestamp = Date.now();

  const cancellationReason = 'Requested by community manager';

  await Promise.all(
    subscriptions.map(async (subscription) => {
      const message = `${subscription.email},${subscription.status},${subscription.amount},${subscription.interval},${subscription.intervalCount},${subscription.billingCycle},${subscription.paymentProvider},${subscription.stripeSubscriptionId}`;
      console.log(message);

      if (subscription.stripeSubscriptionId) {
        await cancelSubscriptionService.cancelPaidSubscription(
          subscription,
          cancellationReason,
          '**************'
        );
      } else {
        await SubscriptionModel.updateOne(
          { _id: subscription._id },
          {
            $set: {
              status: COMMUNITY_SUBSCRIPTION_STATUSES.CANCELLED,
              cancellationReason,
              cancelledAt: timestamp,
              unsubscribedAt: timestamp,
            },
          }
        ).lean();
      }

      await fs.appendFile(
        `logs/cancel_all_subscription-${timestamp}.log`,
        `${message}\n`
      );
    })
  );
  return subscriptions.length;
};

const start = async () => {
  try {
    const { communityCode, csvFilename } = parseArguments();

    await mongoClient.connect();

    const community = await CommunityModel.findOne({
      code: communityCode,
    }).lean();

    if (!community) {
      console.error(`❌ Invalid communityCode: ${communityCode}`);
      process.exit(0);
    }

    console.log(
      `🚀 Starting ${communityCode} subscription cancellation...`
    ); // dynamic feature name

    // Configuration
    const inputCsvPath = path.join(__dirname, `./data/${csvFilename}.csv`); //  dynamic filename
    const outputCsvPath = `./output/${csvFilename}_${Date.now()}.csv`; //  dynamic filename
    const batchSize = 100; // Process in batches to avoid memory issues

    // Ensure output directory exists
    const outputDir = path.dirname(outputCsvPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    let subscriptionProcessed = 0;

    if (!csvFilename) {
      console.log(
        `Cancelling ALL Subscriptions. This will take a while...`
      );
      const subscriptionLength = await cancelSubscription(
        communityCode,
        [],
        true
      );
      subscriptionProcessed += subscriptionLength;
      console.log(`✅ Canceled ALL Subcriptions`);
    } else {
      let emails = [];

      // Read community IDs from CSV if provided
      if (fs.existsSync(inputCsvPath)) {
        console.log(`Reading emails from ${inputCsvPath}`);
        emails = readCommunityIdsFromCSV(inputCsvPath);
      }

      // If no CSV or empty CSV, finish early
      if (emails.length === 0) {
        console.log(' No CSV provided or empty CSV');
        process.exit(0);
      }

      console.log(
        ` Processing ${emails.length} emails in batches of ${batchSize}`
      );

      const batches = [];
      let processedCount = 0;

      for (let i = 0; i < emails.length; i += batchSize) {
        const batch = emails.slice(i, i + batchSize);
        batches.push({
          emails: batch,
          index: Math.floor(i / batchSize) + 1,
        });
      }

      for await (const { emails: batch, index } of batches) {
        console.log(
          `\n🔄 Processing batch ${index}/${Math.ceil(
            emails.length / batchSize
          )}`
        );

        const subscriptionLength = await cancelSubscription(
          communityCode,
          batch
        );

        processedCount += batch.length;
        subscriptionProcessed += subscriptionLength;
        console.log(
          `✅ Batch completed. Processed ${processedCount}/${emails.length} emails. ${subscriptionLength} subscriptions cancelled`
        );

        //  Add a small delay between batches
        if (index + batchSize < emails.length) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      }
    }
    console.log(
      `✅ Script completed. ${subscriptionProcessed} subscriptions cancelled`
    );
  } catch (error) {
    console.log(error);
    console.log('Error running for cancel subscription script');
  } finally {
    process.exit(0);
  }
};

start();
