const MOBILE_NOTIFICATION_KEY = {
  EVENT_PUBLISHED: {
    title: 'event-published-title',
    body: 'event-published-body',
  },
  EVENT_UPDATED_ALL: {
    title: 'event-updated-title',
    body: 'event-updated-body-all',
  },
  EVENT_UPDATED_TITLE_LOCATION: {
    title: 'event-updated-title',
    body: 'event-updated-body-title-location',
  },
  EVENT_UPDATED_TIME_LOCATION: {
    title: 'event-updated-title',
    body: 'event-updated-body-time-location',
  },
  EVENT_UPDATED_TITLE_TIME: {
    title: 'event-updated-title',
    body: 'event-updated-body-title-time',
  },
  EVENT_UPDATED_TITLE: {
    title: 'event-updated-title',
    body: 'event-updated-body-title',
  },
  EVENT_UPDATED_TIME: {
    title: 'event-updated-title',
    body: 'event-updated-body-time',
  },
  EVENT_UPDATED_LOCATION: {
    title: 'event-updated-title',
    body: 'event-updated-body-location',
  },
  EVENT_RECORD_LINK: {
    title: 'event-record-link-title',
    body: 'event-record-link-body',
  },
  EVENT_CANCELLED: {
    title: 'event-cancelled-title',
  },
  FIXED_CHALLENGE_PUBLISHED: {
    title: 'fixed-challenge-published-title',
    body: 'fixed-challenge-published-body',
  },
  ALWAYS_ON_CHALLENGE_PUBLISHED: {
    title: 'alwayson-challenge-published-title',
    body: 'alwayson-challenge-published-body',
  },
  CHALLENGE_TITLE_UPDATED: {
    title: 'challenge-title-updated-title',
    body: 'challenge-title-updated-body',
  },
  FIXED_CHALLENGE_CHECKPOINT_UPDATED_BOTH: {
    title: 'fixed-challenge-checkpoint-updated-title',
    body: 'fixed-challenge-checkpoint-updated-body-both',
  },
  FIXED_CHALLENGE_CHECKPOINT_UPDATED_TIME: {
    title: 'fixed-challenge-checkpoint-updated-title',
    body: 'fixed-challenge-checkpoint-updated-body-time',
  },
  FIXED_CHALLENGE_CHECKPOINT_UPDATED_CONTENT: {
    title: 'fixed-challenge-checkpoint-updated-title',
    body: 'fixed-challenge-checkpoint-updated-body-content',
  },
  ALWAYS_ON_CHALLENGE_CHECKPOINT_UPDATED_BOTH: {
    title: 'alwayson-challenge-checkpoint-updated-title',
    body: 'alwayson-challenge-checkpoint-updated-body-both',
  },
  ALWAYS_ON_CHALLENGE_CHECKPOINT_UPDATED_NEW: {
    title: 'alwayson-challenge-checkpoint-updated-title',
    body: 'alwayson-challenge-checkpoint-updated-body-new',
  },
  ALWAYS_ON_CHALLENGE_CHECKPOINT_UPDATED_CONTENT: {
    title: 'alwayson-challenge-checkpoint-updated-title',
    body: 'alwayson-challenge-checkpoint-updated-body-content',
  },
  FIXED_CHALLENGE_CANCELLED: {
    title: 'fixed-challenge-cancelled-title',
  },
  SESSION_PUBLISHED: {
    title: 'session-published-title',
    body: 'session-published-body',
  },
  SESSION_UPDATED_BOTH: {
    title: 'session-updated-title',
    body: 'session-updated-body-both',
  },
  SESSION_UPDATED_TITLE_CHANGE_TITLE: {
    title: 'session-updated-title-change-title',
    body: 'session-updated-body-title-change',
  },
  SESSION_UPDATED_LOCATION_CHANGE: {
    title: 'session-updated-title',
    body: 'session-updated-body-location-change',
  },
  SESSION_CANCELLED: {
    title: 'session-cancelled-title',
  },
  DIGITAL_FILE_PUBLISHED: {
    title: 'digital-file-published-title',
    body: 'digital-file-published-body',
  },
  COURSE_PUBLISHED: {
    title: 'course-published-title',
    body: 'course-published-body',
  },
};

module.exports = {
  MOBILE_NOTIFICATION_KEY,
};
