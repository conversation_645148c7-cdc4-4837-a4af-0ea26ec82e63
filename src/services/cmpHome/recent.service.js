const CommunityEventModel = require('../../communitiesAPI/models/communityEvents.model');
const CommunityFolderModel = require('../../communitiesAPI/models/communityFolders.model');
const CommunityFolderItemsModel = require('../../communitiesAPI/models/communityFolderItems.model');
const LearnerModel = require('../../models/learners.model');
const MagicReachModel = require('../../models/magicReach/communityMagicReachEmail.model');
const PostModel = require('../../communitiesAPI/models/communityPost.model');
const ProgramModel = require('../../models/program/program.model');

const { countParticipants } = require('../program/common.service');
const {
  getEventAttendeesCount,
} = require('../../communitiesAPI/services/common/eventAttendees.service');

const {
  MAGIC_REACH_MESSAGE_STATUS,
  BUCKET_NAMES,
} = require('../magicReach/constants');
const { PROGRAM_TYPE, PROGRAM_STATUS } = require('../program/constants');
const {
  communityLibraryStatusMap,
  communityFolderTypesMap,
  COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES,
  EVENT_STATUS,
} = require('../../communitiesAPI/constants');

const CREATION_TYPES = {
  EVENT: 'EVENT',
  FOLDER: 'FOLDER',
  DIGITAL_PRODUCT: 'DIGITAL_PRODUCT',
  SESSION: 'SESSION',
  CHALLENGE: 'CHALLENGE',
  ZERO_LINK: 'ZERO_LINK',
  MAGIC_REACH: 'MAGIC_REACH',
  POST: 'POST',
};

class RecentCreationProcessor {
  constructor({ community, limit = 3 }) {
    this.community = community;
    this.limit = limit;
    this.sortBy = 'createdAt';
    this.creationMap = new Map();
    this.recentCreations = [];
    this.defaultProjection = {
      entityObjectId: '$_id',
      title: 1,
      slug: 1,
      createdAt: 1,
    };
  }

  getChallengeProjection() {
    return {
      ...this.defaultProjection,
      type: {
        $cond: {
          if: { $eq: ['$type', PROGRAM_TYPE.CHALLENGE] },
          then: CREATION_TYPES.CHALLENGE,
          else: 'COURSE', // Does not exists yet
        },
      },
      entityLink: {
        $concat: ['/challenges', '$slug'],
      },
      thumbnail: '$cover',
      startTime: 1,
      endTime: 1,
      challengeType: 1,
    };
  }

  getEventProjection() {
    return {
      ...this.defaultProjection,
      type: CREATION_TYPES.EVENT,
      entityLink: { $concat: ['/events', '$slug'] },
      thumbnail: '$bannerImg',
      startTime: 1,
      endTime: 1,
    };
  }

  getFolderProjection() {
    return {
      ...this.defaultProjection,
      type: {
        $cond: {
          if: { $eq: ['$type', communityFolderTypesMap.SESSION] },
          then: CREATION_TYPES.SESSION,
          else: CREATION_TYPES.DIGITAL_PRODUCT,
        },
      },
      entityLink: {
        $concat: ['/products', '$resourceSlug'],
      },
      slug: `$resourceSlug`,
      thumbnail: 1,
      durationIntervalInMinutes: 1,
      hostInfo: 1,
      accessCount: 1,
    };
  }

  getPostProjection() {
    return {
      ...this.defaultProjection,
      type: CREATION_TYPES.POST,
      content: 1,
      impressions: 1,
    };
  }

  getMagicReachProjection() {
    return {
      ...this.defaultProjection,
      type: CREATION_TYPES.MAGIC_REACH,
      content: 1,
      analyticsData: 1,
      announcementObjectId:
        '$sentResults.AnnouncementV2.announcementObjectId',
    };
  }

  async formatRecentDigitalProduct() {
    const values =
      this.creationMap.get(CREATION_TYPES.DIGITAL_PRODUCT) ?? [];
    const folderObjectIds = values.map((folder) => folder._id);
    const folderMap = new Map();
    const folderItems = await CommunityFolderItemsModel.find(
      {
        communityFolderObjectId: { $in: folderObjectIds },
        status: {
          $nin: [
            communityLibraryStatusMap.DELETED,
            communityLibraryStatusMap.DRAFT,
          ],
        },
        parentSectionId: { $eq: null },
      },
      { communityFolderObjectId: 1 }
    ).lean();

    folderItems.forEach((item) => {
      const key = item.communityFolderObjectId.toString();
      let count = folderMap.get(key) ?? 0;
      count += 1;
      folderMap.set(key, count);
    });

    values.forEach((creation) => {
      const formatted = { ...creation };
      formatted.totalItemsCount =
        folderMap.get(creation.entityObjectId.toString()) ?? 0;
      delete formatted._id;
      delete formatted._version;
      this.formattedRecentCreations.push(formatted);
    });
  }

  async formatRecentPost() {
    const values = this.creationMap.get(CREATION_TYPES.POST) ?? [];

    const announcementObjectIds = values.map((msg) => msg._id);
    const magicReachMap = new Map();
    if (announcementObjectIds.length) {
      const magicReachMsgs = await MagicReachModel.find(
        {
          'sentResults.AnnouncementV2.announcementObjectId': {
            $in: announcementObjectIds,
          },
          communityId: this.community._id,
        },
        this.getMagicReachProjection()
      ).lean();
      magicReachMsgs.forEach((msg) =>
        magicReachMap.set(msg.announcementObjectId.toString(), msg)
      );
    }

    values.forEach((creation) => {
      const feedViews = creation.impressions ?? 0;
      const mr = magicReachMap.get(creation.entityObjectId.toString());
      if (!mr) {
        const formatted = { ...creation };
        formatted.views = feedViews;
        delete formatted._id;
        delete formatted._version;
        delete formatted.impressions;
        this.formattedRecentCreations.push(formatted);
      } else {
        const formatted = { ...mr };
        const emailViews = mr?.analyticsData?.Email?.totalOpens ?? 0;
        const whatsappViews =
          mr?.analyticsData?.Whatsapp?.lastReplyMessage
            ?.deliveredRecipients ?? 0;
        formatted.views = emailViews + whatsappViews + feedViews;
        delete formatted._id;
        delete formatted.analyticsData;
        delete formatted.announcementObjectId;
        this.formattedRecentCreations.push(formatted);
      }
    });
  }

  async formatRecentMagicReach() {
    const values = this.creationMap.get(CREATION_TYPES.MAGIC_REACH) ?? [];
    await Promise.all(
      values.map(async (creation) => {
        const formatted = { ...creation };
        const emailViews = creation.analyticsData?.Email?.totalOpens ?? 0;
        const whatsappViews =
          creation.analyticsData?.Whatsapp?.lastReplyMessage
            ?.deliveredRecipients ?? 0;
        let feedViews = 0;
        if (creation.announcementObjectId) {
          const feed = await PostModel.findById(
            creation.announcementObjectId,
            { impressions: 1 }
          ).lean();
          feedViews = feed?.impressions ?? 0;
        }
        formatted.views = emailViews + whatsappViews + feedViews;
        delete formatted._id;
        delete formatted.analyticsData;
        delete formatted.announcementObjectId;
        this.formattedRecentCreations.push(formatted);
      })
    );
  }

  async formatRecentSession() {
    const values = this.creationMap.get(CREATION_TYPES.SESSION) ?? [];
    await Promise.all(
      values.map(async (creation) => {
        const formatted = { ...creation };
        if (
          !creation.hostInfo?.hostTitle &&
          creation.hostInfo.hostLearnerObjectId
        ) {
          const learner = await LearnerModel.findById(
            creation.hostInfo.hostLearnerObjectId,
            { firstName: 1, lastName: 1 }
          ).lean();
          formatted.hostInfo.hostTitle = `${learner.firstName ?? ''} ${
            learner.lastName ?? ''
          }`.trim();
        }

        delete formatted._id;
        delete formatted._version;
        this.formattedRecentCreations.push(formatted);
      })
    );
  }

  async formatRecentChallenge() {
    const values = this.creationMap.get(CREATION_TYPES.CHALLENGE) ?? [];
    await Promise.all(
      values.map(async (creation) => {
        const formatted = { ...creation };
        formatted.participantCount = await countParticipants({
          programId: creation.entityObjectId,
        });
        delete formatted._id;
        delete formatted._version;
        this.formattedRecentCreations.push(formatted);
      })
    );
  }

  async formatRecentEvent() {
    const value = this.creationMap.get(CREATION_TYPES.EVENT) ?? [];
    await Promise.all(
      value.map(async (creation) => {
        const formatted = { ...creation };
        formatted.attendees = await getEventAttendeesCount({
          eventObjectId: creation.entityObjectId,
          status: COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING,
        });
        delete formatted._id;
        delete formatted._version;
        this.formattedRecentCreations.push(formatted);
      })
    );
  }

  async retrieveRecentCreationData() {
    const communityObjectId = this.community._id;
    const [challenges, events, folders, magicReachMsgs] =
      await Promise.all([
        ProgramModel.find(
          {
            communityObjectId,
            type: PROGRAM_TYPE.CHALLENGE,
            status: {
              $in: [PROGRAM_STATUS.DRAFT, PROGRAM_STATUS.PUBLISHED],
            },
          },
          this.getChallengeProjection()
        )
          .sort({ createdAt: -1 })
          .limit(this.limit)
          .lean(),
        CommunityEventModel.find(
          {
            communities: communityObjectId,
            status: {
              $in: [EVENT_STATUS.DRAFT, EVENT_STATUS.PUBLISHED],
            },
          },
          this.getEventProjection()
        )
          .sort({ createdAt: -1 })
          .limit(this.limit)
          .lean(),
        CommunityFolderModel.find(
          {
            communityObjectId,
            status: {
              $in: [
                communityLibraryStatusMap.PUBLISHED,
                communityLibraryStatusMap.UNPUBLISHED,
              ],
            },
          },
          this.getFolderProjection()
        )
          .sort({ [this.sortBy]: -1 })
          .limit(this.limit)
          .lean(),
        MagicReachModel.find(
          {
            communityId: communityObjectId,
            isDraft: { $ne: true },
            status: {
              $nin: [
                MAGIC_REACH_MESSAGE_STATUS.DRAFT,
                MAGIC_REACH_MESSAGE_STATUS.BLOCKED,
              ],
            },
            selectedBuckets: { $ne: BUCKET_NAMES.MAGIC_LEADS_OUTREACH },
          },
          this.getMagicReachProjection()
        )
          .sort({ createdAt: -1 })
          .limit(this.limit)
          .lean(),
      ]);

    const announcementObjectIds = magicReachMsgs.map(
      (msg) => msg.announcementObjectId
    );
    const posts = await PostModel.find(
      {
        communities: communityObjectId,
        _id: { $nin: announcementObjectIds },
      },
      this.getPostProjection()
    )
      .sort({ createdAt: -1 })
      .limit(this.limit)
      .lean();

    const sortedArray = [
      ...challenges,
      ...events,
      ...folders,
      ...magicReachMsgs,
      ...posts,
    ].sort((a, b) => b[this.sortBy] - a[this.sortBy]);

    const recentCreations = sortedArray.slice(0, this.limit);

    this.creationMap.clear();
    recentCreations.forEach((creation) => {
      const existing = this.creationMap.get(creation.type);
      const value = existing ?? [];
      value.push(creation);
      this.creationMap.set(creation.type, value);
    });
  }

  async getFormattedRecentCreations() {
    this.formattedRecentCreations = [];
    const formatPromises = [];
    if (this.creationMap.get(CREATION_TYPES.DIGITAL_PRODUCT)) {
      formatPromises.push(this.formatRecentDigitalProduct());
    }
    if (this.creationMap.get(CREATION_TYPES.POST)) {
      formatPromises.push(this.formatRecentPost());
    }
    if (this.creationMap.get(CREATION_TYPES.MAGIC_REACH)) {
      formatPromises.push(this.formatRecentMagicReach());
    }
    if (this.creationMap.get(CREATION_TYPES.SESSION)) {
      formatPromises.push(this.formatRecentSession());
    }
    if (this.creationMap.get(CREATION_TYPES.CHALLENGE)) {
      formatPromises.push(this.formatRecentChallenge());
    }
    if (this.creationMap.get(CREATION_TYPES.EVENT)) {
      formatPromises.push(this.formatRecentEvent());
    }
    await Promise.all(formatPromises);

    this.formattedRecentCreations = this.formattedRecentCreations.sort(
      (a, b) => b[this.sortBy] - a[this.sortBy]
    );
    return this.formattedRecentCreations;
  }
}

exports.getRecentProducts = async (community) => {
  const processor = new RecentCreationProcessor({
    community,
    limit: 3,
  });

  await processor.retrieveRecentCreationData();
  const recents = await processor.getFormattedRecentCreations();

  return recents;
};
