const CommunityEventModel = require('../../communitiesAPI/models/communityEvents.model');
const CommunityFolderModel = require('../../communitiesAPI/models/communityFolders.model');
const LearnerModel = require('../../models/learners.model');
const ProgramModel = require('../../models/program/program.model');

const {
  retrieveCommunityOwnerDetails,
} = require('../getInspired/common.service');
const {
  communityInfoStatus,
  checkIfCommunityHasMoreThanOneMember,
} = require('../getStartedSuperCharge.service');

const { ParamError } = require('../../utils/error.util');

const {
  SUPPORTED_SOCIAL_MEDIA_TYPES_FOR_TEMPLATES,
} = require('../../constants/common');
const { NEXT_THINGS_TO_DO_KEY } = require('./constants');

const getBusiness = (community) => {
  const isCompleted = communityInfoStatus(community);
  return {
    key: NEXT_THINGS_TO_DO_KEY.BUSINESS,
    index: 0,
    isCompleted,
  };
};

const getInvite = async (community) => {
  const isCompleted = await checkIfCommunityHasMoreThanOneMember(
    community
  );
  return {
    key: NEXT_THINGS_TO_DO_KEY.INVITE,
    index: 3,
    isCompleted,
  };
};

const getProduct = async (community) => {
  const [event, digitalProduct, challenges] = await Promise.all([
    CommunityEventModel.findOne({ communities: community._id })
      .select('_id')
      .lean(),
    CommunityFolderModel.findOne({ communityObjectId: community._id })
      .select('_id')
      .lean(),
    ProgramModel.findOne({ communityObjectId: community._id })
      .select('_id')
      .lean(),
  ]);

  return {
    key: NEXT_THINGS_TO_DO_KEY.PRODUCT,
    index: 2,
    isCompleted: !!event || !!digitalProduct || !!challenges,
  };
};

// Function not needed
// eslint-disable-next-line no-unused-vars
const getInspiredProfile = async (community) => {
  let isCompleted = false;

  const ownerInfo = await retrieveCommunityOwnerDetails(community._id);
  const socialMedia = ownerInfo?.socialMedia?.filter((media) =>
    SUPPORTED_SOCIAL_MEDIA_TYPES_FOR_TEMPLATES.includes(media.type)
  );

  const { communityIntent } = community;
  if (socialMedia?.length && communityIntent) {
    isCompleted = true;
  }
  return {
    key: NEXT_THINGS_TO_DO_KEY.PROFILE,
    index: 1,
    isCompleted,
    metadata: {
      communityInfo: { communityIntent },
      ownerInfo,
    },
  };
};

const getLearnerProfile = async (learnerObjectId) => {
  let isCompleted = false;
  const learnerInfo = await LearnerModel.findById(learnerObjectId, {
    description: 1,
    countryId: 1,
    countryCode: 1,
    firstName: 1,
    lastName: 1,
    socialMedia: 1,
  }).lean();
  if (!learnerInfo) {
    throw new ParamError('Learner not found');
  }
  if (
    learnerInfo.description &&
    (learnerInfo.countryId || learnerInfo.countryCode) &&
    learnerInfo.socialMedia?.length
  ) {
    isCompleted = true;
  }
  return {
    key: NEXT_THINGS_TO_DO_KEY.PROFILE,
    index: 1,
    isCompleted,
    metadata: {
      learnerInfo,
    },
  };
};

exports.getNextThingsToDoDetails = async (community, learnerObjectId) => {
  const business = getBusiness(community);
  const [invite, product, profile] = await Promise.all([
    getInvite(community),
    getProduct(community),
    getLearnerProfile(learnerObjectId),
  ]);

  const sortedResults = [business, profile, product, invite]
    .sort((a, b) => a.index - b.index)
    .sort((a, b) => a.isCompleted - b.isCompleted);

  return {
    business,
    profile,
    product,
    invite,
    sortedResults,
  };
};
