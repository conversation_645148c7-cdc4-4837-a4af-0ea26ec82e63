const messageService = require('./message.service');
const chatCommonService = require('../chat/common.service');
const productTemplateCommonService = require('./ai/productTemplate/common.service');
const aiService = require('./ai');
const commonService = require('./common.service');
const PrimaryMongooseConnection = require('../../rpc/primaryMongooseConnection');
const chatService = require('./chat.service');
const FeaturePermissionManager = require('@/src/services/common/featurePermissionManager.service');
const {
  AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES,
  FEATURE_LIST_ID,
} = require('../../constants/common');

async function retrieveSelectedTemplate({
  communityObjectId,
  templateObjectId,
  chatObjectId,
}) {
  const [
    explicitTemplate,
    latestProductTemplate,
    latestAdsTemplate,
    latestLeadsTemplate,
  ] = await Promise.all([
    templateObjectId
      ? productTemplateCommonService.retrieveTemplate({
          communityObjectId,
          templateObjectId,
        })
      : null,
    productTemplateCommonService.retrieveLatestProductTemplate({
      communityObjectId,
      chatObjectId,
    }),
    productTemplateCommonService.retrieveLatestAdsTemplate({
      communityObjectId,
      chatObjectId,
    }),
    productTemplateCommonService.retrieveLatestLeadsTemplate({
      communityObjectId,
      chatObjectId,
    }),
  ]);

  const selectedTemplate = explicitTemplate ?? latestProductTemplate;

  if (selectedTemplate?.linkedTemplateObjectIds?.length) {
    const additionalTemplates =
      await productTemplateCommonService.getTemplates({
        communityObjectId,
        chatObjectId,
        templateObjectIds: selectedTemplate.linkedTemplateObjectIds,
      });

    selectedTemplate.additionalTemplates = additionalTemplates;
  }

  return { selectedTemplate, latestAdsTemplate, latestLeadsTemplate };
}

exports.sendMessage = async ({
  community,
  chat,
  message,
  attachments,
  intentType,
  responseId,
  learnerObjectId,
  templateObjectId,
  actionType,
  platform,
  timezone = 'America/New_York',
  languagePreference,
  abortController,
  onData,
  onDone,
  onError,
}) => {
  chatCommonService.validateChatIsActive(chat);

  const messageCountInfo = await messageService.retrieveMessageCountInfo({
    communityObjectId: community._id,
    community,
  });

  messageService.validateMessageCountWithinLimit({ messageCountInfo });

  const [
    chatMessage,
    { selectedTemplate, latestAdsTemplate, latestLeadsTemplate },
  ] = await Promise.all([
    messageService.addMessage({
      community,
      chat,
      message,
      learnerObjectId,
      attachments,
      intentType,
      isAiGenerated: false,
      actionType,
    }),
    retrieveSelectedTemplate({
      communityObjectId: community._id,
      templateObjectId,
      chatObjectId: chat._id,
    }),
  ]);

  await aiService.processAiStream({
    messageCountInfo,
    community,
    chatMessage,
    selectedTemplate,
    latestAdsTemplate,
    latestLeadsTemplate,
    responseId,
    platform,
    timezone,
    languagePreference,
    abortController,
    onData,
    onDone,
    onError,
  });
};

exports.addAiMessage = async ({
  community,
  chat,
  message,
  learnerObjectId,
  usage,
  functionCallUsage,
  responseId,
  templateObjectId,
  possibleActions,
  messageIntent,
  userMessageObjectId,
  postProcessEvents,
}) => {
  const metadata = {
    usage,
    functionCallUsage,
    responseId,
    templateObjectId,
    messageIntent,
    possibleActions,
    userMessageObjectId,
  };

  if (postProcessEvents) {
    metadata.additionalTemplates = postProcessEvents.map(
      (postProcessEvent) => {
        return {
          objectId: postProcessEvent.templateObjectId,
          type: postProcessEvent.templateType,
          isSample: !postProcessEvent.templateObjectId,
        };
      }
    );
  }

  const chatMessage = await messageService.addMessage({
    community,
    chat,
    message,
    learnerObjectId,
    isAiGenerated: true,
    metadata,
  });

  return chatMessage;
};

exports.retrieveTemplateViaVersion = async ({
  communityObjectId,
  chatObjectId,
  version,
}) => {
  return productTemplateCommonService.retrieveTemplateViaVersion({
    communityObjectId,
    chatObjectId,
    version,
  });
};

exports.addNewTemplateVersion = async ({
  community,
  chat,
  version,
  learnerObjectId,
}) => {
  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    const { newTemplate, oldTemplate } =
      await productTemplateCommonService.addNewTemplateVersion({
        communityObjectId: community._id,
        chatObjectId: chat._id,
        version,
        session,
      });

    const templateMessage =
      await messageService.retrieveMessageViaTemplate({
        templateObjectId: oldTemplate._id,
        communityObjectId: community._id,
        chatObjectId: chat._id,
      });

    const { responseId, messageIntent, possibleActions } =
      templateMessage.metadata;

    const metadata = {
      responseId,
      templateObjectId: newTemplate._id,
      messageIntent,
      possibleActions,
      copyFrom: newTemplate.copyFrom,
    };

    const createdChatMessage = await messageService.addMessage({
      community,
      chat,
      message: '',
      learnerObjectId,
      isAiGenerated: false,
      isRestoredVersion: true,
      metadata,
      session,
    });

    await session.commitTransaction();

    createdChatMessage.template = newTemplate;

    return createdChatMessage;
  } catch (err) {
    await session.abortTransaction();
    throw err;
  } finally {
    await session.endSession();
  }
};

exports.updateTemplate = async ({
  communityObjectId,
  templateObjectId,
  thumbnailImgSrc,
  metadata,
  pricingConfig,
}) => {
  return productTemplateCommonService.updateTemplate({
    communityObjectId,
    templateObjectId,
    thumbnailImgSrc,
    metadata,
    pricingConfig,
  });
};

exports.shuffleInitialPrompt = async ({
  communityObjectId,
  languagePreference,
}) => {
  const community = await commonService.retrieveActiveCommunity(
    communityObjectId
  );

  const variables = await aiService.shuffleInitialPrompt({
    community,
    languagePreference,
  });

  return { variables };
};

exports.updateChatTitleWithSummary = async ({
  community,
  chat,
  responseId,
}) => {
  if (!responseId) return;

  const title = await aiService.generateChatTitleFromResponse({
    communityObjectId: community._id,
    chatObjectId: chat._id,
    responseId,
  });

  if (!title) {
    return;
  }

  await chatService.updateChatTitle({
    chatObjectId: chat._id,
    title,
  });
};

exports.getTemplates = async ({
  communityObjectId,
  chatObjectId,
  templateObjectIds,
}) => {
  const community = await commonService.retrieveActiveCommunity(
    communityObjectId,
    { config: 1, featurePermissions: 1 }
  );

  const featurePermissionManager = new FeaturePermissionManager(
    community.config.planType,
    community.featurePermissions
  );

  const displayLeadsLimit = featurePermissionManager.getFeature(
    FEATURE_LIST_ID.AI_COFOUNDER_DISPLAY_LEADS_LIMIT
  ).limit;

  const templates = await productTemplateCommonService.getTemplates({
    communityObjectId,
    chatObjectId,
    templateObjectIds,
  });

  const transformedTemplates = templates.map((template) => {
    const transformedTemplate = {
      ...template,
    };

    if (template.type === AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.LEADS) {
      transformedTemplate.isSample = false;

      if (
        transformedTemplate.metadata.icp?.icpVectorSearchContext?.embedding
      ) {
        delete transformedTemplate.metadata.icp.icpVectorSearchContext
          .embedding;
      }

      transformedTemplate.metadata.leads =
        transformedTemplate.metadata.leads?.slice(0, displayLeadsLimit);
    }

    return transformedTemplate;
  });

  return transformedTemplates;
};

exports.generateTemplates = async ({
  communityObjectId,
  templateObjectId,
  templates,
}) => {
  const community = await commonService.retrieveActiveCommunity(
    communityObjectId
  );

  const results = await Promise.allSettled(
    templates.map(async ({ type, assignTemplateObjectId }) => {
      if (type === AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.ADS) {
        return aiService.generateAds({
          community,
          templateObjectId,
          assignTemplateObjectId,
        });
      }

      if (type === AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.LEADS) {
        return aiService.generateLeads({
          community,
          templateObjectId,
          assignTemplateObjectId,
        });
      }
    })
  );

  return results;
};
