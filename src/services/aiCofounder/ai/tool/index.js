const createEventTemplate = require('./createEventTemplate');
const createFolderTemplate = require('./createFolderTemplate');
const createCourseTemplate = require('./createCourseTemplate');
const createSessionTemplate = require('./createSessionTemplate');
const createChallengeTemplate = require('./createChallengeTemplate');
const createMembershipTemplate = require('./createMembershipTemplate');
const messageIntentClassifier = require('./messageIntentClassifier');
const shufflePrompt = require('./shufflePrompt');
const nasioKnowledgeBase = require('./nasioKnowledgeBase');
const createAdsTemplate = require('./createAdsTemplate');
const createLeadsTemplate = require('./createLeadsTemplate');

const { MESSAGE_INTENT_TYPE } = require('../constants');

const sharedProductTemplateTools = (params) => [
  createEventTemplate(params),
  createFolderTemplate(params),
  createCourseTemplate(params),
  createSessionTemplate(params),
  createChallengeTemplate(params),
  createMembershipTemplate(params),
];

const tools = (params) => ({
  [MESSAGE_INTENT_TYPE.PRODUCT_TEMPLATE.type]:
    sharedProductTemplateTools(params),
  [MESSAGE_INTENT_TYPE.CHANGE_TO_SPECIFIC_PRODUCT_TYPE.type]:
    sharedProductTemplateTools(params),
  [MESSAGE_INTENT_TYPE.CHANGE_SOME_INFO_IN_PRODUCT_TEMPLATE.type]:
    sharedProductTemplateTools(params),
  [MESSAGE_INTENT_TYPE.REFINE.type]: sharedProductTemplateTools(params),
  [MESSAGE_INTENT_TYPE.MESSAGE_INTENT_CLASSIFIER.type]: [
    messageIntentClassifier(params),
  ],
  [MESSAGE_INTENT_TYPE.SHUFFLE_PROMPT.type]: [shufflePrompt(params)],
  [MESSAGE_INTENT_TYPE.ABOUT_NAS_IO.type]: [nasioKnowledgeBase(params)],
  [MESSAGE_INTENT_TYPE.ADS_GENERATION.type]: [createAdsTemplate(params)],
  [MESSAGE_INTENT_TYPE.REFINE_ADS.type]: [createAdsTemplate(params)],
  [MESSAGE_INTENT_TYPE.LEADS_GENERATION.type]: [
    createLeadsTemplate(params),
  ],
});

exports.retrieveAllToolsSchema = (messageIntentType, params) => {
  if (!tools(params)[messageIntentType]) {
    return [];
  }

  return tools(params)[messageIntentType].map((tool) => tool.schema);
};

exports.retrieveTool = (name, params) => {
  const selectedTool = Object.values(tools(params))
    .flat()
    .find((tool) => tool.schema.name === name);

  return selectedTool;
};

exports.retrieveAndExecuteTool = async ({
  name,
  args,
  callId,
  communityObjectId,
  chatObjectId = null,
  lastResponseId = null,
  toolParams = null,
  assignTemplateObjectId = null,
}) => {
  if (!name || !args || !callId || !communityObjectId) {
    throw new Error('Invalid arguments');
  }

  const tool = this.retrieveTool(name, toolParams);

  if (!tool) {
    throw new Error(`Invalid or unrecognized intent: ${name}`);
  }

  const argumentInJson = JSON.parse(args);

  const result = await tool.execute(
    argumentInJson,
    communityObjectId,
    chatObjectId,
    callId,
    lastResponseId,
    assignTemplateObjectId
  );

  return {
    functionCallType: result.functionCallType,
    functionCallId: callId,
    functionCallOutput: result.response,
    templateObjectId: result.templateObjectId,
    communityObjectId,
    chatObjectId,
  };
};
