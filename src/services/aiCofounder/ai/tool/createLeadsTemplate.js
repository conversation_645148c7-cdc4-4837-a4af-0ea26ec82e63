const FunctionCall = require('../model/FunctionCall');
const FunctionCallResponse = require('../model/FunctionCallResponse');
const { FUNCTION_CALL_TYPE } = require('../constants');
const icpProfileService = require('@/src/services/magicLeads/icpProfile.service');
const leadsService = require('../leads');

const functionCallType = FUNCTION_CALL_TYPE.LEADS_TEMPLATE;

const generateLeadsTemplateFunctionCall =
  icpProfileService.generateICPFunctionCall;

const generateLeadsTemplateFunctionCallHandler = async (
  args,
  communityObjectId,
  chatObjectId,
  callId,
  aiResponseId,
  assignTemplateObjectId
) => {
  const {
    idealCustomerProfile,
    icpVectorSearchContext,
    jobTitles,
    countries,
    industries,
    targetLanguage,
    en,
  } = args;

  const leadsTemplate = {
    idealCustomerProfile,
    icpVectorSearchContext,
    jobTitles,
    countries,
    industries,
    targetLanguage,
    localization: { en },
  };

  if (assignTemplateObjectId) {
    const updatedLeadsTemplate = await leadsService.updateLeads({
      data: leadsTemplate,
      communityObjectId,
      chatObjectId,
      callId,
      aiResponseId,
      assignTemplateObjectId,
    });

    return new FunctionCallResponse(
      functionCallType,
      updatedLeadsTemplate
    );
  }

  const createdLeadsTemplate = await leadsService.createLeads({
    data: leadsTemplate,
    communityObjectId,
    chatObjectId,
    callId,
    aiResponseId,
  });

  return new FunctionCallResponse(functionCallType, createdLeadsTemplate);
};

const functionCall = (params) =>
  new FunctionCall(
    functionCallType,
    generateLeadsTemplateFunctionCall(params),
    generateLeadsTemplateFunctionCallHandler
  );

module.exports = functionCall;
