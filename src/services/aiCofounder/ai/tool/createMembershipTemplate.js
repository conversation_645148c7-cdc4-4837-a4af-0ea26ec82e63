const FunctionCall = require('../model/FunctionCall');
const FunctionCallResponse = require('../model/FunctionCallResponse');
const {
  FUNCTION_CALL_TYPE,
  MESSAGE_INTENT_TYPE,
} = require('../constants');
const { PRICE_TYPE } = require('../../../../constants/common');
const membershipProductTemplateService = require('../productTemplate/membership.service');

const functionCallType = FUNCTION_CALL_TYPE.MEMBERSHIP_TEMPLATE;

const generateMembershipTemplateFunctionCall = () => ({
  type: 'function',
  name: 'generate_membership_template',
  description: 'Generate the membership',
  parameters: {
    type: 'object',
    properties: {
      title: {
        type: 'string',
        description:
          'The title of the product the same language as the user’s original input.',
      },
      description: {
        type: 'string',
        description:
          'The description of why member should join the same language as the user’s original input.',
      },
      unsplashImageSearchTitle: {
        type: 'string',
        description:
          'A short keyword or phrase that best represents the theme or activity of the product. It will be used to search for a relevant image on Unsplash. Keep it concise and descriptive, in the same language as the user’s original input.',
      },
      membershipInterval: {
        type: 'string',
        description: 'The interval of the membership',
        enum: ['1-month', '3-month', '6-month', '1-year'],
      },
      perks: {
        type: 'array',
        items: {
          type: 'string',
        },
        description: 'The perks of the membership',
        minItems: 2,
        maxItems: 3,
      },
      priceType: {
        type: 'string',
        description: 'The price type of the product',
        enum: [PRICE_TYPE.FIXED],
      },
      localCurrency: {
        type: 'string',
        description:
          'Local currency of the suggested amount, must be same as community currency',
      },
      suggestedAmount: {
        type: 'number',
        description: 'Recommended payment in local currency in cents',
      },
      possibleActions: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            action: {
              type: 'string',
              description: 'The action to take',
              enum: [
                MESSAGE_INTENT_TYPE.CHANGE_PRODUCT_TYPE.type,
                MESSAGE_INTENT_TYPE.REFINE.type,
                MESSAGE_INTENT_TYPE.HOW_TO_SELL.type,
              ],
            },
          },
          required: ['action'],
          additionalProperties: false,
        },
        minItems: 3,
        maxItems: 3,
        description:
          'The possible actions to take after generating the membership',
      },
    },
    required: [
      'title',
      'description',
      'unsplashImageSearchTitle',
      'membershipInterval',
      'perks',
      'priceType',
      'localCurrency',
      'suggestedAmount',
      'possibleActions',
    ],
    additionalProperties: false,
  },
});

const generateMembershipTemplateFunctionCallHandler = async (
  args,
  communityObjectId,
  chatObjectId,
  callId,
  aiResponseId
) => {
  const {
    title,
    description,
    unsplashImageSearchTitle,
    membershipInterval,
    perks,
    priceType,
    suggestedAmount,
    localCurrency,
    possibleActions,
  } = args;

  const [intervalCount, interval] = membershipInterval.split('-');

  const membershipTemplate = {
    title,
    description,
    unsplashImageSearchTitle,
    interval,
    intervalCount,
    perks,
    priceType,
    suggestedAmount,
    localCurrency,
    possibleActions,
  };

  const createdMembershipTemplate =
    await membershipProductTemplateService.createTemplate({
      data: membershipTemplate,
      communityObjectId,
      chatObjectId,
      callId,
      aiResponseId,
    });

  return new FunctionCallResponse(
    functionCallType,
    createdMembershipTemplate
  );
};

const functionCall = (params) =>
  new FunctionCall(
    functionCallType,
    generateMembershipTemplateFunctionCall(params),
    generateMembershipTemplateFunctionCallHandler
  );

module.exports = functionCall;
