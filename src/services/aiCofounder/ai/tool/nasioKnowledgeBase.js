const FunctionCall = require('../model/FunctionCall');
const { PLATFORM } = require('../constants');

const nasioKnowledgeBaseSchema = (params) => {
  const { platform } = params ?? {};

  const platformFilters = [
    {
      type: 'eq',
      key: 'platform',
      value: PLATFORM.SHARED,
    },
  ];

  if (platform === PLATFORM.WEB) {
    platformFilters.push({
      type: 'eq',
      key: 'platform',
      value: PLATFORM.WEB,
    });
  } else if (platform === PLATFORM.APP) {
    platformFilters.push({
      type: 'eq',
      key: 'platform',
      value: PLATFORM.APP,
    });
  }

  return {
    type: 'file_search',
    vector_store_ids: ['vs_68556954c7fc819191bcfe5bc2f4fa28'],
    max_num_results: 10,
    ranking_options: {
      score_threshold: 0.5, // Ensures high relevance
    },
    filters: {
      type: 'or',
      filters: platformFilters,
    },
  };
};

const nasioKnowledgeBase = (params) =>
  new FunctionCall(null, nasioKnowledgeBaseSchema(params), null);

module.exports = nasioKnowledgeBase;
