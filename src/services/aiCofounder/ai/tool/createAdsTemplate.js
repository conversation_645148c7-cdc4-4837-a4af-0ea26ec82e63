const FunctionCall = require('../model/FunctionCall');
const FunctionCallResponse = require('../model/FunctionCallResponse');
const { FUNCTION_CALL_TYPE } = require('../constants');
const adsService = require('../ads/index');

const functionCallType = FUNCTION_CALL_TYPE.ADS_TEMPLATE;

const generateAdsTemplateFunctionCall = (params) => ({
  type: 'function',
  name: 'generate_ads_template',
  description:
    'Generate high-performing Meta ad creatives tailored for Facebook and Instagram placements, including compelling text and AI-powered image prompts.',
  parameters: {
    type: 'object',
    properties: {
      ads: {
        type: 'array',
        description:
          'A list of Meta ad creatives. Each creative includes attention-grabbing copy and a visual prompt designed to drive engagement across Meta platforms.',
        minItems: 1,
        maxItems: params?.adsGenerationMaxItems ?? 3,
        items: {
          type: 'object',
          properties: {
            primaryText: {
              type: 'string',
              description:
                'The main ad copy shown above the visual asset. This should immediately capture attention, communicate the key message, and align with the target audience. Use the same language as the user’s original input.',
            },
            headline: {
              type: 'string',
              description:
                'A bold, concise statement placed below the image or video. It should highlight the core value, product name, or offer to drive clicks. Use the same language as the user’s original input.',
            },
            description: {
              type: 'string',
              description:
                'Optional supporting text shown in select placements under the headline. This should add context or clarify the benefit. Use the same language as the user’s original input.',
            },
            imagePrompt: {
              type: 'string',
              description:
                'A professionally written prompt for generating a realistic, high-conversion ad image. Focus on commercial-quality visuals with emotionally resonant elements, clear subject focus, and relevance to the ad copy. Use language and framing consistent with modern advertising (e.g. "bright natural lighting," "product in use by target audience," "clean lifestyle background"). Must match the user’s input language.',
            },
            imageUrl: {
              type: 'string',
              description:
                'Provide this only when refining an existing ad but not changing the image. Leave empty if generating a new ad or refining the ad with a new image.',
            },
          },
          required: [
            'primaryText',
            'headline',
            'description',
            'imagePrompt',
          ],
          additionalProperties: false,
        },
      },
    },
    required: ['ads'],
    additionalProperties: false,
  },
});

const generateAdsTemplateFunctionCallHandler = async (
  args,
  communityObjectId,
  chatObjectId,
  callId,
  aiResponseId,
  assignTemplateObjectId
) => {
  const { ads } = args;

  const adsTemplate = {
    ads,
  };

  if (assignTemplateObjectId) {
    const updatedAdsTemplate = await adsService.updateAds({
      data: adsTemplate,
      communityObjectId,
      chatObjectId,
      callId,
      aiResponseId,
      assignTemplateObjectId,
    });

    return new FunctionCallResponse(functionCallType, updatedAdsTemplate);
  }

  const createdAdsTemplate = await adsService.createAds({
    data: adsTemplate,
    communityObjectId,
    chatObjectId,
    callId,
    aiResponseId,
  });

  return new FunctionCallResponse(functionCallType, createdAdsTemplate);
};

const functionCall = (params) =>
  new FunctionCall(
    functionCallType,
    generateAdsTemplateFunctionCall(params),
    generateAdsTemplateFunctionCallHandler
  );

module.exports = functionCall;
