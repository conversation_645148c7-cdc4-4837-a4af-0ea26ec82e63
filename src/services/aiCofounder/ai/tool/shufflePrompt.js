const FunctionCall = require('../model/FunctionCall');
const FunctionCallResponse = require('../model/FunctionCallResponse');
const {
  FUNCTION_CALL_TYPE,
  MESSAGE_INTENT_TYPE,
} = require('../constants');

const functionCallType = FUNCTION_CALL_TYPE.SHUFFLE_PROMPT;

const shufflePromptFunctionCall = () => ({
  type: 'function',
  name: MESSAGE_INTENT_TYPE.SHUFFLE_PROMPT.type,
  description: MESSAGE_INTENT_TYPE.SHUFFLE_PROMPT.description,
  parameters: {
    type: 'object',
    properties: {
      product: {
        type: 'string',
        description:
          'The type of product being created (e.g., "7-day challenge", "digital course") in local language.',
      },
      audience: {
        type: 'string',
        description:
          'The target audience the product is designed for (e.g., "busy working professional", "new moms") in local language.',
      },
      outcome: {
        type: 'string',
        description:
          'The desired result or transformation the audience should achieve (e.g., "build consistent fitness habits and eat well") in local language.',
      },
    },
    required: ['product', 'audience', 'outcome'],
    additionalProperties: false,
  },
});

const shufflePromptFunctionCallHandler = async (args) => {
  const { product, audience, outcome } = args;

  const response = [
    {
      name: 'product',
      value: product,
    },
    {
      name: 'audience',
      value: audience,
    },
    {
      name: 'outcome',
      value: outcome,
    },
  ];

  return new FunctionCallResponse(functionCallType, response);
};

const functionCall = (params) =>
  new FunctionCall(
    functionCallType,
    shufflePromptFunctionCall(params),
    shufflePromptFunctionCallHandler
  );

module.exports = functionCall;
