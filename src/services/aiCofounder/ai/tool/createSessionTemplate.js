const FunctionCall = require('../model/FunctionCall');
const FunctionCallResponse = require('../model/FunctionCallResponse');
const {
  FUNCTION_CALL_TYPE,
  MESSAGE_INTENT_TYPE,
} = require('../constants');
const { PRICE_TYPE } = require('../../../../constants/common');
const sessionProductTemplateService = require('../productTemplate/session.service');

const functionCallType = FUNCTION_CALL_TYPE.SESSION_TEMPLATE;

const generateSessionTemplateFunctionCall = () => ({
  type: 'function',
  name: 'generate_session_template',
  description: 'Generate the one on one session',
  parameters: {
    type: 'object',
    properties: {
      title: {
        type: 'string',
        description:
          'The title of the product the same language as the user’s original input.',
      },
      description: {
        type: 'string',
        description:
          'The description of the product the same language as the user’s original input.',
      },
      unsplashImageSearchTitle: {
        type: 'string',
        description:
          'A short keyword or phrase that best represents the theme or activity of the product. It will be used to search for a relevant image on Unsplash. Keep it concise and descriptive, in the same language as the user’s original input.',
      },
      durationInMinutes: {
        type: 'number',
        description:
          'Total duration of the one on one session in minutes (number, e.g., 30).',
      },
      location: {
        type: 'string',
        description: 'Location of the one on one session.',
        enum: ['online', 'inPerson'],
      },
      priceType: {
        type: 'string',
        description: 'The price type of the product',
        enum: Object.values(PRICE_TYPE),
      },
      minAmount: {
        type: 'number',
        description:
          'Minimum payment in local currency in cents which required for priceType FLEXIBLE',
      },
      localCurrency: {
        type: 'string',
        description:
          'Local currency of the suggested amount, must be same as community currency',
      },
      suggestedAmount: {
        type: 'number',
        description: 'Recommended payment in local currency in cents',
      },
      possibleActions: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            action: {
              type: 'string',
              description: 'The action to take',
              enum: [
                MESSAGE_INTENT_TYPE.CHANGE_PRODUCT_TYPE.type,
                MESSAGE_INTENT_TYPE.REFINE.type,
                MESSAGE_INTENT_TYPE.HOW_TO_SELL.type,
              ],
            },
          },
          required: ['action'],
          additionalProperties: false,
        },
        minItems: 3,
        maxItems: 3,
        description:
          'The possible actions to take after generating the one on one session',
      },
    },
    required: [
      'title',
      'description',
      'unsplashImageSearchTitle',
      'durationInMinutes',
      'location',
      'priceType',
      'localCurrency',
      'suggestedAmount',
      'possibleActions',
    ],
    additionalProperties: false,
  },
});

const generateSessionTemplateFunctionCallHandler = async (
  args,
  communityObjectId,
  chatObjectId,
  callId,
  aiResponseId
) => {
  const {
    title,
    description,
    unsplashImageSearchTitle,
    durationInMinutes,
    location,
    priceType,
    minAmount,
    suggestedAmount,
    localCurrency,
    possibleActions,
  } = args;

  const sessionTemplate = {
    title,
    description,
    unsplashImageSearchTitle,
    durationInMinutes,
    location,
    priceType,
    minAmount,
    suggestedAmount,
    localCurrency,
    possibleActions,
  };

  const createdSessionTemplate =
    await sessionProductTemplateService.createTemplate({
      data: sessionTemplate,
      communityObjectId,
      chatObjectId,
      callId,
      aiResponseId,
    });

  return new FunctionCallResponse(
    functionCallType,
    createdSessionTemplate
  );
};

const functionCall = (params) =>
  new FunctionCall(
    functionCallType,
    generateSessionTemplateFunctionCall(params),
    generateSessionTemplateFunctionCallHandler
  );

module.exports = functionCall;
