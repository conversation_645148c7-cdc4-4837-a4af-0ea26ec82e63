const {
  MESSAGE_INTENT_TYPE,
  LOCALIZED_MESSAGE_INTENT_TYPE_MAPPER,
} = require('./constants');

const {
  AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES,
  AI_TEMPLATE_GENERATION_STATUS,
} = require('../../../constants/common');

const { sendMessageToSQSQueue } = require('@/src/handlers/sqs.handler');
const { GENERAL_PURPOSE_TASK_QUEUE_URL } = require('@/src/config');
const { TASK_TYPE } = require('@/src/constants/eventBridge');
const { LEARN_BACKEND_URL } = require('@/src/config');
const { ParamError } = require('@/src/utils/error.util');

const ROUTE_API_KEY = process.env.ROUTE_API_KEY;

const AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES_WITH_RANKING = {
  [AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.CHALLENGE]: 1,
  [AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.EVENT]: 2,
  [AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.COURSE]: 3,
  [AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.FOLDER]: 4,
  [AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.MEMBERSHIP]: 5,
  [AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.SESSION]: 6,
};

exports.addLocalizedKey = (possibleActions) => {
  if (!possibleActions) {
    return [];
  }

  return possibleActions.map((possibleAction) => ({
    ...possibleAction,
    localizedKey:
      LOCALIZED_MESSAGE_INTENT_TYPE_MAPPER[possibleAction.action],
  }));
};

exports.retrievePossibleActionsFromFunctionCallData = (
  functionCallData
) => {
  if (functionCallData?.functionCallOutput?.possibleActions) {
    return this.addLocalizedKey(
      functionCallData.functionCallOutput.possibleActions
    );
  }
};

exports.retrievePossibleActions = ({
  community,
  messageIntent,
  selectedTemplate,
  functionCallData,
}) => {
  if (functionCallData) {
    return this.retrievePossibleActionsFromFunctionCallData(
      functionCallData
    );
  }

  switch (messageIntent) {
    case MESSAGE_INTENT_TYPE.CHANGE_PRODUCT_TYPE.type: {
      const templateType = selectedTemplate?.type;
      const hasSetupPaidSubscriptions = community.isPaidCommunity;

      const excludedTypes = new Set([
        templateType,
        AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.ADS,
        AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.LEADS,
      ]);

      if (hasSetupPaidSubscriptions) {
        excludedTypes.add(AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.MEMBERSHIP);
      }

      const possibleActions = Object.values(
        AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES
      )
        .filter((type) => !excludedTypes.has(type))
        .map((type) => ({
          action: MESSAGE_INTENT_TYPE.CHANGE_TO_SPECIFIC_PRODUCT_TYPE.type,
          targetProductType: type,
        }))
        .sort(
          (a, b) =>
            AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES_WITH_RANKING[
              a.targetProductType
            ] -
            AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES_WITH_RANKING[
              b.targetProductType
            ]
        );

      return this.addLocalizedKey(possibleActions);
    }
    case MESSAGE_INTENT_TYPE.HOW_TO_SELL.type: {
      return this.addLocalizedKey([
        {
          action: MESSAGE_INTENT_TYPE.ADS_GENERATION.type,
        },
        {
          action: MESSAGE_INTENT_TYPE.LEADS_GENERATION.type,
        },
      ]);
    }
    default:
      return [];
  }
};

exports.generateTemplatesInBackground = async ({
  communityObjectId,
  templateObjectId,
  messageObjectId,
  templates,
}) => {
  await sendMessageToSQSQueue({
    queueUrl: GENERAL_PURPOSE_TASK_QUEUE_URL,
    messageBody: {
      tasks: [
        {
          type: TASK_TYPE.CUSTOM_API,
          taskDetails: {
            path: `${LEARN_BACKEND_URL}api/v1/communities/${communityObjectId}/ai-cofounder/templates/${templateObjectId}/generate`,
            method: 'post',
            headers: {
              'api-key': ROUTE_API_KEY,
            },
            bodyParam: {
              messageObjectId,
              templates,
            },
          },
        },
      ],
    },
  });
};

function isMatchingProcessingTemplate({ template, templateType }) {
  return (
    template.type === templateType &&
    template.status === AI_TEMPLATE_GENERATION_STATUS.PROCESSING
  );
}

function validateTemplateStatus({ template, templateType }) {
  const processingErrorMessage =
    'Selected template is still processing. Please try again later.';

  if (
    isMatchingProcessingTemplate({
      template,
      templateType,
    })
  ) {
    throw new ParamError(processingErrorMessage);
  }

  const hasProcessingAdditionalTemplates =
    template.additionalTemplates?.some((additionalTemplate) =>
      isMatchingProcessingTemplate({
        template: additionalTemplate,
        templateType,
      })
    );

  if (hasProcessingAdditionalTemplates) {
    throw new ParamError(processingErrorMessage);
  }
}

exports.validateSelectedTemplateStatus = ({
  messageIntent,
  selectedTemplate,
}) => {
  if (!selectedTemplate) {
    return;
  }

  switch (messageIntent) {
    case MESSAGE_INTENT_TYPE.REFINE_ADS.type:
      validateTemplateStatus({
        template: selectedTemplate,
        templateType: AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.ADS,
      });
      break;
    default:
      break;
  }
};
