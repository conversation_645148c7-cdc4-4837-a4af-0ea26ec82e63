const client = require('./client');
const tools = require('./tool');
const logger = require('@/src/services/logger.service');
const { handleStreamEvent } = require('./handler');
const promptService = require('./prompt');
const productTemplateCommonService = require('./productTemplate/common.service');
const AIContextBuilder = require('./model/AIContextBuilder');
const {
  AI_MODEL,
  MAX_OUTPUT_TOKEN,
  MESSAGE_INTENT_TYPE,
  ALLOWED_MESSAGE_INTENT_TYPES,
  AI_TEMPLATE_PROMPT_TYPE,
  INPUT_ROLE_TYPE,
  TOOL_CHOICE_TYPE,
  MAX_ADS_GENERATION_TOKEN,
} = require('./constants');
const { INTENT_TYPE } = require('../../chat/constants');
const { ParamError } = require('../../../utils/error.util');
const {
  AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES,
  AI_TEMPLATE_GENERATION_STATUS,
} = require('@/src/constants/common');
const commonService = require('./common.service');
const adsService = require('./ads');
const communityNotificationCommonService = require('@/src/services/communityNotification/email/common.service');
const { EVENT_TYPE } = require('./constants');

const allowedIntentTypes = new Set(
  ALLOWED_MESSAGE_INTENT_TYPES.map((intentType) => intentType.type)
);

async function retrieveMessageIntent({ chatMessage, responseId }) {
  const {
    message,
    communityObjectId,
    chatObjectId,
    intentType,
    actionType,
  } = chatMessage;

  if (
    intentType === INTENT_TYPE.ACTION &&
    allowedIntentTypes.has(actionType)
  ) {
    return actionType;
  }

  if (intentType === INTENT_TYPE.ACTION) {
    throw new ParamError(`Invalid action type ${actionType}`);
  }

  const selectedToolsSchema = tools.retrieveAllToolsSchema(
    MESSAGE_INTENT_TYPE.MESSAGE_INTENT_CLASSIFIER.type
  );

  const { prompt, aiModel } =
    await promptService.getProcessedPromptAndModel({
      templateType: AI_TEMPLATE_PROMPT_TYPE.MESSAGE_INTENT_CLASSIFIER,
      variables: {
        intentList: JSON.stringify(ALLOWED_MESSAGE_INTENT_TYPES),
      },
    });

  const model = aiModel?.model ?? AI_MODEL.MESSAGE_INTENT;

  const response = await client.responses.create({
    model,
    previous_response_id: responseId,
    input: [
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: prompt,
      },
      {
        role: INPUT_ROLE_TYPE.USER,
        content: message,
      },
    ],
    tools: selectedToolsSchema,
    tool_choice: TOOL_CHOICE_TYPE.REQUIRED,
    user: communityObjectId,
    max_output_tokens: MAX_OUTPUT_TOKEN,
    parallel_tool_calls: false,
    max_tool_calls: 1,
    metadata: {
      communityObjectId,
      chatObjectId,
    },
    store: false,
    truncation: 'auto',
  });

  const responseOutput = response?.output?.find(
    (output) => output.type === EVENT_TYPE.FUNCTION_CALL_START.itemType
  );

  const functionCallData = await tools.retrieveAndExecuteTool({
    name: responseOutput?.name,
    args: responseOutput?.arguments,
    callId: responseOutput?.call_id,
    communityObjectId,
    chatObjectId,
  });

  return functionCallData.functionCallOutput;
}

function getRandomItem(arr) {
  return arr[Math.floor(Math.random() * arr.length)];
}

function getRandomProduct(products, durations) {
  const product = getRandomItem(products);
  let label = product;

  if (product === 'challenge') {
    const duration = getRandomItem(durations);
    label = `${duration} ${product}`;
  }

  return label;
}

async function generateInitialPrompt({
  community,
  variables,
  promptTemplate,
}) {
  const prompt = promptService.replaceVariablesInPrompt({
    prompt: promptTemplate.prompt,
    variables,
  });

  const model = promptTemplate.aiModel?.model ?? AI_MODEL.DEFAULT;

  const selectedToolsSchema = tools.retrieveAllToolsSchema(
    MESSAGE_INTENT_TYPE.SHUFFLE_PROMPT.type
  );

  const response = await client.responses.create({
    model,
    input: [
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: prompt,
      },
    ],
    tools: selectedToolsSchema,
    tool_choice: TOOL_CHOICE_TYPE.REQUIRED,
    user: community._id,
    max_output_tokens: MAX_OUTPUT_TOKEN,
    parallel_tool_calls: false,
    max_tool_calls: 1,
    metadata: {
      communityObjectId: community._id,
    },
    store: false,
  });

  const responseOutput = response?.output?.find(
    (output) => output.type === EVENT_TYPE.FUNCTION_CALL_START.itemType
  );

  const functionCallData = await tools.retrieveAndExecuteTool({
    name: responseOutput?.name,
    args: responseOutput?.arguments,
    callId: responseOutput?.call_id,
    communityObjectId: community._id,
  });

  return functionCallData.functionCallOutput;
}

exports.shuffleInitialPrompt = async ({
  community,
  languagePreference,
}) => {
  const aiContextBuilder = new AIContextBuilder({
    community,
  });

  const [promptTemplate] = await Promise.all([
    promptService.retrievePromptTemplate({
      templateType: AI_TEMPLATE_PROMPT_TYPE.SHUFFLE_INITIAL_PROMPT,
    }),
    aiContextBuilder.buildContext(),
  ]);

  const productWeights = {
    challenge: 8, // Most frequent
    'digital file': 2,
    course: 2,
    event: 2,
    membership: 1,
    '1:1 session': 1, // Least frequent
  };

  const products = Object.entries(productWeights).flatMap(
    ([product, weight]) =>
      !community.isPaidCommunity && product === 'membership'
        ? []
        : Array(weight).fill(product)
  );

  const durations = ['7-day', '14-day', '30-day'];

  const variables = {
    communityInfo: JSON.stringify(aiContextBuilder.communityInfo),
    learnerInfo: JSON.stringify(aiContextBuilder.learnerInfo),
    languagePreference,
  };

  if (aiContextBuilder.learnerSocials) {
    variables.socialInfo = JSON.stringify(aiContextBuilder.learnerSocials);
  }

  if (aiContextBuilder.topProductsInfo) {
    variables.topProductsInfo = JSON.stringify(
      aiContextBuilder.topProductsInfo
    );
  }

  const randomProduct = getRandomProduct(products, durations);

  const newVariables = {
    ...variables,
    product: randomProduct,
  };

  return generateInitialPrompt({
    community,
    variables: newVariables,
    promptTemplate,
  });
};

exports.processAiStream = async ({
  messageCountInfo,
  community,
  chatMessage,
  selectedTemplate,
  latestAdsTemplate,
  latestLeadsTemplate,
  responseId,
  platform,
  timezone,
  languagePreference,
  abortController,
  onData,
  onDone,
  onError,
}) => {
  const messageIntent = await retrieveMessageIntent({
    chatMessage,
    responseId,
  });

  const isFirstMessage = !responseId;

  const [promptAndModel, initialInstructionAndModel] = await Promise.all([
    promptService.retrievePromptViaMessageIntent({
      messageIntent,
      variables: {
        timezone,
      },
    }),
    isFirstMessage
      ? promptService.getProcessedPromptAndModel({
          templateType: AI_TEMPLATE_PROMPT_TYPE.INITIAL_INSTRUCTION,
        })
      : null,
  ]);

  const currentDateTime = new Date();

  const input = [
    {
      role: INPUT_ROLE_TYPE.SYSTEM,
      content: promptAndModel.prompt,
    },
    {
      role: INPUT_ROLE_TYPE.SYSTEM,
      content: `Current date and time in UTC: ${currentDateTime.toISOString()}\nUser timezone: ${timezone}`,
    },
  ];

  if (isFirstMessage) {
    const aiContextBuilder = new AIContextBuilder({
      community,
    });
    await aiContextBuilder.buildContext();

    input.push(...aiContextBuilder.toSystemMessages());
  }

  commonService.validateSelectedTemplateStatus({
    messageIntent,
    selectedTemplate,
  });

  const { additionalTemplates, ...productTemplate } =
    selectedTemplate ?? {};

  if (productTemplate) {
    input.push({
      role: INPUT_ROLE_TYPE.SYSTEM,
      content: `Selected product template for reference:\n${JSON.stringify(
        productTemplate
      )}`,
    });
  }

  const additionalAdsTemplate = additionalTemplates?.find(
    (adsTemplate) =>
      adsTemplate.type === AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.ADS
  );

  const additionalLeadsTemplate = additionalTemplates?.find(
    (leadsTemplate) =>
      leadsTemplate.type === AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.LEADS
  );

  const selectedAdsTemplate = additionalAdsTemplate ?? latestAdsTemplate;

  if (selectedAdsTemplate) {
    input.push({
      role: INPUT_ROLE_TYPE.SYSTEM,
      content: `Selected ads template for reference:\n${JSON.stringify(
        selectedAdsTemplate
      )}`,
    });
  }

  const selectedLeadsTemplate =
    additionalLeadsTemplate ?? latestLeadsTemplate;

  if (selectedLeadsTemplate) {
    input.push({
      role: INPUT_ROLE_TYPE.SYSTEM,
      content: `Selected leads template for reference:\n${JSON.stringify(
        selectedLeadsTemplate
      )}`,
    });
  }

  const adsGenerationFeaturePermission =
    adsService.retrieveAdsGenerationFeaturePermission({
      community,
    });

  const toolParams = {
    platform,
    adsGenerationMaxItems: adsGenerationFeaturePermission.limit,
    targetLanguage: languagePreference,
  };

  const selectedToolsSchema = tools.retrieveAllToolsSchema(
    messageIntent,
    toolParams
  );

  input.push({
    role: INPUT_ROLE_TYPE.USER,
    content: chatMessage.message,
  });

  const model = promptAndModel.aiModel?.model ?? AI_MODEL.DEFAULT;

  const maxOutputTokens =
    messageIntent === MESSAGE_INTENT_TYPE.ADS_GENERATION.type
      ? MAX_ADS_GENERATION_TOKEN
      : MAX_OUTPUT_TOKEN;

  try {
    const stream = await client.responses.create(
      {
        model,
        previous_response_id: responseId,
        instructions: initialInstructionAndModel?.prompt,
        input,
        stream: true,
        tools: selectedToolsSchema,
        store: true,
        tool_choice:
          selectedToolsSchema.length > 0
            ? TOOL_CHOICE_TYPE.AUTO
            : TOOL_CHOICE_TYPE.NONE,
        parallel_tool_calls: false,
        max_tool_calls: 1,
        include: ['file_search_call.results'],
        user: chatMessage.communityObjectId, // for rate limiting and abuse tracking per user
        max_output_tokens: maxOutputTokens,
        metadata: {
          communityObjectId: chatMessage.communityObjectId,
          chatObjectId: chatMessage.chatObjectId,
          messageIntent,
          learnerObjectId: chatMessage.senderLearnerObjectId,
        },
        truncation: 'auto',
      },
      { signal: abortController.signal }
    );

    await handleStreamEvent({
      stream,
      onData,
      onDone,
      onError,
      communityObjectId: chatMessage.communityObjectId,
      chatObjectId: chatMessage.chatObjectId,
      learnerObjectId: chatMessage.senderLearnerObjectId,
      messageObjectId: chatMessage._id,
      community,
      messageIntent,
      toolParams,
      timezone,
      selectedTemplate,
      messageCountInfo,
      abortController,
    });
  } catch (err) {
    logger.error(`processAiStream: ${err.message}, ${err.stack}`);
    onError({ type: 'error', text: err.message });
  }
};

exports.generateChatTitleFromResponse = async ({
  communityObjectId,
  chatObjectId,
  responseId,
}) => {
  const model = AI_MODEL.MESSAGE_INTENT;

  const prompt = `Generate a short, clear, and engaging chat title (max 8 words) that summarizes the main topic or goal based on the user’s first message and the AI’s first response.
  Be specific, avoid generic titles like “Help Needed” or “Conversation”.`;

  const response = await client.responses.create({
    model,
    input: prompt,
    user: communityObjectId,
    previous_response_id: responseId,
    max_output_tokens: MAX_OUTPUT_TOKEN,
    parallel_tool_calls: false,
    max_tool_calls: 1,
    metadata: {
      communityObjectId,
      chatObjectId,
    },
    store: false,
  });

  const responseOutput = response?.output
    ?.filter((output) => output.type === EVENT_TYPE.TEXT.event)?.[0]
    ?.content?.[0]?.text?.trim();

  return responseOutput;
};

async function updateFunctionCallResult({ template }) {
  const { callId, aiResponseId } = template.metadata;

  if (!callId || !aiResponseId) {
    logger.warn(
      `updateFunctionCallResult: Invalid template metadata: ${JSON.stringify(
        template
      )}`
    );
    return null;
  }

  const response = await client.responses.create({
    model: AI_MODEL.DEFAULT,
    previous_response_id: aiResponseId,
    input: [
      {
        type: 'function_call_output',
        call_id: callId,
        output: JSON.stringify(template),
      },
    ],
    truncation: 'auto',
  });

  return response.id;
}

exports.generateAds = async ({
  community,
  templateObjectId,
  assignTemplateObjectId,
}) => {
  const [promptTemplate, template, assignTemplate] = await Promise.all([
    promptService.retrievePromptTemplate({
      templateType: AI_TEMPLATE_PROMPT_TYPE.ADS_GENERATION,
    }),
    productTemplateCommonService.retrieveTemplate({
      communityObjectId: community._id,
      templateObjectId,
      readFromPrimary: true,
    }),
    productTemplateCommonService.retrieveTemplate({
      communityObjectId: community._id,
      templateObjectId: assignTemplateObjectId,
      readFromPrimary: true,
    }),
  ]);

  if (assignTemplate.status !== AI_TEMPLATE_GENERATION_STATUS.PROCESSING) {
    logger.warn(
      `generateAds: Template status is not processing: ${assignTemplate.status}`
    );
    return;
  }

  let lastResponseId;

  try {
    lastResponseId = await updateFunctionCallResult({
      template,
    });
  } catch (err) {
    logger.error(`generateAds: ${err.message}`);
    return;
  }

  const chatObjectId = template.sourceObjectId;

  const prompt = promptService.replaceVariablesInPrompt({
    prompt: promptTemplate.prompt,
  });

  const model = promptTemplate.aiModel?.model ?? AI_MODEL.DEFAULT;

  const adsGenerationFeaturePermission =
    adsService.retrieveAdsGenerationFeaturePermission({
      community,
    });

  const toolParams = {
    adsGenerationMaxItems: adsGenerationFeaturePermission.limit,
  };

  const selectedToolsSchema = tools.retrieveAllToolsSchema(
    MESSAGE_INTENT_TYPE.ADS_GENERATION.type,
    toolParams
  );

  const response = await client.responses.create({
    model,
    previous_response_id: lastResponseId,
    input: [
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: prompt,
      },
    ],
    tools: selectedToolsSchema,
    tool_choice: TOOL_CHOICE_TYPE.REQUIRED,
    parallel_tool_calls: false,
    max_tool_calls: 1,
    user: community._id,
    max_output_tokens: MAX_ADS_GENERATION_TOKEN,
    metadata: {
      communityObjectId: community._id,
      chatObjectId,
    },
    truncation: 'auto',
  });

  const responseOutput = response?.output?.find(
    (output) => output.type === EVENT_TYPE.FUNCTION_CALL_START.itemType
  );

  const functionCallData = await tools.retrieveAndExecuteTool({
    name: responseOutput?.name,
    args: responseOutput?.arguments,
    callId: responseOutput?.call_id,
    communityObjectId: community._id,
    chatObjectId,
    lastResponseId: response.id,
    assignTemplateObjectId,
  });

  const adsTemplateObjectId =
    functionCallData.functionCallOutput.template._id;

  await productTemplateCommonService.linkTemplateToTemplate({
    communityObjectId: community._id,
    templateObjectId,
    linkedTemplateObjectId: adsTemplateObjectId,
  });

  return {
    templateObjectId: adsTemplateObjectId,
    usage: response.usage,
    responseId: response.id,
    type: AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.ADS,
  };
};

exports.generateLeads = async ({
  community,
  templateObjectId,
  assignTemplateObjectId,
}) => {
  const [promptTemplate, template, assignTemplate, owner] =
    await Promise.all([
      promptService.retrievePromptTemplate({
        templateType: AI_TEMPLATE_PROMPT_TYPE.LEADS_GENERATION,
      }),
      productTemplateCommonService.retrieveTemplate({
        communityObjectId: community._id,
        templateObjectId,
        readFromPrimary: true,
      }),
      productTemplateCommonService.retrieveTemplate({
        communityObjectId: community._id,
        templateObjectId: assignTemplateObjectId,
        readFromPrimary: true,
      }),
      communityNotificationCommonService.retrieveCommunityOwnerInfo(
        community.code
      ),
    ]);

  if (assignTemplate.status !== AI_TEMPLATE_GENERATION_STATUS.PROCESSING) {
    logger.warn(
      `generateLeads: Template status is not processing: ${assignTemplate.status}`
    );
    return;
  }

  let lastResponseId;

  try {
    lastResponseId = await updateFunctionCallResult({
      template,
    });
  } catch (err) {
    logger.error(`generateLeads: ${err.message}`);
    return;
  }

  const chatObjectId = template.sourceObjectId;

  const prompt = promptService.replaceVariablesInPrompt({
    prompt: promptTemplate.prompt,
  });

  const model = promptTemplate.aiModel?.model ?? AI_MODEL.DEFAULT;

  const targetLanguage = owner?.languagePreference ?? 'en';

  const selectedToolsSchema = tools.retrieveAllToolsSchema(
    MESSAGE_INTENT_TYPE.LEADS_GENERATION.type,
    { targetLanguage }
  );

  const response = await client.responses.create({
    model,
    previous_response_id: lastResponseId,
    input: [
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: prompt,
      },
    ],
    tools: selectedToolsSchema,
    tool_choice: TOOL_CHOICE_TYPE.REQUIRED,
    parallel_tool_calls: false,
    max_tool_calls: 1,
    user: community._id,
    max_output_tokens: MAX_OUTPUT_TOKEN,
    metadata: {
      communityObjectId: community._id,
      chatObjectId,
    },
    truncation: 'auto',
  });

  const responseOutput = response?.output?.find(
    (output) => output.type === EVENT_TYPE.FUNCTION_CALL_START.itemType
  );

  const functionCallData = await tools.retrieveAndExecuteTool({
    name: responseOutput?.name,
    args: responseOutput?.arguments,
    callId: responseOutput?.call_id,
    communityObjectId: community._id,
    chatObjectId,
    lastResponseId: response.id,
    assignTemplateObjectId,
    toolParams: { targetLanguage },
  });

  const leadsTemplateObjectId =
    functionCallData.functionCallOutput.template._id;

  await productTemplateCommonService.linkTemplateToTemplate({
    communityObjectId: community._id,
    templateObjectId,
    linkedTemplateObjectId: leadsTemplateObjectId,
  });

  return {
    templateObjectId: leadsTemplateObjectId,
    usage: response.usage,
    responseId: response.id,
    type: AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.LEADS,
  };
};
