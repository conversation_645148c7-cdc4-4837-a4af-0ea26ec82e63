const AI_MODEL = {
  DEFAULT: 'gpt-5-mini',
  FOLLOW_UP: 'gpt-5-nano',
  MESSAGE_INTENT: 'gpt-5-nano',
  IMAGE: 'gpt-image-1',
};

const EVENT_TYPE = {
  TEXT: {
    type: 'text',
    event: 'response.output_text.delta',
  },
  MESSAGE_COUNT: {
    type: 'messageCount',
  },
  FUNCTION_CALL_START: {
    type: 'functionCallStart',
    event: 'response.output_item.added',
    itemType: 'function_call',
  },
  FUNCTION_CALL_READY: {
    type: 'functionCallOutput',
    event: 'response.output_item.done',
    itemType: 'function_call',
  },
  RESPONSE_COMPLETED: {
    type: 'responseCompleted',
    event: 'response.completed',
  },
  FILE_SEARCH_START: {
    type: 'fileSearchStart',
    event: 'response.output_item.added',
    itemType: 'file_search_call',
  },
};

const FUNCTION_CALL_TYPE = {
  SHUFFLE_PROMPT: 'SHUFFLE_PROMPT',
  MESSAGE_INTENT_CLASSIFIER: 'MESSAGE_INTENT_CLASSIFIER',
  EVENT_TEMPLATE: 'EVENT_TEMPLATE',
  DIGITAL_FILE_TEMPLATE: 'DIGITAL_FILE_TEMPLATE',
  COURSE_TEMPLATE: 'COURSE_TEMPLATE',
  SESSION_TEMPLATE: 'SESSION_TEMPLATE',
  CHALLENGE_TEMPLATE: 'CHALLENGE_TEMPLATE',
  MEMBERSHIP_TEMPLATE: 'MEMBERSHIP_TEMPLATE',
  ADS_TEMPLATE: 'ADS_TEMPLATE',
  LEADS_TEMPLATE: 'LEADS_TEMPLATE',
};

const LOCALIZED_FUNCTION_CALL_TYPE_MAPPER = {
  [FUNCTION_CALL_TYPE.SHUFFLE_PROMPT]:
    'ai-cofounder-function-call-shuffle-prompt',
  [FUNCTION_CALL_TYPE.MESSAGE_INTENT_CLASSIFIER]:
    'ai-cofounder-function-call-message-intent-classifier',
  [FUNCTION_CALL_TYPE.EVENT_TEMPLATE]:
    'ai-cofounder-function-call-event-template',
  [FUNCTION_CALL_TYPE.DIGITAL_FILE_TEMPLATE]:
    'ai-cofounder-function-call-digital-file-template',
  [FUNCTION_CALL_TYPE.COURSE_TEMPLATE]:
    'ai-cofounder-function-call-course-template',
  [FUNCTION_CALL_TYPE.SESSION_TEMPLATE]:
    'ai-cofounder-function-call-session-template',
  [FUNCTION_CALL_TYPE.CHALLENGE_TEMPLATE]:
    'ai-cofounder-function-call-challenge-template',
  [FUNCTION_CALL_TYPE.MEMBERSHIP_TEMPLATE]:
    'ai-cofounder-function-call-membership-template',
  [FUNCTION_CALL_TYPE.ADS_TEMPLATE]:
    'ai-cofounder-function-call-ads-template',
  [FUNCTION_CALL_TYPE.LEADS_TEMPLATE]:
    'ai-cofounder-function-call-leads-template',
};

const MESSAGE_INTENT_TYPE = {
  SHUFFLE_PROMPT: {
    type: 'shuffle_prompt',
    description: `Generates a new variation of a product prompt tailored to the given product, audience, and desired outcome.`,
  },
  MESSAGE_INTENT_CLASSIFIER: {
    type: 'message_intent_classifier',
    description: `Classify the message intent`,
  },
  PRODUCT_TEMPLATE: {
    type: 'product_template',
    description: `The user is asking you to generate a product (eg. event, digital file, course, session, challenge, or membership) they can sell on Nas.io based on a broad idea they’ve provided.
    
    ✅ Use this intent even if the user also asks for ads or leads, **as long as the main goal is to create a product**.
    ❌ Do not use this intent if the user only wants help promoting an existing product or finding leads for something that already exists — those should use the appropriate ads or leads intents.`,
  },
  CHANGE_PRODUCT_TYPE: {
    type: 'change_product_type',
    description: `You previously helped the creator generate a product, and now they want to switch it to another product type available on Nas.io.
    This intent is used when the user does not specify which product type they want, only that they want a different one.`,
  },
  CHANGE_TO_SPECIFIC_PRODUCT_TYPE: {
    type: 'change_to_specific_product_type',
    description: `Triggers when the user wants to convert an existing product into a specific Nas.io product type (e.g., challenge, course, digital file, event, paid membership, or 1:1 session) and explicitly states the target type.`,
  },
  CHANGE_SOME_INFO_IN_PRODUCT_TEMPLATE: {
    type: 'change_some_info_in_product_template',
    description: `The user previously received a generated product and now wants to change part of its content.
    They will usually point to a specific field or element, eg. "Change to a 30-day challenge", “Add one more checkpoint".
    The field(s) to be changed vary by product type, and you need to analyze and interpret accordingly.
    
    Note: Changes related to marketing, ads, or promotional content do NOT fall under this intent.`,
  },
  REFINE: {
    type: 'refine_product_template',
    description: `The user wants to polish or improve the previously generated product while keeping the product type the same.
    They may or may not provide specific feedback, but the goal is to make the content more compelling or appropriate for them.`,
  },
  HOW_TO_SELL: {
    type: 'how_to_sell',
    description: `The user already has a product (you-generated or self-created) and now wants to know how to launch it successfully and drive sales.
    In short, the user is looking for go-to-market advice or ways to convert the product into revenue.`,
  },
  SENSITIVE_QUESTIONS: {
    type: 'sensitive_questions',
    description: `The user is asking a sensitive or tricky question. These include but are not limited to:
    - Questions about platforms similar to Nas.io, such as Patreon, Buy Me a Coffee, Gumroad, LemonSqueezy, etc. These platforms also serve creators and focus on community and digital product monetization.
    - Legal or compliance issues, whether related to Nas.io or not
    - Fraud, abuse, or anti-fraud strategy questions
    - Political opinions or positions
    - Questions about Nas company strategy, including but not limited to Nas.io and Nas Academy
    - Personal or company criticism
    - How nas.io is built, or how a certain feature is built
    In short, these are questions where providing answers may:
    - Harm Nas company’s interests or reputation
    - Expose Nas.io’s limitations, leading to potential customer loss
    - Have no constructive value for digital business or digital product creation`,
  },
  UNKNOWN: {
    type: 'unknown',
    description: 'Free input that does not belong to any intent',
  },
  ABOUT_NAS_IO: {
    type: 'about_nas_io',
    description: `Now the user must be asking normal questions about using Nas.io, as a normal customer/creator.
    Typical topics include but are not limited to:
    - Pricing and fees
    - How to use specific features
    - How to create product in nas.io
    - Where to find a specific features
    - Any related helpful resources
    - How to contact support
    We have a knowledge base prepared to support answers for this intent.
    
    Important:
    If the user mentions Nas.io but is asking about:
    - Company information
    - Business operations or financial performance
    - Personal information about the founders or employees
    - Internal product, design, or technical details
    Then classify these under Intent: sensitive questions instead.`,
  },
  ABOUT_DIGITAL_BUSINESS: {
    type: 'about_digital_business',
    description: `The user is asking general questions about starting or growing a digital business.
    Examples:
    - What tools or platforms to use
    - How to grow an audience
    - What skills to learn

    This is different from about_nas_io intent type, the user does not mention Nas.io specifically.`,
  },
  OTHER_COMMUNITIES_OR_PRODUCTS: {
    type: 'other_communities_or_products',
    description: `The user is asking about what other communities or creators are selling on Nas.io.
    They may be curious or looking for inspiration/comparison.`,
  },
  ADS_GENERATION: {
    type: 'ads_generation',
    description: `This intent covers cases where the user wants help creating marketing content to promote their product. This can include paid ads (like Magic Ads on Nas.io) or organic promotional materials for platforms such as Instagram, TikTok, Facebook, or X.
    You should classify the message under this intent if the user shows intent to promote their product and is asking for creative support to promote their product — this can include writing, visual ideas, or campaign suggestions.
    ✅ Look for signals such as:
    - Asking for creative copy, captions, or social post content
    - Requests for images, banners, or creative assets
    - Looking for marketing ideas, hooks, or angles to attract attention
    - Asking how to promote their product on social media or via ads
    It includes both organic and paid promotional requests. It’s okay if the user doesn’t explicitly say “ad” — as long as they’re looking for creative support`,
  },
  REFINE_ADS: {
    type: 'refine_ads',
    description: `This intent is for users who are asking for **revisions or edits to ads or promotional content previously generated** by you. This includes changes to the **text, tone, format, or media assets** such as images or visuals. The user may refer to the content directly or provide new feedback that suggests improvement or adaptation.

    ✅ Use this intent when the user:

    - Refers to a previous ad or marketing material and wants to change it
    - Mentions things like “can you make this shorter,” “add emojis,” “rewrite in a more fun tone,” etc.
    - Asks to tailor the same content for a different channel or audience
    - Shares feedback on a promotional draft and wants a new version
    - Requests to **change visuals or media** (e.g., “change the image,” “use a different graphic”)

    📝 Even if the user doesn’t say “edit,” this intent applies as long as they are clearly trying to improve, adapt, or rework any part of existing ad-related content — whether it’s text or media.`,
  },
  LEADS_GENERATION: {
    type: 'leads_generation',
    description: `This intent is for users who are asking for help finding or identifying potential buyers, audience segments, or leads for their product. The user may be thinking about pre-launch, soft launch, or simply trying to understand who to sell to.
    ✅ Use this intent if the user:
    - Asks for help finding leads, potential customers, or target audiences
    - Says things like “who might buy this,” “who can I send this to,” “can you find buyers for me”
    - Wants to get a list or suggestion of people they can reach out to, pitch, or email
    - Mentions interests related to early feedback, pre-launch pitching, or building a sales pipeline
    📝 It’s okay if they don’t say “leads” — the key is whether they’re asking for people or groups who would be interested in the product.`,
  },
};

const LOCALIZED_MESSAGE_INTENT_TYPE_MAPPER = {
  [MESSAGE_INTENT_TYPE.PRODUCT_TEMPLATE.type]:
    'ai-cofounder-message-intent-product-template',
  [MESSAGE_INTENT_TYPE.CHANGE_PRODUCT_TYPE.type]:
    'ai-cofounder-message-intent-change-product-type',
  [MESSAGE_INTENT_TYPE.CHANGE_TO_SPECIFIC_PRODUCT_TYPE.type]:
    'ai-cofounder-message-intent-change-to-specific-product-type',
  [MESSAGE_INTENT_TYPE.CHANGE_SOME_INFO_IN_PRODUCT_TEMPLATE.type]:
    'ai-cofounder-message-intent-change-some-info-in-product-template',
  [MESSAGE_INTENT_TYPE.REFINE.type]: 'ai-cofounder-message-intent-refine',
  [MESSAGE_INTENT_TYPE.HOW_TO_SELL.type]:
    'ai-cofounder-message-intent-how-to-sell',
  [MESSAGE_INTENT_TYPE.SENSITIVE_QUESTIONS.type]:
    'ai-cofounder-message-intent-sensitive-questions',
  [MESSAGE_INTENT_TYPE.UNKNOWN.type]:
    'ai-cofounder-message-intent-unknown',
  [MESSAGE_INTENT_TYPE.ABOUT_NAS_IO.type]:
    'ai-cofounder-message-intent-about-nas-io',
  [MESSAGE_INTENT_TYPE.ABOUT_DIGITAL_BUSINESS.type]:
    'ai-cofounder-message-intent-about-digital-business',
  [MESSAGE_INTENT_TYPE.OTHER_COMMUNITIES_OR_PRODUCTS.type]:
    'ai-cofounder-message-intent-other-communities-or-products',
  [MESSAGE_INTENT_TYPE.ADS_GENERATION.type]:
    'ai-cofounder-message-intent-ads-generation',
  [MESSAGE_INTENT_TYPE.LEADS_GENERATION.type]:
    'ai-cofounder-message-intent-leads-generation',
};

const AI_TEMPLATE_PROMPT_TYPE = {
  MESSAGE_INTENT_CLASSIFIER:
    MESSAGE_INTENT_TYPE.MESSAGE_INTENT_CLASSIFIER.type,
  PRODUCT_TEMPLATE: MESSAGE_INTENT_TYPE.PRODUCT_TEMPLATE.type,
  CHANGE_PRODUCT_TYPE: MESSAGE_INTENT_TYPE.CHANGE_PRODUCT_TYPE.type,
  CHANGE_TO_SPECIFIC_PRODUCT_TYPE:
    MESSAGE_INTENT_TYPE.CHANGE_TO_SPECIFIC_PRODUCT_TYPE.type,
  CHANGE_SOME_INFO_IN_PRODUCT_TEMPLATE:
    MESSAGE_INTENT_TYPE.CHANGE_SOME_INFO_IN_PRODUCT_TEMPLATE.type,
  REFINE: MESSAGE_INTENT_TYPE.REFINE.type,
  HOW_TO_SELL: MESSAGE_INTENT_TYPE.HOW_TO_SELL.type,
  SENSITIVE_QUESTIONS: MESSAGE_INTENT_TYPE.SENSITIVE_QUESTIONS.type,
  UNKNOWN: MESSAGE_INTENT_TYPE.UNKNOWN.type,
  ABOUT_NAS_IO: MESSAGE_INTENT_TYPE.ABOUT_NAS_IO.type,
  ABOUT_DIGITAL_BUSINESS: MESSAGE_INTENT_TYPE.ABOUT_DIGITAL_BUSINESS.type,
  OTHER_COMMUNITIES_OR_PRODUCTS:
    MESSAGE_INTENT_TYPE.OTHER_COMMUNITIES_OR_PRODUCTS.type,
  PRODUCT_TEMPLATE_FOLLOW_UP: 'product_template_follow_up',
  CHANGE_TO_SPECIFIC_PRODUCT_TYPE_FOLLOW_UP:
    'change_to_specific_product_type_follow_up',
  CHANGE_SOME_INFO_IN_PRODUCT_TEMPLATE_FOLLOW_UP:
    'change_some_info_in_product_template_follow_up',
  REFINE_FOLLOW_UP: 'refine_product_template_follow_up',
  SHUFFLE_INITIAL_PROMPT: 'shuffle_initial_prompt',
  INITIAL_INSTRUCTION: 'initial_instruction',
  ADS_GENERATION: MESSAGE_INTENT_TYPE.ADS_GENERATION.type,
  ADS_GENERATION_FOLLOW_UP: 'ads_generation_follow_up',
  REFINE_ADS: MESSAGE_INTENT_TYPE.REFINE_ADS.type,
  REFINE_ADS_FOLLOW_UP: 'refine_ads_follow_up',
  LEADS_GENERATION: MESSAGE_INTENT_TYPE.LEADS_GENERATION.type,
  LEADS_GENERATION_FOLLOW_UP: 'leads_generation_follow_up',
};

const FOLLOW_UP_TEMPLATE_TYPE_MAPPER = {
  [AI_TEMPLATE_PROMPT_TYPE.PRODUCT_TEMPLATE]:
    AI_TEMPLATE_PROMPT_TYPE.PRODUCT_TEMPLATE_FOLLOW_UP,
  [AI_TEMPLATE_PROMPT_TYPE.CHANGE_TO_SPECIFIC_PRODUCT_TYPE]:
    AI_TEMPLATE_PROMPT_TYPE.CHANGE_TO_SPECIFIC_PRODUCT_TYPE_FOLLOW_UP,
  [AI_TEMPLATE_PROMPT_TYPE.CHANGE_SOME_INFO_IN_PRODUCT_TEMPLATE]:
    AI_TEMPLATE_PROMPT_TYPE.CHANGE_SOME_INFO_IN_PRODUCT_TEMPLATE_FOLLOW_UP,
  [AI_TEMPLATE_PROMPT_TYPE.REFINE]:
    AI_TEMPLATE_PROMPT_TYPE.REFINE_FOLLOW_UP,
  [AI_TEMPLATE_PROMPT_TYPE.ADS_GENERATION]:
    AI_TEMPLATE_PROMPT_TYPE.ADS_GENERATION_FOLLOW_UP,
  [AI_TEMPLATE_PROMPT_TYPE.REFINE_ADS]:
    AI_TEMPLATE_PROMPT_TYPE.REFINE_ADS_FOLLOW_UP,
  [AI_TEMPLATE_PROMPT_TYPE.LEADS_GENERATION]:
    AI_TEMPLATE_PROMPT_TYPE.LEADS_GENERATION_FOLLOW_UP,
};

const ALLOWED_MESSAGE_INTENT_TYPES = [
  MESSAGE_INTENT_TYPE.PRODUCT_TEMPLATE,
  MESSAGE_INTENT_TYPE.CHANGE_PRODUCT_TYPE,
  MESSAGE_INTENT_TYPE.CHANGE_TO_SPECIFIC_PRODUCT_TYPE,
  MESSAGE_INTENT_TYPE.CHANGE_SOME_INFO_IN_PRODUCT_TEMPLATE,
  MESSAGE_INTENT_TYPE.REFINE,
  MESSAGE_INTENT_TYPE.HOW_TO_SELL,
  MESSAGE_INTENT_TYPE.UNKNOWN,
  MESSAGE_INTENT_TYPE.ABOUT_NAS_IO,
  MESSAGE_INTENT_TYPE.ABOUT_DIGITAL_BUSINESS,
  MESSAGE_INTENT_TYPE.OTHER_COMMUNITIES_OR_PRODUCTS,
  MESSAGE_INTENT_TYPE.SENSITIVE_QUESTIONS,
  MESSAGE_INTENT_TYPE.ADS_GENERATION,
  MESSAGE_INTENT_TYPE.REFINE_ADS,
  MESSAGE_INTENT_TYPE.LEADS_GENERATION,
];

const MAX_OUTPUT_TOKEN = 10000;
const MAX_ADS_GENERATION_TOKEN = 30000;

const INPUT_ROLE_TYPE = {
  USER: 'user',
  SYSTEM: 'system',
};

const TOOL_CHOICE_TYPE = {
  AUTO: 'auto',
  REQUIRED: 'required',
  NONE: 'none',
};

const PLATFORM = {
  WEB: 'web',
  APP: 'app',
  SHARED: 'shared',
};

module.exports = {
  AI_MODEL,
  EVENT_TYPE,
  FUNCTION_CALL_TYPE,
  MAX_OUTPUT_TOKEN,
  MESSAGE_INTENT_TYPE,
  ALLOWED_MESSAGE_INTENT_TYPES,
  FOLLOW_UP_TEMPLATE_TYPE_MAPPER,
  AI_TEMPLATE_PROMPT_TYPE,
  INPUT_ROLE_TYPE,
  TOOL_CHOICE_TYPE,
  LOCALIZED_FUNCTION_CALL_TYPE_MAPPER,
  LOCALIZED_MESSAGE_INTENT_TYPE_MAPPER,
  PLATFORM,
  MAX_ADS_GENERATION_TOKEN,
};
