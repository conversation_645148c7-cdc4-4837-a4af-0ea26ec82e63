const TemplatePromptModel = require('../../../../models/ai/aiTemplatePrompts.model');
const { TEMPLATE_SOURCE_TYPE } = require('../../../../constants/common');
const { ParamError } = require('../../../../utils/error.util');
const {
  MESSAGE_INTENT_TYPE,
  AI_TEMPLATE_PROMPT_TYPE,
} = require('../constants');
const { NAS_IO_FRONTEND_URL } = require('../../../../config');

exports.retrievePromptTemplate = async ({
  templateType,
  source = TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
}) => {
  const templatePrompt = await TemplatePromptModel.findOne({
    type: templateType,
    source,
    isActive: true,
  })
    .sort({ version: -1 })
    .lean();

  if (!templatePrompt) {
    throw new ParamError('Template prompt not found');
  }

  return templatePrompt;
};

exports.replaceVariablesInPrompt = ({ prompt, variables = {} }) => {
  const processedTemplatePrompt = prompt
    .replace(/\{([a-zA-Z_][a-zA-Z0-9_]*)\}/g, (_, key) => {
      const value = variables[key];
      return typeof value === 'string' || typeof value === 'number' // To prevent return undefined or null in the prompt
        ? String(value)
        : '';
    })
    .trim();

  return processedTemplatePrompt;
};

exports.getProcessedPromptAndModel = async ({
  templateType,
  source = TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
  variables = {},
}) => {
  const templatePrompt = await this.retrievePromptTemplate({
    templateType,
    source,
  });

  const processedTemplatePrompt = this.replaceVariablesInPrompt({
    prompt: templatePrompt.prompt,
    variables,
  });

  return {
    prompt: processedTemplatePrompt,
    aiModel: templatePrompt.aiModel,
  };
};

exports.retrievePromptViaMessageIntent = async ({
  messageIntent,
  variables = {},
}) => {
  switch (messageIntent) {
    case MESSAGE_INTENT_TYPE.HOW_TO_SELL.type:
      return this.getProcessedPromptAndModel({
        templateType: AI_TEMPLATE_PROMPT_TYPE.HOW_TO_SELL,
        variables: {
          ...variables,
          discountCodeLink: `${NAS_IO_FRONTEND_URL}/portal/promotions`,
        },
      });
    default:
      return this.getProcessedPromptAndModel({
        templateType: messageIntent,
        variables,
      });
  }
};
