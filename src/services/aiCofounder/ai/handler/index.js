const {
  EVENT_TYPE,
  LOCALIZED_FUNCTION_CALL_TYPE_MAPPER,
} = require('../constants');
const { handleTextEvent } = require('./handleTextEvent');
const { handleFunctionCallEvent } = require('./handleFunctionCall');
const { handleFollowUp } = require('./handleFollowup');
const postFunctionCallService = require('./handlePostFunctionCall');
const toolService = require('../tool');
const commonService = require('../common.service');
const logger = require('@/src/services/logger.service');

exports.handleStreamEvent = async ({
  stream,
  onData,
  onDone,
  onError,
  communityObjectId,
  chatObjectId,
  learnerObjectId,
  messageObjectId,
  community,
  messageIntent,
  toolParams,
  selectedTemplate,
  messageCountInfo,
  abortController,
}) => {
  let lastResponseId;
  let templateObjectId;
  let usage;
  let fullText = '';
  let hasSentMessageCount = false;
  let functionCallData;

  try {
    for await (const event of stream) {
      if (event.type === EVENT_TYPE.TEXT.event) {
        fullText += await handleTextEvent(event, onData);

        if (!hasSentMessageCount) {
          // eslint-disable-next-line no-param-reassign
          messageCountInfo.count += 1;
          onData({
            type: EVENT_TYPE.MESSAGE_COUNT.type,
            messageCountInfo,
          });
          hasSentMessageCount = true;
        }
      } else if (
        event.type === EVENT_TYPE.FILE_SEARCH_START.event &&
        event.item.type === EVENT_TYPE.FILE_SEARCH_START.itemType
      ) {
        onData({
          type: EVENT_TYPE.FILE_SEARCH_START.type,
        });
      } else if (
        event.type === EVENT_TYPE.FUNCTION_CALL_START.event &&
        event.item.type === EVENT_TYPE.FUNCTION_CALL_START.itemType
      ) {
        const tool = toolService.retrieveTool(event.item.name, toolParams);
        onData({
          type: EVENT_TYPE.FUNCTION_CALL_START.type,
          functionCallType: tool?.type,
          localizedKey: LOCALIZED_FUNCTION_CALL_TYPE_MAPPER[tool?.type],
        });
      } else if (event.type === EVENT_TYPE.RESPONSE_COMPLETED.event) {
        lastResponseId = event.response.id;
        usage = event.response.usage;

        const functionCallOutputs = event.response.output?.filter(
          (output) =>
            output.type === EVENT_TYPE.FUNCTION_CALL_START.itemType
        );

        if (functionCallOutputs?.length > 0) {
          const latestFunctionCallOutput =
            functionCallOutputs[functionCallOutputs.length - 1];

          functionCallData = await handleFunctionCallEvent({
            functionCallOutput: latestFunctionCallOutput,
            communityObjectId,
            chatObjectId,
            lastResponseId,
            toolParams,
          });

          templateObjectId =
            functionCallData.functionCallOutput.template._id;

          const postProcessEvents =
            await postFunctionCallService.handlePostFunctionCall({
              functionCallData,
              communityObjectId,
              messageIntent,
              messageObjectId,
              chatObjectId,
              onData,
            });

          await handleFollowUp({
            lastResponseId,
            learnerObjectId,
            messageObjectId,
            functionCallData,
            functionCallDataList: functionCallOutputs,
            postProcessEvents,
            messageIntent,
            messageCountInfo,
            functionCallUsage: usage,
            abortController,
            onData,
            onDone,
          });

          return;
        }
      }
    }

    const possibleActions = commonService.retrievePossibleActions({
      community,
      messageIntent,
      selectedTemplate,
      functionCallData,
    });

    await onDone({
      type: EVENT_TYPE.RESPONSE_COMPLETED.type,
      responseId: lastResponseId,
      fullText,
      usage,
      templateObjectId,
      messageIntent,
      possibleActions,
      messageObjectId,
      postProcessEvents: null,
    });
  } catch (error) {
    logger.error(`handleStreamEvent: ${error.message}, ${error.stack}`);
    onError({ type: 'error', text: error.message });
  }
};
