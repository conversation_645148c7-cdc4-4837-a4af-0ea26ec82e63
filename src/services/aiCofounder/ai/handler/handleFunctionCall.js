const tools = require('../tool');
const logger = require('../../../logger.service');

exports.handleFunctionCallEvent = async ({
  functionCallOutput,
  communityObjectId,
  chatObjectId,
  lastResponseId,
  toolParams,
}) => {
  if (!functionCallOutput) {
    return null;
  }

  const {
    name,
    arguments: argsRaw,
    status,
    call_id: callId,
  } = functionCallOutput;

  if (status !== 'completed') {
    logger.warn(`Function call ${name} not completed.`);
    return null;
  }

  const functionCallData = await tools.retrieveAndExecuteTool({
    name,
    args: argsRaw,
    callId,
    communityObjectId,
    chatObjectId,
    lastResponseId,
    toolParams,
  });

  return functionCallData;
};
