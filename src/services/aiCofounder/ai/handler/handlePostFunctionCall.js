const {
  EVENT_TYPE,
  MESSAGE_INTENT_TYPE,
  FUNCTION_CALL_TYPE,
  LOCALIZED_FUNCTION_CALL_TYPE_MAPPER,
} = require('../constants');
const commonService = require('../common.service');
const {
  AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES,
} = require('@/src/constants/common');
const adsService = require('../ads');
const sampleLeadsService = require('../leads/sampleLeads');
const leadsService = require('../leads');
const AIContextBuilder = require('../model/AIContextBuilder');

async function createAdsAndSampleLeadsTemplate({
  communityObjectId,
  chatObjectId,
  messageObjectId,
  functionCallData,
  languagePreference,
  onData,
}) {
  onData({
    type: EVENT_TYPE.FUNCTION_CALL_START.type,
    functionCallType: FUNCTION_CALL_TYPE.ADS_TEMPLATE,
    localizedKey:
      LOCALIZED_FUNCTION_CALL_TYPE_MAPPER[FUNCTION_CALL_TYPE.ADS_TEMPLATE],
  });

  const createdAdsTemplate = await adsService.createAds({
    data: {},
    communityObjectId,
    chatObjectId,
    callId: null,
    aiResponseId: null,
  });

  const [countryInfo] = await Promise.all([
    AIContextBuilder.retrieveCommunityOwnerCountryInfo({
      communityObjectId,
    }),
    commonService.generateTemplatesInBackground({
      communityObjectId,
      templateObjectId: functionCallData.functionCallOutput.template._id,
      messageObjectId,
      templates: [
        {
          type: AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.ADS,
          assignTemplateObjectId: createdAdsTemplate.template._id,
        },
      ],
    }),
  ]);

  onData({
    type: EVENT_TYPE.FUNCTION_CALL_START.type,
    functionCallType: FUNCTION_CALL_TYPE.LEADS_TEMPLATE,
    localizedKey:
      LOCALIZED_FUNCTION_CALL_TYPE_MAPPER[
        FUNCTION_CALL_TYPE.LEADS_TEMPLATE
      ],
  });

  const sampleLeads = sampleLeadsService.retrieveLeads({
    communityObjectId,
    chatObjectId,
    languagePreference,
    countryInfo,
  });

  return { createdAdsTemplate, sampleLeads };
}

async function createAdsAndLeadsTemplate({
  communityObjectId,
  chatObjectId,
  messageObjectId,
  functionCallData,
  onData,
}) {
  onData({
    type: EVENT_TYPE.FUNCTION_CALL_START.type,
    functionCallType: FUNCTION_CALL_TYPE.ADS_TEMPLATE,
    localizedKey:
      LOCALIZED_FUNCTION_CALL_TYPE_MAPPER[FUNCTION_CALL_TYPE.ADS_TEMPLATE],
  });

  const [createdAdsTemplate, createdLeadsTemplate] = await Promise.all([
    adsService.createAds({
      data: {},
      communityObjectId,
      chatObjectId,
      callId: null,
      aiResponseId: null,
    }),
    leadsService.createLeads({
      data: {},
      communityObjectId,
      chatObjectId,
      callId: null,
      aiResponseId: null,
    }),
  ]);

  onData({
    type: EVENT_TYPE.FUNCTION_CALL_START.type,
    functionCallType: FUNCTION_CALL_TYPE.LEADS_TEMPLATE,
    localizedKey:
      LOCALIZED_FUNCTION_CALL_TYPE_MAPPER[
        FUNCTION_CALL_TYPE.LEADS_TEMPLATE
      ],
  });

  await commonService.generateTemplatesInBackground({
    communityObjectId,
    templateObjectId: functionCallData.functionCallOutput.template._id,
    messageObjectId,
    templates: [
      {
        type: AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.ADS,
        assignTemplateObjectId: createdAdsTemplate.template._id,
      },
      {
        type: AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.LEADS,
        assignTemplateObjectId: createdLeadsTemplate.template._id,
      },
    ],
  });

  return { createdAdsTemplate, createdLeadsTemplate };
}

exports.handlePostFunctionCall = async ({
  functionCallData,
  communityObjectId,
  messageIntent,
  messageObjectId,
  chatObjectId,
  onData,
}) => {
  const postProcessEvents = [];

  if (messageIntent === MESSAGE_INTENT_TYPE.PRODUCT_TEMPLATE.type) {
    const { createdAdsTemplate, createdLeadsTemplate } =
      await createAdsAndLeadsTemplate({
        communityObjectId,
        chatObjectId,
        messageObjectId,
        functionCallData,
        onData,
      });

    postProcessEvents.push({
      type: EVENT_TYPE.FUNCTION_CALL_READY.type,
      metadata: createdAdsTemplate.template,
      functionCallType: FUNCTION_CALL_TYPE.ADS_TEMPLATE,
      templateObjectId: createdAdsTemplate.template._id,
      templateType: AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.ADS,
    });

    postProcessEvents.push({
      type: EVENT_TYPE.FUNCTION_CALL_READY.type,
      metadata: createdLeadsTemplate.template,
      functionCallType: FUNCTION_CALL_TYPE.LEADS_TEMPLATE,
      templateObjectId: createdLeadsTemplate.template._id,
      templateType: AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.LEADS,
    });
  }

  return postProcessEvents;
};

exports.handlePostFunctionCallForSampleLeads = async ({
  communityObjectId,
  chatObjectId,
  messageIntent,
  languagePreference,
  onData,
}) => {
  const postProcessEvents = [];

  if (messageIntent === MESSAGE_INTENT_TYPE.LEADS_GENERATION.type) {
    onData({
      type: EVENT_TYPE.FUNCTION_CALL_START.type,
      functionCallType: FUNCTION_CALL_TYPE.LEADS_TEMPLATE,
      localizedKey:
        LOCALIZED_FUNCTION_CALL_TYPE_MAPPER[
          FUNCTION_CALL_TYPE.LEADS_TEMPLATE
        ],
    });

    const countryInfo =
      await AIContextBuilder.retrieveCommunityOwnerCountryInfo({
        communityObjectId,
      });

    const sampleLeads = sampleLeadsService.retrieveLeads({
      communityObjectId,
      chatObjectId,
      languagePreference,
      countryInfo,
    });

    postProcessEvents.push({
      type: EVENT_TYPE.FUNCTION_CALL_READY.type,
      metadata: sampleLeads,
      functionCallType: FUNCTION_CALL_TYPE.LEADS_TEMPLATE,
      templateObjectId: sampleLeads._id,
      templateType: AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.LEADS,
    });
  }

  return postProcessEvents;
};
