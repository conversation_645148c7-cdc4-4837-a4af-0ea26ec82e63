const TemplateLibraryModel = require('../../../../models/getInspired/templateLibrary.model');
const {
  AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES,
  TEMPLATE_SOURCE_TYPE,
  AI_TEMPLATE_GENERATION_STATUS,
} = require('../../../../constants/common');
const aiImageGeneratorRpc = require('@/src/rpc/aiImageGenerator.rpc');
const { ParamError } = require('@/src/utils/error.util');
const FeaturePermissionManager = require('@/src/services/common/featurePermissionManager.service');
const { FEATURE_LIST_ID } = require('../../../../constants/common');

exports.retrieveAdsGenerationFeaturePermission = ({ community }) => {
  const featurePermissionManager = new FeaturePermissionManager(
    community.config.planType,
    community.featurePermissions
  );

  const adsGenerationFeaturePermission =
    featurePermissionManager.getFeature(
      FEATURE_LIST_ID.AI_COFOUNDER_ADS_GENERATION
    );

  return adsGenerationFeaturePermission;
};

exports.createAds = async ({
  data,
  communityObjectId,
  chatObjectId,
  callId,
  aiResponseId,
}) => {
  const { ads } = data;

  let version = 1;

  const latestTemplate = await TemplateLibraryModel.findOne(
    {
      sourceObjectId: chatObjectId,
      source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
      communityObjectId,
      type: AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.ADS,
    },
    { version: 1 }
  )
    .sort({ version: -1 })
    .lean();

  if (latestTemplate) {
    version = latestTemplate.version + 1;
  }

  // When generating ads with the product template, ads will be null at first and filled in asynchronously
  const adsWithStatus = ads?.map((ad) => {
    return {
      ...ad,
      status: ad.imageUrl
        ? AI_TEMPLATE_GENERATION_STATUS.COMPLETED
        : AI_TEMPLATE_GENERATION_STATUS.PROCESSING,
    };
  });

  const adsImagesToGenerate = adsWithStatus?.filter(
    (ad) => ad.status === AI_TEMPLATE_GENERATION_STATUS.PROCESSING
  );

  const template = {
    communityObjectId,
    type: AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.ADS,
    isAIGenerated: true,
    version,
    metadata: {
      ads: adsWithStatus,
      callId,
      aiResponseId,
    },
    source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
    sourceObjectId: chatObjectId,
    status:
      ads == null || adsImagesToGenerate?.length > 0
        ? AI_TEMPLATE_GENERATION_STATUS.PROCESSING
        : AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
  };

  const createdTemplate = await TemplateLibraryModel.create(template);

  if (adsImagesToGenerate?.length > 0) {
    await Promise.all(
      adsImagesToGenerate.map(async (_, index) => {
        await aiImageGeneratorRpc.sendEventToQueue({
          templateObjectId: createdTemplate._id,
          communityObjectId,
          chatObjectId,
          index,
        });
      })
    );
  }

  return {
    template: createdTemplate.toObject(),
    possibleActions: [],
  };
};

exports.updateAds = async ({
  data,
  communityObjectId,
  chatObjectId,
  callId,
  aiResponseId,
  assignTemplateObjectId,
}) => {
  const { ads } = data;

  const template = await TemplateLibraryModel.findOneAndUpdate(
    {
      _id: assignTemplateObjectId,
      communityObjectId,
      sourceObjectId: chatObjectId,
      source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
      type: AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.ADS,
    },
    {
      $set: {
        'metadata.ads': ads,
        'metadata.callId': callId,
        'metadata.aiResponseId': aiResponseId,
      },
    },
    { new: true }
  ).lean();

  if (!template) {
    throw new ParamError('Template not found');
  }

  await Promise.all(
    ads.map(async (ad, index) => {
      await aiImageGeneratorRpc.sendEventToQueue({
        templateObjectId: template._id,
        communityObjectId,
        chatObjectId,
        index,
      });
    })
  );

  return {
    template,
    possibleActions: [],
  };
};
