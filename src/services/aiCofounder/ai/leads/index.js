const TemplateLibraryModel = require('../../../../models/getInspired/templateLibrary.model');
const {
  AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES,
  TEMPLATE_SOURCE_TYPE,
  AI_TEMPLATE_GENERATION_STATUS,
} = require('../../../../constants/common');
const { ParamError } = require('@/src/utils/error.util');
const FeaturePermissionManager = require('@/src/services/common/featurePermissionManager.service');
const {
  FEATURE_LIST_ID,
  EMAIL_VERIFICATION_STATUS,
} = require('../../../../constants/common');
const magicLeadsService = require('@/src/services/magicLeads/leadsGeneration.service');
const icpProfileService = require('@/src/services/magicLeads/icpProfile.service');
const commonService = require('../../common.service');
const logger = require('@/src/services/logger.service');

exports.retrieveGenerationLeadsFeaturePermission = ({ community }) => {
  const featurePermissionManager = new FeaturePermissionManager(
    community.config.planType,
    community.featurePermissions
  );

  const generationLeadsFeaturePermission =
    featurePermissionManager.getFeature(
      FEATURE_LIST_ID.AI_COFOUNDER_GENERATION_LEADS_LIMIT
    );

  return generationLeadsFeaturePermission;
};

exports.retrieveDisplayLeadsFeaturePermission = ({ community }) => {
  const featurePermissionManager = new FeaturePermissionManager(
    community.config.planType,
    community.featurePermissions
  );

  const displayLeadsFeaturePermission =
    featurePermissionManager.getFeature(
      FEATURE_LIST_ID.AI_COFOUNDER_DISPLAY_LEADS_LIMIT
    );

  return displayLeadsFeaturePermission;
};

async function generateIcpWithLeads({ validatedICP, generateLeadsLimit }) {
  const englishIcpVectorSearchContext =
    validatedICP.localization?.en?.icpVectorSearchContext;

  const [embedding, englishEmbeddings] = await Promise.all([
    icpProfileService.generateEmbedding(
      validatedICP.icpVectorSearchContext
    ),
    englishIcpVectorSearchContext
      ? icpProfileService.generateEmbedding(englishIcpVectorSearchContext)
      : null,
  ]);

  const icpVectorSearchContext = {
    text: validatedICP.icpVectorSearchContext,
    embedding,
    generatedAt: new Date(),
    model: 'text-embedding-3-small',
    source: 'openai',
  };

  const englishIcpVectorSearchContextEmbedding = {
    text: englishIcpVectorSearchContext,
    embedding: englishEmbeddings,
    generatedAt: new Date(),
    model: 'text-embedding-3-small',
    source: 'openai',
  };

  const localization = {};

  if (englishIcpVectorSearchContext) {
    localization.en = {
      ...validatedICP.localization.en,
      icpVectorSearchContext: englishIcpVectorSearchContextEmbedding,
    };
  }

  const icp = {
    searchFields: validatedICP.searchFields,
    icpSummary: validatedICP.icpSummary,
    icpVectorSearchContext,
    targetLanguage: validatedICP.targetLanguage,
    localization,
  };

  let leads = [];

  try {
    leads = await magicLeadsService.findLeadsForIcp({
      icp,
      limit: generateLeadsLimit,
      needToCheckEmailExists: false,
    });
  } catch (err) {
    logger.error('Error generating leads for ICP:', {
      error: err.message,
      icp,
      generateLeadsLimit,
    });
  }

  const leadsInfo = leads.map((lead) => {
    const {
      _id: leadObjectId,
      fullName,
      jobTitle,
      company,
      location,
      socialProfiles,
      verification,
    } = lead;

    return {
      leadObjectId,
      fullName,
      jobTitle,
      companyName: company.name,
      countryName: location.countryName,
      cityName: location.city,
      stateName: location.state,
      socialProfiles: socialProfiles.map((socialProfile) => ({
        platform: socialProfile.platform,
        url: socialProfile.url,
      })),
      profileImage: socialProfiles[0].profileImageUrl,
      emailIsVerified:
        verification?.email?.status === EMAIL_VERIFICATION_STATUS.VALID,
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    };
  });

  return { leadsInfo, icp };
}

exports.createLeads = async ({
  data,
  communityObjectId,
  chatObjectId,
  callId,
  aiResponseId,
}) => {
  const {
    idealCustomerProfile,
    icpVectorSearchContext: icpVectorSearchContextText,
    jobTitles,
    countries,
    industries,
    targetLanguage,
    localization,
  } = data;

  const hasIcp = !!idealCustomerProfile;

  const leadsIcp = {
    idealCustomerProfile,
    icpVectorSearchContext: icpVectorSearchContextText,
    jobTitles,
    countries,
    industries,
    targetLanguage,
    localization,
  };

  let version = 1;

  const latestTemplate = await TemplateLibraryModel.findOne(
    {
      sourceObjectId: chatObjectId,
      source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
      communityObjectId,
      type: AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.LEADS,
    },
    { version: 1 }
  )
    .sort({ version: -1 })
    .lean();

  if (latestTemplate) {
    version = latestTemplate.version + 1;
  }

  const [validatedICP, community] = await Promise.all([
    hasIcp ? icpProfileService.validateICP(leadsIcp) : null,
    commonService.retrieveActiveCommunity(communityObjectId, {
      config: 1,
      featurePermissions: 1,
    }),
  ]);

  const generateLeadsLimit = this.retrieveGenerationLeadsFeaturePermission(
    {
      community,
    }
  ).limit;

  const { leadsInfo, icp } = hasIcp
    ? await generateIcpWithLeads({
        validatedICP,
        generateLeadsLimit,
      })
    : {};

  const template = {
    communityObjectId,
    type: AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.LEADS,
    isAIGenerated: true,
    version,
    metadata: {
      icp,
      leads: leadsInfo,
      callId,
      aiResponseId,
    },
    source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
    sourceObjectId: chatObjectId,
    status:
      validatedICP == null
        ? AI_TEMPLATE_GENERATION_STATUS.PROCESSING
        : AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
  };

  const createdTemplate = await TemplateLibraryModel.create(template);

  const transformedTemplate = createdTemplate.toObject();

  const displayLeadsLimit = this.retrieveDisplayLeadsFeaturePermission({
    community,
  }).limit;

  transformedTemplate.isSample = false;

  if (
    transformedTemplate.metadata?.icp?.icpVectorSearchContext?.embedding
  ) {
    delete transformedTemplate.metadata.icp.icpVectorSearchContext
      .embedding;
  }

  transformedTemplate.metadata.leads =
    transformedTemplate.metadata.leads?.slice(0, displayLeadsLimit);

  return {
    template: transformedTemplate,
    possibleActions: [],
  };
};

exports.updateLeads = async ({
  data,
  communityObjectId,
  chatObjectId,
  callId,
  aiResponseId,
  assignTemplateObjectId,
}) => {
  const {
    idealCustomerProfile,
    icpVectorSearchContext: icpVectorSearchContextText,
    jobTitles,
    countries,
    industries,
    targetLanguage,
    localization,
  } = data;

  const leadsIcp = {
    idealCustomerProfile,
    icpVectorSearchContext: icpVectorSearchContextText,
    jobTitles,
    countries,
    industries,
    targetLanguage,
    localization,
  };

  const [validatedICP, community] = await Promise.all([
    icpProfileService.validateICP(leadsIcp),
    commonService.retrieveActiveCommunity(communityObjectId, {
      config: 1,
      featurePermissions: 1,
    }),
  ]);

  const generateLeadsLimit = this.retrieveGenerationLeadsFeaturePermission(
    {
      community,
    }
  ).limit;

  const { leadsInfo, icp } = await generateIcpWithLeads({
    validatedICP,
    generateLeadsLimit,
  });

  const template = await TemplateLibraryModel.findOneAndUpdate(
    {
      _id: assignTemplateObjectId,
      communityObjectId,
      sourceObjectId: chatObjectId,
      source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
      type: AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.LEADS,
    },
    {
      $set: {
        'metadata.icp': icp,
        'metadata.leads': leadsInfo,
        'metadata.callId': callId,
        'metadata.aiResponseId': aiResponseId,
        status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
      },
    },
    { new: true }
  ).lean();

  if (!template) {
    throw new ParamError('Template not found');
  }

  return {
    template,
    possibleActions: [],
  };
};
