const {
  AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES,
  TEMPLATE_SOURCE_TYPE,
  AI_TEMPLATE_GENERATION_STATUS,
} = require('@/src/constants/common');

const sampleData = {
  en: [
    {
      name: '<PERSON>',
      profileImage: 'https://randomuser.me/api/portraits/women/1.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
    {
      name: '<PERSON>',
      profileImage: 'https://randomuser.me/api/portraits/men/1.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
    {
      name: '<PERSON>',
      profileImage: 'https://randomuser.me/api/portraits/men/2.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
    {
      name: '<PERSON>',
      profileImage: 'https://randomuser.me/api/portraits/women/2.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
    {
      name: '<PERSON>',
      profileImage: 'https://randomuser.me/api/portraits/men/3.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
    {
      name: 'Tina',
      profileImage: 'https://randomuser.me/api/portraits/women/3.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
  ],
  ja: [
    {
      name: 'そうた',
      profileImage: 'https://randomuser.me/api/portraits/men/10.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
    {
      name: 'みき',
      profileImage: 'https://randomuser.me/api/portraits/women/10.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
    {
      name: 'ゆい',
      profileImage: 'https://randomuser.me/api/portraits/women/11.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
    {
      name: 'たくみ',
      profileImage: 'https://randomuser.me/api/portraits/men/11.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
    {
      name: 'りん',
      profileImage: 'https://randomuser.me/api/portraits/women/12.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
    {
      name: 'けんと',
      profileImage: 'https://randomuser.me/api/portraits/men/12.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
  ],
  es_mx: [
    {
      name: 'Vale',
      profileImage: 'https://randomuser.me/api/portraits/women/13.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
    {
      name: 'Leo',
      profileImage: 'https://randomuser.me/api/portraits/men/13.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
    {
      name: 'Lupita',
      profileImage: 'https://randomuser.me/api/portraits/women/14.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
    {
      name: 'Nico',
      profileImage: 'https://randomuser.me/api/portraits/men/14.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
    {
      name: 'Dani',
      profileImage: 'https://randomuser.me/api/portraits/women/15.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
    {
      name: 'Isa',
      profileImage: 'https://randomuser.me/api/portraits/women/16.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
  ],
  pt_br: [
    {
      name: 'Eduarda',
      profileImage: 'https://randomuser.me/api/portraits/women/17.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
    {
      name: 'Rafael',
      profileImage: 'https://randomuser.me/api/portraits/men/17.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
    {
      name: 'Beatriz',
      profileImage: 'https://randomuser.me/api/portraits/women/18.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
    {
      name: 'Leonardo',
      profileImage: 'https://randomuser.me/api/portraits/men/18.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
    {
      name: 'Luana',
      profileImage: 'https://randomuser.me/api/portraits/women/19.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
    {
      name: 'Guilherme',
      profileImage: 'https://randomuser.me/api/portraits/men/19.jpg',
      status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
    },
  ],
};

function getFlagEmoji(countryCode) {
  if (!countryCode || countryCode.length !== 2) return '';

  return countryCode
    .toUpperCase()
    .split('')
    .map((char) => String.fromCodePoint(127397 + char.charCodeAt()))
    .join('');
}

exports.retrieveLeads = ({
  communityObjectId,
  chatObjectId,
  languagePreference,
  countryInfo,
}) => {
  const leads = sampleData[languagePreference] ?? sampleData.en;

  const leadsWithCountryInfo = leads.map((lead) => ({
    ...lead,
    countryCode: countryInfo.code,
    countryName: countryInfo.name,
    countryFlagSymbol: getFlagEmoji(countryInfo.code),
  }));

  const template = {
    communityObjectId,
    type: AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.LEADS,
    isAIGenerated: true,
    version: 1,
    metadata: {
      leads: leadsWithCountryInfo,
    },
    source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
    sourceObjectId: chatObjectId,
    status: AI_TEMPLATE_GENERATION_STATUS.COMPLETED,
  };

  return template;
};
