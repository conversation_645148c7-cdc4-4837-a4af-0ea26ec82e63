const {
  AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES,
  TEMPLATE_SOURCE_TYPE,
} = require('../../../../constants/common');
const commonService = require('./common.service');

exports.createTemplate = async ({
  data,
  communityObjectId,
  chatObjectId,
  callId,
  aiResponseId,
}) => {
  const {
    title,
    description,
    unsplashImageSearchTitle,
    interval,
    intervalCount,
    perks,
    priceType,
    localCurrency,
    suggestedAmount,
    predictedSales,
    possibleActions,
  } = data;

  const template = {
    communityObjectId,
    type: AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.MEMBERSHIP,
    isAIGenerated: true,
    thumbnailImgSrc: '',
    version: 1,
    predictedSales,
    localCurrency,
    pricingConfig: {
      priceType,
      suggestedAmount,
    },
    metadata: {
      title,
      description,
      unsplashImageSearchTitle,
      interval,
      intervalCount,
      perks,
      callId,
      aiResponseId,
    },
    source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
    sourceObjectId: chatObjectId,
  };

  const createdTemplate = await commonService.createTemplate({
    chatObjectId,
    communityObjectId,
    template,
  });

  return {
    template: createdTemplate,
    possibleActions,
  };
};
