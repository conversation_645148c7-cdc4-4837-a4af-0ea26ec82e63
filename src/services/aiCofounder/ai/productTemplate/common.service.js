const { ObjectId } = require('mongoose').Types;
const {
  TEMPLATE_SOURCE_TYPE,
  AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES,
  FEATURE_LIST_ID,
} = require('../../../../constants/common');
const TemplateLibraryModel = require('../../../../models/getInspired/templateLibrary.model');
const logger = require('@/src/services/logger.service');
const unsplashRpc = require('../../../../rpc/unsplash.rpc');
const { ParamError } = require('../../../../utils/error.util');
const FeaturePermissionManager = require('@/src/services/common/featurePermissionManager.service');

exports.retrieveTemplate = async ({
  communityObjectId,
  templateObjectId,
  readFromPrimary = false,
}) => {
  const queryOptions = readFromPrimary
    ? { readPreference: 'primary' }
    : {};

  const template = await TemplateLibraryModel.findOne(
    {
      _id: templateObjectId,
      communityObjectId,
      source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
    },
    null,
    queryOptions
  ).lean();

  if (!template) {
    throw new ParamError('Template not found');
  }

  return template;
};

exports.getTemplates = async ({
  communityObjectId,
  chatObjectId,
  templateObjectIds,
}) => {
  if (!templateObjectIds.length) {
    return [];
  }

  if (!templateObjectIds.every((id) => ObjectId.isValid(id))) {
    throw new ParamError('Invalid template object ids');
  }

  const templates = await TemplateLibraryModel.find({
    communityObjectId,
    sourceObjectId: chatObjectId,
    source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
    isAIGenerated: true,
    _id: { $in: templateObjectIds },
  }).lean();

  return templates;
};

exports.retrieveTemplateViaVersion = async ({
  communityObjectId,
  chatObjectId,
  version,
}) => {
  const template = await TemplateLibraryModel.findOne({
    sourceObjectId: chatObjectId,
    source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
    communityObjectId,
    version,
    type: {
      $in: [
        AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.EVENT,
        AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.FOLDER,
        AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.COURSE,
        AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.SESSION,
        AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.CHALLENGE,
        AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.MEMBERSHIP,
      ],
    },
  }).lean();

  if (!template) {
    throw new ParamError('Template not found');
  }

  return template;
};

exports.addNewTemplateVersion = async ({
  communityObjectId,
  chatObjectId,
  version,
  session = undefined,
}) => {
  const [template, latestTemplate] = await Promise.all([
    TemplateLibraryModel.findOne(
      {
        sourceObjectId: chatObjectId,
        source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
        communityObjectId,
        type: {
          $in: [
            AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.EVENT,
            AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.FOLDER,
            AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.COURSE,
            AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.SESSION,
            AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.CHALLENGE,
            AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.MEMBERSHIP,
          ],
        },
        version,
      },
      { createdAt: 0, updatedAt: 0 }
    ).lean(),
    TemplateLibraryModel.findOne(
      {
        sourceObjectId: chatObjectId,
        source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
        communityObjectId,
        type: {
          $in: [
            AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.EVENT,
            AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.FOLDER,
            AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.COURSE,
            AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.SESSION,
            AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.CHALLENGE,
            AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.MEMBERSHIP,
          ],
        },
      },
      { version: 1 }
    )
      .sort({ version: -1 })
      .lean(),
  ]);

  if (!template || !latestTemplate) {
    throw new ParamError('Template not found');
  }

  const { _id: templateObjectId, ...rest } = template;

  const newTemplate = {
    ...rest,
    version: latestTemplate.version + 1,
    copyFrom: {
      templateObjectId,
      version: template.version,
    },
  };

  const [createdTemplate] = await TemplateLibraryModel.create(
    [newTemplate],
    { session }
  );

  return {
    newTemplate: createdTemplate.toObject(),
    oldTemplate: template,
  };
};

exports.retrieveAiCofounderTemplatesCache = async ({
  communityObjectId,
  templateObjectIds,
  community,
}) => {
  if (!templateObjectIds.length) {
    return new Map();
  }

  const featurePermissionManager = new FeaturePermissionManager(
    community.config.planType,
    community.featurePermissions
  );

  const displayLeadsLimit = featurePermissionManager.getFeature(
    FEATURE_LIST_ID.AI_COFOUNDER_DISPLAY_LEADS_LIMIT
  ).limit;

  const templates = await TemplateLibraryModel.find({
    communityObjectId,
    source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
    isAIGenerated: true,
    _id: { $in: templateObjectIds },
  }).lean();

  const templatesCache = templates.reduce((acc, template) => {
    const transformedTemplate = {
      ...template,
    };

    if (
      transformedTemplate.type ===
      AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.LEADS
    ) {
      transformedTemplate.isSample = false;

      if (
        transformedTemplate.metadata?.icp?.icpVectorSearchContext
          ?.embedding
      ) {
        delete transformedTemplate.metadata.icp.icpVectorSearchContext
          .embedding;
      }

      transformedTemplate.metadata.leads =
        transformedTemplate.metadata.leads?.slice(0, displayLeadsLimit);
    }

    acc.set(template._id.toString(), transformedTemplate);
    return acc;
  }, new Map());

  return templatesCache;
};

exports.createTemplate = async ({
  chatObjectId,
  communityObjectId,
  template,
}) => {
  let version = 1;

  const newTemplate = { ...template };

  const unsplashImages = await unsplashRpc.retrieveUnsplashImages(
    newTemplate.metadata.unsplashImageSearchTitle,
    1
  );

  if (unsplashImages?.length) {
    newTemplate.thumbnailImgSrc = unsplashImages[0].url;
  }

  const latestTemplate = await TemplateLibraryModel.findOne(
    {
      sourceObjectId: chatObjectId,
      source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
      communityObjectId,
      type: {
        $in: [
          AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.EVENT,
          AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.FOLDER,
          AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.COURSE,
          AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.SESSION,
          AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.CHALLENGE,
          AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.MEMBERSHIP,
        ],
      },
    },
    { version: 1 }
  )
    .sort({ version: -1 })
    .lean();

  if (latestTemplate) {
    version = latestTemplate.version + 1;
  }

  const createdTemplate = await TemplateLibraryModel.create({
    ...newTemplate,
    version,
    sourceObjectId: chatObjectId,
    communityObjectId,
  });

  logger.info(
    `createTemplate: ${JSON.stringify(createdTemplate.toObject())}`
  );

  return createdTemplate.toObject();
};

exports.updateTemplate = async ({
  communityObjectId,
  templateObjectId,
  thumbnailImgSrc,
  metadata,
  pricingConfig,
}) => {
  const updatedTemplate = await TemplateLibraryModel.findOneAndUpdate(
    {
      _id: templateObjectId,
      communityObjectId,
      type: {
        $in: [
          AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.EVENT,
          AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.FOLDER,
          AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.COURSE,
          AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.SESSION,
          AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.CHALLENGE,
          AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.MEMBERSHIP,
        ],
      },
      source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
      isAIGenerated: true,
    },
    {
      $set: {
        thumbnailImgSrc,
        metadata,
        pricingConfig,
      },
    },
    { new: true }
  ).lean();

  return updatedTemplate;
};

exports.retrieveLatestProductTemplate = async ({
  communityObjectId,
  chatObjectId,
}) => {
  const latestTemplate = await TemplateLibraryModel.findOne({
    sourceObjectId: chatObjectId,
    source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
    communityObjectId,
    type: {
      $in: [
        AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.EVENT,
        AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.FOLDER,
        AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.COURSE,
        AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.SESSION,
        AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.CHALLENGE,
        AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.MEMBERSHIP,
      ],
    },
  })
    .sort({ version: -1 })
    .lean();

  return latestTemplate;
};

exports.retrieveLatestAdsTemplate = async ({
  communityObjectId,
  chatObjectId,
}) => {
  const latestTemplate = await TemplateLibraryModel.findOne({
    sourceObjectId: chatObjectId,
    source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
    communityObjectId,
    type: AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.ADS,
  })
    .sort({ version: -1 })
    .lean();

  return latestTemplate;
};

exports.retrieveLatestLeadsTemplate = async ({
  communityObjectId,
  chatObjectId,
}) => {
  const latestTemplate = await TemplateLibraryModel.findOne({
    sourceObjectId: chatObjectId,
    source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
    communityObjectId,
    type: AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.LEADS,
  })
    .sort({ version: -1 })
    .lean();

  return latestTemplate;
};

exports.linkTemplateToTemplate = async ({
  communityObjectId,
  templateObjectId,
  linkedTemplateObjectId,
}) => {
  const result = await TemplateLibraryModel.updateOne(
    {
      _id: templateObjectId,
      communityObjectId,
    },
    {
      $addToSet: {
        linkedTemplateObjectIds: linkedTemplateObjectId,
      },
    }
  );

  if (!result.modifiedCount) {
    throw new ParamError('Template not updated');
  }
};
