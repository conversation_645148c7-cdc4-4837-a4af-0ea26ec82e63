const { INPUT_ROLE_TYPE } = require('../constants');
const commonService = require('../../common.service');
const {
  COUNTRY_CREATED,
  latamCountriesArray,
} = require('../../../../communitiesAPI/constants');
const {
  DEFAULT_CURRENCY,
  EBANX_SUPPORTED_CURRENCY,
  TEMPLATE_SOURCE_TYPE,
} = require('../../../../constants/common');
const logger = require('../../../logger.service');

const MARKET = {
  US: 'US Market',
  LATAM: 'LATAM Market',
  INR: 'INR Market',
  ROW: 'Rest of the World',
};

const CACHE_TTL_SECONDS = 60 * 60 * 6; // 6 hours

class AIContextBuilder {
  constructor({ community, cacheClient = null }) {
    this.community = community;
    this.cacheClient = cacheClient;
  }

  static async retrieveCommunityOwnerCountryInfo({ communityObjectId }) {
    const communityOwnerMembership =
      await commonService.retrieveCommunityOwnerMembership(
        communityObjectId
      );

    let countryInfo = communityOwnerMembership.countryInfo;

    if (!countryInfo) {
      const community = await commonService.retrieveActiveCommunity(
        communityObjectId,
        { countryCreatedIn: 1 }
      );

      const countryMapping =
        await commonService.retrieveCountryCurrencyMapping(
          community.countryCreatedIn
        );

      countryInfo = {
        _id: countryMapping?.countryId ?? 184,
        code: countryMapping?.countryCode ?? 'US',
        name: countryMapping?.country ?? 'United States',
      };
    }

    return countryInfo;
  }

  _getMarket() {
    const { baseCurrency, countryCreatedIn } = this.community;

    const isLatamCountry = latamCountriesArray.includes(countryCreatedIn);
    const isEbanxCurrency = Object.values(
      EBANX_SUPPORTED_CURRENCY
    ).includes(baseCurrency);
    const isLatamSpecialCurrency = ['USD', 'EUR'].includes(baseCurrency);

    if (
      baseCurrency === DEFAULT_CURRENCY &&
      countryCreatedIn === COUNTRY_CREATED.UNITED_STATES
    ) {
      return MARKET.US;
    }

    if ((isLatamCountry && isLatamSpecialCurrency) || isEbanxCurrency) {
      return MARKET.LATAM;
    }

    if (baseCurrency === 'INR') {
      return MARKET.INR;
    }

    return MARKET.ROW;
  }

  _getCacheKey() {
    return `${TEMPLATE_SOURCE_TYPE.AI_COFOUNDER}:AI_CONTEXT:${this.community._id}`;
  }

  async buildContext() {
    const cacheKey = this._getCacheKey();

    if (this.cacheClient?.getAsync) {
      try {
        const cachedContext = await this.cacheClient.getAsync(cacheKey);

        if (cachedContext) {
          logger.debug(`[AIContextBuilder] Cache hit for ${cacheKey}`);
          const parsedContext = JSON.parse(cachedContext);
          this.communityInfo = parsedContext.communityInfo;
          this.learnerInfo = parsedContext.learnerInfo;
          this.learnerSocials = parsedContext.learnerSocials;
          this.topProductsInfo = parsedContext.topProductsInfo;
          return;
        }

        logger.debug(`[AIContextBuilder] Cache miss for ${cacheKey}`);
      } catch (err) {
        logger.warn(
          `[AIContextBuilder] Failed to parse cache for key: ${cacheKey} ${err.message}`
        );
        // fallback to rebuild context
      }
    }

    const communityOwnerMembership =
      await commonService.retrieveCommunityOwnerMembership(
        this.community._id
      );

    const [learner, learnerSocials, topCommunityProducts] =
      await Promise.all([
        commonService.retrieveLearner(
          communityOwnerMembership.learnerObjectId
        ),
        commonService.retrieveLearnerSocials(
          communityOwnerMembership.learnerObjectId
        ),
        commonService.retrieveTopCommunityProducts({
          communityObjectId: this.community._id,
        }),
      ]);

    const communityInfo = {
      title: this.community.title ?? '',
      description: this.community.description ?? '',
      intent: this.community.communityIntent ?? '',
      localCurrency: this.community.baseCurrency ?? '',
      hasSetupPaidSubscriptions: this.community.isPaidCommunity ?? false,
      subscriptionPrices: this.community.prices ?? [],
      country: this.community.countryCreatedIn ?? '',
      memberCountryDistribution: this.community.memberCountries ?? [],
      market: this._getMarket(),
    };

    const learnerInfo = {
      firstName: learner.firstName ?? '',
      lastName: learner.lastName ?? '',
      timezone: learner.timezone ?? '',
      socialMedia: learner.socialMedia ?? '',
      email: learner.email ?? '',
      description: learner.description ?? '',
      countryCode: communityOwnerMembership.countryInfo?.code ?? '',
    };

    const topProductsInfo = topCommunityProducts.map((product) => {
      return {
        title: product.title ?? '',
        productType: product.productType ?? '',
        sales: product.earningAnalytics?.quantity ?? 0,
        pricingConfig: product.pricingConfig ?? '',
        amount: product.amount ?? '',
        currency: product.currency ?? '',
      };
    });

    this.communityInfo = communityInfo;
    this.learnerInfo = learnerInfo;
    this.learnerSocials = learnerSocials;
    this.topProductsInfo = topProductsInfo;

    if (this.cacheClient?.setExAsync) {
      await this.cacheClient.setExAsync(
        cacheKey,
        CACHE_TTL_SECONDS,
        JSON.stringify({
          communityInfo,
          learnerInfo,
          learnerSocials,
          topProductsInfo,
        })
      );
    }
  }

  toSystemMessages() {
    const systemPrompts = [
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: `Here is the info about the creator’s community on Nas.io.\nCommunity info:\n${JSON.stringify(
          this.communityInfo
        )}`,
      },
    ];

    if (this.learnerInfo) {
      systemPrompts.push({
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: `Creator info:\n${JSON.stringify(this.learnerInfo)}`,
      });
    }

    if (this.learnerSocials) {
      systemPrompts.push({
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: `Creator socials info:\n${JSON.stringify(
          this.learnerSocials
        )}`,
      });
    }

    if (this.topProductsInfo) {
      systemPrompts.push({
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: `The top sales products this creator has on Nas.io:\n${JSON.stringify(
          this.topProductsInfo
        )}`,
      });
    }

    systemPrompts.push({
      role: INPUT_ROLE_TYPE.SYSTEM,
      content: `# How to Use This Information
      This is the available context we have for this creator.
      For any future messages, you should refer to this information when crafting your replies.
      - If the creator is new to Nas.io, many fields above may be empty or incomplete. In that case, rely on the instructions and prompts provided in the user message instead of making assumptions.
      Do not guess or fabricate missing data. Always adapt your answers based on how much context is available.`,
    });

    return systemPrompts;
  }
}

module.exports = AIContextBuilder;
