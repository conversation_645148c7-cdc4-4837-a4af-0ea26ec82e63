class FunctionCall {
  constructor(type, schema, handlerFunction) {
    this.type = type;
    this.schema = schema;
    this.handlerFunction = handlerFunction;
  }

  async execute(
    args,
    communityObjectId,
    chatObjectId,
    callId,
    lastResponseId,
    assignTemplateObjectId
  ) {
    return this.handlerFunction(
      args,
      communityObjectId,
      chatObjectId,
      callId,
      lastResponseId,
      assignTemplateObjectId
    );
  }
}

module.exports = FunctionCall;
