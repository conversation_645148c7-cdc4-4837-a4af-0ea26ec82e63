const { DateTime } = require('luxon');
const { ObjectId } = require('mongoose').Types;

const ChatMessageModel = require('../../models/chat/chatMessage.model');
const chatMessageService = require('../chat/message.service');
const chatCommonService = require('../chat/common.service');
const chatService = require('../chat/chat.service');
const aiCofounderChatService = require('./chat.service');
const templateCommonService = require('./ai/productTemplate/common.service');
const commonService = require('./common.service');
const aiCommonService = require('./ai/common.service');
const sampleLeadsService = require('./ai/leads/sampleLeads');
const FeaturePermissionManager = require('../common/featurePermissionManager.service');
const AIContextBuilder = require('./ai/model/AIContextBuilder');
const { FEATURE_LIST_ID } = require('../../constants/common');
const {
  hgetAsync,
  hincrbyAsync,
  expireAt,
  existsAsync,
  hsetAsync,
  hdelAsync,
} = require('../../redisClient');
const { getLock } = require('../../redisLock');
const logger = require('../logger.service');

const PrimaryMongooseConnection = require('../../rpc/primaryMongooseConnection');

const {
  MESSAGE_TYPE,
  CHAT_SOURCE_TYPE,
  INTENT_TYPE,
} = require('../chat/constants');
const { ParamError } = require('../../utils/error.util');

const BUFFER_MESSAGE_PERCENTAGE = 5;

const REDIS_CACHE_FIELD = {
  COUNT_IS_FROM_DB: 'countIsFromDb',
  COUNT: 'count',
};

const REDIS_CACHE_COUNT_IS_FROM_DB_VALUE = '1';

exports.validateMessageCountWithinLimit = ({ messageCountInfo }) => {
  if (messageCountInfo.count >= messageCountInfo.limit) {
    throw new ParamError('Message count limit exceeded');
  }
};

exports.addMessage = async ({
  community,
  chat,
  message,
  attachments = [],
  learnerObjectId,
  intentType = INTENT_TYPE.MESSAGE,
  isAiGenerated = false,
  isRestoredVersion = false,
  actionType = null,
  metadata = null,
  session: databaseSession = undefined,
}) => {
  const isManagerCache = new Map();

  if (!isAiGenerated) {
    isManagerCache.set(learnerObjectId.toString(), true);
  }

  const entitiesCache = new Map();

  const messageData = {
    message,
    messageType: MESSAGE_TYPE.TEXT,
    attachments,
    senderLearnerObjectId: learnerObjectId,
    isWelcomeMessage: false,
    source: CHAT_SOURCE_TYPE.AI_COFOUNDER,
    intentType,
    actionType,
  };

  const processedMessage = chatMessageService.processMessage({
    chat,
    message: messageData,
    community,
    isManagerCache,
    entitiesCache,
    isAiGenerated,
    isRestoredVersion,
    metadata,
  });

  let session;

  if (!databaseSession) {
    const primaryMongooseConnection =
      await PrimaryMongooseConnection.connect();

    session = await primaryMongooseConnection.startSession();
    session.startTransaction();
  } else {
    session = databaseSession;
  }

  try {
    const [chatMessage] = await ChatMessageModel.create(
      [processedMessage],
      {
        session,
      }
    );

    await aiCofounderChatService.updateChatLastSendAndReadMessage({
      chatMessage,
      session,
    });

    if (!databaseSession) {
      await session.commitTransaction();
    }

    return chatMessage.toObject();
  } catch (error) {
    logger.error(
      `addMessage: error: ${error.message}, stack: ${error.stack}`
    );
    if (!databaseSession) {
      await session.abortTransaction();
    }
    throw error;
  } finally {
    if (!databaseSession) {
      await session.endSession();
    }
  }
};

exports.retrieveMessages = async ({
  chatObjectId,
  communityObjectId,
  batchSize = 100,
  previousObjectId,
  languagePreference,
}) => {
  chatCommonService.validateBatchSize(batchSize);

  const [messageCountInfo, chat, countryInfo, community] =
    await Promise.all([
      this.retrieveMessageCountInfo({
        communityObjectId,
      }),
      chatService.retrieveChat({
        communityObjectId,
        chatObjectId,
      }),
      AIContextBuilder.retrieveCommunityOwnerCountryInfo({
        communityObjectId,
      }),
      commonService.retrieveActiveCommunity(communityObjectId, {
        config: 1,
        featurePermissions: 1,
      }),
    ]);

  const matchFilter = {
    chatObjectId,
    communityObjectId,
  };

  if (previousObjectId) {
    matchFilter._id = {
      $lt: previousObjectId,
    };
  }

  const chatMessages = await ChatMessageModel.find(matchFilter)
    .sort({ _id: -1 })
    .limit(batchSize)
    .lean();

  chatMessages.reverse();

  const templateObjectIds = [];

  chatMessages.forEach((message) => {
    if (message.metadata?.templateObjectId) {
      templateObjectIds.push(message.metadata.templateObjectId);
    }

    if (message.metadata?.additionalTemplates) {
      templateObjectIds.push(
        ...message.metadata.additionalTemplates
          .filter((template) => template.objectId)
          .map((template) => template.objectId)
      );
    }
  });

  const templatesCache =
    await templateCommonService.retrieveAiCofounderTemplatesCache({
      communityObjectId,
      templateObjectIds,
      community,
    });

  const chatMessagesWithAdditionalInfo = chatMessages.map((message) => {
    const newMessageWithAdditionalInfo = { ...message };

    if (message.metadata?.templateObjectId) {
      newMessageWithAdditionalInfo.template = templatesCache.get(
        message.metadata.templateObjectId.toString()
      );
    }

    if (message.metadata?.additionalTemplates) {
      newMessageWithAdditionalInfo.additionalTemplates =
        message.metadata.additionalTemplates.map((additionalTemplate) => {
          const isSample = !additionalTemplate.objectId;
          let template;

          if (isSample) {
            template = sampleLeadsService.retrieveLeads({
              communityObjectId,
              chatObjectId,
              languagePreference,
              countryInfo,
            });
          } else {
            template = templatesCache.get(
              additionalTemplate.objectId.toString()
            );
          }

          return {
            ...template,
            isSample,
          };
        });
    }

    if (message.metadata?.possibleActions) {
      newMessageWithAdditionalInfo.metadata.possibleActions =
        aiCommonService.addLocalizedKey(message.metadata.possibleActions);
    }

    return newMessageWithAdditionalInfo;
  });

  return {
    chat,
    messages: chatMessagesWithAdditionalInfo,
    metadata: {
      previousObjectId: chatMessagesWithAdditionalInfo[0]?._id,
      messageCountInfo,
    },
  };
};

exports.retrieveMessageViaTemplate = async ({
  templateObjectId,
  communityObjectId,
  chatObjectId,
}) => {
  const template = await ChatMessageModel.findOne({
    communityObjectId,
    chatObjectId,
    'metadata.templateObjectId': templateObjectId,
  }).lean();

  if (!template) {
    throw new ParamError('Template not found');
  }

  return template;
};

function calculateNearLimitThreshold(limit) {
  const threshold = Math.ceil((limit * BUFFER_MESSAGE_PERCENTAGE) / 100);
  return limit - Math.max(1, threshold);
}

function retrieveMessageCountKey({
  communityObjectId,
  currentDateTime = DateTime.utc(),
}) {
  const dateString = currentDateTime.toISODate();
  return `${CHAT_SOURCE_TYPE.AI_COFOUNDER}:${communityObjectId}:${dateString}:messageCount`;
}

async function retrieveChatMessageCountFromDb({
  communityObjectId,
  currentDateTime,
  interval,
}) {
  return ChatMessageModel.countDocuments({
    communityObjectId,
    source: CHAT_SOURCE_TYPE.AI_COFOUNDER,
    isAiGenerated: true,
    createdAt: {
      $gte: currentDateTime.startOf(interval).toJSDate(),
      $lte: currentDateTime.endOf(interval).toJSDate(),
    },
  });
}

async function retrieveMessageFeaturePermission({
  communityObjectId,
  community = null,
}) {
  let communityInfo = community;
  if (!communityInfo) {
    communityInfo = await commonService.retrieveActiveCommunity(
      communityObjectId,
      {
        config: 1,
        featurePermissions: 1,
      }
    );
  }

  const planType = communityInfo?.config?.planType;
  const featurePermissions = communityInfo?.featurePermissions || [];

  const featurePermissionManager = new FeaturePermissionManager(
    planType,
    featurePermissions
  );

  const feature = featurePermissionManager.getFeature(
    FEATURE_LIST_ID.AI_COFOUNDER_MESSAGE
  );

  return feature;
}

/**
 * Get ai cofounder message usage
 * @param {{DateTime}} now
 * @param {{ObjectId}} communityObjectId
 * @param {{int}} limit // Needed to calculate Near Limit Threshold
 * @returns
 */
async function retrieveMessageCount({
  now,
  communityObjectId,
  interval,
  limit,
}) {
  const chatMessageCountKey = retrieveMessageCountKey({
    communityObjectId,
    currentDateTime: now,
  });

  const nearLimitThreshold = calculateNearLimitThreshold(limit);
  let chatMessageCountFromDb;

  try {
    const [countIsFromDb, count] = await Promise.all([
      hgetAsync(chatMessageCountKey, REDIS_CACHE_FIELD.COUNT_IS_FROM_DB),
      hgetAsync(chatMessageCountKey, REDIS_CACHE_FIELD.COUNT),
    ]);

    // If the count was previously verified against the DB, return it as trusted
    // It only happens when cache count is mismatched with db count and there is no increment
    if (countIsFromDb === REDIS_CACHE_COUNT_IS_FROM_DB_VALUE) {
      return Number(count);
    }

    const chatMessageCountFromCache = count ? Number(count) : null;

    const shouldCheckDb =
      chatMessageCountFromCache == null ||
      chatMessageCountFromCache >= nearLimitThreshold;

    // If cache is valid and under threshold, return it
    if (!shouldCheckDb) {
      return chatMessageCountFromCache;
    }

    // Fallback: query actual count from MongoDB
    chatMessageCountFromDb = await retrieveChatMessageCountFromDb({
      communityObjectId,
      currentDateTime: now,
      interval,
    });

    const cacheCountMismatch =
      chatMessageCountFromDb !== chatMessageCountFromCache;

    if (cacheCountMismatch) {
      logger.info(
        `retrieveMessageCount: cache mismatch — Redis: ${chatMessageCountFromCache}, DB: ${chatMessageCountFromDb}`
      );
    }

    // If Redis value was missing or outdated, update Redis with the DB value
    if (chatMessageCountFromCache == null || cacheCountMismatch) {
      // Acquire a Redis-based lock to avoid race conditions on the increment
      const lock = await getLock();
      const releaseLock = await lock(chatMessageCountKey, 5000); // 5 sec

      try {
        // Write the verified DB value back to Redis and mark it as from DB
        await hsetAsync(chatMessageCountKey, {
          [REDIS_CACHE_FIELD.COUNT]: chatMessageCountFromDb,
          [REDIS_CACHE_FIELD.COUNT_IS_FROM_DB]:
            REDIS_CACHE_COUNT_IS_FROM_DB_VALUE,
        });

        // Set the Redis key to expire at the end of the day
        const expireAtTimestamp = Math.floor(
          now.endOf(interval).toSeconds()
        );
        await expireAt(chatMessageCountKey, expireAtTimestamp);
      } catch (err) {
        logger.warn(`retrieveMessageCount: redis error: ${err.message}`);
        throw err;
      } finally {
        await releaseLock();
      }
    }

    return chatMessageCountFromDb;
  } catch (err) {
    logger.error(`retrieveMessageCount: error: ${err.message}`);

    // Fallback: return DB value if available, otherwise re-query it
    const messageCount =
      chatMessageCountFromDb ??
      (await retrieveChatMessageCountFromDb({
        communityObjectId,
        currentDateTime: now,
        interval,
      }));
    return messageCount;
  }
}

/**
 * Get ai cofounder message usage
 * @param {{ObjectId}} communityObjectId
 * @param {{int}} limit // Needed to calculate Near Limit Threshold
 * @returns
 */
exports.retrieveMessageUsage = async ({
  communityObjectId,
  interval,
  limit,
}) => {
  const count = await retrieveMessageCount({
    now: DateTime.utc(),
    communityObjectId,
    interval,
    limit,
  });
  return count;
};

// Retrieves the message count for the current day, using Redis for performance
// and falling back to MongoDB if the cache is missing or near quota limits.
exports.retrieveMessageCountInfo = async ({
  communityObjectId,
  community = null,
}) => {
  const messageFeaturePermission = await retrieveMessageFeaturePermission({
    communityObjectId,
    community,
  });

  const now = DateTime.utc();
  const endOfInterval = now.endOf(messageFeaturePermission.interval);
  const resetDateTime = endOfInterval.plus({ seconds: 1 }).toJSDate();

  const limit = messageFeaturePermission.limit;

  const messageCountInfo = {
    limit,
    count: 0,
    resetDateTime,
  };

  messageCountInfo.count = await retrieveMessageCount({
    now,
    communityObjectId,
    interval: messageFeaturePermission.interval,
    limit,
  });

  return messageCountInfo;
};

// Increments the message count for a community in Redis.
// Uses a lock to prevent race conditions on first-time writes.
exports.incrementMessageCount = async ({ community, count = 1 }) => {
  const now = DateTime.utc();

  const chatMessageCountKey = retrieveMessageCountKey({
    communityObjectId: community._id,
    currentDateTime: now,
  });

  const messageFeaturePermission = await retrieveMessageFeaturePermission({
    communityObjectId: community._id,
    community,
  });

  const limit = messageFeaturePermission.limit;

  // Acquire a Redis-based lock to avoid race conditions on the increment
  const lock = await getLock();
  let releaseLock;

  try {
    releaseLock = await lock(chatMessageCountKey, 5000); // 5 sec
    const existsKey = await existsAsync(chatMessageCountKey);

    // Increment the cached count and clear the verification flag (since it's no longer from DB)
    const [incrementedCount] = await Promise.all([
      hincrbyAsync(chatMessageCountKey, REDIS_CACHE_FIELD.COUNT, count),
      hdelAsync(chatMessageCountKey, REDIS_CACHE_FIELD.COUNT_IS_FROM_DB),
    ]);

    const endOfInterval = now.endOf(messageFeaturePermission.interval);
    const resetDateTime = endOfInterval.plus({ seconds: 1 });

    // If this is the first write for the day, set the expiration to end-of-day
    if (!existsKey) {
      const endOfIntervalTimestamp = Math.floor(endOfInterval.toSeconds());
      await expireAt(chatMessageCountKey, endOfIntervalTimestamp);
    }

    return {
      limit,
      count: Number(incrementedCount),
      resetDateTime: resetDateTime.toJSDate(),
    };
  } catch (err) {
    logger.warn(`incrementMessageCount: redis error: ${err.message}`);
  } finally {
    if (releaseLock) {
      await releaseLock();
    }
  }
};

exports.updateMessage = async ({
  communityObjectId,
  messageObjectId,
  templateObjectId = null,
  additionalMatchFilter = {},
  updateQuery = {},
  pushQuery = {},
}) => {
  const matchFilter = {
    communityObjectId,
    _id: messageObjectId,
    ...additionalMatchFilter,
  };

  if (templateObjectId) {
    matchFilter['metadata.templateObjectId'] = new ObjectId(
      templateObjectId
    );
  }

  const message = await ChatMessageModel.findOneAndUpdate(
    matchFilter,
    {
      $set: updateQuery,
      $push: pushQuery,
    },
    {
      new: true,
    }
  ).lean();

  if (!message) {
    throw new ParamError('Message not found');
  }

  return message;
};
