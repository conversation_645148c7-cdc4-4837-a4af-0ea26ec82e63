const CommunityModel = require('../../communitiesAPI/models/community.model');
const LearnerModel = require('../../models/learners.model');
const LearnerSocialsModel = require('../../models/learnerSocials.model');
const MembershipModel = require('../../models/membership/membership.model');
const CommunityProductModel = require('../../models/product/communityProduct.model');
const CountryCurrencyMappingModel = require('@/src/models/countryInfoMapping.model');
const { ParamError } = require('../../utils/error.util');
const { aclRoles } = require('../../communitiesAPI/constants');

exports.retrieveActiveCommunity = async (
  communityObjectId,
  projection = {}
) => {
  const community = await CommunityModel.findOne(
    {
      _id: communityObjectId,
      isActive: true,
    },
    projection
  ).lean();

  if (!community) {
    throw new ParamError('Community not found');
  }

  return community;
};

exports.retrieveLearner = async (learnerObjectId) => {
  const learner = await LearnerModel.findOne({
    _id: learnerObjectId,
  }).lean();

  if (!learner) {
    throw new ParamError('Learner not found');
  }

  return learner;
};

exports.retrieveLearnerSocials = async (learnerObjectId) => {
  const learnerSocials = await LearnerSocialsModel.findOne({
    learnerObjectId,
  }).lean();

  return learnerSocials;
};

exports.retrieveCommunityOwnerMembership = async (communityObjectId) => {
  const communityOwnerMembership = await MembershipModel.findOne(
    {
      communityObjectId,
      communityRole: aclRoles.OWNER,
    },
    {
      _id: 0,
      email: 1,
      name: 1,
      countryInfo: 1,
      learnerObjectId: 1,
    }
  ).lean();

  if (!communityOwnerMembership) {
    throw new ParamError('Community owner not found');
  }

  return communityOwnerMembership;
};

exports.retrieveTopCommunityProducts = async ({
  communityObjectId,
  limit = 5,
}) => {
  const communityProducts = await CommunityProductModel.find(
    {
      communityObjectId,
    },
    {
      _id: 0,
      title: 1,
      productType: 1,
      config: 1,
      earningAnalytics: 1,
      pricingConfig: 1,
      amount: 1,
      currency: 1,
    }
  )
    .sort({ 'earningAnalytics.revenueInUsd': -1 })
    .limit(limit)
    .lean();

  return communityProducts;
};

exports.retrieveCountryCurrencyMapping = async (countryName) => {
  const countryCurrencyMapping = await CountryCurrencyMappingModel.findOne(
    {
      country: countryName,
    }
  ).lean();

  return countryCurrencyMapping;
};
