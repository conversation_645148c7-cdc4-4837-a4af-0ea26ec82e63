exports.CATEGORY_TYPES = {
  MEMBERSHIP: 'trigger-category-membership',
  EVENT: 'trigger-category-event',
  CONTENT: 'trigger-category-content',
  WHATSAPP: 'trigger-category-whatsapp',
  CHALLENGE: 'trigger-category-challenge',
  ABANDONED_CARTS: 'trigger-category-abandoned-carts',
  AFFILIATE: 'trigger-category-affiliate',
};

exports.scheduledNotificationStatuses = {
  SENT_TO_QUEUE: 'SentToQueue',
  NO_RECIPIENTS: 'NoRecipients',
  FINISHED: 'Finished',
  FAILED: 'Failed',
  PENDING: 'Pending',
};

exports.TRIGGER_TYPES = {
  MEMBER_PORTAL: 'MEMBER_PORTAL',
  MANAGER_PORTAL: 'MANAGER_PORTAL',
};

exports.SOURCE_TYPE = {
  LEAD: 'lead',
};
