const bcrypt = require('bcrypt');
const learnersModel = require('../../../models/learners.model');
const emailUnsubscribeModel = require('../../../communitiesAPI/models/emailUnsubscribe.model');
const communityModel = require('../../../communitiesAPI/models/community.model');
const mailConfigModel = require('../../../models/notificationBackend/mailConfig.model');
const membershipModel = require('../../../models/membership/membership.model');
const EnrichedLeadModel = require('../../../models/magicLeads/enrichedLeads.model');
const {
  DEFAULT_MAIL_COURSE_OR_MAIL_COURSE_OFFER,
  MAIL_RECORD_TYPE,
  NO_MAIL_TYPES,
} = require('../../mail/constants');
const logger = require('../../logger.service');
const { ParamError } = require('../../../utils/error.util');
const { CATEGORY } = require('../../../controllers/sendgrid/constants');
const { CATEGORY_TYPES, SOURCE_TYPE } = require('../constants');
const {
  stopSendingAbandonedCartReminder,
} = require('../../communitySignup/signup/processTransaction/abandonedCarts.service');
const {
  flushLearnerCache,
} = require('../../redisCache/redisCache.service');

const validateToken = async ({
  token,
  email,
  mailType,
  communityCode,
}) => {
  // Construct the original string based on email and communityCode
  const originalString = `email: ${email}, communityCode: ${communityCode}, mailType: ${mailType}`;

  // Compare the original string with the token (hash)
  const isMatch = await bcrypt.compare(originalString, token);
  if (!isMatch) {
    throw new ParamError('Invalid token');
  }
};

exports.unsubscribe = async (params = {}) => {
  const {
    token,
    email,
    mailType,
    communityCode,

    // These fields are toggle for entity level notification
    entityType,
    entityObjectId,
    entityName,
    membershipId,
    sourceObjectId,
    source,
  } = params;

  await validateToken({ token, email, mailType, communityCode });

  logger.info(
    `Opting out the email ${email} for communityCode ${communityCode} and type ${mailType}`
  );
  const community = await communityModel.findOne({
    code: communityCode,
  });

  if (!community) {
    throw new ParamError('Community does not exists');
  }

  const mailConfig = await mailConfigModel
    .findOne({
      mailType,
      mailCourse: DEFAULT_MAIL_COURSE_OR_MAIL_COURSE_OFFER,
      mailCourseOffer: DEFAULT_MAIL_COURSE_OR_MAIL_COURSE_OFFER,
      recordType: MAIL_RECORD_TYPE.GENERAL,
    })
    .select(' triggerName triggerDescription triggerCategory triggerType')
    .lean();

  if (!mailConfig) {
    throw new ParamError('MailType does not exists');
  }
  delete mailConfig._id;

  const [emailUnsubscribe, learner] = await Promise.all([
    emailUnsubscribeModel.findOne({ email, communityCode }).lean(),
    learnersModel
      .findOne({ email, isActive: true })
      .select('_id notificationsPreferences')
      .lean(),
  ]);

  if (mailType === NO_MAIL_TYPES.MAGIC_REACH) {
    await membershipModel.findOneAndUpdate(
      { email, communityCode },
      { 'reachInfo.optOutFromEmail': true }
    );
  }

  if (source === SOURCE_TYPE.LEAD && sourceObjectId) {
    await EnrichedLeadModel.updateOne(
      { _id: sourceObjectId },
      {
        $addToSet: {
          'reachInfo.optOutFromEmail.communityCodes': communityCode,
        },
      }
    );
    return mailConfig;
  }

  if (!learner) {
    emailUnsubscribeModel.findOneAndUpdate(
      { email, communityObjectId: community._id, communityCode },
      { source: mailType },
      { upsert: true }
    );
    return mailConfig;
  }

  const triggerNameMailConfigs = await mailConfigModel.find({
    triggerName: mailConfig.triggerName,
    mailCourse: DEFAULT_MAIL_COURSE_OR_MAIL_COURSE_OFFER,
    mailCourseOffer: DEFAULT_MAIL_COURSE_OR_MAIL_COURSE_OFFER,
    recordType: MAIL_RECORD_TYPE.GENERAL,
  });

  const updateData = {};
  const notificationsPreferences = learner.notificationsPreferences || {};

  logger.info(
    `Unsubscribing ${email} from all mailTypes with same triggerName sent by community ${communityCode}|triggerName=${mailConfig.triggerName}|sourceMailType=${mailType}`
  );

  triggerNameMailConfigs.forEach((config) => {
    const { mailType: type } = config;

    // Init the preference setting for this mail type
    notificationsPreferences[type] = {
      optOutForCommunities:
        learner.notificationsPreferences?.[type]?.optOutForCommunities ||
        [],
      optInForAll:
        learner.notificationsPreferences?.[type]?.optInForAll || true,
      optInForCommunities:
        learner.notificationsPreferences?.[type]?.optInForCommunities ||
        [],
      optOutForAll:
        learner.notificationsPreferences?.[type]?.optOutForAll || false,
      optOutForEntities:
        learner.notificationsPreferences?.[type]?.optOutForEntities || [],
    };
    if (entityObjectId) {
      // Convert the { entityType, entityObjectId } objects into a unique string key
      const optOutForEntitiesSet = new Set(
        notificationsPreferences[type].optOutForEntities.map(
          ({
            entityType: localEntityType,
            entityObjectId: localEntityObjectId,
          }) => `${localEntityType}-${localEntityObjectId}`
        )
      );

      // Add the new entity by converting it to the same string format
      optOutForEntitiesSet.add(`${entityType}-${entityObjectId}`);

      // Convert the Set back into the array of objects
      notificationsPreferences[type].optOutForEntities = Array.from(
        optOutForEntitiesSet,
        (str) => {
          const [localEntityType, localEntityObjectId] = str.split('-');
          return {
            entityType: localEntityType,
            entityObjectId: localEntityObjectId,
          };
        }
      );
    } else {
      const optOutForCommunitiesSet = new Set(
        notificationsPreferences[type].optOutForCommunities
      );
      optOutForCommunitiesSet.add(communityCode);
      notificationsPreferences[type].optOutForCommunities = [
        ...optOutForCommunitiesSet,
      ];
    }

    updateData[`notificationsPreferences.${type}`] =
      notificationsPreferences[type];
  });

  logger.info(updateData);

  // Update the learner document using findOneAndUpdate
  await learnersModel.findByIdAndUpdate(
    learner._id,
    {
      $set: updateData,
    },
    {
      new: true, // Return the updated document
    }
  );

  await flushLearnerCache(learner._id);

  if (mailConfig.triggerCategory === CATEGORY_TYPES.ABANDONED_CARTS) {
    // Change trigger name for FE to display in unsubscribe page
    mailConfig.triggerName = entityName;

    await stopSendingAbandonedCartReminder(
      learner._id,
      entityType,
      entityObjectId,
      membershipId
    );
  }
  return mailConfig;
};

exports.resubscribe = async (params = {}) => {
  const { token, email, mailType, communityCode, sourceObjectId, source } =
    params;
  await validateToken({ token, email, mailType, communityCode });

  logger.info(
    `Resubscribe the email ${email} for communityCode ${communityCode} and type ${mailType}`
  );
  const community = await communityModel.findOne({
    code: communityCode,
  });

  if (!community) {
    throw new ParamError('Community does not exists');
  }

  const mailConfig = await mailConfigModel
    .findOne({
      mailType,
      mailCourse: DEFAULT_MAIL_COURSE_OR_MAIL_COURSE_OFFER,
      mailCourseOffer: DEFAULT_MAIL_COURSE_OR_MAIL_COURSE_OFFER,
      recordType: MAIL_RECORD_TYPE.GENERAL,
    })
    .select(' triggerName triggerDescription triggerCategory triggerType')
    .lean();

  if (!mailConfig) {
    throw new ParamError('MailType does not exists');
  }
  delete mailConfig._id;

  const [emailUnsubscribe, learner] = await Promise.all([
    emailUnsubscribeModel.findOne({ email, communityCode }).lean(),
    learnersModel
      .findOne({ email, isActive: true })
      .select('_id notificationsPreferences')
      .lean(),
  ]);

  if (mailType === NO_MAIL_TYPES.MAGIC_REACH) {
    if (emailUnsubscribe) {
      await emailUnsubscribeModel.deleteOne({ email, communityCode });
    }
    await membershipModel.findOneAndUpdate(
      { email, communityCode },
      { 'reachInfo.optOutFromEmail': false }
    );
  }

  if (source === SOURCE_TYPE.LEAD && sourceObjectId) {
    await EnrichedLeadModel.updateOne(
      { _id: sourceObjectId },
      {
        $pull: {
          'reachInfo.optOutFromEmail.communityCodes': communityCode,
        },
      }
    );
    return mailConfig;
  }

  if (!learner) {
    return mailConfig;
  }

  const triggerNameMailConfigs = await mailConfigModel.find({
    triggerName: mailConfig.triggerName,
    mailCourse: DEFAULT_MAIL_COURSE_OR_MAIL_COURSE_OFFER,
    mailCourseOffer: DEFAULT_MAIL_COURSE_OR_MAIL_COURSE_OFFER,
    recordType: MAIL_RECORD_TYPE.GENERAL,
  });

  const updateData = {};
  const notificationsPreferences = learner.notificationsPreferences || {};

  logger.info(
    `Resubscribing ${email} from all mailTypes with same triggerKey sent by community ${communityCode}|triggerName=${mailConfig.triggerName}|sourceMailType=${mailType}`
  );
  triggerNameMailConfigs.forEach((config) => {
    const { mailType: type } = config;
    const optOutForCommunitiesSet = new Set(
      learner.notificationsPreferences?.[type]?.optOutForCommunities || []
    );
    optOutForCommunitiesSet.delete(communityCode);
    notificationsPreferences[type] = {
      optOutForCommunities: [...optOutForCommunitiesSet],
      optInForAll:
        learner.notificationsPreferences?.[type]?.optInForAll || true,
      optInForCommunities:
        learner.notificationsPreferences?.[type]?.optOutForCommunities ||
        [],
      optOutForAll:
        learner.notificationsPreferences?.[type]?.optOutForAll || false,
    };

    if (
      !notificationsPreferences[type].optInForAll &&
      notificationsPreferences[type].optOutForAll
    ) {
      const optInForCommunitiesSet = new Set(
        learner.notificationsPreferences?.[type]?.optInForCommunities || []
      );
      optInForCommunitiesSet.add(communityCode);
      notificationsPreferences[type]['optInForCommunities'] = [
        ...optInForCommunitiesSet,
      ];
    }

    updateData[`notificationsPreferences.${type}`] =
      notificationsPreferences[type];
  });

  // Update the learner document using findOneAndUpdate
  await learnersModel.findByIdAndUpdate(
    learner._id,
    {
      $set: updateData,
    },
    {
      new: true, // Return the updated document
    }
  );

  await flushLearnerCache(learner._id);

  return mailConfig;
};
