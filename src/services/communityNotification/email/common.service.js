const httpContext = require('express-http-context');
const { PAYMENT_MAILER_URL } = require('../../../config');
const { aclRoles } = require('../../../communitiesAPI/constants');
const MongodbUtils = require('../../../utils/mongodb.util');
const NameUtils = require('../../../utils/name.util');
const CommunityRoleModel = require('../../../communitiesAPI/models/communityRole.model');
const LearnerModel = require('../../../models/learners.model');
const { sendMessageToSQSQueue } = require('../../../handlers/sqs.handler');
const {
  DEFAULT_COMMUNITY_PROFILE_IMAGE,
} = require('../../../constants/common');
const logger = require('../../logger.service');

exports.retrieveCommunityOwnerCacheInfo = async (communityCodes) => {
  const matchFilter = {
    communityCode: { $in: communityCodes },
    role: aclRoles.OWNER,
  };

  const query = [
    {
      $match: matchFilter,
    },
    ...MongodbUtils.lookupAndUnwind(
      'users',
      'userObjectId',
      '_id',
      'user'
    ),
    ...MongodbUtils.lookupAndUnwind(
      'learners',
      'user.learner',
      '_id',
      'learner'
    ),
    {
      $project: {
        communityCode: 1,
        profileImage: '$learner.profileImage',
        firstName: '$learner.firstName',
        lastName: '$learner.lastName',
        email: 1,
        timezone: '$learner.timezone',
        languagePreference: '$learner.languagePreference',
        _id: 0,
      },
    },
  ];

  const owners = await CommunityRoleModel.aggregate(query);
  const ownerCache = new Map();
  owners.forEach((owner) => {
    const value = owner;
    value.name = `${owner.firstName ?? ''} ${owner.lastName ?? ''}`.trim();
    ownerCache.set(owner.communityCode, value);
  });

  return ownerCache;
};

exports.retrieveCommunityOwnerInfo = async (communityCode) => {
  const query = [
    {
      $match: {
        communityCode,
        role: aclRoles.OWNER,
      },
    },
    ...MongodbUtils.lookupAndUnwind(
      'users',
      'userObjectId',
      '_id',
      'user'
    ),
    ...MongodbUtils.lookupAndUnwind(
      'learners',
      'user.learner',
      '_id',
      'learner'
    ),
    {
      $project: {
        profileImage: '$learner.profileImage',
        firstName: '$learner.firstName',
        lastName: '$learner.lastName',
        email: 1,
        timezone: '$learner.timezone',
        languagePreference: '$learner.languagePreference',
        _id: 0,
      },
    },
  ];

  const communityOwnerLearner = (
    await CommunityRoleModel.aggregate(query)
  )[0];
  communityOwnerLearner.name = `${communityOwnerLearner.firstName ?? ''} ${
    communityOwnerLearner.lastName ?? ''
  }`.trim();
  return communityOwnerLearner;
};

exports.retrieveCommunityManagerInfo = async (communityCode, mailType) => {
  logger.info(
    `retrieveCommunityManagerInfo: ${communityCode} ${mailType}`
  );

  const managers = await CommunityRoleModel.find(
    {
      communityCode,
      role: aclRoles.ADMIN,
    },
    { email: 1, preferences: 1 }
  ).lean();

  logger.info(
    `retrieveCommunityManagerInfo: managers: ${JSON.stringify(managers)}`
  );

  const managersEmail = managers
    .filter(({ preferences }) => {
      if (!preferences?.emailPreference) {
        return true;
      }

      const userEmailPreference = preferences.emailPreference.find(
        (emailPreference) => emailPreference.mailType === mailType
      );

      return userEmailPreference?.isEnabled ?? true;
    })
    .map(({ email }) => email);

  logger.info(
    `retrieveCommunityManagerInfo: managersEmail: ${JSON.stringify(
      managersEmail
    )}`
  );

  const learners = await LearnerModel.find(
    {
      email: { $in: managersEmail },
    },
    { email: 1, firstName: 1, lastName: 1, learnerId: 1 }
  ).lean();

  logger.info(
    `retrieveCommunityManagerInfo: learners: ${JSON.stringify(learners)}`
  );

  const learnersCache = learners.reduce(
    (acc, learnerInfo) => acc.set(learnerInfo.email, learnerInfo),
    new Map()
  );
  const emailArr = [];
  const nameArr = [];
  const learnerIds = [];
  managersEmail.forEach((managerEmail) => {
    const learnerInfo = learnersCache.get(managerEmail);
    const { firstName, lastName, email, learnerId: id } = learnerInfo;
    const name = NameUtils.getName(firstName, lastName, email);
    emailArr.push(managerEmail);
    nameArr.push(name);
    learnerIds.push(id);
  });

  return {
    emails: emailArr,
    names: nameArr,
    learnerIds,
  };
};

exports.retrieveWhatsappLink = (community) => {
  const { platforms } = community;

  if (!platforms || platforms.length === 0) {
    return;
  }

  const whatsappPlatform = platforms.find(
    (platform) => platform.name === 'whatsapp'
  );

  return whatsappPlatform?.link;
};

exports.retrieveCommunityProfileImage = (community) => {
  const communityProfileImage =
    community.thumbnailImgData?.mobileImgData?.src ??
    DEFAULT_COMMUNITY_PROFILE_IMAGE;

  return communityProfileImage;
};

exports.sendMailToQueue = async (
  mailType,
  mailCourse,
  mailCourseOffer,
  toMail,
  toMailName,
  data,
  mailSubject,
  managerEmailConfig,
  config
) => {
  const requestId = httpContext.get('reqId');

  const requestData = {
    mailType,
    mailCourse,
    mailCourseOffer,
    toMail,
    toMailName,
    data,
    requesterServiceName: 'LPBE',
    mailSubject,
    managerEmailConfig,
    config,
  };

  logger.info(
    `sendMailToQueue: requestData: ${JSON.stringify(requestData)}`
  );

  const message = {
    data: requestData,
    requestor: 'LPBE',
    requestId,
  };

  try {
    const result = await sendMessageToSQSQueue({
      queueUrl: PAYMENT_MAILER_URL,
      messageBody: message,
    });

    logger.info(`sendMailToQueue: result: ${JSON.stringify(result)}`);
  } catch (err) {
    logger.error(`sendMailToQueue: error: ${err.response?.data ?? err}`);
  }
};
