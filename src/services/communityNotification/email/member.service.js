const { DateTime } = require('luxon');

const {
  DEFAULT_COMMUNITY_HOST_PROFILE_IMAGE,
  PURCHASE_TYPE,
} = require('../../../constants/common');
const {
  REROUTE_MEMBER_LINK,
  NAS_IO_FRONTEND_URL,
} = require('../../../config');
const CommonService = require('./common.service');
const NameUtils = require('../../../utils/name.util');
const MailUtils = require('../../../utils/mail.util');
const AuthServiceRpc = require('../../../rpc/authService.rpc');
const receiptService = require('../../receipts/receipts.service');

const logger = require('../../logger.service');
const {
  ENROLMENT_MAIL_TYPES,
  PLAN_MAIL_TYPE,
} = require('../../mail/constants');
const { ENTITY_TYPE } = require('../../plan/constants');

const checkIfCommRequireApplication = (community) => {
  const { applicationConfigDataFields = [] } = community;

  const requireApplication = applicationConfigDataFields.some(
    (applicationConfigDataField) => !applicationConfigDataField.isDeleted
  );

  return requireApplication;
};

function retrieveEnrollmentMailType(community, options = {}) {
  const {
    isWhatsappExperienceCommunity,
    request_approval: requestApproval,
  } = community;
  const requireApplication = checkIfCommRequireApplication(community);

  const bypassApproval = options.bypassPendingApproval;

  if (requestApproval && !bypassApproval) {
    return ENROLMENT_MAIL_TYPES.MEMBER_COMMUNITY_ENROLMENT_APPLICATION;
  }

  if (requireApplication) {
    if (isWhatsappExperienceCommunity)
      return ENROLMENT_MAIL_TYPES.MEMBER_COMMUNITY_ENROLMENT_APPLICATION_APPROVED_FOR_WHATSAPP;
    return ENROLMENT_MAIL_TYPES.MEMBER_COMMUNITY_ENROLMENT_APPLICATION_APPROVED;
  }

  // without application communities
  if (isWhatsappExperienceCommunity)
    return ENROLMENT_MAIL_TYPES.MEMBER_COMMUNITY_ENROLMENT_WITHOUT_APPLICATION_FOR_WHATSAPP;

  return ENROLMENT_MAIL_TYPES.MEMBER_COMMUNITY_ENROLMENT_WITHOUT_APPLICATION;
}

exports.sendEnrollmentMail = async ({
  communityOwnerInfo,
  community,
  learner,
  purchaseTransaction,
  options,
}) => {
  const name = NameUtils.getName(
    learner.firstName,
    learner.lastName,
    learner.email
  );

  const {
    code: communityCode,
    _id: communityObjectId,
    countryCreatedIn,
    request_approval: requestApproval,
  } = community;

  const communityOwnerEmail = communityOwnerInfo.email;
  const communityOwnerName = NameUtils.getName(
    communityOwnerInfo.firstName,
    communityOwnerInfo.lastName,
    communityOwnerInfo.email
  );

  const managerEmailConfig = MailUtils.retrieveManagerMailConfig(
    community.title,
    community.link,
    communityOwnerEmail,
    communityOwnerName
  );

  logger.info(
    `sendEnrollmentMail: manager email config: ${JSON.stringify(
      managerEmailConfig
    )}`
  );

  const communityProfileImage =
    CommonService.retrieveCommunityProfileImage(community);

  const hostProfileImage =
    communityOwnerInfo.profileImage ??
    DEFAULT_COMMUNITY_HOST_PROFILE_IMAGE;

  const whatsappLink = `${REROUTE_MEMBER_LINK}?&activeCommunityId=${communityObjectId}&memberExperience=1&tab=chat&withAutoJoin=1`;
  const communityLink = `${REROUTE_MEMBER_LINK}?&activeCommunityId=${communityObjectId}&memberExperience=1`;

  const authServiceRpc = new AuthServiceRpc();
  const { token } = await authServiceRpc.generateEmailToken(learner.email);

  const emailData = {
    name: learner.firstName ?? '',
    community_name: community.title,
    community_profile_image: communityProfileImage,
    community_country: countryCreatedIn,
    community_link: `${communityLink}&accessToken=${token}`,
    whatsapp_link: `${whatsappLink}&accessToken=${token}`,
    community_host: communityOwnerName,
    host_profile_image: hostProfileImage,
    community_code: communityCode,
    student_header_name: `Welcome ${learner.firstName ?? ''}`,
    student_name: learner.firstName ?? '',
  };

  logger.info(
    `sendEnrollmentMail: email data: ${JSON.stringify(emailData)}`
  );

  const generateReceipt =
    !requestApproval &&
    purchaseTransaction &&
    purchaseTransaction.amount > 0;

  const mailType = retrieveEnrollmentMailType(community, options);

  const config = receiptService.generateReceiptConfig({
    purchasedId: purchaseTransaction?._id,
    purchaseType: PURCHASE_TYPE.SUBSCRIPTION,
    entityObjectId: community._id,
    communityObjectId: community._id,
    learnerObjectId: learner._id,
    generateReceipt,
  });

  await CommonService.sendMailToQueue(
    mailType,
    communityCode,
    'All',
    [learner.email],
    [name],
    emailData,
    null,
    managerEmailConfig,
    config
  );
};

function retrieveNumberOfMonthsViaIntervalAndIntervalCount(
  interval,
  intervalCount
) {
  if (interval === 'year') {
    return intervalCount * 12;
  }

  return intervalCount;
}

function retrieveRenewalFailureMailType(attemptCount) {
  if (attemptCount == null) {
    return;
  }

  let mailType;

  switch (attemptCount) {
    case 1:
      mailType = 'MEMBER_COMMUNITY_RETRY_SUBSCRIPTION_PAYMENT_FAILED_1ST';
      break;
    case 2:
      mailType = 'MEMBER_COMMUNITY_RETRY_SUBSCRIPTION_PAYMENT_FAILED_2ND';
      break;
    case 3:
      mailType = 'MEMBER_COMMUNITY_RETRY_SUBSCRIPTION_PAYMENT_FAILED_3RD';
      break;
    case 4:
      mailType = 'MEMBER_COMMUNITY_RETRY_SUBSCRIPTION_PAYMENT_FAILED_4TH';
      break;
    default:
      break;
  }

  return mailType;
}

function retrieveRenewalFailurePlanMailType(attemptCount, planOrder) {
  if (attemptCount == null) {
    return;
  }

  const { entityType } = planOrder;
  let mailType;
  switch (attemptCount) {
    case 1:
      mailType =
        PLAN_MAIL_TYPE[`NAS_IO_${entityType}_RENEWAL_FAILURE_MAIL_1ST`];
      break;
    case 2:
      mailType =
        PLAN_MAIL_TYPE[`NAS_IO_${entityType}_RENEWAL_FAILURE_MAIL_2ND`];
      break;
    case 3:
      mailType =
        PLAN_MAIL_TYPE[`NAS_IO_${entityType}_RENEWAL_FAILURE_MAIL_3RD`];
      break;
    case 4:
      mailType =
        PLAN_MAIL_TYPE[`NAS_IO_${entityType}_RENEWAL_FAILURE_MAIL_4TH`];
      break;
    default:
      break;
  }

  return mailType;
}

exports.renewalFailureMail = async ({
  communityOwnerInfo,
  community,
  learner,
  subscription,
  paymentMetadata,
}) => {
  const name = NameUtils.getName(
    learner.firstName,
    learner.lastName,
    learner.email
  );

  const { code: communityCode } = community;

  const communityOwnerEmail = communityOwnerInfo.email;
  const communityOwnerName = NameUtils.getName(
    communityOwnerInfo.firstName,
    communityOwnerInfo.lastName,
    communityOwnerInfo.email
  );

  const managerEmailConfig = MailUtils.retrieveManagerMailConfig(
    community.title,
    community.link,
    communityOwnerEmail,
    communityOwnerName
  );

  logger.info(
    `renewalFailureMail: manager email config: ${JSON.stringify(
      managerEmailConfig
    )}`
  );

  const communityProfileImage =
    CommonService.retrieveCommunityProfileImage(community);

  const hostProfileImage =
    communityOwnerInfo.profileImage ??
    DEFAULT_COMMUNITY_HOST_PROFILE_IMAGE;

  const { nextBillingDate, interval, intervalCount } = subscription;

  const currentDateTime = DateTime.utc();

  let newSubscriptionStartDate = currentDateTime.toFormat('dd LLL yyyy');
  let newSubscriptionEndDate = currentDateTime.toFormat('dd LLL yyyy');

  if (nextBillingDate) {
    const numberOfMonths =
      retrieveNumberOfMonthsViaIntervalAndIntervalCount(
        interval,
        intervalCount
      );
    newSubscriptionStartDate =
      DateTime.fromJSDate(nextBillingDate).toFormat('dd LLL yyyy');
    newSubscriptionEndDate = DateTime.fromJSDate(nextBillingDate)
      .plus({
        months: numberOfMonths,
      })
      .toFormat('dd LLL yyyy');
  }

  const {
    cardLastDigits = '',
    checkoutLink,
    memberPaymentCard = '',
    attemptCount,
    failureReason = '',
    memberPaidAmount,
    memberPaidCurrency,
    paymentProvider,
    paymentMethod = '',
    paymentBrand = '',
  } = paymentMetadata;

  const emailData = {
    community_code: community.code,
    community_name: community.title,
    community_profile_image: communityProfileImage,
    host_name: community.By,
    host_profile_image: hostProfileImage,
    host_email: community.createdBy,
    name: learner.firstName ?? '',
    member_email: learner.email,
    date: currentDateTime.toFormat('dd MMM yyyy'),
    subscription_expiry_date: newSubscriptionStartDate,
    subscription_new_start_date: newSubscriptionStartDate,
    subscription_new_end_date: newSubscriptionEndDate,
    subscription_new_start_date_ics: currentDateTime.toFormat('YYYYMMDD'),
    renew_membership_link: checkoutLink,
    member_paid_amount: memberPaidAmount,
    member_paid_currency: memberPaidCurrency,
    member_payment_provider: paymentProvider,
    member_payment_method: paymentMethod,
    member_payment_brand: paymentBrand,
    member_payment_card_last4: cardLastDigits,
    member_payment_card: memberPaymentCard,
    payment_failure_reason: failureReason,
  };

  logger.info(
    `renewalFailureMail: email data: ${JSON.stringify(emailData)}`
  );

  const mailType = retrieveRenewalFailureMailType(attemptCount);

  if (!mailType) {
    return;
  }

  await CommonService.sendMailToQueue(
    mailType,
    communityCode,
    'All',
    [learner.email],
    [name],
    emailData,
    null,
    managerEmailConfig
  );
};

exports.cancelSubscriptionMail = async ({
  communityOwnerInfo,
  community,
  learner,
  subscription,
}) => {
  const name = NameUtils.getName(
    learner.firstName,
    learner.lastName,
    learner.email
  );

  const { code: communityCode, title } = community;

  const communityOwnerEmail = communityOwnerInfo.email;
  const communityOwnerName = NameUtils.getName(
    communityOwnerInfo.firstName,
    communityOwnerInfo.lastName,
    communityOwnerInfo.email
  );

  const managerEmailConfig = MailUtils.retrieveManagerMailConfig(
    community.title,
    community.link,
    communityOwnerEmail,
    communityOwnerName
  );

  logger.info(
    `cancelSubscriptionMail: manager email config: ${JSON.stringify(
      managerEmailConfig
    )}`
  );

  const communityProfileImage =
    CommonService.retrieveCommunityProfileImage(community);

  const { unsubscribedAt, cancelledAt, cancellationReason } = subscription;

  const unsubscribedAtFormatDate =
    DateTime.fromJSDate(unsubscribedAt).toFormat('dd LLL yyyy');
  const cancelledAtFormatDate =
    DateTime.fromJSDate(cancelledAt).toFormat('dd LLL yyyy');

  const emailData = {
    community_name: title,
    unsubscribed_date: unsubscribedAtFormatDate,
    next_billing_date: cancelledAtFormatDate,
    cancellation_reason: cancellationReason,
    community_profile_pic: communityProfileImage,
    student_name: name,
  };

  logger.info(
    `cancelSubscriptionMail: email data: ${JSON.stringify(emailData)}`
  );

  const mailType =
    'COMMUNITY_MEMBER_UNSUBSCRIBE_ALERT_FOR_MEMBER_WITH_BILLING_DATE';

  await CommonService.sendMailToQueue(
    mailType,
    communityCode,
    'All',
    [learner.email],
    [name],
    emailData,
    null,
    managerEmailConfig
  );
};

exports.purchasePlanMail = async ({ community, learner }) => {
  const name = NameUtils.getName(learner.firstName, learner.lastName);

  const { code: communityCode, title } = community;

  const emailData = {
    community_name: title,
    first_name: learner.firstName,
    community_link: `${REROUTE_MEMBER_LINK}?activeCommunityId=${community._id}`,
  };

  logger.info(
    `purchasePlanMail: email data: ${JSON.stringify(emailData)}`
  );

  let mailType;
  const { planType } = community.config;
  switch (planType) {
    case ENTITY_TYPE.PLATINUM:
      mailType = PLAN_MAIL_TYPE.NAS_IO_PLATINUM_WELCOME_MAIL;
      break;
    case ENTITY_TYPE.PRO:
      mailType = PLAN_MAIL_TYPE.NAS_IO_PRO_WELCOME_MAIL;
      break;
    default:
  }

  await CommonService.sendMailToQueue(
    mailType,
    communityCode,
    'All',
    [learner.email],
    [name],
    emailData,
    null,
    null
  );
};

exports.cancelledPlanMail = async ({
  entityType,
  community,
  learner,
  subscriptionExpiryDate,
}) => {
  const name = NameUtils.getName(learner.firstName, learner.lastName);

  const { code: communityCode, title } = community;

  const emailData = {
    community_name: title,
    first_name: learner.firstName,
    community_link: `${REROUTE_MEMBER_LINK}?activeCommunityId=${community._id}`,
    subscription_expiry_date: subscriptionExpiryDate,
  };

  logger.info(
    `cancelledPlanMail: email data: ${JSON.stringify(emailData)}`
  );

  let mailType = PLAN_MAIL_TYPE.NAS_IO_PRO_CANCEL_MAIL;
  if (entityType === ENTITY_TYPE.PLATINUM) {
    mailType = PLAN_MAIL_TYPE.NAS_IO_PLATINUM_CANCEL_MAIL;
  }

  await CommonService.sendMailToQueue(
    mailType,
    communityCode,
    'All',
    [learner.email],
    [name],
    emailData,
    null,
    null
  );
};

exports.renewalFailurePlanMail = async ({
  planOrder,
  community,
  learner,
  paymentMetadata,
}) => {
  const name = NameUtils.getName(learner.firstName, learner.lastName);

  const nasIoProTabLink = `${NAS_IO_FRONTEND_URL}/portal/settings?activeCommunityId=${community._id}&tab=NAS_IO_PRO`;

  const {
    checkoutLink = '',
    memberPaymentCard = '',
    attemptCount,
    failureReason = '',
    paymentMethod = '',
  } = paymentMetadata;

  const communityProfileImage =
    CommonService.retrieveCommunityProfileImage(community);

  const emailData = {
    community_code: community.code,
    community_name: community.title,
    community_profile_image: communityProfileImage,
    name,
    renew_membership_link: checkoutLink || nasIoProTabLink,
    member_payment_method: paymentMethod,
    member_payment_card: memberPaymentCard,
    payment_failure_reason: failureReason,
  };

  logger.info(
    `renewalFailurePlanMail: email data: ${JSON.stringify(emailData)}`
  );

  const mailType = retrieveRenewalFailurePlanMailType(
    attemptCount,
    planOrder
  );

  if (!mailType) {
    return;
  }

  await CommonService.sendMailToQueue(
    mailType,
    community.code,
    'All',
    [learner.email],
    [name],
    emailData,
    null,
    null
  );
};

exports.planReferralRewardMail = async ({
  community,
  refereeCommunity,
  learner,
  rewardAmount,
  currency,
  recurringRewardAmount,
  planType,
}) => {
  const name = NameUtils.getName(learner.firstName, learner.lastName);

  const { code: communityCode } = community;
  const { title: refereeCommunityTitle } = refereeCommunity;

  const emailData = {
    referee_community_name: refereeCommunityTitle,
    name,
    referrer_upfront_reward_amount: rewardAmount / 100,
    referrer_upfront_reward_currency: currency,
    referrer_recurring_reward_amount: recurringRewardAmount / 100,
    referrer_recurring_reward_currency: currency,
  };

  logger.info(
    `planReferralRewardMail: email data: ${JSON.stringify(emailData)}`
  );

  let mailType =
    PLAN_MAIL_TYPE.TO_REFERRER_FOR_SUCCESSFUL_PAYMENT_VIA_COMMUNITY_REFERRAL_LINK;

  if (planType === ENTITY_TYPE.PLATINUM) {
    mailType =
      PLAN_MAIL_TYPE.TO_REFERRER_FOR_SUCCESSFUL_PAYMENT_VIA_COMMUNITY_REFERRAL_LINK_PLATINUM;
  }

  await CommonService.sendMailToQueue(
    mailType,
    communityCode,
    'All',
    [learner.email],
    [name],
    emailData,
    null,
    null
  );
};

exports.revokeCancellationPlanMail = async ({
  entityType,
  community,
  learner,
  reason,
}) => {
  const name = NameUtils.getName(learner.firstName, learner.lastName);
  const { code: communityCode, title } = community;

  const emailData = {
    community_name: title,
    first_name: learner.firstName,
    community_link: `${REROUTE_MEMBER_LINK}?activeCommunityId=${community._id}`,
    revocation_reason: reason || 'No reason provided',
    revocation_date: DateTime.utc().toFormat('dd LLL yyyy'),
  };

  logger.info('revokeCancellationPlanMail: email data:', emailData);

  let mailType = PLAN_MAIL_TYPE.NAS_IO_PRO_REVOKE_CANCELLATION_MAIL;
  if (entityType === ENTITY_TYPE.PLATINUM) {
    mailType = PLAN_MAIL_TYPE.NAS_IO_PLATINUM_REVOKE_CANCELLATION_MAIL;
  }

  await CommonService.sendMailToQueue(
    mailType,
    communityCode,
    'All',
    [learner.email],
    [name],
    emailData,
    null,
    null
  );
};
