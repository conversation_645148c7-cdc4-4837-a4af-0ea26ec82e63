const CommonService = require('./common.service');
const MemberService = require('./member.service');
const ManagerService = require('./manager.service');
const { affiliateMailService } = require('../../affiliate');
const {
  countService: MembershipCountService,
} = require('../../membership');
const logger = require('../../logger.service');
const CommunityModel = require('../../../communitiesAPI/models/community.model');
const LearnerModel = require('../../../models/learners.model');
const CommunitySubscriptionsModel = require('../../../communitiesAPI/models/communitySubscriptions.model');
const klaviyoClient = require('../../../klaviyoClient');
const emailUtils = require('../../../utils/email.util');
const {
  EMAIL_PROVIDER,
  CONFIG_TYPES,
} = require('../../../constants/common');
const { getConfigByType } = require('../../config.service');

exports.enrollmentMail = async ({
  community,
  learner,
  purchaseTransaction,
  application,
  options = {
    bypassPendingApproval: false,
  },
}) => {
  const { code: communityCode } = community;

  const [communityOwnerInfo, memberCountInfo] = await Promise.all([
    CommonService.retrieveCommunityOwnerInfo(communityCode),
    MembershipCountService.countCommunityMembers({
      communityCode,
    }),
  ]);

  const result = await Promise.allSettled([
    MemberService.sendEnrollmentMail({
      communityOwnerInfo,
      community,
      learner,
      purchaseTransaction,
      options,
    }),
    // NOTE: disable zapier to lark notification
    // AdminService.sendEnrollmentMall({
    //   community,
    //   purchaseTransaction,
    //   learner,
    //   memberCountInfo,
    // }),
    ManagerService.sendEnrollmentMail({
      community,
      learner,
      purchaseTransaction,
      application,
      memberCountInfo,
      options,
    }),
  ]);

  logger.info(`enrollmentMail: ${JSON.stringify(result)}`);
};

exports.renewalFailureMail = async ({
  purchaseTransactionObjectId,
  community,
  learner,
  paymentMetadata,
}) => {
  const { code: communityCode } = community;

  const [communityOwnerInfo, subscription] = await Promise.all([
    CommonService.retrieveCommunityOwnerInfo(communityCode),
    CommunitySubscriptionsModel.findOne({
      communitySignupId: purchaseTransactionObjectId,
    }).lean(),
  ]);

  const result = await Promise.all([
    MemberService.renewalFailureMail({
      communityOwnerInfo,
      community,
      learner,
      subscription,
      paymentMetadata,
    }),
  ]);

  logger.info(`renewalFailureMail: ${JSON.stringify(result)}`);
};

exports.cancelSubscriptionMail = async ({
  subscription,
  community,
  learner,
}) => {
  const { code: communityCode } = community;

  const communityOwnerInfo =
    await CommonService.retrieveCommunityOwnerInfo(communityCode);

  const result = await Promise.allSettled([
    MemberService.cancelSubscriptionMail({
      communityOwnerInfo,
      community,
      learner,
      subscription,
    }),
  ]);

  logger.info(`cancelSubscriptionMail: ${JSON.stringify(result)}`);
};

exports.affiliateSaleMail = async ({
  paidAmount,
  currency,
  transactionCreatedAt,
  commissionEarningAmount,
  commissionEarningCurrency,
  entityTitle,
  communityObjectId,
  learnerObjectId,
}) => {
  const [community, learner] = await Promise.all([
    CommunityModel.findById(communityObjectId).lean(),
    LearnerModel.findById(learnerObjectId).lean(),
  ]);

  await affiliateMailService.formatAndSendNewAffiliateSaleEmail({
    paidAmount,
    currency,
    transactionCreatedAt,
    commissionEarningAmount,
    commissionEarningCurrency,
    entityTitle,
    learner,
    community,
  });
};

exports.saleMail = async ({
  paidAmount,
  currency,
  entityTitle,
  entityLink,
  communityObjectId,
  learnerObjectId,
}) => {
  const { envVarData = null } = await getConfigByType(
    CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
  );
  const emailProvider =
    envVarData?.EMAIL_PROVIDER ?? EMAIL_PROVIDER.SENDGRID;

  if (emailProvider === EMAIL_PROVIDER.SENDGRID) {
    return;
  }

  // eslint-disable-next-line no-unused-vars
  const [community, learner] = await Promise.all([
    CommunityModel.findById(communityObjectId).lean(),
    LearnerModel.findById(learnerObjectId).lean(),
  ]);

  if (!learner) {
    return;
  }

  const klaviyoListIds = {
    en: 'UvNm9K',
    'es-mx': 'UHjRSW',
    'pt-br': 'VJ5s8n',
    ja: 'SieVLK',
  };

  const languagePreference = learner.languagePreference ?? 'en';
  const listId = klaviyoListIds[languagePreference];

  const profile = {
    email: emailUtils.addTimestampToEmail(learner.email),
    properties: {
      first_name: learner.firstName ?? '',
      product_purchased: entityTitle,
      purchase_amount: paidAmount,
      purchase_currency: currency,
      product_link: entityLink,
    },
  };

  await klaviyoClient.addProfileToList({ profile, listId });
};

exports.purchasePlanMail = async ({ community, learner }) => {
  const result = await Promise.all([
    MemberService.purchasePlanMail({
      community,
      learner,
    }),
  ]);

  logger.info(`planPurchaseMail: ${JSON.stringify(result)}`);
};

exports.cancelledPlanMail = async ({
  community,
  learner,
  subscriptionExpiryDate,
  planOrder,
}) => {
  if (!planOrder) {
    throw new Error('Plan order not found');
  }
  const result = await Promise.all([
    MemberService.cancelledPlanMail({
      entityType: planOrder.entityType,
      community,
      learner,
      subscriptionExpiryDate,
    }),
  ]);

  logger.info(`cancelledPlanMail: ${JSON.stringify(result)}`);
};

exports.renewalFailurePlanMail = async ({
  planOrder,
  community,
  learner,
  paymentMetadata,
}) => {
  const result = await Promise.all([
    MemberService.renewalFailurePlanMail({
      planOrder,
      community,
      learner,
      paymentMetadata,
    }),
  ]);

  logger.info(`renewalFailurePlanMail: ${JSON.stringify(result)}`);
};

exports.planReferralRewardMail = async ({
  community,
  refereeCommunity,
  learner,
  rewardAmount,
  currency,
  recurringRewardAmount,
  isFirstBillingCycle,
  planType,
}) => {
  if (!isFirstBillingCycle) {
    logger.info(
      `planReferralRewardMail: bypass sending of email as it is not the first billing cycle`
    );
    return;
  }

  const result = await Promise.all([
    MemberService.planReferralRewardMail({
      community,
      refereeCommunity,
      learner,
      rewardAmount,
      currency,
      recurringRewardAmount,
      planType,
    }),
  ]);

  logger.info(`planReferralRewardMail: ${JSON.stringify(result)}`);
};

exports.revokeCancellationPlanMail = async ({
  community,
  learner,
  planOrder,
  reason,
}) => {
  const result = await Promise.all([
    MemberService.revokeCancellationPlanMail({
      entityType: planOrder.entityType,
      community,
      learner,
      reason,
    }),
  ]);

  logger.info('revokeCancellationPlanMail:', result);
};
