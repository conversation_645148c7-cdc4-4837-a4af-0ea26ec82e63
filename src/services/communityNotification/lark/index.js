const CommunityModel = require('../../../communitiesAPI/models/community.model');
const CommunityPlanOrderModel = require('../../../models/plan/communityPlanOrder.model');

const larkService = require('../../../communitiesAPI/services/common/lark.service');

const {
  NASIO_PRO_LARK_WEBHOOK,
  NASIO_PRO_CANCELLED_LARK_WEBHOOK,
  CALENDAR_ERROR_LARK_WEBHOOK,
  NASIO_PLATINUM_LARK_WEBHOOK,
  NASIO_PLATINUM_CANCELLED_LARK_WEBHOOK,
  NASIO_SUBSCRIPTION_LARK_ALERT_QUEUE_URL,
  LEARN_BACKEND_URL,
} = require('../../../config');
const { ENTITY_TYPE } = require('../../plan/constants');
const { sendMessageToSQSQueue } = require('../../../handlers/sqs.handler');

const sendLarkAlert = async ({ header, contentList, webhookLink }) => {
  const template = larkService.generateMessageTemplate(header);
  const content = contentList.map((item) => {
    if (item.href) {
      return larkService.generateLinkPayload(
        item.title,
        item.value,
        item.href
      );
    }
    return larkService.generateTextPayload(item.title, item.value);
  });

  const payload = await larkService.insertContentToTemplate(
    template,
    content
  );
  await larkService.sendPushNotificationToLark(webhookLink, payload);
};

const purchasePlanAlert = async ({
  planOrderObjectId,
  community,
  learner,
  amount,
  currency,
  nextBillingAmount,
  sendLarkNoti = false,
}) => {
  if (!sendLarkNoti) {
    await sendMessageToSQSQueue({
      queueUrl: NASIO_SUBSCRIPTION_LARK_ALERT_QUEUE_URL,
      messageBody: {
        channels: [
          {
            type: 'lark',
            commonVariables: {
              path: `${LEARN_BACKEND_URL}api/v1/notification/plan/enroll`,
              headers: {
                'api-key': process.env.ROUTE_API_KEY,
              },
              bodyParam: {
                planOrderObjectId,
                learnerObjectId: learner._id,
                communityObjectId: community._id,
                amount,
                currency,
                sendMail: false,
                sendLarkNoti: true,
              },
            },
          },
        ],
        communityId: community._id,
        entityId: planOrderObjectId,
        entityCollection: 'community_plan_orders',
      },
    });
    return;
  }
  const planOrder = await CommunityPlanOrderModel.findOne({
    _id: planOrderObjectId,
    communityObjectId: community._id,
    learnerObjectId: learner._id,
  }).lean();

  if (!planOrder) {
    throw new Error('Plan order not found');
  }

  let referrerCommunityLink;
  if (planOrder.referrerCommunityObjectId) {
    const referrerCommunity = await CommunityModel.findById(
      planOrder.referrerCommunityObjectId,
      { link: 1 }
    ).lean();
    if (referrerCommunity) {
      referrerCommunityLink = `https://nas.io${referrerCommunity.link}`;
    }
  }
  const header = `New Nas.io ${planOrder.entityType} Subscription`;
  const contentList = [
    {
      title: 'Community Name: ',
      value: community.title,
    },
    {
      title: 'Community Link: ',
      href: `https://nas.io${community.link}`,
      value: `https://nas.io${community.link}`,
    },
    {
      title: 'Community Owner: ',
      value: community.createdBy,
    },
    {
      title: 'Subscriber: ',
      value: learner.email,
    },
    {
      title: 'Country: ',
      value: community.countryCreatedIn,
    },
    {
      title: 'Interval: ',
      value: `${planOrder.interval}`,
    },
    {
      title: `Amount in ${currency ?? planOrder.localCurrency}: `,
      value: `${(amount ?? planOrder.amountInLocalCurrency) / 100}`,
    },
    {
      title: `Next Billing Amount in ${
        currency ?? planOrder.localCurrency
      }: `,
      value: `${
        (nextBillingAmount ?? planOrder.amountInLocalCurrency) / 100
      }`,
    },
    {
      title: 'Community Creation Source: ',
      value: community.trackingData?.source || 'unknown',
    },
    {
      title: 'Pro Purchase Source: ',
      value: planOrder.trackingData?.source || 'unknown',
    },
    {
      title: 'Triggered From: ',
      value: planOrder.requestor || 'unknown',
    },
    {
      title: 'Payment Provider: ',
      value: planOrder.paymentDetails.paymentProvider ?? '',
    },
    {
      title: 'Plan Order ID: ',
      value: planOrderObjectId,
    },
    {
      title: 'Phone number: ',
      value: planOrder.phoneNumber ?? '',
    },
  ];

  if (referrerCommunityLink) {
    contentList.push({
      title: 'Referrer: ',
      href: referrerCommunityLink,
      value: referrerCommunityLink,
    });
  }

  let webhookLink;
  switch (planOrder.entityType) {
    case ENTITY_TYPE.PRO:
      webhookLink = NASIO_PRO_LARK_WEBHOOK;
      break;
    case ENTITY_TYPE.PLATINUM:
      webhookLink = NASIO_PLATINUM_LARK_WEBHOOK;
      break;
    default:
      webhookLink = NASIO_PRO_LARK_WEBHOOK;
  }

  await sendLarkAlert({
    header,
    contentList,
    webhookLink,
  });
};

const cancelPlanAlert = async ({
  planOrder,
  community,
  learner,
  subscriptionExpiryDate,
  failureReason,
  cancellationReasons,
}) => {
  if (!planOrder) {
    throw new Error('Plan order not found');
  }

  let upgradeReason;
  if (planOrder.nextPlanOrder?.planOrderObjectId) {
    upgradeReason = 'change-tier';
  }

  const header = `Nas.io ${planOrder.entityType} Subscription (Cancelled)`;
  const contentList = [
    {
      title: 'Community Name: ',
      value: community.title,
    },
    {
      title: 'Community Link: ',
      href: `https://nas.io${community.link}`,
      value: `https://nas.io${community.link}`,
    },
    {
      title: 'Community Owner: ',
      value: community.createdBy,
    },
    {
      title: 'Subscriber: ',
      value: learner.email,
    },
    {
      title: 'Country: ',
      value: community.countryCreatedIn,
    },
    {
      title: 'Interval: ',
      value: `${planOrder.interval}`,
    },
    {
      title: `Amount in ${planOrder.localCurrency}: `,
      value: `${planOrder.amountInLocalCurrency / 100}`,
    },
    {
      title: 'First billing date: ',
      value: planOrder.createdAt.toISOString().split('T')[0],
    },
    {
      title: 'Last billing date: ',
      value: subscriptionExpiryDate,
    },
    {
      title: 'Reason: ',
      value: cancellationReasons ?? upgradeReason ?? failureReason ?? '',
    },
    {
      title: 'Community Creation Source: ',
      value: community.trackingData?.source || 'unknown',
    },
    {
      title: 'Pro Purchase Source: ',
      value: planOrder.trackingData?.source || 'unknown',
    },
    {
      title: 'Payment Provider: ',
      value: planOrder.paymentDetails.paymentProvider ?? '',
    },
    {
      title: 'Plan Order ID: ',
      value: planOrder._id,
    },
  ];

  let webhookLink;
  switch (planOrder.entityType) {
    case ENTITY_TYPE.PRO:
      webhookLink = NASIO_PRO_CANCELLED_LARK_WEBHOOK;
      break;
    case ENTITY_TYPE.PLATINUM:
      webhookLink = NASIO_PLATINUM_CANCELLED_LARK_WEBHOOK;
      break;
    default:
      webhookLink = NASIO_PRO_CANCELLED_LARK_WEBHOOK;
  }

  await sendLarkAlert({
    header,
    contentList,
    webhookLink,
  });
};

const googleCalendarAlert = async ({
  sessionAttendee,
  error,
  calendarAccount,
}) => {
  const header = 'Google Calendar Error';
  const contentList = [
    {
      title: 'Google Calendar AccountId ',
      value: calendarAccount?.accountId ?? 'Not specified',
    },
    {
      title: 'Sessioin Attendee ID: ',
      value: sessionAttendee?._id ?? 'Not specified',
    },
    {
      title: 'Error: ',
      value: JSON.stringify(error.message),
    },
  ];
  await sendLarkAlert({
    header,
    contentList,
    webhookLink: CALENDAR_ERROR_LARK_WEBHOOK,
  });
};

const revokeCancellationPlanAlert = async ({
  planOrder,
  community,
  learner,
  reason,
}) => {
  if (!planOrder) {
    throw new Error('Plan order not found');
  }

  const header = `Nas.io ${planOrder.entityType} Subscription (Revocation)`;
  const contentList = [
    {
      title: 'Community Name: ',
      value: community.title,
    },
    {
      title: 'Community Link: ',
      href: `https://nas.io${community.link}`,
      value: `https://nas.io${community.link}`,
    },
    {
      title: 'Community Owner: ',
      value: community.createdBy,
    },
    {
      title: 'Subscriber: ',
      value: learner.email,
    },
    {
      title: 'Country: ',
      value: community.countryCreatedIn,
    },
    {
      title: 'Interval: ',
      value: `${planOrder.interval}`,
    },
    {
      title: `Amount in ${planOrder.localCurrency}: `,
      value: `${planOrder.amountInLocalCurrency / 100}`,
    },
    {
      title: 'Revocation Date: ',
      value: planOrder.revokedAt.toISOString().split('T')[0],
    },
    {
      title: 'Revocation Reason: ',
      value: reason || 'No reason provided',
    },
    {
      title: 'Payment Provider: ',
      value: planOrder.paymentDetails.paymentProvider ?? '',
    },
    {
      title: 'Plan Order ID: ',
      value: planOrder._id,
    },
  ];

  let webhookLink;
  switch (planOrder.entityType) {
    case ENTITY_TYPE.PRO:
      webhookLink = NASIO_PRO_LARK_WEBHOOK;
      break;
    case ENTITY_TYPE.PLATINUM:
      webhookLink = NASIO_PLATINUM_LARK_WEBHOOK;
      break;
    default:
      webhookLink = NASIO_PRO_LARK_WEBHOOK;
  }

  await sendLarkAlert({
    header,
    contentList,
    webhookLink,
  });
};

module.exports = {
  purchasePlanAlert,
  cancelPlanAlert,
  googleCalendarAlert,
  revokeCancellationPlanAlert,
};
