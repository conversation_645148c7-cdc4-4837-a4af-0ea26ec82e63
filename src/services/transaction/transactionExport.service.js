/* eslint-disable no-unused-vars */
const { Readable } = require('stream');
const { DateTime } = require('luxon');
const FindIpRpc = require('../../rpc/findIp.rpc');
const CommonService = require('./common.service');
const RevenueTransactionModel = require('../../models/revenueTransaction.model');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const RawTransactionModel = require('../../models/rawTransaction.model');
const {
  PURCHASE_TYPE,
  CURRENCY_WITH_NON_DECIMAL_POINTS,
  TRANSACTION_TYPE,
  PAYMENT_PROVIDER,
  PRODUCT_PURCHASE_TYPES,
  NAS_STRUCTURE_LAUNCH_DATE,
} = require('../../constants/common');
const logger = require('../logger.service');
const { CENTS_PER_DOLLAR } = require('../../communitiesAPI/constants');
const { ParamError } = require('../../utils/error.util');

function getAmountInDollarOrCents(amount, currency) {
  if (amount == null || amount === 0) return amount;
  return amount / CENTS_PER_DOLLAR;
}

function retrieveCountry({
  purchaseType,
  subscriptionData,
  zeroLinkData,
  eventData,
  folderData,
  challengeData,
}) {
  if (PRODUCT_PURCHASE_TYPES.includes(purchaseType)) {
    return folderData.country;
  }

  switch (purchaseType) {
    case PURCHASE_TYPE.SUBSCRIPTION:
      return subscriptionData.country;
    case PURCHASE_TYPE.EVENT:
      return eventData.country;
    case PURCHASE_TYPE.CHALLENGE:
      return challengeData.country;
    case PURCHASE_TYPE.ZERO_LINK:
      return zeroLinkData.country;
    default:
      return '';
  }
}

async function retrieveIpAddressCity(ipAddress) {
  if (ipAddress == null) {
    return '';
  }

  try {
    const ipAddressInfo = await FindIpRpc.retrieveIpAddressInfo(ipAddress);
    return ipAddressInfo?.city?.names?.en ?? '';
  } catch (err) {
    logger.error(`retrieveIpAddressCity: ${err.message}`);
    return '';
  }
}

function retrieveLabel(
  purchaseType,
  transactionType,
  zeroLinkData,
  eventData,
  folderData,
  challengeData,
  metadata
) {
  let title = '';
  let purchaseTypeLabel = purchaseType;

  switch (purchaseType) {
    case PURCHASE_TYPE.SUBSCRIPTION:
      title = 'Membership';
      purchaseTypeLabel = 'Subscription';
      break;
    case PURCHASE_TYPE.EVENT:
      title = eventData?.title ?? '';
      purchaseTypeLabel = 'Event';
      break;
    case PURCHASE_TYPE.FOLDER:
      title = folderData?.title ?? '';
      purchaseTypeLabel = 'Digital Product';
      break;
    case PURCHASE_TYPE.SESSION:
      title = folderData?.title ?? '';
      purchaseTypeLabel = 'Session';
      break;
    case PURCHASE_TYPE.CHALLENGE:
      title = challengeData?.title ?? '';
      purchaseTypeLabel = 'Challenge';
      break;
    case PURCHASE_TYPE.ZERO_LINK:
      title = zeroLinkData?.title ?? '';
      purchaseTypeLabel = 'Zero Link';
      break;
    case PURCHASE_TYPE.REFERRAL_REWARD:
      purchaseTypeLabel = 'Referral Reward';
      break;
    default:
      break;
  }

  let transactionTypeLabel = 'Payment';

  if (transactionType === TRANSACTION_TYPE.OUTBOUND) {
    transactionTypeLabel = 'Refund';
    if (metadata?.isChargeback) {
      transactionTypeLabel = 'Chargeback';
    }
  }

  return {
    title,
    transactionTypeLabel,
    purchaseTypeLabel,
  };
}

function retrieveDiscountInfoCsv(
  originalDiscountAmount,
  discountData,
  discountCode
) {
  if (originalDiscountAmount > 0) {
    return `${discountCode ?? ''},${discountData?.value ?? ''}`;
  }

  return ',';
}

function retrieveTransactionDetailsCsv(
  transactionDetails,
  multiplier,
  transactionCreatedAt
) {
  const {
    itemPrice,
    revenueShare,
    paymentGatewayFee,
    payout,
    whtFee,
    refundProcessingFee,
    paid,
    affiliate,
  } = transactionDetails;

  let finalPaidCurrency = paid.converted?.currency ?? paid.currency;
  let finalPaidAmount =
    getAmountInDollarOrCents(
      paid.converted?.amount ?? paid.amount,
      finalPaidCurrency
    ) * multiplier;

  if (
    transactionCreatedAt?.toISOString()?.split('T')[0] >=
    NAS_STRUCTURE_LAUNCH_DATE
  ) {
    finalPaidCurrency = itemPrice.currency;
    finalPaidAmount =
      getAmountInDollarOrCents(
        itemPrice.amountAfterDiscount ?? itemPrice.amount,
        finalPaidCurrency
      ) * multiplier;
  }

  const finalPayoutCurrency =
    payout.converted?.currency ?? payout.currency;
  const finalPayoutAmount =
    getAmountInDollarOrCents(
      payout.converted?.amount ?? payout.amount,
      finalPayoutCurrency
    ) * multiplier;

  const csv = `${finalPaidCurrency ?? ''},${finalPaidAmount ?? ''},${
    getAmountInDollarOrCents(whtFee?.amount, whtFee?.currency) ?? ''
  },${getAmountInDollarOrCents(
    revenueShare.totalAmount - (revenueShare.taxAmount ?? 0),
    revenueShare.currency
  )},${
    getAmountInDollarOrCents(
      revenueShare.taxAmount,
      revenueShare.currency
    ) ?? ''
  },${getAmountInDollarOrCents(
    paymentGatewayFee.totalAmount - (paymentGatewayFee.taxAmount ?? 0),
    paymentGatewayFee.currency
  )},${
    getAmountInDollarOrCents(
      paymentGatewayFee.taxAmount,
      paymentGatewayFee.currency
    ) ?? ''
  },${
    getAmountInDollarOrCents(
      refundProcessingFee,
      paymentGatewayFee.currency
    ) ?? ''
  },${affiliate?.commissionPercentage ?? ''},${
    affiliate
      ? getAmountInDollarOrCents(affiliate.amount, affiliate.currency) *
        multiplier
      : ''
  },${
    getAmountInDollarOrCents(payout.amount, payout.currency) * multiplier
  },${finalPayoutCurrency},${finalPayoutAmount}`;

  return csv;
}

function retrievePaymentInfoCsv(
  paymentMethod,
  paymentBrand,
  paymentProvider,
  originalCurrency,
  rawFee,
  amountBreakdownInUsd,
  amountBreakdownInLocalCurrency,
  transactionDetails
) {
  let currencyExchangeRate = 1;

  if (
    paymentProvider === PAYMENT_PROVIDER.STRIPE &&
    (originalCurrency !== 'SGD' || originalCurrency !== 'USD')
  ) {
    currencyExchangeRate = amountBreakdownInUsd.exchangeRate;
  }

  const { paid, payout } = transactionDetails;
  const finalPaidCurrency = paid.converted?.currency ?? paid.currency;
  let finalPaidCurrencyExchangeRate = 1;

  if (finalPaidCurrency === 'USD') {
    finalPaidCurrencyExchangeRate = amountBreakdownInUsd.exchangeRate;
  } else {
    finalPaidCurrencyExchangeRate =
      amountBreakdownInLocalCurrency.exchangeRate;
  }

  const finalPayoutCurrency =
    payout.converted?.currency ?? payout.currency;
  let finalPayoutCurrencyExchangeRate = 1;

  if (finalPayoutCurrency === 'USD') {
    finalPayoutCurrencyExchangeRate = amountBreakdownInUsd.exchangeRate;
  } else {
    finalPayoutCurrencyExchangeRate =
      amountBreakdownInLocalCurrency.exchangeRate;
  }

  return `${paymentMethod},${paymentBrand},${paymentProvider},${rawFee.currency},${currencyExchangeRate},${finalPaidCurrencyExchangeRate},${finalPayoutCurrencyExchangeRate}`;
}

async function retrieveStripeIndiaRawTransactionIpCityCache(
  revenueTransactions
) {
  const transactionReferenceIds = revenueTransactions
    .filter(
      ({ paymentProvider }) =>
        paymentProvider === PAYMENT_PROVIDER.STRIPE_INDIA
    )
    .map(({ transactionReferenceId }) => transactionReferenceId);

  const rawTransactions = await RawTransactionModel.find(
    {
      transactionReferenceId: { $in: transactionReferenceIds },
      status: 'Success',
      transactionType: TRANSACTION_TYPE.INBOUND,
    },
    {
      ipAddress: '$source.charge.metadata.client_ip_address',
      paymentIntentIpAddress:
        '$source.paymentIntent.metadata.client_ip_address',
      subscriptionIpAddress:
        '$source.invoice.subscription_details.metadata.client_ip_address',
      transactionReferenceId: 1,
    }
  ).lean();

  const ipAddressCities = await Promise.all(
    rawTransactions.map(async (rawTransaction) => {
      const { transactionReferenceId } = rawTransaction;
      const ipAddress = rawTransaction.ipAddress;
      const paymentIntentIpAddress = rawTransaction.paymentIntentIpAddress;
      const subscriptionIpAddress = rawTransaction.subscriptionIpAddress;
      const finalIpAddress =
        ipAddress ?? paymentIntentIpAddress ?? subscriptionIpAddress ?? '';

      const city = await retrieveIpAddressCity(finalIpAddress);
      return { transactionReferenceId, city };
    })
  );

  const ipAddressCityCache = ipAddressCities.reduce(
    (acc, ipAddressCity) => {
      acc.set(ipAddressCity.transactionReferenceId, ipAddressCity.city);
      return acc;
    },
    new Map()
  );

  return ipAddressCityCache;
}

exports.getCommunity = async (communityId) => {
  const community = await CommunityModel.findById(communityId, {
    code: 1,
    baseCurrency: 1,
  }).lean();

  if (!community) {
    throw new ParamError('Invalid community id');
  }

  return community;
};

exports.generateTransactionsCsvStream = async ({
  communityId,
  community,
  purchaseType: selectedPurchaseType,
  discountCodes,
  eventName,
  folderName,
  zeroLinkIds,
  eventsIds,
  productsIds,
  challengesIds,
  subscriptionIntervals,
  transactionType: filterTransactionType,
  startDate,
  endDate,
  pageSize,
  sortOrder,
  sortBy,
  search,
  payoutId,
}) => {
  try {
    let startDateInUtc;
    let endDateInUtc;

    if (startDate && endDate) {
      startDateInUtc = DateTime.fromISO(startDate, { zone: 'utc' });
      endDateInUtc = DateTime.fromISO(endDate, { zone: 'utc' });
    }

    const csvStream = new Readable({
      objectMode: true,
      read() {},
    });

    csvStream.push(
      `Purchase Date,Full name,Email,Phone number,Transaction Type,Purchase Type,Title,Paid Currency,Paid Amount,Discount Code,Discount Amount,Display Currency,Earning Amount,Withholding tax amount (Stripe India only),Nas.io Processing Fees,Tax on Nas.io Processing Fees,Payment Gateway Fees,Tax on Payment Gateway Fees,Refund Processing Fees,Affiliate Commission Percentage,Affiliate Commission Earnings,Payout Amount,User's Payout Currency,User's Payout Amount,Payment Method,Payment Brand,Payment Provider,Reference Currency,Reference Currency Rate,Display Currency Rate (From reference currency),User's Payout Currency Rate (From reference currency),Subscription Interval,Subscription Interval Count,Country,City,VAT amount\n`
    );

    let pageNo = 1;
    let hasNextPage = true;

    const getNextPage = async () => {
      const pipelineQuery =
        await CommonService.generateTransactionsPipelineQuery(
          communityId,
          selectedPurchaseType,
          startDateInUtc,
          endDateInUtc,
          pageNo,
          pageSize,
          sortOrder,
          sortBy,
          discountCodes,
          eventName,
          folderName,
          zeroLinkIds,
          eventsIds,
          productsIds,
          challengesIds,
          subscriptionIntervals,
          filterTransactionType,
          search,
          payoutId
        );

      logger.info(
        `retrieveTransactions: pipeline query ${JSON.stringify(
          pipelineQuery
        )}`
      );

      const revenueTransactions = await RevenueTransactionModel.aggregate(
        pipelineQuery
      );

      const [
        stripeIndiaRawTransactionsIpCityCache,
        revenueTransactionsWithMetadata,
      ] = await Promise.all([
        retrieveStripeIndiaRawTransactionIpCityCache(revenueTransactions),
        CommonService.retrieveMetadata(
          revenueTransactions,
          community.baseCurrency
        ),
      ]);

      revenueTransactionsWithMetadata.forEach(
        ({
          transactionReferenceId,
          transactionCreatedAt,
          transactionType,
          purchaseType,
          zeroLinkData,
          eventData,
          folderData,
          challengeData,
          originalDiscountAmount,
          originalPaidAmount,
          originalCurrency,
          discountCode: usedDiscountCode,
          discountData,
          transactionDetails,
          fullName = '',
          email,
          phoneNumber = '',
          rawFee,
          paymentMethod,
          paymentBrand,
          paymentProvider,
          amountBreakdownInUsd,
          amountBreakdownInLocalCurrency,
          subscriptionData,
          metadata,
          vatAmount,
        }) => {
          const { title, transactionTypeLabel, purchaseTypeLabel } =
            retrieveLabel(
              purchaseType,
              transactionType,
              zeroLinkData,
              eventData,
              folderData,
              challengeData,
              metadata
            );

          const multiplier =
            transactionType === TRANSACTION_TYPE.OUTBOUND ? -1 : 1;

          // to handle the case that title include double quote
          const escapedTitle = title.replace(/"/g, '""');

          const transactionBasicInfoCsv = `${transactionCreatedAt.toISOString()},"${
            fullName ?? ''
          }",${email},${
            phoneNumber ?? ''
          },${transactionTypeLabel},${purchaseTypeLabel},"${escapedTitle}",${originalCurrency},${
            getAmountInDollarOrCents(
              originalPaidAmount,
              originalCurrency
            ) * multiplier
          }`;

          const discountInfoCsv = retrieveDiscountInfoCsv(
            originalDiscountAmount,
            discountData,
            usedDiscountCode
          );

          const transactionDetailsCsv = retrieveTransactionDetailsCsv(
            transactionDetails,
            multiplier,
            transactionCreatedAt
          );

          const paymentInfoCsv = retrievePaymentInfoCsv(
            paymentMethod,
            paymentBrand,
            paymentProvider,
            originalCurrency,
            rawFee,
            amountBreakdownInUsd,
            amountBreakdownInLocalCurrency,
            transactionDetails
          );

          const country = retrieveCountry({
            purchaseType,
            subscriptionData,
            zeroLinkData,
            eventData,
            folderData,
            challengeData,
          });

          const city =
            stripeIndiaRawTransactionsIpCityCache.get(
              transactionReferenceId
            ) ?? '';

          const csvRow = `${transactionBasicInfoCsv},${discountInfoCsv},${transactionDetailsCsv},${paymentInfoCsv},${
            subscriptionData?.interval ?? ''
          },${
            subscriptionData?.interval_count ?? ''
          },"${country}","${city}","${vatAmount ?? ''}"\n`;

          csvStream.push(csvRow);
        }
      );

      pageNo += 1;
      hasNextPage = revenueTransactionsWithMetadata.length === pageSize;
      if (!hasNextPage) {
        csvStream.push(null);
      }
    };

    csvStream._read = () => {
      if (hasNextPage) {
        getNextPage();
      } else {
        csvStream.push(null);
      }
    };

    return csvStream;
  } catch (err) {
    logger.error(`generateTransactionsCsvStream: ${err}`);
    throw new Error('Error generating transactions CSV stream');
  }
};
