const httpContext = require('express-http-context');
const rawTransactionModel = require('@/src/models/rawTransaction.model');
const revenueTransactionModel = require('@/src/models/revenueTransaction.model');
const { sendMessageToSQSQueue } = require('@/src/handlers/sqs.handler');
const CommunityModel = require('@/src/communitiesAPI/models/community.model');
const { GENERATE_INVOICE_URL } = require('@/src/config');
const CommunitySubscriptionsModel = require('@/src/communitiesAPI/models/communitySubscriptions.model');
const { TRANSACTION_STATUS } = require('@/src/communitiesAPI/constants');
const { PURCHASE_TYPE } = require('@/src/constants/common');

const generateInvoice = async ({
  generateInvoiceSchema,
  communityId,
  transactionId,
  managerLearnerId,
}) => {
  const { sendEmail, ...restOfTheInvoicePayload } = generateInvoiceSchema;

  const revenueTransaction = await revenueTransactionModel
    .findById(transactionId)
    .lean();

  if (!revenueTransaction) {
    throw new Error('Transaction not found');
  }

  const giftPurchaseTypes = {
    [PURCHASE_TYPE.CAMPAIGN_REWARD]: true,
    [PURCHASE_TYPE.REFERRAL_BONUS]: true,
    [PURCHASE_TYPE.ONE_DOLLAR_GIFT]: true,
  };
  if (giftPurchaseTypes[revenueTransaction.purchaseType]) {
    throw new Error('Cannot generate invoice for gift purchases.');
  }

  const rawTransaction = await rawTransactionModel
    .findOne({
      transactionReferenceId: revenueTransaction.transactionReferenceId,
      status: TRANSACTION_STATUS.SUCCESS,
    })
    .select('invoiceInfo')
    .lean();

  const { invoiceInfo = {} } = rawTransaction || {};

  const hasChanges = Object.entries(restOfTheInvoicePayload).some(
    ([key, value]) => invoiceInfo[key] !== value
  );

  if (!hasChanges && !sendEmail) {
    return true; // No changes to save
  }

  const alreadySent = Boolean(rawTransaction?.invoiceInfo?.isInvoiceSent);
  const shouldMarkSent = Boolean(sendEmail);
  const query = {
    transactionReferenceId: revenueTransaction.transactionReferenceId,
    status: TRANSACTION_STATUS.SUCCESS,
  };

  if (hasChanges) {
    await rawTransactionModel.updateOne(query, {
      $set: {
        invoiceInfo: {
          ...restOfTheInvoicePayload,
          isInvoiceSent: alreadySent || shouldMarkSent,
        },
      },
    });

    const {
      communityAddress,
      communityTaxPercentage,
      communityTaxId,
      communityTaxLabel,
    } = restOfTheInvoicePayload;

    const updateFields = Object.fromEntries(
      Object.entries({
        'config.communityAddress': communityAddress,
        'config.communityTaxPercentage': communityTaxPercentage,
        'config.communityTaxId': communityTaxId,
        'config.communityTaxLabel': communityTaxLabel,
      }).filter(([, value]) => value !== undefined)
    );

    if (Object.keys(updateFields).length) {
      await CommunityModel.updateOne(
        { _id: communityId },
        { $set: updateFields }
      );
    }
  }

  if (shouldMarkSent && !alreadySent && !hasChanges) {
    await rawTransactionModel.updateOne(query, {
      $set: { 'invoiceInfo.isInvoiceSent': true },
    });
  }
  const requestData = {
    requesterServiceName: 'LPBE',
    purchasedId: revenueTransaction.purchasedId.toString(),
    revenueTransactionId: revenueTransaction._id.toString(),
    sendEmail,
    communityId,
    managerLearnerId,
    learnerObjectId: revenueTransaction.learnerObjectId.toString(),
    purchaseType: revenueTransaction.purchaseType,
    entityObjectId: revenueTransaction.entityObjectId.toString(),
  };

  const message = {
    data: requestData,
    requestor: 'LPBE',
    requestId: httpContext.get('reqId'),
  };

  await sendMessageToSQSQueue({
    queueUrl: GENERATE_INVOICE_URL,
    messageBody: message,
  });
  return true;
};

const getInvoiceConfig = async ({ transactionId, communityId }) => {
  const revenueTransaction = await revenueTransactionModel
    .findById(transactionId)
    .lean();

  if (!revenueTransaction) {
    throw new Error('Transaction not found');
  }

  // Fetch rawTransaction + community in parallel
  const [rawTransaction, community, communitySubscriptionInfo] =
    await Promise.all([
      rawTransactionModel
        .findOne({
          transactionReferenceId:
            revenueTransaction.transactionReferenceId,
          status: TRANSACTION_STATUS.SUCCESS,
        })
        .select('invoiceInfo')
        .lean(),
      CommunityModel.findById(communityId).select('config').lean(),
      CommunitySubscriptionsModel.findOne({
        communitySignupId: String(revenueTransaction.purchasedId),
      }),
    ]);

  const { invoiceInfo = {} } = rawTransaction || {};
  const { config = {} } = community || {};

  return {
    memberAddress: invoiceInfo.memberAddress || '',
    memberLabel: invoiceInfo.memberLabel || '',
    memberId: invoiceInfo.memberId || '',
    subscriptionStartDate:
      communitySubscriptionInfo?.applicationReviewDate ??
      communitySubscriptionInfo?.createdAt ??
      null,
    ...invoiceInfo,
    communityAddress: config.communityAddress || '',
    communityTaxPercentage: config.communityTaxPercentage || '',
    communityTaxId: config.communityTaxId || '',
    communityTaxLabel: config.communityTaxLabel || '',
    isInvoiceSent: invoiceInfo.isInvoiceSent || false,
  };
};

const getInvoice = async ({ transactionId }) => {
  const revenueTransaction = await revenueTransactionModel
    .findById(transactionId)
    .lean();

  if (!revenueTransaction) {
    throw new Error('Transaction not found');
  }
  const rawTransaction = await rawTransactionModel
    .findOne({
      transactionReferenceId: revenueTransaction.transactionReferenceId,
      status: TRANSACTION_STATUS.SUCCESS,
    })
    .select('invoiceInfo')
    .lean();
  return rawTransaction.invoiceInfo || {};
};

module.exports = {
  generateInvoice,
  getInvoiceConfig,
  getInvoice,
};
