const path = require('path');
const PDFDocument = require('pdfkit');
const { DateTime } = require('luxon');
const { PURCHASE_TYPE } = require('../../constants/common');

const {
  getPaymentDate,
  getText,
  staticKeys,
  getOwnerName,
  getProductTitleSubText,
} = require('./localizedData.service');
const {
  processDiscount,
  processSubscription,
  getBufferImagesForReceipt,
} = require('./receiptUtils.service');
const { getOptimizedImageSrc } = require('../../communitiesAPI/utils');
const logger = require('../logger.service');

const NAS_IO_IMAGE =
  'https://d2oi1rqwb0pj00.cloudfront.net/nasIOReceipts/nas_io_image_2.png';

const {
  INVOICE,
  INVOICE_NUMBER,
  INVOICE_DATE,
  POWERED_BY_TEXT,
  ADDRESS_TEXT,
  COUNTRY_TEXT,
  VAT_ID,
  UEN_TEXT,
  BILL_TO,
  ITEM_DESCRIPTION,
  QUANTITY,
  UNIT_PRICE,
  DISCOUNT,
  AMOUNT,
  SUBTOTAL,
  PROCESSING_FEE_TEXT,
  TOTAL_PAID,
  INCLUSIVE,
  NOTES,
  INVOICE_NOTES_LIST,
  ADS_BALANCE_DEDUCTION,
  MAGIC_AD_FEE,
  ID,
} = staticKeys;

const pickFont = (text, isBold, currentFont) => {
  const CYR = /[\u0400-\u04FF]/;

  const isCyrillic = CYR.test(text);

  if (isCyrillic) {
    return isBold ? 'NotoSans-Bold' : 'NotoSans';
  }
  return currentFont;
};

const createHeaderComponent = ({
  doc,
  languagePreference,
  invoiceId,
  dateOfPaymentFormatted,
  nasIOImageBuffer,
  rightSideMaxEnding,
  headerVariables,
  FONT_FAMILY,
}) => {
  const {
    poweredByText,
    addressText,
    countryText,
    uenText,
    poweredByTextWidth,
    addressTextWidth,
    countryTextWidth,
    uenTextWidth,
  } = headerVariables;
  doc
    .fillColor('black')
    .font('Helvetica-Bold')
    .fontSize(18)
    .text(getText(INVOICE, languagePreference))
    .fontSize(9);

  // Receipt details
  doc.moveDown(1);
  doc
    .font('Helvetica-Bold')
    .text(getText(INVOICE_NUMBER, languagePreference), { continued: true })
    .font(FONT_FAMILY)
    .text(`${invoiceId}`);
  doc.moveDown(0.5);
  doc
    .font('Helvetica-Bold')
    .text(getText(INVOICE_DATE, languagePreference), {
      continued: true,
    })
    .font(FONT_FAMILY)
    .text(`${dateOfPaymentFormatted}`);
  doc.moveDown(0.5);

  // Badge dimensions
  const badgeWidth = 32;
  const badgeHeight = 16;
  const x = doc.page.margins.left; // X position where you want it
  // current y position + 5
  const y = doc.y + 5;

  // Draw the rounded rectangle (background)
  doc
    .roundedRect(x, y, badgeWidth, badgeHeight, 4)
    .fillAndStroke('#E6F4EC', '#C7E5D1'); // light green fill & border

  doc.font('Helvetica-Bold').fontSize(9).fillColor('#18774C');
  const label = 'Paid';
  const size = 9;
  const ascent = (doc._font.ascender / 1000) * size;
  const descent = (Math.abs(doc._font.descender) / 1000) * size;
  const contentH = ascent + descent;

  // top of the text box so the glyph box is vertically centered
  const textY = y + (badgeHeight - contentH) / 2;

  doc.text(label, x, textY, {
    width: badgeWidth,
    align: 'center',
    lineBreak: false,
  });
  doc.moveDown(0.5);
  // Nas.io Info (Top-Right)
  if (nasIOImageBuffer) {
    doc.image(nasIOImageBuffer, rightSideMaxEnding - 88, 40, {
      width: 88,
      height: 18,
    });
  }

  doc
    .font(FONT_FAMILY)
    .fontSize(9)
    .fillColor('#8D8D86')
    .text(poweredByText, rightSideMaxEnding - poweredByTextWidth, 72, {
      align: 'right',
    })
    .text(addressText, rightSideMaxEnding - addressTextWidth, 88, {
      align: 'right',
    })
    .text(countryText, rightSideMaxEnding - countryTextWidth, 104, {
      align: 'right',
    })
    .text(uenText, rightSideMaxEnding - uenTextWidth, 120, {
      align: 'right',
    });
};

const createBillToComponent = ({
  doc,
  languagePreference,
  learnerName,
  learnerEmail,
  FONT_FAMILY,
  memberAddress,
  memberId,
  memberLabel,
}) => {
  doc
    .font('Helvetica-Bold')
    .fillColor('black')
    .fontSize(9)
    .text(
      getText(BILL_TO, languagePreference),
      doc.page.margins.left,
      190
    );

  if (learnerName) {
    doc.moveDown(0.5);
    doc.font(FONT_FAMILY).fontSize(9).text(learnerName);
  }

  doc.moveDown(0.5);
  doc.font(FONT_FAMILY).fontSize(9).text(learnerEmail);

  if (memberAddress) {
    doc.moveDown(0.5).text(memberAddress);
  }
  if (memberId) {
    doc.moveDown(0.5);

    if (memberLabel) {
      doc.text(`${memberLabel}: ${memberId}`);
    } else {
      doc.text(`${getText(ID, languagePreference)} ${memberId}`);
    }
  }
};

const createPricingSummaryComponent = ({
  doc,
  isProcessingFee,
  languagePreference,
  currency,
  subTotalAmountNumber,
  currentY,
  processingFee,
  totalPaidAmount,
  lineEndX,
  lineStartX,
  FONT_FAMILY,
  isAdsCampaignTopUp = false,
  adsBalanceDeduction = null,
  communityTaxLabel = null,
  communityTaxPercentage = null,
  taxValue = {},
  originalCurrency,
  originalPaidAmount,
}) => {
  if (isProcessingFee) {
    doc.text(getText(SUBTOTAL, languagePreference), 267.5, currentY);
    doc.text(
      `${currency} ${subTotalAmountNumber.toFixed(2)}`,
      267.5,
      currentY,
      {
        align: 'right',
      }
    );
    // eslint-disable-next-line no-param-reassign
    currentY += 20;

    if (isAdsCampaignTopUp) {
      doc.text(getText(MAGIC_AD_FEE, languagePreference), 267.5, currentY);
    } else {
      doc.text(
        getText(PROCESSING_FEE_TEXT, languagePreference),
        267.5,
        currentY
      );
    }

    doc.text(
      `${currency} ${(processingFee / 100).toFixed(2)}`,
      267.5,
      currentY,
      {
        align: 'right',
      }
    );

    if (adsBalanceDeduction) {
      // eslint-disable-next-line no-param-reassign
      currentY += 20;
      doc.text(
        getText(ADS_BALANCE_DEDUCTION, languagePreference),
        267.5,
        currentY
      );

      doc.text(
        `— ${currency} ${(adsBalanceDeduction / 100).toFixed(2)}`,
        267.5,
        currentY,
        {
          align: 'right',
        }
      );
    }
    // eslint-disable-next-line no-param-reassign
    currentY += 20;

    const rightX = doc.page.width - doc.page.margins.right;
    const y = currentY; // lock the baseline

    // Left label
    doc
      .font('Helvetica-Bold')
      .fillColor('black')
      .text(getText(TOTAL_PAID, languagePreference), 267.5, y);

    const showOriginal =
      originalCurrency &&
      originalCurrency !== currency &&
      Number.isFinite(originalPaidAmount);

    const mainText = `${totalPaidAmount}`;
    const mainWidth = doc.widthOfString(mainText);

    let origText = '';
    let origWidth = 0;

    if (showOriginal) {
      origText = ` (${originalCurrency} ${(
        originalPaidAmount / 100
      ).toFixed(2)})`;
      doc.font(FONT_FAMILY);
      origWidth = doc.widthOfString(origText);
    }

    // starting X for the mainText so both fit at right edge
    const startX = rightX - (mainWidth + origWidth);

    // main (bold)
    doc
      .font('Helvetica-Bold')
      .text(mainText, startX, y, { lineBreak: false });

    // original (normal) if needed
    if (showOriginal) {
      doc
        .font(FONT_FAMILY)
        .text(origText, startX + mainWidth, y, { lineBreak: false });
    }

    if (communityTaxPercentage) {
      // eslint-disable-next-line no-param-reassign
      currentY += 12;
      doc.font(FONT_FAMILY).fontSize(7);
      doc
        .fillColor('#8D8D86')
        .text(
          `${getText(
            INCLUSIVE,
            languagePreference
          )} ${communityTaxLabel} (${communityTaxPercentage}%)`,
          267.5,
          currentY
        );
      doc
        .fillColor('#8D8D86')
        .text(`${taxValue.taxAmountText}`, 267.5, currentY, {
          align: 'right',
        });
    }

    // eslint-disable-next-line no-param-reassign
    currentY += 15;

    // Horizontal line
    doc.moveTo(lineStartX, currentY).lineTo(lineEndX, currentY).stroke();
  } else {
    doc.font('Helvetica-Bold');

    // Only total paid
    doc.text(getText(TOTAL_PAID, languagePreference), 267.5, currentY);
    doc.text(`${totalPaidAmount}`, 267.5, currentY, { align: 'right' });
    if (communityTaxPercentage) {
      // eslint-disable-next-line no-param-reassign
      currentY += 12;
      doc.font(FONT_FAMILY).fontSize(7);
      doc
        .fillColor('#8D8D86')
        .text(
          `Inclusive ${communityTaxLabel} (${communityTaxPercentage}%)`,
          267.5,
          currentY
        );
      doc
        .fillColor('#8D8D86')
        .text(`${taxValue.taxAmountText}`, 267.5, currentY, {
          align: 'right',
        });
    }

    doc
      .moveTo(lineStartX, currentY + 17)
      .lineTo(lineEndX, currentY + 17)
      .stroke();
  }

  doc.font(FONT_FAMILY).fontSize(9);
};

const createFooterComponent = ({
  doc,
  languagePreference,
  currentY,
  FONT_FAMILY,
}) => {
  doc.text(
    getText(NOTES, languagePreference),
    doc.page.margins.left,
    currentY
  );

  doc.font(FONT_FAMILY).fontSize(9);
  // eslint-disable-next-line no-param-reassign
  currentY += 15;

  const notesList = getText(INVOICE_NOTES_LIST, languagePreference);

  // Render list
  doc.list(notesList, doc.page.margins.left + 2, currentY, {
    width: 480,
    bulletRadius: 1,
    bulletIndent: 2,
    textIndent: 6,
    lineGap: 2,
  });

  // eslint-disable-next-line no-param-reassign
};
const createCommunityInfoComponent = ({
  doc,
  originalCursorX,
  originalCursorY,
  imageWidth,
  horizontalPadding,
  communityTitle,
  ownerName,
  languagePreference,
  profileImageBuffer,
  imageHeight,
  FONT_FAMILY,
  communityTaxId,
  communityAddress,
  communityTaxLabel,
}) => {
  // Profile image (left side)
  if (profileImageBuffer) {
    const radius = 6; // Adjust as needed

    doc.save(); // Save the current graphics state

    // Apply rounded clipping path
    doc
      .roundedRect(
        originalCursorX,
        originalCursorY,
        imageWidth,
        imageHeight,
        radius
      )
      .clip();

    try {
      doc.image(profileImageBuffer, originalCursorX, originalCursorY, {
        width: imageWidth,
        height: imageHeight,
      });
    } catch (error) {
      logger.error('Error rendering image:', error);
    }

    doc.restore(); // Restore graphics state
  }

  let currentY = originalCursorY;

  // Community title
  doc
    .font(pickFont(communityTitle, false, FONT_FAMILY))
    .fontSize(9)
    .text(
      communityTitle,
      originalCursorX + imageWidth + horizontalPadding,
      currentY + 2,
      {
        underline: true,
      }
    );

  // Owner name
  currentY += doc.heightOfString(communityTitle || '') + 6;
  doc
    .font(pickFont(ownerName, false, FONT_FAMILY))
    .fontSize(9)
    .fillColor('#8D8D86')
    .text(
      getOwnerName(ownerName, languagePreference),
      originalCursorX + imageWidth + horizontalPadding,
      currentY
    )
    .fill('black');

  // Address (if any)
  currentY += doc.heightOfString(ownerName || '') + 3;
  if (communityAddress) {
    doc
      .font(pickFont(communityAddress, false, FONT_FAMILY))
      .fontSize(9)
      .text(
        communityAddress,
        originalCursorX + imageWidth + horizontalPadding,
        currentY
      );

    currentY += doc.heightOfString(communityAddress || '') + 2;
  }

  // Tax ID (if any)
  if (communityTaxId) {
    doc
      .font(pickFont(communityTaxId, false, FONT_FAMILY))
      .fontSize(9)
      .text(
        `${
          communityTaxLabel || getText(VAT_ID, languagePreference)
        }: ${communityTaxId}`,
        originalCursorX + imageWidth + horizontalPadding,
        currentY
      );
  }
};

/********************************************
 * Dynamic Column Width Calculation
 ********************************************/
function getColumnWidths(totalWidth, columnRatios) {
  const totalRatio = columnRatios.reduce((sum, ratio) => sum + ratio, 0);
  return columnRatios.map((ratio) => (ratio / totalRatio) * totalWidth);
}
/********************************************
 * Render Table Function
 ********************************************/
function renderTable(
  doc,
  tableHeaders,
  tableData,
  isSubscription,
  isDiscounted,
  subscriptionInfo,
  discountInfoObj,
  FONT_FAMILY
) {
  const leftMargin = doc.page.margins.left;
  const rightMargin = doc.page.margins.right;
  const totalWidth = doc.page.width - leftMargin - rightMargin;

  // Define the width ratio for each column
  //   const columnRatios = [43, 13, 16, 16, 12];
  //   const columnRatios = [41, 9, 17, 18, 15];
  const columnRatios = [41.07, 4.78, 17.95, 18.95, 19.25];

  // 196.584696
  //185.38524356435644
  // Calculate column widths dynamically
  const columnWidths = getColumnWidths(totalWidth, columnRatios);

  let currentY = doc.y;
  let currentX = leftMargin;

  // Render Headers
  doc.font('Helvetica-Bold').fontSize(8).fill('#63635E');
  tableHeaders.forEach((header, index) => {
    doc.text(header, currentX, currentY, {
      width: columnWidths[index],
      align: index < 2 ? 'left' : 'right',
    });
    // }
    currentX += columnWidths[index]; // Move to the next column
  });

  doc.font(FONT_FAMILY).fontSize(9).fill('black');
  // Draw horizontal line under headers
  currentY += 15;
  doc
    .moveTo(leftMargin, currentY)
    .lineTo(doc.page.width - rightMargin, currentY)
    .stroke('#E9E8E6');

  // Render Data Rows
  doc.font(FONT_FAMILY).fontSize(9).fill('black');
  tableData.forEach((row) => {
    currentY += 10;
    currentX = leftMargin; // Reset X to left margin

    const rowValues = [
      row.itemDescription,
      row.quantity,
      row.unitPrice,
      row.discount,
      row.amount,
    ];

    rowValues.forEach((value, index) => {
      doc
        .font(pickFont(value, false, FONT_FAMILY))
        .text(value, currentX, currentY, {
          width: columnWidths[index],
          align: index < 2 ? 'left' : 'right',
        });

      if (index === 3 && isDiscounted) {
        doc
          .fontSize(8)
          .fill('#8D8D86')
          .text(
            `${discountInfoObj.discountedSubText}`,
            currentX,
            currentY + 12,
            {
              align: 'right',
              width: columnWidths[index],
            }
          );
        doc.fontSize(9).fill('black');
      }
      //   }
      currentX += columnWidths[index]; // Move to the next column
    });

    if (isSubscription) {
      const heightOfItemDescription = doc.heightOfString(
        row.itemDescription || '',
        {
          width: columnWidths[0],
          align: 'left',
        }
      );
      currentY += heightOfItemDescription;
      doc
        .fontSize(8)
        .fill('#8D8D86')
        .text(`${subscriptionInfo.fromAndToDate}`, leftMargin, currentY);
    }

    if (isDiscounted && row.discount) {
      currentY += 6;
      doc
        .font(pickFont(row.discount, false, FONT_FAMILY))
        .fontSize(8)
        .fill('#8D8D86')
        .text(
          `CODE: ${discountInfoObj.discountCode}`,
          leftMargin,
          currentY + 6
        );
    }

    doc.fontSize(9).fill('black');
  });
}

/**
 * create the PDF buffer for the receipt based on the schema and language preferences
 * @param {any} createReceiptSchema
 * @param {any} languagePreference
 * @returns {Promise<Buffer>} PDF Buffer of the receipt
 */
async function createInvoice(createReceiptSchema, languagePreference) {
  const {
    invoiceId,
    transactionCreatedAt,
    communityInfo,
    recipient,
    item: productItemInfo,
    processingFee,
    subTotalAmount,
    paidAmount,
    adsBalanceDeduction,
    originalCurrency,
    originalPaidAmount,
  } = createReceiptSchema;
  let FONT_FAMILY = 'Helvetica';

  const {
    title: communityTitle,
    ownerName,
    profileImage,
    communityAddress,
    communityTaxId,
    communityTaxLabel,
    communityTaxPercentage,
  } = communityInfo;
  const {
    name: learnerName,
    email: learnerEmail,
    timezone: learnerTimezone,
    memberId,
    memberAddress,
    memberLabel,
  } = recipient;

  // Deconstruct product item info
  const {
    title,
    purchaseType,
    quantity,
    currency,
    discountInfo,
    startDate,
    endDate,
    plan,
    unitPrice,
    totalAmount,
  } = productItemInfo;

  // Determine if this is a subscription
  const isSubscription = purchaseType === PURCHASE_TYPE.SUBSCRIPTION;
  const isAdsCampaignTopUp =
    purchaseType === PURCHASE_TYPE.ADS_CAMPAIGN_TOPUP;
  const isDiscounted = Boolean(
    discountInfo && Object.keys(discountInfo).length
  );
  const isProcessingFee = processingFee > 0; // joel to check if we can assume this
  const poweredByText = getText(POWERED_BY_TEXT, languagePreference);
  const addressText = getText(ADDRESS_TEXT, languagePreference);
  const countryText = getText(COUNTRY_TEXT, languagePreference);
  const uenText = getText(UEN_TEXT, languagePreference);
  const currencyUnitPrice = `${currency} ${(unitPrice / 100).toFixed(2)}`;
  const currencyPaidAmount = `${currency} ${(totalAmount / 100).toFixed(
    2
  )}`;
  const totalPaidAmount = `${currency} ${(paidAmount / 100).toFixed(2)}`;
  const subTotalAmountNumber = subTotalAmount / 100;
  const dateOfPaymentInLuxon = DateTime.fromJSDate(
    transactionCreatedAt
  ).setZone(learnerTimezone);
  const endDateInLuxon =
    DateTime.fromJSDate(endDate).setZone(learnerTimezone);
  const startDateInLuxon =
    DateTime.fromJSDate(startDate).setZone(learnerTimezone);
  const dateOfPaymentFormatted = getPaymentDate(
    dateOfPaymentInLuxon,
    learnerTimezone,
    languagePreference
  );
  let titleSubText = '';
  const taxValue = {};

  if (communityTaxPercentage) {
    const amountWithoutTax =
      paidAmount / (1 + communityTaxPercentage / 100);

    const taxAmount = paidAmount - amountWithoutTax;
    taxValue['taxAmount'] = taxAmount / 100;
    taxValue['taxAmountText'] = `${currency} ${taxValue.taxAmount.toFixed(
      2
    )}`;
  }

  if (isSubscription) {
    titleSubText = getProductTitleSubText(
      plan?.intervalCount,
      plan?.interval,
      languagePreference
    );
  }

  /********************************************
   * Fetch Images (dont stop if image fetch fails)
   ********************************************/

  const { profileImageBuffer, nasIOImageBuffer } =
    await getBufferImagesForReceipt({
      profileImage: getOptimizedImageSrc(profileImage),
      nasIOImage: NAS_IO_IMAGE,
    });

  /********************************************
   * Handle Subscription & Discount Info
   ********************************************/
  let subscriptionInfo = {};
  let discountInfoObj = {};

  // Subscription Info
  if (isSubscription) {
    subscriptionInfo = processSubscription(
      plan,
      learnerTimezone,
      dateOfPaymentInLuxon,
      endDateInLuxon,
      startDateInLuxon,
      isDiscounted,
      paidAmount,
      languagePreference,
      currency
    );
  }

  // Discount Info
  if (isDiscounted) {
    discountInfoObj = processDiscount(
      discountInfo,
      currency,
      isSubscription,
      languagePreference
    );
  }

  /********************************************
   * Initialize PDF
   ********************************************/
  const doc = new PDFDocument({
    margins: { top: 40, bottom: 24, left: 64, right: 64 },
    size: 'A4',
  });

  if (languagePreference === 'ja') {
    FONT_FAMILY = 'Helvetica-JP';
  }

  // A4 width = 595.28 - 64(right margin) = 531.28
  // A4 height = 841.89
  // Register your custom fonts
  if (languagePreference === 'ja') {
    doc.registerFont(
      FONT_FAMILY,
      path.join(__dirname, 'fonts', 'NotoSansJP-Regular.ttf')
    );

    doc.registerFont(
      'Helvetica-Bold',
      path.join(__dirname, 'fonts', 'NotoSansJP-Bold.ttf')
    );
  }
  doc.registerFont(
    'NotoSans',
    path.join(__dirname, 'fonts', 'NotoSans-Regular.ttf')
  );

  doc.registerFont(
    'NotoSans-Bold',
    path.join(__dirname, 'fonts', 'NotoSans-Bold.ttf')
  );

  const rightSideMaxEnding = doc.page.width - doc.page.margins.right;
  const poweredByTextWidth = doc.widthOfString(poweredByText);
  const addressTextWidth = doc.widthOfString(addressText);
  const countryTextWidth = doc.widthOfString(countryText);
  const uenTextWidth = doc.widthOfString(uenText);

  const buffers = [];
  doc.on('data', (chunk) => buffers.push(chunk));
  doc.font(FONT_FAMILY);

  const headerColorFill = '#F9F9F8';
  const headerColorFillWidth = 660;
  const headerColorFillHeight = 270;

  const memberAddressHeight = doc.heightOfString(memberAddress || '');
  const memberIdHeight = doc.heightOfString(memberId || '');

  /********************************************
   * Header Section
   ********************************************/
  doc
    .rect(
      0,
      0,
      headerColorFillWidth,
      headerColorFillHeight + memberAddressHeight + memberIdHeight
    )
    .fill(headerColorFill);

  createHeaderComponent({
    doc,
    languagePreference,
    invoiceId,
    dateOfPaymentFormatted,
    nasIOImageBuffer,
    rightSideMaxEnding,
    headerVariables: {
      poweredByText,
      addressText,
      countryText,
      uenText,
      poweredByTextWidth,
      addressTextWidth,
      countryTextWidth,
      uenTextWidth,
    },
    FONT_FAMILY,
  });

  /********************************************
   * Bill-To Section
   ********************************************/
  createBillToComponent({
    doc,
    languagePreference,
    learnerName,
    learnerEmail,
    FONT_FAMILY,
    memberAddress,
    memberId,
    memberLabel,
  });
  doc.moveDown(7.5);

  /********************************************
   * Community Info & Subscription Details
   ********************************************/
  const originalCursorX = doc.x;
  const originalCursorY = doc.y;
  const imageWidth = 26;
  const imageHeight = 26;
  const horizontalPadding = 6;

  // Profile image (left side)
  createCommunityInfoComponent({
    doc,
    originalCursorX,
    originalCursorY,
    imageWidth,
    horizontalPadding,
    communityTitle,
    ownerName,
    languagePreference,
    profileImageBuffer,
    imageHeight,
    FONT_FAMILY,
    communityTaxId,
    communityAddress,
    communityTaxLabel,
  });

  doc.moveDown(4);

  const tableData = [
    {
      itemDescription: `${title} ${titleSubText}`,
      quantity,
      unitPrice: currencyUnitPrice,
      discount: isDiscounted ? `- ${discountInfoObj.discountedText}` : '-',
      amount: currencyPaidAmount,
    },
  ];

  const tableHeaders = [
    getText(ITEM_DESCRIPTION, languagePreference),
    getText(QUANTITY, languagePreference),
    getText(UNIT_PRICE, languagePreference),
    getText(DISCOUNT, languagePreference),
    getText(AMOUNT, languagePreference),
  ];

  doc.x = doc.page.margins.left;

  renderTable(
    doc,
    tableHeaders,
    tableData,
    isSubscription,
    isDiscounted,
    subscriptionInfo,
    discountInfoObj,
    FONT_FAMILY
  );

  // Reset fill and font
  doc.fontSize(9).fill('black');
  doc.moveDown(2);

  /********************************************
   * Pricing Summary (Subtotal / Fees / Total)
   ********************************************/
  let currentY = doc.y + 10;
  const lineStartX = 267.5;
  const lineEndX = doc.page.width - doc.page.margins.right;

  // Horizontal line
  doc.moveTo(lineStartX, currentY).lineTo(lineEndX, currentY).stroke();
  currentY += 10;

  createPricingSummaryComponent({
    doc,
    isProcessingFee,
    languagePreference,
    currency,
    subTotalAmountNumber,
    currentY,
    processingFee,
    totalPaidAmount,
    lineEndX,
    lineStartX,
    FONT_FAMILY,
    isAdsCampaignTopUp,
    adsBalanceDeduction,
    communityTaxLabel,
    communityTaxPercentage,
    taxValue,
    originalCurrency,
    originalPaidAmount,
  });

  /********************************************
   * Footer Notes
   ********************************************/
  // Position near bottom
  currentY = doc.page.height - 100;
  doc.fontSize(8).fill('#63635E').font('Helvetica-Bold');
  createFooterComponent({
    doc,
    languagePreference,
    currentY,
    FONT_FAMILY,
  });

  return new Promise((resolve, reject) => {
    doc.on('end', () => {
      const pdfBuffer = Buffer.concat(buffers);
      resolve(pdfBuffer); // Return the buffer
    });

    doc.on('error', (err) => reject(err));
    doc.end(); // Triggers the 'end' event
  });
}

/********************************************
 * Helper Functions
 ********************************************/

module.exports = {
  createInvoice,
};
