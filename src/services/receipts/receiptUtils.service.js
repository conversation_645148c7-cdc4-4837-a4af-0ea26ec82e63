const https = require('https');
const { DateTime } = require('luxon');
const {
  getDiscountedSubText,
  getIntervalText,
  getToAndFromDate,
} = require('./localizedData.service');
const logger = require('../logger.service');

const getReceiptFilePath = ({ languagePreference, receiptId }) => {
  const filePath = `nasio/receipts/${receiptId}_${new Date().getTime()}_${languagePreference}.pdf`;
  return filePath;
};

const getInvoiceFilePath = ({ languagePreference, invoiceId }) => {
  const filePath = `nasio/invoices/${invoiceId}_${new Date().getTime()}_${languagePreference}.pdf`;
  return filePath;
};

/**
 * Fetches an image from a remote URL and returns it as a Buffer.
 * @param {string} url - The URL of the image to fetch.
 * @returns {Promise<Buffer>}
 */
async function fetchImageAsBuffer(url) {
  return new Promise((resolve, reject) => {
    https
      .get(url, (res) => {
        if (res.statusCode !== 200) {
          return reject(
            new Error(`Failed to get '${url}' (${res.statusCode})`)
          );
        }
        const data = [];
        res.on('data', (chunk) => data.push(chunk));
        res.on('end', () => resolve(Buffer.concat(data)));
      })
      .on('error', (err) => {
        console.error(`Failed to fetch image from '${url}':`, err);
        reject(err);
      });
  });
}
const processSubscription = (
  plan,
  learnerTimezone,
  endDateInLuxon,
  startDateInLuxon,
  isDiscounted,
  paidAmount,
  languagePreference,
  currency
) => {
  const {
    interval,
    intervalCount,
    nextBillingDate,
    price,
    originalPrice,
    priceWithPassOn,
  } = plan || {};
  const nextBilling =
    DateTime.fromJSDate(nextBillingDate).setZone(learnerTimezone);

  // If discounted, membership price might differ
  const membershipPlanText = isDiscounted
    ? `${(paidAmount / 100).toFixed(2)} / ${getIntervalText(
        intervalCount,
        interval,
        languagePreference
      )}`
    : `${(price / 100).toFixed(2)} / ${getIntervalText(
        intervalCount,
        interval,
        languagePreference
      )}`;

  return {
    interval,
    intervalCount,
    nextBillingDate: nextBilling.toFormat('d MMMM yyyy'),
    price,
    originalPrice,
    priceWithPassOn: priceWithPassOn
      ? `${currency} ${(priceWithPassOn / 100).toFixed(
          2
        )} / ${getIntervalText(
          intervalCount,
          interval,
          languagePreference
        )}`
      : null,
    membershipPlan: `${currency} ${membershipPlanText}`,
    fromAndToDate: `${getToAndFromDate(
      startDateInLuxon,
      endDateInLuxon,
      learnerTimezone,
      languagePreference
    )}`,
  };
};

const processDiscount = (
  discountInfo,
  currency,
  isSubscription,
  languagePreference
) => {
  const { amount, code } = discountInfo || {};
  return {
    discountedText: `${currency} ${(amount / 100).toFixed(2)}`,
    discountedSubText: getDiscountedSubText(
      isSubscription,
      discountInfo,
      languagePreference
    ),
    discountCode: code,
  };
};

const getBufferImagesForReceipt = async ({ profileImage, nasIOImage }) => {
  let profileImageBuffer;
  let nasIOImageBuffer;
  try {
    [profileImageBuffer, nasIOImageBuffer] = await Promise.all([
      fetchImageAsBuffer(profileImage),
      fetchImageAsBuffer(nasIOImage),
    ]);
  } catch (err) {
    logger.error('Error fetching profile image:', err);
  }

  return { profileImageBuffer, nasIOImageBuffer };
};

module.exports = {
  processSubscription,
  processDiscount,
  fetchImageAsBuffer,
  getReceiptFilePath,
  getInvoiceFilePath,
  getBufferImagesForReceipt,
};
