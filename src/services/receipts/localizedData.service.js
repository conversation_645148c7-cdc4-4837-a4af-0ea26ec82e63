const { DateTime } = require('luxon');

const staticKeys = {
  INVOICE: 'invoice',
  RECEIPT: 'receipt',
  RECEIPT_NUMBER: 'receiptNumber',
  INVOICE_NUMBER: 'invoiceNumber',
  TIME_SUFFIX: 'timeSuffix',
  DATE_OF_PAYMENT: 'dateOfPayment',
  INVOICE_DATE: 'invoiceDate',
  PAYMENT_METHOD: 'paymentMethod',
  POWERED_BY_TEXT: 'poweredByText',
  ADDRESS_TEXT: 'addressText',
  COUNTRY_TEXT: 'countryText',
  VAT_ID: 'vatId',
  ID: 'id',
  UEN_TEXT: 'UENText',
  BILL_TO: 'billTo',
  MEMBERSHIP_PLAN: 'membershipPlan',
  NEXT_BILLING_DATE: 'nextBillingDate',
  ITEM_DESCRIPTION: 'itemDescription',
  QUANTITY: 'quantity',
  DISCOUNT: 'discount',
  AMOUNT: 'amount',
  SUBTOTAL: 'subtotal',
  PROCESSING_FEE_TEXT: 'processingFeeText',
  TOTAL_PAID: 'totalPaid',
  INCLUSIVE: 'inclusive',
  NOTES: 'notes',
  NOTES_LIST: 'notesList',
  INVOICE_NOTES_LIST: 'invoiceNotesList',
  BUILD_YOUR_BUSINESS: 'buildYourBusiness',
  SUPPORT_TEXT: 'supportText',
  UNIT_PRICE: 'unitPrice',
  MAGIC_AD_FEE: 'magicAdFee',
  ADS_BALANCE_DEDUCTION: 'adsBalanceDeduction',
};
const translations = {
  en: {
    ID: 'ID:',
    vatId: 'VAT ID:',
    invoice: 'Invoice',
    invoiceNumber: 'Invoice Number: ',
    invoiceDate: 'Date of invoice: ',
    adsBalanceDeduction: 'Ads Balance Deduction',
    oneTimeDiscount: '{discount}% off',
    firstBillingCycle: '{discount}% off first billing cycle',
    foreverDiscount: '{discount}% off forever',
    byOwner: 'By {ownerName}',
    processingFee: 'after processing fee: {processingFeePlan}',
    magicAdFee: 'Magic ad fee',
    intervals: {
      year: 'Year',
      month: 'month',
      threeMonths: '3 months',
      sixMonths: '6 months',
    },
    membershipInterval: {
      monthly: '(Monthly)',
      yearly: '(Yearly)',
      quarterly: '(Every 3 months)',
      semiAnnually: '(Every 6 months)',
    },
    timeSuffix: 'Time',
    receipt: 'Receipt',
    receiptNumber: 'Receipt Number: ',
    dateOfPayment: 'Date of Payment: ',
    paymentMethod: 'Payment Method: ',
    poweredByText: 'Powered by Nas Education Pte Ltd',
    addressText: '10 Central Exchange Green #02-02',
    countryText: 'Singapore 138649',
    UENText: 'UEN No: 201933647N',
    billTo: 'Bill to:',
    membershipPlan: 'Membership Plan: ',
    nextBillingDate: 'Next Billing Date: ',
    itemDescription: 'ITEM DESCRIPTION',
    quantity: 'QTY',
    discount: 'DISCOUNT',
    amount: 'AMOUNT',
    unitPrice: 'UNIT PRICE',
    subtotal: 'Subtotal',
    processingFeeText: 'Processing Fee',
    totalPaid: 'Total Paid',
    notes: 'NOTES:',
    invoiceNotesList: [
      'This invoice confirms your purchase of digital content via nas.io.',
      'No physical goods will be shipped.',
      'As all digital products are immediately accessible on the platform, they are not refundable via Nas.io. Please contact creator for special refund requests.',
    ],
    notesList: [
      'This receipt confirms your purchase of digital content via nas.io.',
      'No physical goods will be shipped.',
      'As all digital products are immediately accessible on the platform, they are not refundable via Nas.io. Please contact the creator for special refund requests.',
    ],
    buildYourBusiness: 'Build Your Business with Nas.io',
    supportText: 'Need support? <NAME_EMAIL>',
    inclusive: 'Inclusive',
  },
  'es-mx': {
    id: 'ID:',
    inclusive: 'Incluido',
    vatId: 'VAT ID:',
    invoice: 'Factura',
    invoiceDate: 'Fecha de factura: ',
    invoiceNumber: 'Número de factura: ',
    adsBalanceDeduction: 'Deducción de saldo de anuncios',
    oneTimeDiscount: '{discount}% de descuento',
    firstBillingCycle: '{discount}% de descuento en el primer ciclo',
    foreverDiscount: '{discount}% de descuento para siempre',
    byOwner: 'Por {ownerName}',
    processingFee:
      'después de la tarifa de procesamiento: {processingFeePlan}',
    intervals: {
      year: 'Año',
      month: 'mes',
      threeMonths: '3 meses',
      sixMonths: '6 meses',
    },
    magicAdFee: 'Magic ad fee',
    membershipInterval: {
      monthly: '(Mensual)',
      yearly: '(Anual)',
      quarterly: '(Cada tres meses)',
      semiAnnually: '(Cada seis meses)',
    },
    timeSuffix: 'Hora',
    receipt: 'Recibo',
    receiptNumber: 'Número de recibo: ',
    dateOfPayment: 'Fecha de pago: ',
    paymentMethod: 'Método de pago: ',
    poweredByText: 'Impulsado por Nas Education Pte Ltd',
    addressText: '10 Central Exchange Green #02-02',
    countryText: 'Singapur 138649',
    UENText: 'UEN No: 201933647N',
    billTo: 'Facturar a:',
    membershipPlan: 'Plan de membresía',
    nextBillingDate: 'Próxima fecha de facturación: ',
    itemDescription: 'DESCRIPCIÓN DEL ARTÍCULO',
    quantity: 'CTD',
    discount: 'DESCUENTO',
    amount: 'IMPORTE',
    unitPrice: 'PRECIO UNITARIO',
    subtotal: 'Subtotal',
    processingFeeText: 'Tarifa de procesamiento',
    totalPaid: 'Total pagado',
    notes: 'NOTAS:',
    invoiceNotesList: [
      'Esta factura confirma su compra de contenido digital a través de nas.io.',
      'No se enviarán productos físicos.',
      'Como todos los productos digitales son accesibles de inmediato en la plataforma, no son reembolsables a través de Nas.io. Comuníquese con el creador para solicitudes de reembolso especiales.',
    ],
    notesList: [
      'Este recibo confirma su compra de contenido digital a través de nas.io.',
      'No se enviarán productos físicos.',
      'Como todos los productos digitales son accesibles de inmediato en la plataforma, no son reembolsables a través de Nas.io. Comuníquese con el creador para solicitudes de reembolso especiales.',
    ],
    buildYourBusiness: 'Construye tu negocio con Nas.io',
    supportText: '¿Necesitas ayuda? Por favor, <NAME_EMAIL>',
  },
  'pt-br': {
    id: 'ID:',
    inclusive: 'Inclusivo',
    vatId: 'VAT ID:',
    invoice: 'Fatura',
    invoiceDate: 'Data da fatura: ',
    invoiceNumber: 'Número da fatura: ',
    adsBalanceDeduction: 'Dedução de saldo de anúncios',
    oneTimeDiscount: '{discount}% de desconto',
    firstBillingCycle: '{discount}% de desconto no primeiro ciclo',
    foreverDiscount: '{discount}% de desconto para sempre',
    byOwner: 'Por {ownerName}',
    magicAdFee: 'Magic ad fee',
    processingFee: 'após a taxa de processamento: {processingFeePlan}',
    intervals: {
      year: 'Ano',
      month: 'mês',
      threeMonths: '3 meses',
      sixMonths: '6 meses',
    },
    membershipInterval: {
      monthly: '(Mensal)',
      yearly: '(Anual)',
      quarterly: '(A cada três meses)',
      semiAnnually: '(A cada seis meses)',
    },
    timeSuffix: 'Hora',
    receipt: 'Recibo',
    receiptNumber: 'Número do recibo: ',
    dateOfPayment: 'Data do pagamento: ',
    paymentMethod: 'Método de pagamento: ',
    poweredByText: 'Desenvolvido por Nas Education Pte Ltd',
    addressText: '10 Central Exchange Green #02-02',
    countryText: 'Singapura 138649',
    UENText: 'UEN No: 201933647N',
    billTo: 'Faturar para:',
    membershipPlan: 'Plano de associação',
    nextBillingDate: 'Próxima data de faturamento: ',
    itemDescription: 'DESCRIÇÃO DO ITEM',
    quantity: 'QNT',
    discount: 'DESCONTO',
    unitPrice: 'PREÇO UNITÁRIO',
    amount: 'VALOR',
    subtotal: 'Subtotal',
    processingFeeText: 'Taxa de processamento',
    totalPaid: 'Total pago',
    notes: 'NOTAS:',
    invoiceNotesList: [
      'Esta fatura confirma sua compra de conteúdo digital via nas.io.',
      'Nenhum produto físico será enviado.',
      'Como todos os produtos digitais são imediatamente acessíveis na plataforma, eles não são reembolsáveis via Nas.io. Entre em contato com o criador para solicitações de reembolso especiais.',
    ],
    notesList: [
      'Este recibo confirma sua compra de conteúdo digital via nas.io.',
      'Nenhum produto físico será enviado.',
      'Como todos os produtos digitais são imediatamente acessíveis na plataforma, eles não são reembolsáveis via Nas.io. Entre em contato com o criador para solicitações de reembolso especiais.',
    ],
    buildYourBusiness: 'Construa seu negócio com Nas.io',
    supportText:
      'Precisa de ajuda? Por favor, entre em contato: <EMAIL>',
  },
  ja: {
    id: 'ID:',
    inclusive: '含まれています',
    vatId: 'VAT ID:',
    invoiceNumber: '請求書番号: ',
    invoiceDate: '請求書日付: ',
    invoice: '請求書',
    adsBalanceDeduction: '広告残高控除',
    oneTimeDiscount: '{discount}%オフ',
    firstBillingCycle: '初回請求サイクル{discount}%オフ',
    foreverDiscount: 'ずっと{discount}%オフ',
    byOwner: '{ownerName}によって',
    magicAdFee: 'Magic ad fee',
    intervals: {
      year: '年',
      month: '月',
      threeMonths: '3ヶ月',
      sixMonths: '6ヶ月',
    },
    membershipInterval: {
      monthly: '(毎月)',
      yearly: '(毎年)',
      quarterly: '(3か月ごと)',
      semiAnnually: '(6か月ごと)',
    },
    processingFee: '手数料後: {processingFeePlan}',
    timeSuffix: '時間',
    receipt: '領収書',
    receiptNumber: '領収書番号: ',
    dateOfPayment: '支払日: ',
    paymentMethod: '支払方法: ',
    poweredByText: 'Powered by Nas Education Pte Ltd',
    addressText: '10 Central Exchange Green #02-02',
    countryText: 'Singapore 138649',
    UENText: 'UEN No: 201933647N',
    billTo: '請求先:',
    membershipPlan: '会員プラン',
    nextBillingDate: '次回請求日: ',
    itemDescription: 'アイテムの説明',
    quantity: '数量',
    discount: '割引',
    unitPrice: '単価',
    amount: '金額',
    subtotal: '小計',
    processingFeeText: '手数料',
    totalPaid: '合計支払額',
    notes: 'メモ:',
    invoiceNotesList: [
      'この請求書は、nas.ioを通じてデジタルコンテンツの購入を確認します。',
      '物理的な商品は発送されません。',
      'すべてのデジタル製品はプラットフォームで直ちにアクセスできるため、Nas.io経由で返金されません。特別な返金リクエストについては、作成者にお問い合わせください。',
    ],
    notesList: [
      'この領収書は、nas.io経由でのデジタルコンテンツの購入を確認します。',
      '物理的な商品は発送されません。',
      'すべてのデジタル製品はプラットフォームで直ちにアクセスできるため、Nas.io経由で返金されません。特別な返金リクエストについては、作成者にお問い合わせください。',
    ],
    buildYourBusiness: 'Nas.ioでビジネスを展開しましょう',
    supportText: 'サポートが必要ですか？ <EMAIL> までご連絡ください',
  },
};

function applyTemplate(template, variables = {}) {
  let result = template;

  // For each key in `variables`, replace every occurrence of {key}
  for (const [key, value] of Object.entries(variables)) {
    // Replace all occurrences using a global regex
    const placeholderRegex = new RegExp(`\\{${key}\\}`, 'g');
    result = result.replace(
      placeholderRegex,
      value !== undefined ? value : ''
    );
  }

  return result;
}
/*
 * Returns the discount text for both one-time purchases and subscriptions.
 * @param {boolean} isSubscription
 * @param {object} plan
 * @param {object} discountInfo
 * @returns {string} - e.g. "20% off", "20% off first billing cycle", "20% off forever"
 */
function getDiscountedSubText(
  isSubscription,
  discountInfo,
  languagePreference
) {
  const { interval, percentage: discountPercent } = discountInfo;

  // 2) Fallback to English if the specified language isn't available
  const selectedLang = translations[languagePreference] || translations.en;
  if (!isSubscription) {
    // One-time purchase discount
    return applyTemplate(selectedLang.oneTimeDiscount, {
      discount: discountPercent,
    });
  }

  if (interval === 'forever') {
    return applyTemplate(selectedLang.foreverDiscount, {
      discount: discountPercent,
    });
  }
  return applyTemplate(selectedLang.firstBillingCycle, {
    discount: discountPercent,
  });
}

/**
 * Returns the translated interval text based on the intervalCount, interval, and language.
 * Falls back to English if translation is not available.
 *
 * @param {number} intervalCount - The count of the interval (e.g., 1, 3, 6)
 * @param {string} interval - The type of interval ('month', 'year')
 * @param {string} languagePreference - The preferred language key ('en', 'es-mx', 'pt-br', 'ja')
 * @returns {string} - The translated interval text (e.g., "Year", "3 months")
 */
function getIntervalText(
  intervalCount,
  interval,
  languagePreference = 'en'
) {
  const translationSet =
    translations[languagePreference] || translations.en;

  if (interval === 'year') return translationSet.intervals.year;
  if (intervalCount === 1) return translationSet.intervals.month;
  if (intervalCount === 3) return translationSet.intervals.threeMonths;
  if (intervalCount === 6) return translationSet.intervals.sixMonths;

  return ''; // Return empty string if interval is not recognized
}

/**
 * Formats the payment date based on the provided locale and timezone.
 *
 * @param {object} dateOfPaymentInLuxon - A Luxon DateTime object representing the payment date.
 * @param {string} learnerTimezone - The learner's timezone, e.g., "Asia/Singapore".
 * @param {string} languagePreference - The preferred language key, e.g., "en", "es-mx", "pt-br", "ja".
 * @returns {string} - Formatted payment date string.
 */
function getPaymentDate(
  dateOfPaymentInLuxon,
  learnerTimezone,
  languagePreference = 'en'
) {
  const selectedLang = translations[languagePreference] || translations.en;
  const timeSuffix = selectedLang.timeSuffix || 'Time';

  // Format the date using the specified locale
  const formattedDate = dateOfPaymentInLuxon.toFormat(
    languagePreference === 'ja'
      ? 'yyyy年MM月dd日, HH時mm分' // For Japanese
      : 'dd MMM yyyy, HH:mm', // For other locales
    {
      locale: languagePreference,
    }
  );

  // Extracting the relevant part of the timezone (e.g., "Singapore" from "Asia/Singapore")
  const timezoneRegion = learnerTimezone.split('/').slice(-1)[0];

  // Combining the formatted date, timezone region, and localized time suffix
  return `${formattedDate} ${timezoneRegion} ${timeSuffix}`;
}

function getToAndFromDate(
  dateOfPaymentInLuxon,
  endDate,
  learnerTimezone,
  languagePreference = 'en'
) {
  const startDateFormatted = dateOfPaymentInLuxon
    .setZone(learnerTimezone)
    .toFormat(
      languagePreference === 'ja'
        ? 'M月d日' // Japanese format: Month月Day日
        : 'd MMM',
      { locale: languagePreference }
    );

  const endDateObj = DateTime.fromISO(endDate).setZone(learnerTimezone);

  const endDateFormatted = endDateObj.toFormat(
    languagePreference === 'ja'
      ? 'yyyy年M月d日' // Japanese: Year年Month月Day日
      : 'd MMM yyyy', // Standard: Day Month Year
    { locale: languagePreference }
  );

  return `${startDateFormatted} - ${endDateFormatted}`;
}

const getProductTitleSubText = (
  intervalCount,
  interval,
  languagePreference = 'en'
) => {
  const translationSet =
    translations[languagePreference] || translations.en;

  if (interval === 'year') return translationSet.membershipInterval.yearly;
  if (intervalCount === 1)
    return translationSet.membershipInterval.monthly;
  if (intervalCount === 3)
    return translationSet.membershipInterval.quarterly;
  if (intervalCount === 6)
    return translationSet.membershipInterval.semiAnnually;

  return '';
};

const getText = (key, languagePreference) => {
  const selectedLang = translations[languagePreference] || translations.en;
  return selectedLang[key];
};

const getOwnerName = (ownerName, languagePreference) => {
  const selectedLang = translations[languagePreference] || translations.en;
  return applyTemplate(selectedLang.byOwner, { ownerName });
};

const getProcessingFeeNote = (processingFeePlan, languagePreference) => {
  const selectedLang = translations[languagePreference] || translations.en;
  return applyTemplate(selectedLang.processingFee, { processingFeePlan });
};
module.exports = {
  getDiscountedSubText,
  getIntervalText,
  getPaymentDate,
  getToAndFromDate,
  getText,
  staticKeys,
  getOwnerName,
  getProcessingFeeNote,
  getProductTitleSubText,
};
