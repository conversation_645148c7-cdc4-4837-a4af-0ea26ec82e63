const path = require('path');
const PDFDocument = require('pdfkit');
const { DateTime } = require('luxon');
const { PURCHASE_TYPE } = require('../../constants/common');

const {
  getPaymentDate,
  getIntervalText,
  getText,
  staticKeys,
  getOwnerName,
  getProcessingFeeNote,
  getProductTitleSubText,
} = require('./localizedData.service');
const {
  processDiscount,
  processSubscription,
  getBufferImagesForReceipt,
} = require('./receiptUtils.service');
const { getOptimizedImageSrc } = require('../../communitiesAPI/utils');
const logger = require('../logger.service');

const NAS_IO_IMAGE =
  'https://d2oi1rqwb0pj00.cloudfront.net/nasIOReceipts/nas_io_image_2.png';

const {
  RECEIPT,
  RECEIPT_NUMBER,
  DATE_OF_PAYMENT,
  PAYMENT_METHOD,
  POWERED_BY_TEXT,
  ADDRESS_TEXT,
  COUNTRY_TEXT,
  UEN_TEXT,
  BILL_TO,
  MEMBERSHIP_PLAN,
  NEXT_BILLING_DATE,
  ITEM_DESCRIPTION,
  QUANTITY,
  UNIT_PRICE,
  DISCOUNT,
  AMOUNT,
  SUBTOTAL,
  PROCESSING_FEE_TEXT,
  TOTAL_PAID,
  NOTES,
  NOTES_LIST,
  BUILD_YOUR_BUSINESS,
  SUPPORT_TEXT,
  ADS_BALANCE_DEDUCTION,
  MAGIC_AD_FEE,
} = staticKeys;

const createHeaderComponent = ({
  doc,
  languagePreference,
  receiptId,
  dateOfPaymentFormatted,
  paymentBrand,
  cardLast4,
  nasIOImageBuffer,
  rightSideMaxEnding,
  headerVariables,
  FONT_FAMILY,
}) => {
  const {
    poweredByText,
    addressText,
    countryText,
    uenText,
    poweredByTextWidth,
    addressTextWidth,
    countryTextWidth,
    uenTextWidth,
  } = headerVariables;
  doc
    .fillColor('black')
    .font('Helvetica-Bold')
    .fontSize(18)
    .text(getText(RECEIPT, languagePreference))
    .fontSize(9);

  // Receipt details
  doc.moveDown(1);
  doc
    .font('Helvetica-Bold')
    .text(getText(RECEIPT_NUMBER, languagePreference), { continued: true })
    .font(FONT_FAMILY)
    .text(`${receiptId}`);
  doc.moveDown(0.5);
  doc
    .font('Helvetica-Bold')
    .text(getText(DATE_OF_PAYMENT, languagePreference), {
      continued: true,
    })
    .font(FONT_FAMILY)
    .text(`${dateOfPaymentFormatted}`);
  doc.moveDown(0.5);
  doc
    .font('Helvetica-Bold')
    .text(getText(PAYMENT_METHOD, languagePreference), { continued: true })
    .font(FONT_FAMILY)
    .text(`${paymentBrand} `, { continued: true });

  if (cardLast4) {
    doc
      .text(`••••`, { continued: true })
      .fontSize(9)
      .text(` ${cardLast4}`);
  }

  // Nas.io Info (Top-Right)

  if (nasIOImageBuffer) {
    doc.image(nasIOImageBuffer, rightSideMaxEnding - 88, 40, {
      width: 88,
      height: 18,
    });
  }

  doc
    .font(FONT_FAMILY)
    .fontSize(9)
    .fillColor('#8D8D86')
    .text(poweredByText, rightSideMaxEnding - poweredByTextWidth, 72, {
      align: 'right',
    })
    .text(addressText, rightSideMaxEnding - addressTextWidth, 88, {
      align: 'right',
    })
    .text(countryText, rightSideMaxEnding - countryTextWidth, 104, {
      align: 'right',
    })
    .text(uenText, rightSideMaxEnding - uenTextWidth, 120, {
      align: 'right',
    });
};

const createBillToComponent = ({
  doc,
  languagePreference,
  learnerName,
  learnerEmail,
  FONT_FAMILY,
}) => {
  doc
    .font('Helvetica-Bold')
    .fillColor('black')
    .fontSize(9)
    .text(
      getText(BILL_TO, languagePreference),
      doc.page.margins.left,
      190
    );
  doc.moveDown(0.5);
  doc
    .font(FONT_FAMILY)
    .fontSize(9)
    .text(learnerName)
    .moveDown(0.5)
    .text(learnerEmail);
};

const createSubscriptionInfoComponent = ({
  doc,
  contentY,
  isDiscounted,
  currency,
  subTotalAmountNumber,
  subscriptionInfo,
  membershipPlanLabelString,
  rightSideMaxEnding,
  languagePreference,
  FONT_FAMILY,
}) => {
  const rightEdge = doc.page.width - doc.page.margins.right;
  doc.font(FONT_FAMILY).fontSize(9);
  // Initialize text chunks (discount scenario or not)
  let discountedPriceString = '';
  let originalPriceString = '';
  let intervalString = '';
  if (subscriptionInfo.originalPrice) {
    // e.g. “USD 16.00 ”
    discountedPriceString = ` ${currency} ${subTotalAmountNumber.toFixed(
      2
    )} `;
    // e.g. “20.00” (to be struck out)
    originalPriceString = `${(
      subscriptionInfo.originalPrice / 100
    ).toFixed(2)}`;
    // e.g. “ / month”
    intervalString = ` / ${getIntervalText(
      subscriptionInfo.intervalCount,
      subscriptionInfo.interval
    )}`;
  } else {
    // Not discounted: entire membership plan
    intervalString = subscriptionInfo.membershipPlan || '';
  }

  doc.font('Helvetica-Bold').fontSize(9).fill('black');

  const membershipPlanLabelWidth = doc.widthOfString(
    membershipPlanLabelString
  );

  doc.font(FONT_FAMILY).fontSize(9);
  // Total width needed for the ENTIRE membership-plan line
  const totalMembershipPlanWidth =
    doc.widthOfString(
      discountedPriceString + originalPriceString + intervalString
    ) + membershipPlanLabelWidth;

  const membershipPlanStartX =
    rightSideMaxEnding - totalMembershipPlanWidth; // hugging right
  // eslint-disable-next-line no-param-reassign
  doc.x = membershipPlanStartX;

  doc.font('Helvetica-Bold').fontSize(9).text(membershipPlanLabelString, {
    lineBreak: false,
    continued: true,
  });

  doc.font(FONT_FAMILY).fontSize(9);

  if (isDiscounted) {
    doc.text(discountedPriceString, {
      lineBreak: false,
      continued: true,
    });

    doc.text(originalPriceString, {
      lineBreak: false,
      continued: true,
    });
    // draw the line for strike
    const lineHeight = doc.currentLineHeight();
    const lastTextWidth = doc.widthOfString(originalPriceString);
    doc
      .moveTo(doc.x - lastTextWidth, doc.y + lineHeight / 2)
      .lineTo(doc.x, doc.y + lineHeight / 2)
      .stroke();
    // 3) intervalString
    doc.text(intervalString, {
      lineBreak: false,
      continued: false,
    });
  } else {
    // Not discounted
    doc.text(intervalString, {
      lineBreak: false,
      continued: false,
    });
  }

  if (subscriptionInfo.priceWithPassOn) {
    const processingFeeNote = getProcessingFeeNote(
      subscriptionInfo.priceWithPassOn,
      languagePreference
    );

    doc.fontSize(7).font(FONT_FAMILY); // Set the same font and size
    const processingFeeNoteWidth = doc.widthOfString(processingFeeNote);

    const processingFeeXPosition =
      rightSideMaxEnding - processingFeeNoteWidth;
    doc
      .fontSize(7)
      .fill('#8D8D86')
      .text(processingFeeNote, processingFeeXPosition, contentY + 15, {
        lineBreak: false,
      });
    doc.fontSize(9).fill('black');
    // eslint-disable-next-line no-param-reassign
    contentY += 13;
  }

  // Move down after finishing membership plan line
  doc.moveDown(1.3);

  const nextBillingLabelString = getText(
    NEXT_BILLING_DATE,
    languagePreference
  );
  const nextBillingDateString = subscriptionInfo.nextBillingDate || '';

  doc.font('Helvetica-Bold').fontSize(9);
  const nextBillingLabelWidth = doc.widthOfString(nextBillingLabelString);

  doc.font(FONT_FAMILY).fontSize(9);
  const nextBillingDateWidth = doc.widthOfString(nextBillingDateString);

  const totalNextBillingWidth =
    nextBillingLabelWidth + nextBillingDateWidth;

  let nextBillingStartX = rightEdge - totalNextBillingWidth; // hugging right
  if (nextBillingStartX < doc.page.margins.left) {
    nextBillingStartX = doc.page.margins.left;
  }

  // eslint-disable-next-line no-param-reassign
  doc.x = nextBillingStartX;

  // print
  doc.font('Helvetica-Bold').fontSize(9).text(nextBillingLabelString, {
    lineBreak: false,
    continued: true,
  });

  doc.font(FONT_FAMILY).fontSize(9).text(nextBillingDateString, {
    lineBreak: false,
    continued: false,
  });
};

const createPricingSummaryComponent = ({
  doc,
  isProcessingFee,
  languagePreference,
  currency,
  subTotalAmountNumber,
  currentY,
  processingFee,
  totalPaidAmount,
  lineEndX,
  lineStartX,
  FONT_FAMILY,
  isAdsCampaignTopUp = false,
  adsBalanceDeduction = null,
}) => {
  if (isProcessingFee) {
    doc.text(getText(SUBTOTAL, languagePreference), 267.5, currentY);
    doc.text(
      `${currency} ${subTotalAmountNumber.toFixed(2)}`,
      267.5,
      currentY,
      {
        align: 'right',
      }
    );
    // eslint-disable-next-line no-param-reassign
    currentY += 20;

    if (isAdsCampaignTopUp) {
      doc.text(getText(MAGIC_AD_FEE, languagePreference), 267.5, currentY);
    } else {
      doc.text(
        getText(PROCESSING_FEE_TEXT, languagePreference),
        267.5,
        currentY
      );
    }

    doc.text(
      `${currency} ${(processingFee / 100).toFixed(2)}`,
      267.5,
      currentY,
      {
        align: 'right',
      }
    );

    if (adsBalanceDeduction) {
      // eslint-disable-next-line no-param-reassign
      currentY += 20;
      doc.text(
        getText(ADS_BALANCE_DEDUCTION, languagePreference),
        267.5,
        currentY
      );

      doc.text(
        `— ${currency} ${(adsBalanceDeduction / 100).toFixed(2)}`,
        267.5,
        currentY,
        {
          align: 'right',
        }
      );
    }
    // eslint-disable-next-line no-param-reassign
    currentY += 20;

    doc.font('Helvetica-Bold');
    doc.text(getText(TOTAL_PAID, languagePreference), 267.5, currentY);
    doc.text(`${totalPaidAmount}`, 267.5, currentY, { align: 'right' });
    // eslint-disable-next-line no-param-reassign
    currentY += 15;

    // Horizontal line
    doc.moveTo(lineStartX, currentY).lineTo(lineEndX, currentY).stroke();
  } else {
    doc.font('Helvetica-Bold');

    // Only total paid
    doc.text(getText(TOTAL_PAID, languagePreference), 267.5, currentY);
    doc.text(`${totalPaidAmount}`, 267.5, currentY, { align: 'right' });
    doc
      .moveTo(lineStartX, currentY + 17)
      .lineTo(lineEndX, currentY + 17)
      .stroke();
  }

  doc.font(FONT_FAMILY).fontSize(9);
};

const createFooterComponent = ({
  doc,
  languagePreference,
  currentY,
  FONT_FAMILY,
}) => {
  doc.text(
    getText(NOTES, languagePreference),
    doc.page.margins.left,
    currentY
  );

  doc.font(FONT_FAMILY).fontSize(9);
  // eslint-disable-next-line no-param-reassign
  currentY += 15;

  const notesList = getText(NOTES_LIST, languagePreference);

  // Render list
  doc.list(notesList, doc.page.margins.left + 2, currentY, {
    width: 480,
    bulletRadius: 1,
    bulletIndent: 2,
    textIndent: 6,
    lineGap: 2,
  });

  // eslint-disable-next-line no-param-reassign

  const pageHeight = doc.page.height;
  const pageWidth = doc.page.width;
  const rectHeight = 60;
  const bottomMargin = 20;
  const spacing = 18;

  // Light background rectangle for bottom
  doc
    .rect(0, pageHeight - rectHeight, pageWidth, rectHeight)
    .fill('#F9F9F8');

  doc.font(FONT_FAMILY).fillColor('#8D8D86');
  doc.text(
    getText(BUILD_YOUR_BUSINESS, languagePreference),
    doc.page.margins.left,
    pageHeight - bottomMargin - spacing,
    {
      link: 'https://nas.io',
      underline: true,
    }
  );
  doc.text(
    getText(SUPPORT_TEXT, languagePreference),
    doc.page.margins.left,
    pageHeight - bottomMargin - spacing,
    {
      align: 'right',
    }
  );
};
const createCommunityInfoComponent = ({
  doc,
  originalCursorX,
  originalCursorY,
  imageWidth,
  horizontalPadding,
  communityTitle,
  ownerName,
  languagePreference,
  profileImageBuffer,
  imageHeight,
  FONT_FAMILY,
}) => {
  // Profile image (left side)
  if (profileImageBuffer) {
    const radius = 6; // Adjust as needed

    doc.save(); // Save the current graphics state

    // Apply rounded clipping path
    doc
      .roundedRect(
        originalCursorX,
        originalCursorY,
        imageWidth,
        imageHeight,
        radius
      )
      .clip();

    try {
      doc.image(profileImageBuffer, originalCursorX, originalCursorY, {
        width: imageWidth,
        height: imageHeight,
      });
    } catch (error) {
      logger.error('Error rendering image:', error);
    }

    doc.restore(); // Restore graphics state
  }
  doc
    .font('Helvetica-Bold')
    .fontSize(9)
    .text(
      communityTitle,
      originalCursorX + imageWidth + horizontalPadding,
      originalCursorY + 2,
      {
        underline: true,
      }
    )
    .font(FONT_FAMILY);
  doc
    .fontSize(9)
    .fillColor('#8D8D86')
    .text(
      `${getOwnerName(ownerName, languagePreference)}`,
      originalCursorX + imageWidth + horizontalPadding,
      originalCursorY + 16
    )
    .fill('black');
};

/********************************************
 * Dynamic Column Width Calculation
 ********************************************/
function getColumnWidths(totalWidth, columnRatios) {
  const totalRatio = columnRatios.reduce((sum, ratio) => sum + ratio, 0);
  return columnRatios.map((ratio) => (ratio / totalRatio) * totalWidth);
}
/********************************************
 * Render Table Function
 ********************************************/
function renderTable(
  doc,
  tableHeaders,
  tableData,
  isSubscription,
  isDiscounted,
  subscriptionInfo,
  discountInfoObj,
  FONT_FAMILY
) {
  const leftMargin = doc.page.margins.left;
  const rightMargin = doc.page.margins.right;
  const totalWidth = doc.page.width - leftMargin - rightMargin;

  // Define the width ratio for each column
  //   const columnRatios = [43, 13, 16, 16, 12];
  //   const columnRatios = [41, 9, 17, 18, 15];
  const columnRatios = [41.07, 4.78, 17.95, 18.95, 19.25];

  // 196.584696
  //185.38524356435644
  // Calculate column widths dynamically
  const columnWidths = getColumnWidths(totalWidth, columnRatios);

  let currentY = doc.y;
  let currentX = leftMargin;

  // Render Headers
  doc.font('Helvetica-Bold').fontSize(8).fill('#63635E');
  tableHeaders.forEach((header, index) => {
    doc.text(header, currentX, currentY, {
      width: columnWidths[index],
      align: index < 2 ? 'left' : 'right',
    });
    // }
    currentX += columnWidths[index]; // Move to the next column
  });

  doc.font(FONT_FAMILY).fontSize(9).fill('black');
  // Draw horizontal line under headers
  currentY += 15;
  doc
    .moveTo(leftMargin, currentY)
    .lineTo(doc.page.width - rightMargin, currentY)
    .stroke('#E9E8E6');

  // Render Data Rows
  doc.font(FONT_FAMILY).fontSize(9).fill('black');
  tableData.forEach((row) => {
    currentY += 10;
    currentX = leftMargin; // Reset X to left margin

    const rowValues = [
      row.itemDescription,
      row.quantity,
      row.unitPrice,
      row.discount,
      row.amount,
    ];

    rowValues.forEach((value, index) => {
      doc.text(value, currentX, currentY, {
        width: columnWidths[index],
        align: index < 2 ? 'left' : 'right',
      });

      if (index === 3 && isDiscounted) {
        doc
          .fontSize(8)
          .fill('#8D8D86')
          .text(
            `${discountInfoObj.discountedSubText}`,
            currentX,
            currentY + 12,
            {
              align: 'right',
              width: columnWidths[index],
            }
          );
        doc.fontSize(9).fill('black');
      }
      //   }
      currentX += columnWidths[index]; // Move to the next column
    });

    if (isSubscription) {
      currentY += 7;
      doc
        .fontSize(8)
        .fill('#8D8D86')
        .text(
          `${subscriptionInfo.fromAndToDate}`,
          leftMargin,
          currentY + 6
        );
    }

    if (isDiscounted && row.discount) {
      currentY += 12;
      doc
        .fontSize(8)
        .fill('#8D8D86')
        .text(
          `CODE: ${discountInfoObj.discountCode}`,
          leftMargin,
          currentY + 6
        );
    }

    doc.fontSize(9).fill('black');
  });
}

/**
 * create the PDF buffer for the receipt based on the schema and language preferences
 * @param {any} createReceiptSchema
 * @param {any} languagePreference
 * @returns {Promise<Buffer>} PDF Buffer of the receipt
 */
async function createReceipt(createReceiptSchema, languagePreference) {
  const {
    receiptId,
    transactionCreatedAt,
    communityInfo,
    paymentMethod,
    recipient,
    item: productItemInfo,
    processingFee,
    subTotalAmount,
    paidAmount,
    adsBalanceDeduction,
  } = createReceiptSchema;
  let FONT_FAMILY = 'Helvetica';

  const { title: communityTitle, ownerName, profileImage } = communityInfo;
  const { paymentBrand, cardLast4 } = paymentMethod;
  const {
    name: learnerName,
    email: learnerEmail,
    timezone: learnerTimezone,
  } = recipient;

  // Deconstruct product item info
  const {
    title,
    purchaseType,
    quantity,
    currency,
    discountInfo,
    endDate,
    plan,
    unitPrice,
    totalAmount,
  } = productItemInfo;

  // Determine if this is a subscription
  const isSubscription = purchaseType === PURCHASE_TYPE.SUBSCRIPTION;
  const isAdsCampaignTopUp =
    purchaseType === PURCHASE_TYPE.ADS_CAMPAIGN_TOPUP;
  const isDiscounted = Boolean(
    discountInfo && Object.keys(discountInfo).length
  );
  const isProcessingFee = processingFee > 0; // joel to check if we can assume this
  const poweredByText = getText(POWERED_BY_TEXT, languagePreference);
  const addressText = getText(ADDRESS_TEXT, languagePreference);
  const countryText = getText(COUNTRY_TEXT, languagePreference);
  const membershipPlanLabelString = getText(
    MEMBERSHIP_PLAN,
    languagePreference
  );

  const uenText = getText(UEN_TEXT, languagePreference);
  const currencyUnitPrice = `${currency} ${(unitPrice / 100).toFixed(2)}`;
  const currencyPaidAmount = `${currency} ${(totalAmount / 100).toFixed(
    2
  )}`;
  const totalPaidAmount = `${currency} ${(paidAmount / 100).toFixed(2)}`;
  const subTotalAmountNumber = subTotalAmount / 100;
  const dateOfPaymentInLuxon = DateTime.fromJSDate(
    transactionCreatedAt
  ).setZone(learnerTimezone);
  const endDateInLuxon =
    DateTime.fromJSDate(endDate).setZone(learnerTimezone);
  const dateOfPaymentFormatted = getPaymentDate(
    dateOfPaymentInLuxon,
    learnerTimezone,
    languagePreference
  );
  let titleSubText = '';

  if (isSubscription) {
    titleSubText = getProductTitleSubText(
      plan?.intervalCount,
      plan?.interval,
      languagePreference
    );
  }

  /********************************************
   * Fetch Images (dont stop if image fetch fails)
   ********************************************/

  const { profileImageBuffer, nasIOImageBuffer } =
    await getBufferImagesForReceipt({
      profileImage: getOptimizedImageSrc(profileImage),
      nasIOImage: NAS_IO_IMAGE,
    });

  /********************************************
   * Handle Subscription & Discount Info
   ********************************************/
  let subscriptionInfo = {};
  let discountInfoObj = {};

  // Subscription Info
  if (isSubscription) {
    subscriptionInfo = processSubscription(
      plan,
      learnerTimezone,
      endDateInLuxon,
      dateOfPaymentInLuxon,
      isDiscounted,
      paidAmount,
      languagePreference,
      currency
    );
  }

  // Discount Info
  if (isDiscounted) {
    discountInfoObj = processDiscount(
      discountInfo,
      currency,
      isSubscription,
      languagePreference
    );
  }

  /********************************************
   * Initialize PDF
   ********************************************/
  const doc = new PDFDocument({
    margins: { top: 40, bottom: 24, left: 64, right: 64 },
    size: 'A4',
  });

  if (languagePreference === 'ja') {
    FONT_FAMILY = 'Helvetica-JP';
  }

  // A4 width = 595.28 - 64(right margin) = 531.28
  // A4 height = 841.89
  // Register your custom fonts
  if (languagePreference === 'ja') {
    doc.registerFont(
      FONT_FAMILY,
      path.join(__dirname, 'fonts', 'NotoSansJP-Regular.ttf')
    );

    doc.registerFont(
      'Helvetica-Bold',
      path.join(__dirname, 'fonts', 'NotoSansJP-Bold.ttf')
    );
  }
  const rightSideMaxEnding = doc.page.width - doc.page.margins.right;
  const poweredByTextWidth = doc.widthOfString(poweredByText);
  const addressTextWidth = doc.widthOfString(addressText);
  const countryTextWidth = doc.widthOfString(countryText);
  const uenTextWidth = doc.widthOfString(uenText);

  const buffers = [];
  doc.on('data', (chunk) => buffers.push(chunk));
  doc.font(FONT_FAMILY);

  /********************************************
   * Header Section
   ********************************************/
  doc.rect(0, 0, 660, 270).fill('#F9F9F8');

  createHeaderComponent({
    doc,
    languagePreference,
    receiptId,
    dateOfPaymentFormatted,
    paymentBrand,
    cardLast4,
    nasIOImageBuffer,
    rightSideMaxEnding,
    headerVariables: {
      poweredByText,
      addressText,
      countryText,
      uenText,
      poweredByTextWidth,
      addressTextWidth,
      countryTextWidth,
      uenTextWidth,
    },
    FONT_FAMILY,
  });

  /********************************************
   * Bill-To Section
   ********************************************/
  createBillToComponent({
    doc,
    languagePreference,
    learnerName,
    learnerEmail,
    FONT_FAMILY,
  });
  doc.moveDown(7.5);

  /********************************************
   * Community Info & Subscription Details
   ********************************************/
  const originalCursorX = doc.x;
  const originalCursorY = doc.y;
  const imageWidth = 26;
  const imageHeight = 26;
  const horizontalPadding = 6;

  // Profile image (left side)
  createCommunityInfoComponent({
    doc,
    originalCursorX,
    originalCursorY,
    imageWidth,
    horizontalPadding,
    communityTitle,
    ownerName,
    languagePreference,
    profileImageBuffer,
    imageHeight,
    FONT_FAMILY,
  });

  const contentY = originalCursorY;

  // Membership plan (for Subscription)
  if (isSubscription) {
    // Move the cursor down (or set doc.y) so we start at the correct line.
    doc.y = contentY + 1;
    createSubscriptionInfoComponent({
      doc,
      contentY,
      isDiscounted,
      currency,
      subTotalAmountNumber,
      subscriptionInfo,
      membershipPlanLabelString,
      rightSideMaxEnding,
      languagePreference,
      FONT_FAMILY,
    });
  }

  doc.moveDown(4);

  const tableData = [
    {
      itemDescription: `${title} ${titleSubText}`,
      quantity,
      unitPrice: currencyUnitPrice,
      discount: isDiscounted ? `- ${discountInfoObj.discountedText}` : '-',
      amount: currencyPaidAmount,
    },
  ];

  const tableHeaders = [
    getText(ITEM_DESCRIPTION, languagePreference),
    getText(QUANTITY, languagePreference),
    getText(UNIT_PRICE, languagePreference),
    getText(DISCOUNT, languagePreference),
    getText(AMOUNT, languagePreference),
  ];

  doc.x = doc.page.margins.left;

  renderTable(
    doc,
    tableHeaders,
    tableData,
    isSubscription,
    isDiscounted,
    subscriptionInfo,
    discountInfoObj,
    FONT_FAMILY
  );

  // Reset fill and font
  doc.fontSize(9).fill('black');
  doc.moveDown(2);

  /********************************************
   * Pricing Summary (Subtotal / Fees / Total)
   ********************************************/
  let currentY = doc.y + 10;
  const lineStartX = 267.5;
  const lineEndX = doc.page.width - doc.page.margins.right;

  // Horizontal line
  doc.moveTo(lineStartX, currentY).lineTo(lineEndX, currentY).stroke();
  currentY += 10;

  createPricingSummaryComponent({
    doc,
    isProcessingFee,
    languagePreference,
    currency,
    subTotalAmountNumber,
    currentY,
    processingFee,
    totalPaidAmount,
    lineEndX,
    lineStartX,
    FONT_FAMILY,
    isAdsCampaignTopUp,
    adsBalanceDeduction,
  });

  /********************************************
   * Footer Notes
   ********************************************/
  // Position near bottom
  currentY = doc.page.height - 150;
  doc.fontSize(8).fill('#63635E').font('Helvetica-Bold');
  createFooterComponent({
    doc,
    languagePreference,
    currentY,
    FONT_FAMILY,
  });

  return new Promise((resolve, reject) => {
    doc.on('end', () => {
      const pdfBuffer = Buffer.concat(buffers);
      resolve(pdfBuffer); // Return the buffer
    });

    doc.on('error', (err) => reject(err));
    doc.end(); // Triggers the 'end' event
  });
}

/********************************************
 * Helper Functions
 ********************************************/

module.exports = {
  createReceipt,
};
