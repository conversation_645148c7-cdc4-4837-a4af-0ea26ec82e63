const {
  ENTITY_LANDING_PAGE,
} = require('@/src/utils/memberPortalLinks.utils');

const PRODUCT_TYPE = {
  SUBSCRIPTION: 'subscription',
  PROGRAM: 'program',
  COMMUNITY_EVENTS: 'community_events',
  COURSE: 'course',
  DIGITAL_PRODUCT: 'digital_product',
  SESSION: 'session',
};

const PRODUCT_TYPES = Object.values(PRODUCT_TYPE);

const PRIMARY_GROUPS = [
  { name: 'event', buckets: ['community_events'] },
  { name: 'challenge', buckets: ['program'] },
  { name: 'membership', buckets: ['subscription'] },
  { name: 'courseOrFile', buckets: ['course', 'digital_product'] }, // choose 1 of these
];

const PRODUCT_TYPE_TO_LANDING_PAGE_ENTITY_TYPE_MAP = {
  [PRODUCT_TYPE.COMMUNITY_EVENTS]: ENTITY_LANDING_PAGE.EVENT,
  [PRODUCT_TYPE.PROGRAM]: ENTITY_LANDING_PAGE.CHALLENGE,
  [PRODUCT_TYPE.COURSE]: ENTITY_LANDING_PAGE.COURSE,
  [PRODUCT_TYPE.DIGITAL_PRODUCT]: ENTITY_LANDING_PAGE.DIGITAL_PRODUCT,
  [PRODUCT_TYPE.SESSION]: ENTITY_LANDING_PAGE.SESSION,
};

const PAGE_LIMIT = 12; // UI shows 12 cards
const BUCKET_FETCH_SIZE = 24; // grab extras for “all” shuffle
const PRODUCTS_PER_GROUP = 6;

module.exports = {
  PRODUCT_TYPES,
  PRIMARY_GROUPS,
  PAGE_LIMIT,
  BUCKET_FETCH_SIZE,
  PRODUCTS_PER_GROUP,
  PRODUCT_TYPE,
  PRODUCT_TYPE_TO_LANDING_PAGE_ENTITY_TYPE_MAP,
};
