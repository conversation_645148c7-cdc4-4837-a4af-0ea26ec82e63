/* eslint-disable no-param-reassign */
const TopProductsDaily = require('../../models/topProducts/topProductsDaily.model');
const Community = require('../../communitiesAPI/models/community.model');
const CommunityEvent = require('../../communitiesAPI/models/communityEvents.model');
const CommunityFolder = require('../../communitiesAPI/models/communityFolders.model');
const Program = require('../../models/program/program.model');
const { allPrompts } = require('./allPrompts');
const {
  PRODUCT_TYPES,
  BUCKET_FETCH_SIZE,
  PRODUCTS_PER_GROUP,
  PAGE_LIMIT,
  PRIMARY_GROUPS,
  PRODUCT_TYPE,
  PRODUCT_TYPE_TO_LANDING_PAGE_ENTITY_TYPE_MAP,
} = require('./constants');
const {
  getEntityPublicPageUrl,
  getFullCommunityLink,
} = require('../../utils/memberPortalLinks.utils');

const PRODUCT_SLUG_SEL = {
  subscription: () => '',
  community_events: ({ slug }) => slug,
  session: ({ resourceSlug }) => resourceSlug,
  digital_product: ({ resourceSlug }) => resourceSlug,
  course: ({ resourceSlug }) => resourceSlug,
  program: ({ slug }) => slug,
};

/* type → accessor for the hero image we want to show */
const PRODUCT_IMAGE_SEL = {
  session: (p) => p?.thumbnail ?? '',
  digital_product: (p) => p?.thumbnail ?? '',
  course: (p) => p?.thumbnail ?? '',
  community_events: (p) => p?.bannerImg ?? '',
  program: (p) => p?.cover ?? '',
  subscription: (p) => p?.thumbnailImgData?.mobileImgData?.src ?? '',
};

/**
 * Returns the title, canonical URL and hero image for a product card.
 */
function normaliseProductInfo(product, communitySlug, productType) {
  const getSlug = PRODUCT_SLUG_SEL[productType] ?? (() => '');
  const getImage = PRODUCT_IMAGE_SEL[productType] ?? (() => '');

  const productUrl =
    productType === PRODUCT_TYPE.SUBSCRIPTION
      ? getFullCommunityLink({ communitySlug })
      : getEntityPublicPageUrl({
          communitySlug,
          entityType:
            PRODUCT_TYPE_TO_LANDING_PAGE_ENTITY_TYPE_MAP[productType],
          entitySlug: getSlug(product),
        });

  return {
    productType,
    productTitle: product?.title ?? '',
    productUrl,
    productImage: getImage(product),
  };
}

function cherryPickData(community, product, salesRow) {
  const { productType, sales, isFree, isMXMarket } = salesRow;

  const communityLogo =
    community?.thumbnailImgData?.mobileImgData?.src ?? '';
  const communityBanner =
    community?.fullScreenBannerImgData?.mobileImgProps?.src ?? '';

  return {
    productObjectId: String(product?._id),
    communityLogo,
    communtyBannerImage: communityBanner,
    communityName: community?.title ?? '',
    sales,
    isFree,
    isMXMarket,
    ...normaliseProductInfo(product, community?.link ?? '', productType),
  };
}

async function getLatestSnapshotDate() {
  const row = await TopProductsDaily.findOne({}, { snapshotDate: 1 })
    .sort({ snapshotDate: -1 })
    .lean();
  return row?.snapshotDate;
}

// async function loadBucket(
//   date,
//   productType,
//   isMxn,
//   limit = BUCKET_FETCH_SIZE
// ) {
//   return TopProductsDaily.find(
//     { productType, snapshotDate: date, isMXMarket: isMxn },
//     { _id: 0, __v: 0 }
//   )
//     .sort({ sales: -1 })
//     .limit(limit)
//     .lean();
// }

async function loadBucket(
  range, // { from, to }
  productType,
  isMxn,
  limit = BUCKET_FETCH_SIZE
) {
  return TopProductsDaily.aggregate([
    {
      $match: {
        productType,
        isMXMarket: isMxn,
        snapshotDate: { $gte: range.from, $lte: range.to },
      },
    },
    {
      $group: {
        _id: '$productObjectId',
        productObjectId: { $first: '$productObjectId' },
        communityObjectId: { $first: '$communityObjectId' },
        productType: { $first: '$productType' },
        isFree: { $first: '$isFree' },
        isMXMarket: { $first: '$isMXMarket' },
        sales: { $sum: '$sales' }, //  ← summed sales
      },
    },
    { $sort: { sales: -1 } },
    { $limit: limit },
    { $project: { _id: 0 } }, // match shape of old .find().lean()
  ]);
}

async function loadAllBuckets(range, isMxn) {
  const pairs = await Promise.all(
    PRODUCT_TYPES.map(async (productType) =>
      loadBucket(range, productType, isMxn).then((docs) => [
        productType,
        docs,
      ])
    )
  );
  return Object.fromEntries(pairs);
}

function buildRange(latestSnapshotDate, days = 2) {
  const MS_IN_DAY = 24 * 60 * 60 * 1000;
  const to = new Date(latestSnapshotDate); // inclusive upper-bound
  const from = new Date(to.getTime() - (days - 1) * MS_IN_DAY);
  return { from, to };
}

function makeFreeFilter() {
  const seen = new Set(); // communityIds with a free listing
  return (doc) => {
    if (!doc.isFree) return true; // paid → always accept
    const id = String(doc.communityObjectId);
    if (seen.has(id)) return false; // duplicate free → reject
    seen.add(id); // first free → remember
    return true;
  };
}

function pickBestAcross(buckets, bucketNames, accept) {
  let bestDoc = null;
  let bestBucket = null;

  for (const name of bucketNames) {
    const bucket = buckets[name];
    while (bucket.length) {
      const doc = bucket[0]; // peek fastest candidate
      if (!accept(doc)) {
        bucket.shift();
        // eslint-disable-next-line no-continue
        continue;
      } // drop & continue
      if (!bestDoc || doc.sales > bestDoc.sales) {
        bestDoc = doc;
        bestBucket = name;
      }
      break; // found an acceptable head
    }
  }
  if (bestDoc) buckets[bestBucket].shift(); // remove chosen doc
  return bestDoc;
}

function composeDiverseGroups(buckets) {
  const accept = makeFreeFilter();
  const firstSix = [];
  const secondSix = [];

  for (const target of [firstSix, secondSix]) {
    for (const group of PRIMARY_GROUPS) {
      // basically what we do is that we pick one from each group
      const doc = pickBestAcross(buckets, group.buckets, accept);
      if (doc) target.push(doc);
    }

    const hasCourseOrDigital = () =>
      target.some(
        (d) =>
          d.productType === 'course' || d.productType === 'digital_product'
      );

    if (target.length < PRODUCTS_PER_GROUP) {
      const leftovers = PRODUCT_TYPES.flatMap((b) => buckets[b]).sort(
        (a, b) => b.sales - a.sales
      );

      for (const doc of leftovers) {
        if (target.length === PRODUCTS_PER_GROUP) break;

        const isCourseOrDigital =
          doc.productType === 'course' ||
          doc.productType === 'digital_product';
        // eslint-disable-next-line no-continue
        if (isCourseOrDigital && hasCourseOrDigital()) continue;
        if (accept(doc)) target.push(doc);
      }
    }

    target.sort((a, b) => b.sales - a.sales);
  }

  return { firstSix, secondSix };
}

/* ── 5. hydrator ───────────────────────────────────────────────── */
const PRODUCT_TYPE_TO_COLLECTION = {
  program: 'program',
  community_events: 'community_events',
  session: 'community_folders',
  course: 'community_folders',
  digital_product: 'community_folders',
  subscription: 'community',
};

const COLLECTION_TO_MODEL = {
  community_events: CommunityEvent,
  community_folders: CommunityFolder,
  program: Program,
};

async function hydrateProducts(rows) {
  // 5a. gather IDs
  const idsByCollection = {
    community_events: new Set(),
    community_folders: new Set(),
    program: new Set(),
    community: new Set(),
  };
  const communityIds = new Set();

  rows.forEach(({ productType, productObjectId, communityObjectId }) => {
    const collection = PRODUCT_TYPE_TO_COLLECTION[productType];
    idsByCollection[collection].add(String(productObjectId));
    communityIds.add(String(communityObjectId));
  });

  // 5b. fetch docs in parallel
  const fetchDocs = async (model, ids) =>
    ids.size ? model.find({ _id: { $in: [...ids] } }).lean() : [];
  const [events, folders, programs, communities] = await Promise.all([
    fetchDocs(
      COLLECTION_TO_MODEL.community_events,
      idsByCollection.community_events
    ),
    fetchDocs(
      COLLECTION_TO_MODEL.community_folders,
      idsByCollection.community_folders
    ),
    fetchDocs(COLLECTION_TO_MODEL.program, idsByCollection.program),
    Community.find({ _id: { $in: [...communityIds] } }).lean(),
  ]);

  const toMap = (arr) =>
    Object.fromEntries(arr.map((d) => [String(d._id), d]));
  const maps = {
    community_events: toMap(events),
    community_folders: toMap(folders),
    program: toMap(programs),
    community: toMap(communities),
  };

  // 5c. embed docs
  return rows.map((row) => {
    const collection =
      PRODUCT_TYPE_TO_COLLECTION[row.productType] || 'community_folders';

    const cherryPickedData = cherryPickData(
      maps.community[row.communityObjectId],
      maps[collection][String(row.productObjectId)],
      row,
      row.productType
    );
    return cherryPickedData;
  });
}

async function getTopProductsService(tab, isMxn, aggregationDays = 2) {
  const latest = await getLatestSnapshotDate();
  if (!latest) throw new Error('Top-products snapshot missing');

  const range = buildRange(latest, aggregationDays); // ← new

  // Single category tab
  if (tab !== 'all' && tab !== 'all_products') {
    const docs = await loadBucket(range, tab, isMxn, PAGE_LIMIT);
    return hydrateProducts(docs);
  }

  // “All” tab
  if (tab === 'all') {
    const buckets = await loadAllBuckets(range, isMxn);
    const { firstSix, secondSix } = composeDiverseGroups(buckets);
    const finalDocs = [...firstSix, ...secondSix]; // always 12
    return hydrateProducts(finalDocs);
  }

  if (tab === 'all_products') {
    const [courseSalesData, digitalFileSalesData] = await Promise.all([
      loadBucket(range, 'course', isMxn, PAGE_LIMIT),
      loadBucket(range, 'digital_product', isMxn, PAGE_LIMIT),
    ]);

    // get only 12 from all 24 sorted by sales and make it alternative like one course, one digital product
    const finalDocs = [];
    const maxLength = Math.max(
      courseSalesData.length,
      digitalFileSalesData.length
    );
    for (let i = 0; i < maxLength; i++) {
      if (i < digitalFileSalesData.length) {
        finalDocs.push({ ...digitalFileSalesData[i] });
      }
      if (i < courseSalesData.length) {
        finalDocs.push({ ...courseSalesData[i] });
      }
    }

    return hydrateProducts(finalDocs.slice(0, PAGE_LIMIT));
  }
}

function getPromptTemplate(currentPromptId) {
  // choose a random prompt from the list of allPrompts

  const randomIndex = Math.floor(Math.random() * allPrompts.length);

  const choosenPrompt = allPrompts[randomIndex];
  // check if the random prompt is the same as the current prompt
  if (choosenPrompt?.id === currentPromptId) {
    return getPromptTemplate(currentPromptId); // try again if it's the same
  }
  return allPrompts[randomIndex];
}

module.exports = { getTopProductsService, getPromptTemplate };
