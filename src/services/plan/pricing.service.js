const paymentProviderUtils = require('../../utils/paymentProvider.util');
const priceUtils = require('../../utils/price.util');

const { ParamError } = require('../../utils/error.util');
const {
  DEFAULT_CURRENCY,
  PAYMENT_PROVIDER,
} = require('../../constants/common');

function formatPaymentProviderPriceFromPrice(price = {}) {
  const { id, currency, amount, recurring, defaultPrice = false } = price;
  return {
    id,
    currency,
    interval: recurring.interval,
    intervalCount: recurring.interval_count,
    unitAmount: amount,
    checkoutAmount: amount,
    autoGenerated: false,
    isDefault: defaultPrice,
  };
}

function formatPricesFromNextBillingPlanPrices(
  price = {},
  nextBillingPlanPriceCache = new Map()
) {
  const paymentProviderPrice = formatPaymentProviderPriceFromPrice(price);
  return [...nextBillingPlanPriceCache.values()].map((value) => {
    const nextBillingPrice = {
      unitAmount: value.amount,
      checkoutAmount: value.amount,
      interval: value.recurring.interval,
      intervalCount: value.recurring.interval_count,
      id: value.id,
    };
    return { ...paymentProviderPrice, nextBillingPrice };
  });
}

function formatPricesFromFirstBillingPrices(
  prices = [],
  nextBillingPlanPriceCache = new Map()
) {
  const paymentProviderPrices = prices
    .map(priceUtils.transformAmountToCents)
    .map((price) => {
      const { currency, recurring } = price;

      const key = `${recurring.interval}-${recurring.interval_count}-${currency}`;
      const nextBillingPlanPrice = nextBillingPlanPriceCache.get(key);

      const paymentProviderPrice =
        formatPaymentProviderPriceFromPrice(price);

      if (nextBillingPlanPrice) {
        paymentProviderPrice.nextBillingPrice = {
          unitAmount: nextBillingPlanPrice.amount,
          checkoutAmount: nextBillingPlanPrice.amount,
          interval: nextBillingPlanPrice.recurring.interval,
          intervalCount: nextBillingPlanPrice.recurring.interval_count,
          id: nextBillingPlanPrice.id,
        };
      }
      return paymentProviderPrice;
    });
  return paymentProviderPrices;
}

async function retrievePaymentProviderPrices({
  paymentBackendRpc,
  selectedPaymentProvider,
  nextBillingPlan,
  baseCurrency,
  localCurrency,
  isOnePriceForAllNextBillingPlanPrices,
}) {
  const hasNextBillingPlanPaymentProvider =
    nextBillingPlan?.paymentProviders?.find(
      ({ provider }) => provider === selectedPaymentProvider.provider
    );

  const nextBillingPrices = hasNextBillingPlanPaymentProvider
    ? await paymentBackendRpc.listAllStripeProductPricing({
        stripeProductId: hasNextBillingPlanPaymentProvider.productId,
        paymentProvider: hasNextBillingPlanPaymentProvider.provider,
        localCurrency,
        baseCurrency,
        allCurrency: false,
        onlySelectedCurrency: true,
      })
    : null;

  const nextBillingPlanPriceCache = priceUtils.convertToPriceIntervalCache(
    nextBillingPrices?.price
  );

  const prices = await paymentBackendRpc.listAllStripeProductPricing({
    stripeProductId: selectedPaymentProvider.productId,
    paymentProvider: selectedPaymentProvider.provider,
    localCurrency:
      nextBillingPrices?.price?.[0]?.currency?.toUpperCase() ??
      localCurrency,
    baseCurrency,
    allCurrency: false,
    onlySelectedCurrency: true,
  });

  if (isOnePriceForAllNextBillingPlanPrices) {
    // TODO: To adjust the filter logic if PM decide to do A/B testing
    const transformPrices = prices.price.map(
      priceUtils.transformAmountToCents
    );

    const commonFirstBillingPrice = transformPrices.filter(
      (price) => price.defaultPrice
    )?.[0];

    // We return the same first billing price for all kinds of next billing prices (diff intervals)
    // Eg: monthly: 1/2 weeks -> 29/moth
    //     yearly: 1/2 weeks -> 249/year
    return formatPricesFromNextBillingPlanPrices(
      commonFirstBillingPrice ?? transformPrices[0],
      nextBillingPlanPriceCache
    );
  }

  // We return all first billing prices and match next billing plan price based on interval
  // Eg: monthly: 1/month -> 29/month
  //     yearly: 199/year -> 349/year
  return formatPricesFromFirstBillingPrices(
    prices.price,
    nextBillingPlanPriceCache
  );
}

async function retrievePaymentProviderPrice({
  paymentBackendRpc,
  selectedPaymentProvider,
  priceId,
  localCurrency,
}) {
  const price = await paymentBackendRpc.getStripePrice({
    priceId,
    paymentProvider: selectedPaymentProvider.provider,
    withOtherCurrencies: true,
    localCurrency,
  });

  if (!price) {
    throw new ParamError(
      `Invalid price id ${priceId} for ${localCurrency}`
    );
  }

  return priceUtils.transformAmountToCents(price);
}

function retrieveLocalCurrency(countryInfo, community) {
  const localCurrency = countryInfo?.localisePrice
    ? countryInfo?.currencyCode
    : community?.baseCurrency ?? DEFAULT_CURRENCY;

  return localCurrency;
}

async function retrieveNextBillingPlanLocalCurrency({
  nextBillingPlan,
  localCurrency,
  baseCurrency,
  paymentBackendRpc,
}) {
  if (!nextBillingPlan) {
    return localCurrency;
  }

  // INR currency does not support other local currency
  if (localCurrency === 'INR') {
    return localCurrency;
  }

  const hasNextBillingPlanPaymentProvider =
    nextBillingPlan.paymentProviders?.find(
      ({ provider }) => provider === PAYMENT_PROVIDER.STRIPE_US
    );

  const nextBillingPrices = hasNextBillingPlanPaymentProvider
    ? await paymentBackendRpc.listAllStripeProductPricing({
        stripeProductId: hasNextBillingPlanPaymentProvider.productId,
        paymentProvider: hasNextBillingPlanPaymentProvider.provider,
        localCurrency,
        baseCurrency,
        allCurrency: false,
        onlySelectedCurrency: true,
      })
    : null;

  return (
    nextBillingPrices?.price?.[0]?.currency?.toUpperCase() ?? localCurrency
  );
}

exports.retrievePrices = async ({
  plan,
  localCurrencyEnforced = null,
  community = null,
  countryInfo = null,
  paymentBackendRpc,
}) => {
  const localCurrency =
    localCurrencyEnforced ?? retrieveLocalCurrency(countryInfo, community);

  const {
    paymentProviders,
    nextBillingPlan,
    isOnePriceForAllNextBillingPlanPrices,
  } = plan;

  const baseCurrency = community?.baseCurrency ?? localCurrency;

  let paymentProvider =
    localCurrency === 'INR'
      ? PAYMENT_PROVIDER.STRIPE_INDIA
      : PAYMENT_PROVIDER.STRIPE_US;

  if (community) {
    const { payment_methods: paymentMethods } = community;
    paymentProvider = paymentProviderUtils.retrievePaymentProviderForPlan(
      paymentMethods,
      localCurrency
    );
  }

  const selectedPaymentProvider = paymentProviders.find(
    ({ provider }) => provider === paymentProvider
  );

  if (!selectedPaymentProvider) {
    throw new ParamError(
      `Selected payment provider for ${paymentProvider} not found`
    );
  }

  const paymentProviderPrices = await retrievePaymentProviderPrices({
    paymentBackendRpc,
    selectedPaymentProvider,
    baseCurrency,
    nextBillingPlan,
    localCurrency,
    isOnePriceForAllNextBillingPlanPrices,
  });

  return paymentProviderPrices;
};

exports.retrievePrice = async ({
  plan,
  community,
  countryInfo,
  localCurrencyEnforced,
  priceId,
  paymentBackendRpc,
  retrieveLocalCurrencyFromNextBillingPlan = false,
  nextBillingPlan = null,
}) => {
  if (!priceId) return null;
  const localCurrency =
    localCurrencyEnforced ?? retrieveLocalCurrency(countryInfo, community);

  const { paymentProviders } = plan;

  const { payment_methods: paymentMethods } = community;

  const paymentProvider =
    paymentProviderUtils.retrievePaymentProviderForPlan(
      paymentMethods,
      localCurrency
    );

  const selectedPaymentProvider = paymentProviders.find(
    ({ provider }) => provider === paymentProvider
  );

  if (!selectedPaymentProvider) {
    throw new ParamError(
      `Selected payment provider for ${paymentProvider} not found`
    );
  }

  let localCurrencyToUse = localCurrency;

  if (retrieveLocalCurrencyFromNextBillingPlan) {
    localCurrencyToUse = await retrieveNextBillingPlanLocalCurrency({
      nextBillingPlan,
      localCurrency,
      baseCurrency: community.baseCurrency,
      paymentBackendRpc,
    });
  }

  const paymentProviderPrice = await retrievePaymentProviderPrice({
    paymentBackendRpc,
    selectedPaymentProvider,
    priceId,
    localCurrency: localCurrencyToUse,
  });

  return paymentProviderPrice;
};

exports.retrieveSpecificPrices = async ({
  plan,
  planOrder,
  paymentBackendRpc,
}) => {
  const { localCurrency, interval, intervalCount } = planOrder;

  const { paymentProviders } = plan;

  const selectedPaymentProvider = paymentProviders.find(
    ({ provider }) => provider === planOrder.paymentProviderForPriceId
  );

  if (!selectedPaymentProvider) {
    throw new ParamError(
      `Selected payment provider for ${planOrder.paymentProviderForPriceId} not found`
    );
  }

  const paymentProviderPrices = await retrievePaymentProviderPrices({
    paymentBackendRpc,
    selectedPaymentProvider,
    baseCurrency: localCurrency,
    nextBillingPlan: null,
    localCurrency,
  });

  return paymentProviderPrices.map((price) => {
    const newPrice = {
      ...price,
      isCurrentPrice:
        price.interval.toUpperCase() === interval.toUpperCase() &&
        price.intervalCount === intervalCount,
    };

    return newPrice;
  });
};

exports.retrieveSpecificPrice = async ({
  priceId,
  plan,
  planOrder,
  paymentBackendRpc,
}) => {
  const { localCurrency } = planOrder;

  const { paymentProviders } = plan;

  const selectedPaymentProvider = paymentProviders.find(
    ({ provider }) => provider === planOrder.paymentProviderForPriceId
  );

  if (!selectedPaymentProvider) {
    throw new ParamError(
      `Selected payment provider for ${planOrder.paymentProviderForPriceId} not found`
    );
  }

  const price = await retrievePaymentProviderPrice({
    paymentBackendRpc,
    selectedPaymentProvider,
    priceId,
    localCurrency,
  });

  const { recurring, amount, defaultPrice = false, id, currency } = price;

  const paymentProviderPrice = {
    id,
    currency,
    interval: recurring.interval,
    intervalCount: recurring.interval_count,
    unitAmount: amount,
    checkoutAmount: amount,
    autoGenerated: false,
    isDefault: defaultPrice,
  };

  return paymentProviderPrice;
};
