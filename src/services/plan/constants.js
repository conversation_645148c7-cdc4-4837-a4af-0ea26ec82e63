const ORDER_STATUS = {
  DRAFT: 'draft',
  CURRENT: 'current',
  CANCELLED: 'cancelled',
  PENDING: 'pending',
  INVALID: 'invalid', // cancelled before plan activiated (pending > invalid)
};

const ENTITY_TYPE = {
  PRO: 'PRO',
  PLATINUM: 'PLATINUM',
};

const ENTITY_TYPE_RANKS = {
  PLATINUM: 0,
  PRO: 1,
};

const TIER_CHANGE_CATEGORY = {
  UPGRADE: 'UPGRADE',
  DOWNGRADE: 'DOWNGRADE',
};

const PLAN_FEATURE = {
  STORAGE_UPGRADE_IN_GB: 'STORAGE_UPGRADE_IN_GB',
};

const PLAN_BILLING_MODEL = {
  RECURRING: 'RECURRING',
};

const PLAN_ADHOC_STATUS = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
};

const NASIO_PRO_CANCELLATION_REASON = {
  EXPLORE: 'pro-cancellation-reason-explore',
  NOT_USING_ENOUGH: 'pro-cancellation-reason-not-using-enough',
  NOT_ENOUGH_VALUE_FROM_FEATURES:
    'pro-cancellation-reason-not-enough-value-from-features',
  ONLY_NEED_FOR_SHORT_TERM_PROJECT:
    'pro-cancellation-reason-only-need-for-short-term-project',
  OTHER: 'pro-cancellation-reason-other',
};

const NASIO_PRO_CANCELLATION_REASON_WITH_EXPLANATIONS = [
  NASIO_PRO_CANCELLATION_REASON.EXPLORE,
  NASIO_PRO_CANCELLATION_REASON.OTHER,
];

const NASIO_PRO_COMMUNITY_CODE = 'NAS.IO_MILLION_DOLLAR_CLUB';

const NASIO_AI_SCHOOL_COMMUNITY_CODE = 'AI_ENTREPRENEUR_SCHOOL';

const ORDER_PAYMENT_DETAIL_STATUSES = {
  INCOMPLETE: 'incomplete',
  PENDING: 'pending',
  SUCCESS: 'success',
  FAILED: 'failed',
  REFUNDED: 'refunded',
  REFUNDED_FAILED: 'refunded_failed',
};

const STRIPE_ADVANCE_PROCESSING_BUFFER_HOURS = 24;

module.exports = {
  ORDER_STATUS,
  ENTITY_TYPE,
  NASIO_AI_SCHOOL_COMMUNITY_CODE,
  PLAN_FEATURE,
  PLAN_BILLING_MODEL,
  PLAN_ADHOC_STATUS,
  NASIO_PRO_CANCELLATION_REASON,
  NASIO_PRO_CANCELLATION_REASON_WITH_EXPLANATIONS,
  NASIO_PRO_COMMUNITY_CODE,
  ENTITY_TYPE_RANKS,
  TIER_CHANGE_CATEGORY,
  ORDER_PAYMENT_DETAIL_STATUSES,
  STRIPE_ADVANCE_PROCESSING_BUFFER_HOURS,
};
