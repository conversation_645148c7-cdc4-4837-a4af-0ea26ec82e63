const { DateTime } = require('luxon');

const { PAYMENT_PROVIDER } = require('../../constants/common');
const { ORDER_STATUS, TIER_CHANGE_CATEGORY } = require('./constants');
const { ParamError } = require('../../utils/error.util');
const { PAYMENT_STATUSES } = require('../../communitiesAPI/constants');

function validateNextBillingDateWithinThreshold({ planOrder }) {
  const currentDateTime = DateTime.utc();

  const nextBillingDate = DateTime.fromJSDate(planOrder.nextBillingDate);

  const thresholdDateTime = nextBillingDate.minus({ hours: 2 });

  if (currentDateTime > thresholdDateTime) {
    throw new ParamError(
      'Plan order changes are not allowed within 2 hours of the next billing date'
    );
  }
}

function validateActiveSubscription({
  planOrder,
  bypassChangeTier = false,
}) {
  const { status } = planOrder;

  // Allow update payment method for downgrade as the current subscription will be cancelled
  // Upgrade can just based on active plan order status
  if (
    bypassChangeTier &&
    status === ORDER_STATUS.CANCELLED &&
    planOrder.cancelledAt > new Date() &&
    planOrder.nextPlanOrder
  ) {
    return;
  }

  if (status !== ORDER_STATUS.CURRENT) {
    throw new ParamError('Plan order is not active');
  }
}

function validatePaidSubscription({ planOrder }) {
  const { paymentProviderSubscriptionId } = planOrder;

  if (!paymentProviderSubscriptionId) {
    throw new ParamError('Only available for paid plan order');
  }
}

function validateNoRecentPlanChange({ planOrder }) {
  const { planHistory = [], billingCycle } = planOrder;

  if (planHistory.length === 0) {
    return;
  }

  // Filter out revoked plan changes
  const nonRevokedHistory = planHistory.filter(
    (history) => !history.revoked
  );

  if (nonRevokedHistory.length === 0) {
    return;
  }

  const latestPlanHistory = nonRevokedHistory.sort(
    (a, b) => b.billingCycle - a.billingCycle
  )[0];

  if (latestPlanHistory.billingCycle === billingCycle) {
    throw new ParamError(
      'The plan order has already been changed for the current billing cycle'
    );
  }
}

function validateSingleCurrencyPaymentProvider({ planOrder, price }) {
  const { paymentProvider } = planOrder.paymentDetails;
  const { localCurrency } = planOrder;

  if (!price) {
    return;
  }

  const singleCurrencyOnlyPaymentProviders = [
    PAYMENT_PROVIDER.STRIPE,
    PAYMENT_PROVIDER.STRIPE_US,
    PAYMENT_PROVIDER.STRIPE_INDIA,
    PAYMENT_PROVIDER.EBANX,
  ];

  if (
    singleCurrencyOnlyPaymentProviders.includes(paymentProvider) &&
    price.currency.toUpperCase() !== localCurrency
  ) {
    throw new ParamError(
      'This plan order does not allow multiple currencies'
    );
  }
}

function validateRecurringSubscription({ planOrder }) {
  const recurringPurchase =
    planOrder.paymentDetails.recurringPurchase ?? true;

  if (!recurringPurchase) {
    throw new ParamError('Only available for recurring subscription');
  }
}

function validateSamePurchaser({ learnerObjectId, planOrder }) {
  if (
    learnerObjectId.toString() !== planOrder.learnerObjectId.toString()
  ) {
    throw new ParamError('Only purchaser able to change plan');
  }
}

function validateSuccessPayment({ planOrder }) {
  if (
    [
      PAYMENT_STATUSES.FAILED,
      PAYMENT_STATUSES.INCOMPLETE,
      PAYMENT_STATUSES.PENDING,
    ].includes(planOrder.paymentDetails.status)
  ) {
    throw new ParamError('Only available for success payment');
  }
}

function validateNextBillingDateWithinThresholdForTierChanges({
  planOrder,
}) {
  const currentDateTime = DateTime.utc();

  const nextBillingDate = DateTime.fromJSDate(planOrder.nextBillingDate);

  const thresholdDateTime = nextBillingDate.minus({ days: 1 });

  if (currentDateTime > thresholdDateTime) {
    throw new ParamError(
      'Plan order tier changes are not allowed within 24 hours of the next billing date'
    );
  }
}

// eslint-disable-next-line no-unused-vars
function validateNoRecentTierChange({ planOrder }) {
  const { billingCycle, tierChangeCategory } = planOrder;
  if (!Object.values(TIER_CHANGE_CATEGORY).includes(tierChangeCategory)) {
    return;
  }
  if (billingCycle === 1) {
    throw new ParamError(
      'The plan order swapped plan type for the current billing cycle'
    );
  }
}

function validatePendingOrder({ planOrder, pendingPlanOrder }) {
  const { tierChangeCategory } = planOrder;
  if (!Object.values(TIER_CHANGE_CATEGORY).includes(tierChangeCategory)) {
    return;
  }
  if (pendingPlanOrder) {
    throw new ParamError(
      'As existing pending plan order while current plan is an active cancelled plan order'
    );
  }
}

exports.isPlanPurchaser = ({
  planOrder,
  learnerObjectId,
  throwError = false,
}) => {
  try {
    validateSamePurchaser({ learnerObjectId, planOrder });

    return true;
  } catch (err) {
    if (throwError) {
      throw err;
    }
    return false;
  }
};

exports.checkIfCanCancelPlan = ({
  planOrder,
  learnerObjectId,
  throwError = false,
}) => {
  try {
    validateSamePurchaser({ learnerObjectId, planOrder });
    validatePaidSubscription({ planOrder });
    validateActiveSubscription({ planOrder });

    return true;
  } catch (err) {
    if (throwError) {
      throw err;
    }

    return false;
  }
};

exports.checkIfCanChangeTier = ({
  pendingPlanOrder,
  planOrder,
  price,
  learnerObjectId,
  throwError = false,
}) => {
  try {
    validatePendingOrder({ pendingPlanOrder, planOrder });
    validateNextBillingDateWithinThresholdForTierChanges({ planOrder });
    // validateNoRecentTierChange({ planOrder });
    validateActiveSubscription({ planOrder });
    validatePaidSubscription({ planOrder });
    validateSingleCurrencyPaymentProvider({ planOrder, price });
    validateRecurringSubscription({ planOrder });
    validateSamePurchaser({ learnerObjectId, planOrder });
    validateSuccessPayment({ planOrder });

    return true;
  } catch (err) {
    if (throwError) {
      throw err;
    }
    return false;
  }
};

exports.checkIfCanChangePlan = ({
  pendingPlanOrder,
  planOrder,
  price,
  learnerObjectId,
  throwError = false,
}) => {
  try {
    validatePendingOrder({ pendingPlanOrder, planOrder });
    validateNextBillingDateWithinThreshold({ planOrder });
    validateActiveSubscription({ planOrder });
    validatePaidSubscription({ planOrder });
    validateNoRecentPlanChange({ planOrder });
    validateSingleCurrencyPaymentProvider({ planOrder, price });
    validateRecurringSubscription({ planOrder });
    validateSamePurchaser({ learnerObjectId, planOrder });
    validateSuccessPayment({ planOrder });

    return true;
  } catch (err) {
    if (throwError) {
      throw err;
    }

    return false;
  }
};

exports.checkIfCanChangePaymentMethod = ({
  planOrder,
  learnerObjectId,
  throwError = false,
}) => {
  try {
    validateActiveSubscription({ planOrder, bypassChangeTier: true });
    validatePaidSubscription({ planOrder });
    validateRecurringSubscription({ planOrder });
    validateSamePurchaser({ learnerObjectId, planOrder });

    return true;
  } catch (err) {
    if (throwError) {
      throw err;
    }

    return false;
  }
};

exports.checkIfCanUpdateClientInfo = ({
  planOrder,
  learnerObjectId,
  throwError = false,
}) => {
  try {
    validateActiveSubscription({ planOrder });
    validateSamePurchaser({ learnerObjectId, planOrder });

    return true;
  } catch (err) {
    if (throwError) {
      throw err;
    }

    return false;
  }
};
