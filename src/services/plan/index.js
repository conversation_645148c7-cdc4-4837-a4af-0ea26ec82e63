const {
  getCountryInfoByCountryFromDB,
} = require('../countryInfoMapping/countryInfoMapping.service');
const PaymentBackendRpc = require('../../rpc/paymentBackend');
const { DEFAULT_COUNTRY } = require('../../constants/common');
const { ForbiddenError, ParamError } = require('../../utils/error.util');
const CommunitySubscriptionService = require('../../communitiesAPI/services/common/communitySubscriptions.service');

const planPriceService = require('./planPrices.service');
const commonService = require('./common.service');
const planCancellationReasonService = require('./planCancellationReason.service');
const planService = require('./plan.service');
const pricingService = require('./pricing.service');
const planOrderService = require('./planOrder.service');
const billingHistoryService = require('./billingHistory');
const planTransactionService = require('./planTransaction.service');
const paymentMethodService = require('./paymentMethod.service');
const communityService = require('./community.service');
const getInspiredService = require('../getInspired/getInspired.service');
const referralRewardService = require('./referralReward.service');
const subscriptionService = require('./subscription.service');
const PrimaryMongooseConnection = require('../../rpc/primaryMongooseConnection');
const {
  ORDER_STATUS,
  NASIO_PRO_COMMUNITY_CODE,
  ENTITY_TYPE,
  PLAN_ADHOC_STATUS,
} = require('./constants');
const PlanModel = require('../../models/plan/communityPlan.model');
const CommunityModel = require('../../communitiesAPI/models/community.model');

async function validateIsManagerRole({ communityObjectId, email }) {
  const isManagerRole = await commonService.isManagerRole({
    communityObjectId,
    email,
  });

  if (!isManagerRole) {
    throw new ForbiddenError('Only managers can have access');
  }
}

function validatePlanOrderIsActive(planOrder) {
  if (planOrder.status === ORDER_STATUS.CURRENT) {
    return true;
  }

  if (
    planOrder.status === ORDER_STATUS.CANCELLED &&
    planOrder.cancelledAt > new Date()
  ) {
    return true;
  }

  return false;
}

/**
 * Formats the plan prices to be returned to FE.
 * @param {Array} prices - Array of plan prices.
 * @returns {Array} - Formatted plan prices.
 */
function formatPlanPrices({
  plan,
  planPrices,
  alreadySubscribed,
  isCommunityReferralCodeApplied,
  referrer,
}) {
  return {
    _id: plan._id,
    type: plan.type,
    entityType: plan.entityType,
    isActive: plan.isActive,
    billingModel: plan.billingModel,
    pricing: planPrices,
    alreadySubscribed,
    isCommunityReferralCodeApplied,
    referrer,
  };
}

exports.retrievePlanPrices = async ({
  communityObjectId,
  entityType,
  country = DEFAULT_COUNTRY,
  email,
  communityReferralCode,
}) => {
  const [countryInfo, community, existingPlanOrder] = await Promise.all([
    getCountryInfoByCountryFromDB(country),
    commonService.retrieveActiveCommunity(communityObjectId),
    planOrderService.retrieveExistingPlanOrder({
      communityObjectId,
    }),
  ]);
  const {
    plan,
    planPrices,
    alreadySubscribed,
    isCommunityReferralCodeApplied,
    referrer,
  } = await planPriceService.retrievePlanPrices({
    countryInfo,
    community,
    existingPlanOrder,
    entityType,
    email,
    communityReferralCode,
  });

  return formatPlanPrices({
    plan,
    planPrices,
    alreadySubscribed,
    isCommunityReferralCodeApplied,
    referrer,
  });
};

exports.retrievePlanInfo = async ({
  communityObjectId,
  email,
  learnerObjectId,
}) => {
  const [
    existingPlanOrder,
    pendingPlanOrder,
    planTransactions,
    community,
  ] = await Promise.all([
    planOrderService.retrieveExistingPlanOrder({
      communityObjectId,
    }),
    planOrderService.retrievePendingPlanOrder({ communityObjectId }),
    planTransactionService.retrieveInboundPlanTransactions({
      communityObjectId,
    }),
    CommunityModel.findById(communityObjectId).lean(),
    validateIsManagerRole({
      communityObjectId,
      email,
    }),
  ]);

  const billingHistory = await billingHistoryService.getBillingHistory(
    planTransactions
  );

  if (!existingPlanOrder) {
    let isAdhoc = false;
    if (community?.config?.planType) {
      const planAdhocRecord =
        await planOrderService.retrieveExistingPlanAdhocRecord({
          communityObjectId,
        });
      if (planAdhocRecord) {
        isAdhoc = true;
      }
    }
    return planOrderService.transformToPlanInfoForNonPaidPlan({
      billingHistory,
      learnerObjectId,
      isAdhoc,
    });
  }

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  const [paymentMethod, plan] = await Promise.all([
    paymentMethodService.retrievePaymentMethod(existingPlanOrder),
    PlanModel.findById(existingPlanOrder.planObjectId).lean(),
  ]);

  let nextBillingPrice;
  const isTrial = existingPlanOrder.isOnTrial;
  const latestBillingTransaction = billingHistory?.[0];
  const isLatestBillingTransactionTrial =
    latestBillingTransaction?.isRenewalPayment === false;

  if (isTrial && !isLatestBillingTransactionTrial) {
    const nextBillingPlan = await PlanModel.findById(
      plan.nextBillingPlanObjectId
    ).lean();
    if (existingPlanOrder.metadata?.nextBillingPriceId) {
      nextBillingPrice = await pricingService.retrieveSpecificPrice({
        priceId: existingPlanOrder.metadata?.nextBillingPriceId,
        plan: nextBillingPlan,
        planOrder: existingPlanOrder,
        paymentBackendRpc,
      });
    } else {
      const prices = await pricingService.retrieveSpecificPrices({
        plan: nextBillingPlan,
        planOrder: existingPlanOrder,
        paymentBackendRpc,
      });
      nextBillingPrice = prices.find((price) => price.isCurrentPrice);
    }
  }
  let referrer;
  let planPrices;
  let pendingReferrer;

  if (existingPlanOrder.referrerCommunityObjectId) {
    const referrerCommunity = await CommunityModel.findById(
      existingPlanOrder.referrerCommunityObjectId,
      { _id: 1, title: 1, link: 1, thumbnailImgData: 1 }
    ).lean();

    const planPricesInfo = await planPriceService.retrievePlanPrices({
      countryInfo: null,
      community,
      existingPlanOrder,
      entityType: existingPlanOrder.entityType,
      email,
      communityReferralCode: existingPlanOrder.communityReferralCode,
      bypassReferralCodeValidation: true,
    });
    planPrices = planPricesInfo.planPrices;

    if (planPricesInfo.referrer) {
      referrer = {
        title: referrerCommunity.title,
        link: referrerCommunity.link,
        profileImage:
          referrerCommunity.thumbnailImgData?.desktopImgData?.src,
        communityObjectId: referrerCommunity._id,
        referralRewardTemplateObjectId:
          planPricesInfo.referrer.referralRewardTemplateObjectId,
        communityReferralCode:
          planPricesInfo.referrer.communityReferralCode,
      };
    }
  }

  if (pendingPlanOrder?.referrerCommunityObjectId) {
    const referrerCommunity = await CommunityModel.findById(
      pendingPlanOrder.referrerCommunityObjectId,
      {
        _id: 1,
        title: 1,
        link: 1,
        thumbnailImgData: 1,
        communityReferralCode: 1,
      }
    ).lean();

    if (referrerCommunity) {
      pendingReferrer = {
        title: referrerCommunity.title,
        link: referrerCommunity.link,
        profileImage:
          referrerCommunity.thumbnailImgData?.desktopImgData?.src,
        communityObjectId: referrerCommunity._id,
        communityReferralCode: referrerCommunity.communityReferralCode,
      };
    }
  }

  return planOrderService.transformToPlanInfo({
    pendingPlanOrder,
    pendingReferrer,
    referrer,
    planOrder: existingPlanOrder,
    paymentMethod,
    billingHistory,
    learnerObjectId,
    nextBillingPrice,
    isTrial,
    isLatestBillingTransactionTrial,
    latestBillingTransaction,
    planPrices,
  });
};

exports.retrievePlanCancelReasons = async ({
  languagePreference = 'en',
}) => {
  const reasons =
    await planCancellationReasonService.retrieveValidCancellationReasons();

  const formattedReasons = reasons.map((reason) => {
    const final = {
      key: reason.key,
      label: reason.label?.[languagePreference] ?? reason.label.en ?? '',
      enableUserInput: reason.enableUserInput,
    };
    if (reason.enableUserInput) {
      final.userInput = {
        isRequired:
          reason.userInput?.isRequired !== null
            ? reason.userInput?.isRequired
            : true,
        placeholder:
          reason.userInput?.placeholder?.[languagePreference] ??
          reason.userInput?.placeholder?.en ??
          '',
      };
    }
    return final;
  });
  return formattedReasons;
};

exports.cancelPlan = async ({
  communityObjectId,
  cancellationReasons,
  email,
}) => {
  const [existingPlanOrder, formattedCancellationReasons] =
    await Promise.all([
      planOrderService.retrieveExistingPlanOrder({
        communityObjectId,
      }),
      planCancellationReasonService.validateAndFormatCancellationReasons(
        cancellationReasons
      ),
      validateIsManagerRole({
        communityObjectId,
        email,
      }),
    ]);

  const updatedPlanOrder = await planOrderService.cancelExistingPlanOrder(
    existingPlanOrder,
    formattedCancellationReasons
  );

  return updatedPlanOrder;
};

exports.revokeCancellation = async ({ communityObjectId, email }) => {
  const [existingPlanOrder] = await Promise.all([
    planOrderService.retrieveExistingPlanOrder({
      communityObjectId,
    }),
    validateIsManagerRole({
      communityObjectId,
      email,
    }),
  ]);

  const updatedPlanOrder = await planOrderService.revokeCancelledPlanOrder(
    existingPlanOrder
  );

  return updatedPlanOrder;
};

exports.revokePlanChange = async ({ communityObjectId, email }) => {
  const [existingPlanOrder] = await Promise.all([
    planOrderService.retrieveExistingPlanOrder({
      communityObjectId,
    }),
    validateIsManagerRole({
      communityObjectId,
      email,
    }),
  ]);

  const updatedPlanOrder = await planOrderService.revokePlanChangeOrder(
    existingPlanOrder
  );

  return updatedPlanOrder;
};

async function addSubscriptionToProCommunity({
  communityObjectId,
  email,
}) {
  const [purchaserLearner, purchasedCommunity, nasioProCommunity] =
    await Promise.all([
      commonService.retrieveLearnerByEmail(email),
      commonService.retrieveActiveCommunity(communityObjectId),
      commonService.retrieveCommunityByCode(NASIO_PRO_COMMUNITY_CODE),
    ]);

  const purchasingLearnerEmail = purchaserLearner.email;
  const purchasedCommunityOwnerEmail = purchasedCommunity.createdBy;

  const communityOwnerLearner = await commonService.retrieveLearnerByEmail(
    purchasedCommunityOwnerEmail
  );

  const communityOwnerSubParams = {
    communityCode: nasioProCommunity.code,
    learnerObjectId: communityOwnerLearner._id,
    learnerId: communityOwnerLearner.learnerId,
    email: communityOwnerLearner.email,
  };

  const subscriptionOptions = {
    sendNotification: true,
    bypassPendingApproval: true,
  };

  const subscriptions = [];

  const communityOwnerSubscription =
    await CommunitySubscriptionService.findOneOrCreateSubscription(
      communityOwnerSubParams,
      nasioProCommunity,
      subscriptionOptions
    );

  subscriptions.push(communityOwnerSubscription);

  if (purchasingLearnerEmail !== purchasedCommunityOwnerEmail) {
    // add purchasingLearnerEmail as member to pro community
    const subscriptionDoc = {
      communityCode: nasioProCommunity.code,
      learnerObjectId: purchaserLearner._id,
      learnerId: purchaserLearner.learnerId,
      email: purchaserLearner.email,
    };

    const purchasingLearnerSubscription =
      await CommunitySubscriptionService.findOneOrCreateSubscription(
        subscriptionDoc,
        nasioProCommunity,
        subscriptionOptions
      );

    subscriptions.push(purchasingLearnerSubscription);
  }

  return subscriptions;
}

exports.handleProPlanPurchaseSuccess = async ({ planOrderObjectId }) => {
  const planOrder = await planOrderService.retrievePlanOrder(
    planOrderObjectId
  );

  if (!planOrder) {
    throw new ParamError(
      `Plan order for id: ${planOrderObjectId} not found`
    );
  }

  const isActivePlanOrder = validatePlanOrderIsActive(planOrder);
  if (!isActivePlanOrder) {
    throw new ParamError(
      `Plan order for id: ${planOrder._id} is not active`
    );
  }

  const purchaserLearnerObjectId = planOrder.learnerObjectId;
  const purchasedCommunityObjectId = planOrder.communityObjectId;

  const [purchaserLearner, purchasedCommunity] = await Promise.all([
    commonService.retrieveLearner(purchaserLearnerObjectId),
    commonService.retrieveActiveCommunity(purchasedCommunityObjectId),
  ]);

  // verify purchased community is a pro community
  if (
    ![ENTITY_TYPE.PRO, ENTITY_TYPE.PLATINUM].includes(
      purchasedCommunity.config.planType
    )
  ) {
    throw new ParamError('Community is not a pro community');
  }

  const addedSubscriptions = await addSubscriptionToProCommunity({
    communityObjectId: purchasedCommunityObjectId,
    email: purchaserLearner.email,
  });

  return addedSubscriptions;
};

exports.handleCancelCommunityPlanSuccess = async ({
  planOrderObjectId,
}) => {
  // verify plan order is of status cancelled
  const planOrder = await planOrderService.retrievePlanOrder(
    planOrderObjectId
  );
  if (!planOrder) {
    throw new ParamError('Plan order not found');
  }
  if (planOrder.status !== ORDER_STATUS.CANCELLED) {
    throw new ParamError('Plan order is not cancelled');
  }

  const communityObjectId = planOrder.communityObjectId;
  const purchaserLearnerObjectId = planOrder.learnerObjectId;

  const result =
    await subscriptionService.removeLearnerAndCommOwnerFromProCommunity({
      purchaserLearnerObjectId,
      communityObjectId,
      options: {
        removalReason: 'Community pro plan cancelled',
      },
    });

  return result;
};

exports.handleDeactivateAdhocProPlan = async ({
  communityObjectId,
  email,
}) => {
  const adhocRecordLearner = await commonService.retrieveLearnerByEmail(
    email
  );

  if (!adhocRecordLearner) {
    throw new ParamError(`Learner not found for email ${email}`);
  }

  const result =
    await subscriptionService.removeLearnerAndCommOwnerFromProCommunity({
      purchaserLearnerObjectId: adhocRecordLearner._id,
      communityObjectId,
      options: {
        removalReason: 'Adhoc pro record deactivated',
      },
    });

  return result;
};

async function retrieveFeaturesAndEntityType({
  planObjectId = null,
  planOrderObjectId = null,
  fromAdhoc = false,
}) {
  if (fromAdhoc && !planObjectId) {
    throw new ParamError('Plan id is required');
  }

  if (!fromAdhoc && !planOrderObjectId) {
    throw new ParamError('Plan order id is required');
  }

  if (fromAdhoc) {
    const plan = await planService.retrievePlanFromPlanObjectId(
      planObjectId
    );

    return {
      features: plan.features,
      entityType: plan.entityType,
    };
  }

  const planOrder = await planOrderService.retrievePlanOrder(
    planOrderObjectId
  );

  if (!planOrder) {
    throw new ParamError('Plan order not found');
  }

  return {
    previousPlanOrder: planOrder.previousPlanOrder,
    features: planOrder.features,
    entityType: planOrder.entityType,
  };
}

exports.switchEnrollPlanOrder = async ({
  planObjectId = null,
  planOrderObjectId = null,
  existingPlanObjectId = null,
  communityObjectId,
  fromAdhoc,
}) => {
  const [{ features = [], entityType }, community] = await Promise.all([
    retrieveFeaturesAndEntityType({
      planObjectId,
      planOrderObjectId,
      fromAdhoc,
    }),
    commonService.retrieveActiveCommunity(communityObjectId),
    getInspiredService.generateAITemplateForCommunity({
      communityId: communityObjectId,
    }),
  ]);

  const existingPlanInfo = {};
  const newPlanInfo = { features, entityType };
  if (fromAdhoc) {
    const [planOrder, pendingPlanOrder] = await Promise.all([
      planOrderService.retrieveExistingPlanOrder({
        communityObjectId,
        entityType,
      }),
      planOrderService.retrievePendingPlanOrder({
        communityObjectId,
      }),
    ]);

    if (planOrder || pendingPlanOrder) {
      throw new ParamError('Plan order already exists');
    }

    const planDetails = retrieveFeaturesAndEntityType({
      planObjectId: existingPlanObjectId,
      fromAdhoc,
    });
    existingPlanInfo.features = planDetails.features ?? [];
  } else {
    existingPlanInfo.features = [];
  }

  const result = await communityService.switchEnrollPlanOrder({
    community,
    communityObjectId,
    existingPlanInfo,
    newPlanInfo,
  });

  const isUpdated = result.modifiedCount > 0;

  return { isUpdated };
};

exports.enrollPlanOrder = async ({
  planObjectId = null,
  planOrderObjectId = null,
  communityObjectId,
  fromAdhoc = false,
}) => {
  const [{ features, entityType }, community] = await Promise.all([
    retrieveFeaturesAndEntityType({
      planObjectId,
      planOrderObjectId,
      fromAdhoc,
    }),
    commonService.retrieveActiveCommunity(communityObjectId),
    getInspiredService.generateAITemplateForCommunity({
      communityId: communityObjectId,
    }),
  ]);

  if (fromAdhoc) {
    const planOrder = await planOrderService.retrieveExistingPlanOrder({
      communityObjectId,
      entityType,
    });

    if (planOrder) {
      throw new ParamError('Plan order already exists');
    }
  }

  const result = await communityService.enrollPlanOrder({
    community,
    features,
    entityType,
    communityObjectId,
  });

  const isUpdated = result.modifiedCount > 0;

  return { isUpdated };
};

exports.unenrollPlanOrder = async ({
  planObjectId,
  planOrderObjectId,
  communityObjectId,
  fromAdhoc,
}) => {
  const [{ features, entityType }, community] = await Promise.all([
    retrieveFeaturesAndEntityType({
      planObjectId,
      planOrderObjectId,
      fromAdhoc,
    }),
    commonService.retrieveCommunity(communityObjectId),
  ]);

  if (fromAdhoc) {
    const planOrder = await planOrderService.retrieveExistingPlanOrder({
      communityObjectId,
      entityType,
    });

    if (planOrder) {
      throw new ParamError('Plan order already exists');
    }
  } else {
    const planAdhocRecord =
      await planOrderService.retrieveExistingPlanAdhocRecord({
        communityObjectId,
      });

    if (planAdhocRecord) {
      return { isUpdated: false }; // Prevent webhook from retrying
    }
  }

  const result = await communityService.cancelPlanOrder({
    community,
    features,
    entityType,
    communityObjectId,
  });

  const isUpdated = result.modifiedCount > 0;

  return { isUpdated };
};

exports.processAdhocEnrollments = async ({ adhocRecords, addedBy }) => {
  const maxLimit = 1000;

  if (adhocRecords.length > maxLimit) {
    throw new ParamError(
      `Too many records. Max limit is ${maxLimit}, please split into smaller batches`
    );
  }

  await Promise.all(
    adhocRecords.map(async (adhocRecord) => {
      // switchedTier (switch)
      // status === Active && !switchedTier (enroll)
      // status !== Active && !switchedTier !created (unenroll)
      // status !== Active && !switchedTier created (ignore since didnt have enrollment in the first place)
      const {
        existingPlanAdhocRecord,
        planAdhocRecord,
        created,
        switchedTier,
      } = await planOrderService.createOrUpdatePlanAdhocRecord({
        adhocRecord,
        addedBy,
      });

      if (!planAdhocRecord) {
        return;
      }

      if (switchedTier) {
        // go through switchTier flow
        await this.switchEnrollPlanOrder({
          planOrderObjectId: null,
          planObjectId: planAdhocRecord.planObjectId,
          existingPlanObjectId: existingPlanAdhocRecord.planObjectId,
          communityObjectId: planAdhocRecord.communityObjectId,
          fromAdhoc: true,
        });
      } else if (adhocRecord.status === PLAN_ADHOC_STATUS.ACTIVE) {
        await this.enrollPlanOrder({
          planObjectId: planAdhocRecord.planObjectId,
          planOrderObjectId: null,
          communityObjectId: planAdhocRecord.communityObjectId,
          fromAdhoc: true,
        });

        await addSubscriptionToProCommunity({
          communityObjectId: planAdhocRecord.communityObjectId,
          email: planAdhocRecord.creatorEmail,
        });
      } else if (!created) {
        await this.unenrollPlanOrder({
          planObjectId: planAdhocRecord.planObjectId,
          planOrderObjectId: null,
          communityObjectId: planAdhocRecord.communityObjectId,
          fromAdhoc: true,
        });

        await this.handleDeactivateAdhocProPlan({
          communityObjectId: planAdhocRecord.communityObjectId,
          email: planAdhocRecord.creatorEmail,
        });
      }
    })
  );
};

exports.cancelAllAdhocPlan = async ({ email }) => {
  const planAdhocRecords =
    await planOrderService.cancelAllPlanAdhocRecords({
      email,
    });

  await Promise.all(
    planAdhocRecords.map(async (planAdhocRecord) => {
      if (!planAdhocRecord) {
        return;
      }

      await this.unenrollPlanOrder({
        planObjectId: planAdhocRecord.planObjectId,
        planOrderObjectId: null,
        communityObjectId: planAdhocRecord.communityObjectId,
        fromAdhoc: true,
      });

      await this.handleDeactivateAdhocProPlan({
        communityObjectId: planAdhocRecord.communityObjectId,
        email: planAdhocRecord.creatorEmail,
      });
    })
  );
};

exports.transferPlan = async ({
  entityType,
  transferFromCommunityObjectId,
  transferToCommunityObjectId,
}) => {
  const [
    transferFromPlanOrder,
    transferToPlanOrder,
    transferFromCommunity,
    transferToCommunity,
  ] = await Promise.all([
    planOrderService.retrieveExistingPlanOrder({
      communityObjectId: transferFromCommunityObjectId,
      entityType,
    }),
    planOrderService.retrieveExistingPlanOrder({
      communityObjectId: transferToCommunityObjectId,
      entityType,
    }),
    commonService.retrieveActiveCommunity(transferFromCommunityObjectId),
    commonService.retrieveActiveCommunity(transferToCommunityObjectId),
  ]);

  if (!transferFromPlanOrder) {
    throw new ParamError('Plan order for transfer from not found');
  }

  if (transferToPlanOrder) {
    throw new ParamError('Plan order for transfer to already exists');
  }

  const learner = await commonService.retrieveLearner(
    transferFromPlanOrder.learnerObjectId
  );

  const isManagerRole = await commonService.isManagerRole({
    communityObjectId: transferToCommunityObjectId,
    email: learner.email,
  });

  if (!isManagerRole) {
    throw new ParamError(
      'Only can transfer plan to community with the same manager email'
    );
  }

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  if (!isManagerRole) {
    throw new ParamError('Only managers can transfer plans');
  }

  try {
    await communityService.enrollPlanOrder({
      community: transferToCommunity,
      features: transferFromPlanOrder.features,
      entityType: transferFromPlanOrder.entityType,
      communityObjectId: transferToCommunityObjectId,
      session,
    });

    await communityService.cancelPlanOrder({
      community: transferFromCommunity,
      features: transferFromPlanOrder.features,
      entityType: transferFromPlanOrder.entityType,
      communityObjectId: transferFromCommunityObjectId,
      session,
    });

    await planOrderService.transferPlanOrder({
      planOrder: transferFromPlanOrder,
      transferToCommunityObjectId,
      session,
    });

    await planTransactionService.transferPlanTransaction({
      planOrder: transferFromPlanOrder,
      transferToCommunityObjectId,
      session,
    });

    await referralRewardService.transferReferralRewardAndRevenueTransaction(
      {
        planOrder: transferFromPlanOrder,
        transferFromCommunityObjectId,
        transferToCommunityObjectId,
        session,
      }
    );
    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};
