const httpContext = require('express-http-context');
const moment = require('moment');

const PrimaryMongooseConnection = require('@src/rpc/primaryMongooseConnection');
const PlanOrderModel = require('../../models/plan/communityPlanOrder.model');
const PlanAdhocRecordsModel = require('../../models/plan/communityPlanAdhocRecords.model');
const PlanModel = require('../../models/plan/communityPlan.model');

const PaymentBackendRpc = require('../../rpc/paymentBackend');
const { PAYMENT_PROVIDER } = require('../../constants/common');
const { PAYMENT_STATUSES } = require('../../communitiesAPI/constants');
const { ParamError } = require('../../utils/error.util');
const {
  ORDER_STATUS,
  PLAN_ADHOC_STATUS,
  ENTITY_TYPE_RANKS,
  TIER_CHANGE_CATEGORY,
  STRIPE_ADVANCE_PROCESSING_BUFFER_HOURS,
} = require('./constants');
const paymentProviderUtils = require('../../utils/paymentProvider.util');
const notificationService = require('../communityNotification');
const validationService = require('./validation.service');
const commonService = require('./common.service');
const logger = require('../logger.service');
const paymentProviderService = require('./changePlan/paymentProvider');

function getGracePeriodEndDate(planOrder) {
  const paymentProvider = planOrder.paymentDetails.paymentProvider;

  const gracePeriodEndDate = moment(planOrder.nextBillingDate);
  switch (paymentProvider) {
    case PAYMENT_PROVIDER.STRIPE:
    case PAYMENT_PROVIDER.STRIPE_INDIA:
    case PAYMENT_PROVIDER.STRIPE_US:
    case PAYMENT_PROVIDER.EBANX:
      gracePeriodEndDate.add(7, 'day');
      break;
    default:
      throw new Error(`Invalid payment provider: ${paymentProvider}`);
  }

  return gracePeriodEndDate.toDate();
}

exports.lockAndRetrievePlanOrderForRenewal = async (planOrderObjectId) => {
  // To prevent double charge
  const planOrder = await PlanOrderModel.findOneAndUpdate(
    {
      _id: planOrderObjectId,
      'paymentDetails.renewalLock': { $exists: false },
    },
    {
      $set: { 'paymentDetails.renewalLock': true },
    },
    { new: true }
  ).lean();

  if (!planOrder) {
    throw new Error(
      `This plan order is under renewal process: planOrderObjectId=${planOrderObjectId}`
    );
  }

  return planOrder;
};

exports.unlockPlanOrderForRenewal = async (planOrderObjectId) => {
  const result = await PlanOrderModel.updateOne(
    {
      _id: planOrderObjectId,
      'paymentDetails.renewalLock': true,
    },
    {
      $unset: { 'paymentDetails.renewalLock': 1 },
    }
  );

  if (result.modifiedCount !== 1) {
    throw new Error(
      `This plan order cannot be unlocked: planOrderObjectId=${planOrderObjectId}`
    );
  }
};

exports.retrievePlanOrder = async (planOrderObjectId) => {
  const planOrder = await PlanOrderModel.findById(planOrderObjectId)
    .read('primary')
    .lean();

  return planOrder;
};

exports.retrieveExistingPlanAdhocRecord = async ({
  communityObjectId,
}) => {
  const planAdhocRecord = await PlanAdhocRecordsModel.findOne({
    communityObjectId,
    status: PLAN_ADHOC_STATUS.ACTIVE,
  }).lean();

  return planAdhocRecord;
};

async function retrievePlanByType({ type }) {
  const plan = await PlanModel.findOne({
    type,
    isActive: true,
  }).lean();

  if (!plan) {
    throw new ParamError('Plan not found');
  }

  return plan;
}

exports.createOrUpdatePlanAdhocRecord = async ({
  adhocRecord,
  addedBy,
}) => {
  const { entityType, communityCode, creatorEmail, notes, status } =
    adhocRecord;

  const planType = `${entityType}_ADHOC`;

  const [community, plan] = await Promise.all([
    commonService.retrieveCommunityByCode(communityCode),
    retrievePlanByType({ type: planType }),
  ]);

  const communityObjectId = community._id;

  const [planOrder, pendingPlanOrder] = await Promise.all([
    this.retrieveExistingPlanOrder({
      communityObjectId,
    }),
    this.retrievePendingPlanOrder({
      communityObjectId,
    }),
  ]);

  if (planOrder || pendingPlanOrder) {
    throw new ParamError('Plan order already exists');
  }

  const adhocRecordData = {
    entityType,
    communityObjectId,
    status,
    planObjectId: plan._id,
    creatorEmail,
    notes,
    addedBy,
  };

  const existingPlanAdhocRecord = await PlanAdhocRecordsModel.findOne({
    communityObjectId,
  }).lean();

  if (!existingPlanAdhocRecord) {
    if (status === PLAN_ADHOC_STATUS.INACTIVE) {
      // Not even enrolled can ignore
      return {
        planAdhocRecord: null,
        created: false,
        switchedTier: false,
      };
    }
    try {
      const planAdhocRecord = await PlanAdhocRecordsModel.create(
        adhocRecordData
      );

      return {
        planAdhocRecord: planAdhocRecord.toObject(),
        created: true, // (enroll)
        switchedTier: false,
      };
    } catch (err) {
      if (err.code === 11000) {
        return {
          planAdhocRecord: null,
          created: false,
          switchedTier: false,
        };
      }

      throw err;
    }
  }

  const created = false;

  const isEntitySame = existingPlanAdhocRecord.entityType === entityType;
  const isStatusSame = existingPlanAdhocRecord.status === status;

  let switchedTier = false;

  if (!isEntitySame && status === PLAN_ADHOC_STATUS.INACTIVE) {
    // INACTIVE PRO <> INACTIVE PLATINUM
    // ACTIVE PRO > INACTIVE PLATINUM
    // ACTIVE PLATINUM > INACTIVE PRO
    throw new Error(
      `Cannot make plan ${status} as there is no existing active ${entityType} plan for ${communityCode}`
    );
  }

  if (!isEntitySame && status === PLAN_ADHOC_STATUS.ACTIVE) {
    if (isStatusSame) {
      // ACTIVE PRO > ACTIVE PLATINUM (Upgrade)
      // ACTIVE PLATINUM > ACTIVE PRO (Downgrade)
      switchedTier = true;
    }
  }
  // (isEntitySame && !isStatusSame)
  // -- ACTIVE <> INACTIVE for PRO (unenroll/enroll)
  // -- ACTIVE <> INACTIVE for PLATINUM (unenroll/enroll)
  // (!isEntitySame && !isStatusSame && status === PLAN_ADHOC_STATUS.ACTIVE)
  // -- INACTIVE PRO > ACTIVE PLATINUM (enroll)
  // -- INACTIVE PLATINUM > ACTIVE PRO (enroll)
  // (isEntitySame && isStatusSame)

  const updatedPlanAdhocRecord =
    await PlanAdhocRecordsModel.findOneAndUpdate(
      {
        communityObjectId,
      },
      adhocRecordData,
      { new: true }
    ).lean();

  if (isEntitySame && isStatusSame) {
    // No change needed
    return {
      planAdhocRecord: null,
      created,
      switchedTier,
    };
  }
  return {
    existingPlanAdhocRecord,
    planAdhocRecord: updatedPlanAdhocRecord,
    created: false,
    switchedTier,
  };
};

exports.cancelAllPlanAdhocRecords = async ({
  email,
  entityType = null,
}) => {
  const matchFilter = {
    creatorEmail: email,
    status: PLAN_ADHOC_STATUS.ACTIVE,
  };

  if (entityType) {
    matchFilter.entityType = entityType;
  }

  const planAdhocRecords = await PlanAdhocRecordsModel.find(
    matchFilter
  ).lean();

  const result = await Promise.all(
    planAdhocRecords.map(async (planAdhocRecord) =>
      PlanAdhocRecordsModel.findByIdAndUpdate(
        planAdhocRecord._id,
        {
          $set: { status: PLAN_ADHOC_STATUS.INACTIVE },
        },
        { new: true }
      ).lean()
    )
  );

  return result;
};

exports.retrieveExistingPlanOrder = async ({
  communityObjectId,
  entityType = null,
}) => {
  const filter = {
    communityObjectId,
    $or: [
      {
        status: ORDER_STATUS.CURRENT,
      },
      {
        status: ORDER_STATUS.CANCELLED,
        cancelledAt: { $gte: new Date() },
      },
    ],
  };
  if (entityType) {
    filter.entityType = entityType;
  }
  const planOrder = await PlanOrderModel.findOne(filter).lean();

  return planOrder;
};

exports.retrievePendingPlanOrder = async ({ communityObjectId }) => {
  const filter = {
    communityObjectId,
    status: ORDER_STATUS.PENDING,
  };
  const planOrder = await PlanOrderModel.findOne(filter).lean();
  return planOrder;
};

/**
 * Validate if cancellation can be revoked without violating the 24-hour buffer
 * @param {Object} planOrder - The plan order to validate
 * @param {Boolean} throwError - Whether to throw errors (true) or return boolean (false)
 * @returns {Boolean} - True if can revoke, false otherwise (when throwError=false)
 * @throws {ParamError} - When validation fails and throwError=true
 */
function validateRevokeCancellationBuffer(planOrder, throwError = false) {
  // Check if not in cancelled status
  if (planOrder.status !== ORDER_STATUS.CANCELLED) {
    if (throwError) {
      throw new ParamError('Plan order is not in cancelled status');
    }
    return false;
  }

  const now = new Date();
  const BUFFER_MS =
    STRIPE_ADVANCE_PROCESSING_BUFFER_HOURS * 60 * 60 * 1000;

  // Check if cancellation has already taken effect
  if (planOrder.cancelledAt && now >= new Date(planOrder.cancelledAt)) {
    if (throwError) {
      throw new ParamError('Cancellation has already taken effect');
    }
    return false;
  }

  // Check if within 24 hours of cancellation date
  if (planOrder.cancelledAt) {
    const cancelDeadline = new Date(
      planOrder.cancelledAt.getTime() - BUFFER_MS
    );
    if (now >= cancelDeadline) {
      if (throwError) {
        throw new ParamError(
          `Cannot revoke cancellation within ${STRIPE_ADVANCE_PROCESSING_BUFFER_HOURS} hours of scheduled cancellation due to payment provider processing`
        );
      }
      return false;
    }
  }

  // Check if within 24 hours of next billing date (important for downgrades)
  if (planOrder.nextBillingDate) {
    const billingDeadline = new Date(
      planOrder.nextBillingDate.getTime() - BUFFER_MS
    );
    if (now >= billingDeadline) {
      if (throwError) {
        throw new ParamError(
          `Cannot revoke cancellation within ${STRIPE_ADVANCE_PROCESSING_BUFFER_HOURS} hours of next billing date due to payment provider processing`
        );
      }
      return false;
    }
  }

  return true; // Can revoke - not within buffer period
}

function validateRevokePlanChangeBuffer(planOrder, throwError = false) {
  // Check if not in current status
  if (planOrder.status !== ORDER_STATUS.CURRENT) {
    if (throwError) {
      throw new ParamError('Plan order is not in current status');
    }
    return false;
  }

  // Check if has plan history
  if (!planOrder.planHistory || planOrder.planHistory.length === 0) {
    if (throwError) {
      throw new ParamError('No plan changes to revoke');
    }
    return false;
  }

  const now = new Date();
  const BUFFER_MS =
    STRIPE_ADVANCE_PROCESSING_BUFFER_HOURS * 60 * 60 * 1000;

  // Check if within 24 hours of the current plan's next billing date
  if (planOrder.nextBillingDate) {
    const billingDeadline = new Date(
      planOrder.nextBillingDate.getTime() - BUFFER_MS
    );
    if (now >= billingDeadline) {
      if (throwError) {
        throw new ParamError(
          `Cannot revoke plan changes within ${STRIPE_ADVANCE_PROCESSING_BUFFER_HOURS} hours of next billing date due to payment provider processing`
        );
      }
      return false;
    }
  }

  return true; // Can revoke - not within buffer period
}

/**
 * Format get plan api response for non paid plans like Free tier and adhoc plans
 * @param {Object} params
 * @returns {Object} planInfo
 */
exports.transformToPlanInfoForNonPaidPlan = ({
  billingHistory,
  learnerObjectId,
  isAdhoc,
}) => {
  const isSubscriber = false;
  const isPaymentFailed = false;
  const canChangePaymentMethod = false;
  const canCancelPlan = false;
  const canChangePlan = !isAdhoc;
  const canChangeTier = !isAdhoc;
  const canRevokeCancellation = false; // Non-paid plans cannot have cancellation revoked
  const canRevokeTierChange = false; // Non-paid plans cannot have tier changes revoked
  const canRevokePlanChange = false; // Non-paid plans cannot have plan changes revoked

  const planInfo = {
    learnerObjectId,
    isSubscriber,
    isPaymentFailed,
    canChangePaymentMethod,
    canChangeTier,
    canChangePlan,
    canCancelPlan,
    canRevokeCancellation,
    canRevokeTierChange,
    canRevokePlanChange,
    billingHistory,
    isAdhoc,
  };

  return planInfo;
};

function retrievePlanOriginalPrice({
  planPrices,
  interval,
  intervalCount,
}) {
  const currentPrice = planPrices?.find(
    (price) =>
      price.interval === interval && price.intervalCount === intervalCount
  );

  return currentPrice?.originalAmount;
}

exports.transformToPlanInfo = ({
  pendingPlanOrder,
  pendingReferrer,
  planOrder,
  paymentMethod,
  billingHistory,
  learnerObjectId,
  nextBillingPrice,
  isTrial,
  isLatestBillingTransactionTrial,
  latestBillingTransaction,
  referrer,
  planPrices = null,
}) => {
  const {
    _id: planOrderObjectId,
    planObjectId,
    learnerObjectId: purchaserLearnerObjectId,
    paymentProviderSubscriptionId,
    interval,
    intervalCount,
    nextBillingDate,
    status,
    amountInLocalCurrency,
    localCurrency,
    paymentDetails,
    billingCycle,
    cancelledAt,
    previousAmountInLocalCurrency,
    previousLocalCurrency,
    planHistory,
    entityType,
  } = planOrder;

  const canCancelPlan = validationService.checkIfCanCancelPlan({
    planOrder,
    learnerObjectId,
    throwError: false,
  });

  const canChangePlan = validationService.checkIfCanChangePlan({
    pendingPlanOrder,
    planOrder,
    price: null,
    learnerObjectId,
    throwError: false,
  });

  const canChangeTier = validationService.checkIfCanChangeTier({
    pendingPlanOrder,
    planOrder,
    price: null,
    learnerObjectId,
    throwError: false,
  });

  const isSubscriber = validationService.isPlanPurchaser({
    planOrder,
    learnerObjectId,
    throwError: false,
  });

  const canChangePaymentMethod =
    validationService.checkIfCanChangePaymentMethod({
      planOrder,
      learnerObjectId,
      throwError: false,
    });

  const isPaymentFailed =
    paymentDetails.status === PAYMENT_STATUSES.FAILED;

  // Check if can revoke - distinguish between cancellation, tier change, and plan change
  let canRevokeCancellation = false;
  let canRevokeTierChange = false;
  let canRevokePlanChange = false;

  if (planOrder.status === ORDER_STATUS.CANCELLED) {
    // Both cancellation and downgrade result in CANCELLED status
    // Check for nextPlanOrder field or pendingPlanOrder to distinguish between them

    if (!pendingPlanOrder) {
      // Regular cancellation (no pending plan)
      canRevokeCancellation =
        planOrder.restrictRevokeCancellation != null
          ? !planOrder.restrictRevokeCancellation
          : validateRevokeCancellationBuffer(planOrder, false);
    } else if (
      pendingPlanOrder?.tierChangeCategory ===
      TIER_CHANGE_CATEGORY.DOWNGRADE
    ) {
      // Only allow revocation for downgrades
      canRevokeTierChange = validateRevokeCancellationBuffer(
        planOrder,
        false
      );
    } else {
      // Everything else (upgrades, unknown types, etc.) - cannot be revoked
      logger.info('Detected non-revocable scenario', {
        hasPendingOrder: !!pendingPlanOrder,
        pendingTierChangeCategory: pendingPlanOrder?.tierChangeCategory,
        hasNextPlanOrder: !!planOrder.nextPlanOrder,
        reason:
          pendingPlanOrder?.tierChangeCategory ===
          TIER_CHANGE_CATEGORY.UPGRADE
            ? 'upgrade'
            : 'unknown',
      });
      canRevokeTierChange = false;
    }
  } else if (
    planOrder.status === ORDER_STATUS.CURRENT &&
    planOrder.planHistory?.length > 0
  ) {
    // For active plans with plan interval change history
    canRevokePlanChange = validateRevokePlanChangeBuffer(planOrder, false);
    logger.debug('Plan interval change revocation scenario', {
      canRevokePlanChange,
    });
  }

  logger.info('Final revocation flags', {
    canRevokeCancellation,
    canRevokeTierChange,
    canRevokePlanChange,
  });

  const planInfo = {
    referrer,
    planOrderObjectId,
    planObjectId,
    learnerObjectId: purchaserLearnerObjectId,
    paymentProviderSubscriptionId,
    interval,
    intervalCount,
    nextBillingDate,
    cancelledAt,
    paymentProvider: paymentDetails.paymentProvider,
    status,
    amount: amountInLocalCurrency,
    currency: localCurrency,
    paymentDetails,
    billingCycle,
    isTrial,
    currentPaymentMethod: paymentMethod,
    isSubscriber,
    canChangeTier,
    canChangePlan,
    canChangePaymentMethod,
    canCancelPlan,
    canRevokeCancellation,
    canRevokeTierChange,
    canRevokePlanChange,
    previousAmountInLocalCurrency,
    previousLocalCurrency,
    billingHistory,
    planHistory,
    isPaymentFailed,
    phoneNumber: planOrder.phoneNumber,
  };

  planInfo.currentPlan = {
    cancelledAt,
    nextBillingDate,
    interval,
    intervalCount,
    currency: localCurrency,
    amount: amountInLocalCurrency,
    entityType,
    billingCycle,
    isTrial,
    selectedInterval: interval, // Interval of the current plan, used for display in the change plan UI
    selectedIntervalCount: intervalCount, // Interval count of the current plan, used for display in the change plan UI
  };

  const originalPrice = retrievePlanOriginalPrice({
    planPrices,
    interval,
    intervalCount,
  });

  if (originalPrice) {
    planInfo.currentPlan.originalPrice = originalPrice;
  }

  const planExistsInPlanHistory = (planInfo.planHistory ?? []).find(
    (info) => info.billingCycle === billingCycle
  );

  if (planExistsInPlanHistory) {
    planInfo.currentPlan = {
      nextBillingDate: planExistsInPlanHistory.nextBillingDate,
      interval: planExistsInPlanHistory.interval,
      intervalCount: planExistsInPlanHistory.intervalCount,
      currency: planExistsInPlanHistory.localCurrency,
      amount: planExistsInPlanHistory.amountInLocalCurrency,
      entityType,
      selectedInterval: planExistsInPlanHistory.interval,
      selectedIntervalCount: planExistsInPlanHistory.intervalCount,
    };

    const planHistoryOriginalPrice = retrievePlanOriginalPrice({
      planPrices,
      interval: planExistsInPlanHistory.interval,
      intervalCount: planExistsInPlanHistory.intervalCount,
    });

    if (planHistoryOriginalPrice) {
      planInfo.currentPlan.originalPrice = planHistoryOriginalPrice;
    }

    planInfo.nextBillingPrice = {
      interval,
      intervalCount,
      currency: localCurrency,
      amount: amountInLocalCurrency,
      entityType,
    };
  }

  if (isTrial) {
    if (nextBillingPrice) {
      planInfo.nextBillingPrice = {
        interval: nextBillingPrice.interval,
        intervalCount: nextBillingPrice.intervalCount,
        currency: nextBillingPrice.currency,
        amount: nextBillingPrice.unitAmount,
      };
    }

    if (isLatestBillingTransactionTrial) {
      planInfo.previousBillingPrice = {
        interval: latestBillingTransaction.interval,
        intervalCount: latestBillingTransaction.intervalCount,
        currency: latestBillingTransaction.currency,
        amount: latestBillingTransaction.amount,
      };

      planInfo.currentPlan = {
        nextBillingDate,
        interval: latestBillingTransaction.interval,
        intervalCount: latestBillingTransaction.intervalCount,
        currency: latestBillingTransaction.currency,
        amount: latestBillingTransaction.amount,
        originalPrice: latestBillingTransaction.originalAmount,
        entityType,
        selectedInterval: interval,
        selectedIntervalCount: intervalCount,
      };
    }
  }

  if (pendingPlanOrder) {
    planInfo.isDowngradedPlan = true;
    planInfo.nextBillingPrice = {
      interval: pendingPlanOrder.interval,
      intervalCount: pendingPlanOrder.intervalCount,
      currency: pendingPlanOrder.localCurrency,
      amount: pendingPlanOrder.amountInLocalCurrency,
      entityType: pendingPlanOrder.entityType,
      referrer: pendingReferrer,
    };
  }

  if (isPaymentFailed) {
    planInfo.gracePeriodEndDate = getGracePeriodEndDate(planOrder);
  }

  return planInfo;
};

exports.retrieveLastPurchasePlanOrder = async ({ communityObjectId }) => {
  const planOrder = await PlanOrderModel.findOne({
    status: { $nin: [ORDER_STATUS.PENDING, ORDER_STATUS.INVALID] },
    communityObjectId,
    'paymentDetails.completedPayment': true,
  })
    .sort({ _id: -1 })
    .lean();

  return planOrder;
};

function isUpdateOperation(decodedSignupToken) {
  return decodedSignupToken?.planOrderObjectId != null;
}

exports.isTierUpgrade = ({ newEntityType, existingEntityType }) => {
  return (
    ENTITY_TYPE_RANKS[newEntityType] <
    ENTITY_TYPE_RANKS[existingEntityType]
  );
};

function getReformattedPlanOrderFieldsForCreditDetails({
  planOrder,
  creditDetails,
}) {
  if (!creditDetails?.creditPlan) {
    return planOrder;
  }
  /**
   * Either a zero Price plan that points to originalAmount plan
   * Eg. Original Price: 100
   *     Scenario 1 - Credit in wallet: 230 | FirstBillingPaidAmount: 0 | RenewalAmount: 0
   *                  Plan Price points to $0
   *     Scenario 2 - Credit in wallet: 130 | FirstBillingPaidAmount: 0 | RenewalAmount: 30
   *                  Plan Price points to $70 with 1 billingcycle trial
   *     Scenario 3 - Credit in wallet: 30 | FirstBillingPaidAmount: 70 | RenewalAmount: 100
   *                  Plan Price points to $70
   */
  const {
    creditHistory,
    creditPlan: entityInfo,
    creditPrice: price,
  } = creditDetails;

  const priceId = price?.priceId;
  const localCurrency = price?.currency;
  const paymentProviderForPriceId = price?.paymentProviderForPriceId;
  const firstBillingCredit = creditHistory.find(
    (credit) => credit.billingCycle === 1
  );

  return {
    ...planOrder,
    paymentProviderPriceId: priceId, // finalPrice to charge after accounting all credits
    paymentProviderForPriceId,
    entityType: entityInfo.entityType,
    planObjectId: entityInfo._id,
    billingModel: entityInfo.billingModel,
    features: entityInfo.features,
    // metadata fields used for data dashboard. should not be reassigned
    metadata: {
      firstBilling: {
        ...planOrder.metadata.firstBilling,
        priceId,
        localCurrency,
        amountInLocalCurrency: planOrder.amountInLocalCurrency, // plan Amount
        creditsInLocalCurrency: firstBillingCredit?.paidCreditAmount, // credit used
        paidAmountInLocalCurrency: firstBillingCredit?.paidAmount, // final paidAmount
      },
    },
    creditHistory,
  };
}

function generatePlanOrder({
  community,
  learner,
  countryInfo,
  timezone,
  requestor,
  trackingData,
  plan,
  paymentProvider,
}) {
  const { priceId, metadata, nextBillingPriceId, referralDetails } = plan;

  const {
    price,
    entityInfo,
    existingPlanOrder,
    prorationDetails,
    creditDetails,
    isChangeTier,
    isTierUpgrade,
  } = metadata;

  const { _id: communityObjectId, payment_methods: paymentMethods } =
    community;

  const localAmount = price.unitAmount;
  const localCurrency = price.currency;

  const paymentProviderForPriceId =
    paymentProviderUtils.retrievePaymentProviderForPlan(
      paymentMethods,
      localCurrency
    );

  const { _id: learnerObjectId } = learner;

  const recurring = price.recurring;

  const interval = recurring.interval;
  const intervalCount = recurring.interval_count;

  const completedPayment = false;

  const country = {
    code: countryInfo.countryCode,
    name: countryInfo.country,
  };

  const planOrder = {
    communityObjectId,
    learnerObjectId,
    entityType: entityInfo.entityType,
    country,
    requestor,
    amountInLocalCurrency: localAmount,
    localCurrency,
    paymentDetails: {
      completedPayment,
      status: PAYMENT_STATUSES.INCOMPLETE,
      latestUpdatedTime: new Date(),
      recurringPurchase: true,
      paymentProvider,
      requestId: httpContext.get('reqId'),
    },
    applyDiscount: false,
    timezone,
    trackingData,
    interval,
    intervalCount,
    paymentProviderPriceId: priceId,
    paymentProviderForPriceId,
    planObjectId: entityInfo._id,
    billingModel: entityInfo.billingModel,
    features: entityInfo.features,
    status: ORDER_STATUS.DRAFT,
    billingCycle: 1,
    // metadata fields used for data dashboard. should not be reassigned
    metadata: {
      firstBilling: {
        priceId,
        interval,
        intervalCount,
        amountInLocalCurrency: localAmount,
        paidAmountInLocalCurrency: localAmount,
        localCurrency,
      },
    },
  };

  if (referralDetails) {
    planOrder.communityReferralCode =
      referralDetails.communityReferralCode;
    planOrder.referralRewardTemplateObjectId =
      referralDetails.referralRewardTemplateObjectId;
    planOrder.referrerCommunityObjectId =
      referralDetails.referrerCommunityObjectId;
  }

  if (nextBillingPriceId) {
    // Fields to identify the nextBillingPlan the user has chosen
    planOrder.isOnTrial = true;
    planOrder.metadata.firstBilling.isTrial = true;
    planOrder.metadata.nextBillingPriceId = nextBillingPriceId;
  }

  if (isChangeTier) {
    planOrder.isOnTrial = false;
    planOrder.metadata.firstBilling.isTrial = false;
    planOrder.tierChangeCategory = isTierUpgrade
      ? TIER_CHANGE_CATEGORY.UPGRADE
      : TIER_CHANGE_CATEGORY.DOWNGRADE;
    planOrder.previousPlanOrder = {
      planOrderObjectId: existingPlanOrder._id,
      entityType: existingPlanOrder.entityType,
      interval: existingPlanOrder.interval,
      intervalCount: existingPlanOrder.intervalCount,
      billingCycle: existingPlanOrder.billingCycle,
    };
    if (isTierUpgrade) {
      if (prorationDetails) {
        planOrder.prorationDetails = prorationDetails;
      }
      if (creditDetails) {
        const creditPlanOrder =
          getReformattedPlanOrderFieldsForCreditDetails({
            planOrder,
            creditDetails,
          });
        return creditPlanOrder;
      }
    }
  }
  return planOrder;
}

async function updatePlanOrder(
  planOrderObjectId,
  updateData,
  session,
  ignoreFilter = false
) {
  const filter = {
    _id: planOrderObjectId,
  };

  let updatedPlanOrder;

  if (ignoreFilter) {
    updatedPlanOrder = await PlanOrderModel.findOneAndUpdate(
      filter,
      updateData,
      {
        new: true,
        session,
      }
    ).lean();
  } else {
    filter.$and = [
      { 'paymentDetails.status': { $ne: PAYMENT_STATUSES.PENDING } },
      { 'paymentDetails.status': { $ne: PAYMENT_STATUSES.SUCCESS } },
    ];

    updatedPlanOrder = await PlanOrderModel.findOneAndReplace(
      filter,
      updateData,
      {
        new: true,
        session,
      }
    ).lean();
  }

  if (!updatedPlanOrder) {
    throw new ParamError('Plan order cannot be updated');
  }

  return updatedPlanOrder;
}

exports.createOrUpdatePlanOrder = async ({
  community,
  learner,
  countryInfo,
  timezone,
  requestor,
  trackingData,
  plan,
  decodedSignupToken,
  paymentProvider,
  session,
}) => {
  const generatedPlanOrder = generatePlanOrder({
    community,
    learner,
    countryInfo,
    timezone,
    requestor,
    trackingData,
    plan,
    paymentProvider,
  });

  const updateOperation = isUpdateOperation(decodedSignupToken);

  let planOrder;

  if (updateOperation) {
    planOrder = await updatePlanOrder(
      decodedSignupToken.planOrderObjectId,
      generatedPlanOrder,
      session
    );
  } else {
    planOrder = (
      await PlanOrderModel.create([generatedPlanOrder], {
        session,
      })
    )[0].toObject();
  }

  return planOrder;
};

async function handleCancellationBeforeDowngradeOccurs({
  planOrder,
  paymentBackendRpc,
}) {
  if (!planOrder.nextPlanOrder?.planOrderObjectId) {
    return;
  }
  const nextPlanOrder = await PlanOrderModel.findById(
    planOrder.nextPlanOrder.planOrderObjectId
  ).lean();

  if (
    !nextPlanOrder ||
    nextPlanOrder.status !== ORDER_STATUS.PENDING ||
    nextPlanOrder.tierChangeCategory !== TIER_CHANGE_CATEGORY.DOWNGRADE
  ) {
    return;
  }
  const updateQuery = {
    status: ORDER_STATUS.INVALID,
    cancelledAt: planOrder.cancelledAt,
    unsubscribedAt: planOrder.unsubscribedAt,
    cancellationReasons: planOrder.cancellationReasons,
  };

  const paymentProvider = nextPlanOrder.paymentDetails.paymentProvider;

  const { paymentProviderSubscriptionId } = nextPlanOrder;

  const cancellationReason =
    'User cancelled previous plan before nextBilling plan becomes active';

  switch (paymentProvider) {
    case PAYMENT_PROVIDER.STRIPE:
    case PAYMENT_PROVIDER.STRIPE_US:
    case PAYMENT_PROVIDER.STRIPE_INDIA:
      await paymentBackendRpc.cancelStripeSubscription(
        paymentProviderSubscriptionId,
        cancellationReason,
        paymentProvider,
        false,
        true
      );
      break;
    case PAYMENT_PROVIDER.EBANX:
      updateQuery.scheduledCancellation = true;
      break;
    default:
      throw new ParamError(
        `Payment provider ${paymentProvider} not supported`
      );
  }

  await PlanOrderModel.findOneAndUpdate(
    { _id: nextPlanOrder._id, status: ORDER_STATUS.PENDING },
    {
      $set: updateQuery,
    },
    { new: true }
  );
}

exports.cancelExistingPlanOrder = async (
  existingPlanOrder,
  formattedCancellationReasons = []
) => {
  if (!existingPlanOrder) {
    throw new ParamError('Plan order not found');
  }

  if (existingPlanOrder.status !== ORDER_STATUS.CURRENT) {
    throw new ParamError('Plan order is not active');
  }

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  const currentDate = new Date();

  // When a payment fails, the subscription enters a grace period.
  // If the calculated `cancelledAt` (based on nextBillingDate) is already in the past,
  // we override it with the current time to reflect immediate cancellation.
  const selectedCancelledAt =
    existingPlanOrder.nextBillingDate > currentDate
      ? existingPlanOrder.nextBillingDate
      : currentDate;

  const updateQuery = {
    status: ORDER_STATUS.CANCELLED,
    cancelledAt: selectedCancelledAt,
    unsubscribedAt: currentDate,
  };

  let cancellationReasonStr = '';

  if (formattedCancellationReasons.length) {
    updateQuery.cancellationReasons = formattedCancellationReasons;
    formattedCancellationReasons.forEach((reason) => {
      if (reason.reason) {
        cancellationReasonStr += `${reason.reason} | `;
      }
    });
  }

  const paymentProvider = existingPlanOrder.paymentDetails.paymentProvider;

  const { paymentProviderSubscriptionId } = existingPlanOrder;

  const cancellationReason = 'User cancelled';

  switch (paymentProvider) {
    case PAYMENT_PROVIDER.STRIPE:
    case PAYMENT_PROVIDER.STRIPE_US:
    case PAYMENT_PROVIDER.STRIPE_INDIA:
      await paymentBackendRpc.cancelStripeSubscription(
        paymentProviderSubscriptionId,
        cancellationReason,
        paymentProvider,
        false,
        true
      );
      break;
    case PAYMENT_PROVIDER.EBANX:
      updateQuery.scheduledCancellation = true;
      break;
    default:
      throw new ParamError(
        `Payment provider ${paymentProvider} not supported`
      );
  }

  const updatedPlanOrder = await PlanOrderModel.findOneAndUpdate(
    { _id: existingPlanOrder._id, status: ORDER_STATUS.CURRENT },
    {
      $set: updateQuery,
    },
    { new: true }
  );

  if (!updatedPlanOrder) {
    throw new ParamError('Plan order cannot be cancelled');
  }
  await handleCancellationBeforeDowngradeOccurs({
    planOrder: updatedPlanOrder,
    paymentBackendRpc,
  });

  await notificationService.sendCancelledPlanNotification({
    planOrderObjectId: updatedPlanOrder._id,
    communityObjectId: updatedPlanOrder.communityObjectId,
    learnerObjectId: updatedPlanOrder.learnerObjectId,
    subscriptionExpiryDate: updatedPlanOrder.cancelledAt,
    failureReason: cancellationReason,
    cancellationReasons: cancellationReasonStr,
    alreadyCancelled: false,
  });

  return updatedPlanOrder;
};

exports.changePlanOrderInterval = async ({
  planOrder,
  price,
  planObjectId,
  referrer = null,
  session = undefined,
}) => {
  const planHistory = {
    paymentProviderPriceId: planOrder.paymentProviderPriceId,
    intervalCount: planOrder.intervalCount,
    interval: planOrder.interval,
    nextBillingDate: planOrder.nextBillingDate,
    changedDateTime: new Date(),
    billingCycle: planOrder.billingCycle,
    amountInLocalCurrency: planOrder.amountInLocalCurrency,
    localCurrency: planOrder.localCurrency,
  };

  if (planOrder.referrerCommunityObjectId) {
    planHistory.communityReferralCode = planOrder.communityReferralCode;
    planHistory.referrerCommunityObjectId =
      planOrder.referrerCommunityObjectId;
    planHistory.referralRewardTemplateObjectId =
      planOrder.referralRewardTemplateObjectId;
  }

  if (planOrder.previousAmountInLocalCurrency) {
    planHistory.previousAmountInLocalCurrency =
      planOrder.previousAmountInLocalCurrency;
  }

  const newPlan = {
    interval: price.interval,
    intervalCount: price.intervalCount,
    amountInLocalCurrency: price.checkoutAmount,
    paymentProviderPriceId: price.id,
    planObjectId,
  };

  if (referrer) {
    newPlan.communityReferralCode = referrer.communityReferralCode;
    newPlan.referrerCommunityObjectId = referrer.communityObjectId;
    newPlan.referralRewardTemplateObjectId =
      referrer.referralRewardTemplateObjectId;
  }

  const updatedPlanOrder = await PlanOrderModel.findByIdAndUpdate(
    planOrder._id,
    {
      $set: newPlan,
      $unset: {
        previousAmountInLocalCurrency: 1,
        previousLocalCurrency: 1,
      },
      $push: {
        planHistory,
      },
    },
    { new: true, session }
  ).lean();

  if (!updatedPlanOrder) {
    throw new ParamError('Plan order not updated');
  }

  return updatedPlanOrder;
};

exports.updateRenewalAmount = async ({
  planOrder,
  amountInLocalCurrency,
  session = undefined,
}) => {
  const updatedPlanOrder = await PlanOrderModel.updateOne(
    { _id: planOrder._id },
    {
      $set: {
        'paymentDetails.renewalAmount': amountInLocalCurrency,
      },
    },
    { new: true, session }
  ).lean();

  return updatedPlanOrder;
};

exports.transferPlanOrder = async ({
  planOrder,
  transferToCommunityObjectId,
  session = undefined,
}) => {
  await PlanOrderModel.updateOne(
    { _id: planOrder._id },
    {
      $set: {
        communityObjectId: transferToCommunityObjectId,
      },
    },
    { session }
  );
};

async function reactivatePaymentProviderSubscription(planOrder) {
  const paymentProvider = planOrder.paymentDetails.paymentProvider;
  const subscriptionId = planOrder.paymentProviderSubscriptionId;

  if (!subscriptionId) {
    throw new ParamError('No subscription ID found for reactivation');
  }

  switch (paymentProvider) {
    case PAYMENT_PROVIDER.STRIPE:
    case PAYMENT_PROVIDER.STRIPE_US:
    case PAYMENT_PROVIDER.STRIPE_INDIA:
      try {
        const paymentBackendRpc = new PaymentBackendRpc();
        await paymentBackendRpc.init();

        await paymentBackendRpc.reactivateStripeSubscription({
          subscriptionId,
          paymentProvider,
        });

        logger.info('Stripe subscription reactivated successfully', {
          subscriptionId,
          paymentProvider,
          planOrderId: planOrder._id.toString(),
        });
      } catch (error) {
        logger.error('Failed to reactivate Stripe subscription', {
          subscriptionId,
          paymentProvider,
          planOrderId: planOrder._id.toString(),
          error: error.message,
        });
        throw new ParamError(
          `Failed to reactivate Stripe subscription: ${error.message}`
        );
      }
      break;

    case PAYMENT_PROVIDER.EBANX:
      // EBANX reactivation is handled by database flag (scheduledCancellation: false)
      // No API call needed - already set in database update above
      logger.info('EBANX subscription reactivated via database flag', {
        planOrderId: planOrder._id.toString(),
        subscriptionId,
      });
      break;

    default:
      throw new ParamError(
        `Payment provider ${paymentProvider} not supported for reactivation`
      );
  }
}

const cancelPendingPlanAtPaymentProvider = async (planOrderObjectId) => {
  const pendingPlanOrder = await PlanOrderModel.findById(
    planOrderObjectId
  ).lean();

  if (!pendingPlanOrder) {
    logger.warn('Pending plan order not found:', planOrderObjectId);
    return;
  }

  const { paymentDetails, paymentProviderSubscriptionId } =
    pendingPlanOrder;
  const { paymentProvider } = paymentDetails;

  if (paymentProviderSubscriptionId) {
    const paymentBackendRpc = new PaymentBackendRpc();
    await paymentBackendRpc.init();

    switch (paymentProvider) {
      case PAYMENT_PROVIDER.STRIPE:
      case PAYMENT_PROVIDER.STRIPE_INDIA:
      case PAYMENT_PROVIDER.STRIPE_US:
        await paymentBackendRpc.cancelStripeSubscription(
          paymentProviderSubscriptionId,
          'Cancelled due to revocation of previous plan cancellation',
          paymentProvider,
          false, // issueRefund - no refund needed
          false // cancelledAtNextBillingDate - cancel immediately
        );
        break;

      case PAYMENT_PROVIDER.EBANX:
        // EBANX cancellation is handled via database flag only
        logger.info(
          'EBANX pending plan cancellation handled via database only',
          {
            planOrderId: planOrderObjectId,
          }
        );
        break;

      default:
        logger.warn(
          'Unsupported payment provider for pending plan cancellation:',
          paymentProvider
        );
    }
  }

  logger.info('Successfully cancelled pending plan at payment provider:', {
    planOrderId: planOrderObjectId.toString(),
    paymentProvider,
  });
};

async function handlePlanChangeRevocation(existingPlanOrder) {
  logger.info('Revoking plan change for current plan', {
    planOrderId: existingPlanOrder._id,
    communityId: existingPlanOrder.communityObjectId,
  });

  const lastHistory =
    existingPlanOrder.planHistory[
      existingPlanOrder.planHistory.length - 1
    ];
  const now = new Date();

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();
  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    // Restore previous plan settings
    const updatedPlanOrder = await PlanOrderModel.findByIdAndUpdate(
      existingPlanOrder._id,
      {
        $set: {
          interval: lastHistory.interval,
          intervalCount: lastHistory.intervalCount,
          amountInLocalCurrency: lastHistory.amountInLocalCurrency,
          localCurrency: lastHistory.localCurrency,
          paymentProviderPriceId: lastHistory.paymentProviderPriceId,
          revokedAt: now,
        },
        $pop: { planHistory: 1 }, // Remove last history entry
      },
      { new: true, session }
    ).lean();

    if (!updatedPlanOrder) {
      throw new ParamError('Failed to revoke plan change');
    }

    // Update payment provider subscription
    const paymentBackendRpc = new PaymentBackendRpc();
    await paymentBackendRpc.init();

    await paymentProviderService.changePlan({
      planOrder: updatedPlanOrder,
      priceId: lastHistory.paymentProviderPriceId,
      paymentBackendRpc,
    });

    await session.commitTransaction();

    // Send notification (outside transaction) - using same notification as tier revoke
    notificationService
      .sendPlanRevocationNotification({
        planOrderObjectId: updatedPlanOrder._id,
        communityObjectId: updatedPlanOrder.communityObjectId,
        learnerObjectId: updatedPlanOrder.learnerObjectId,
      })
      .catch((err) => {
        logger.error(
          'Failed to send plan change revocation notification:',
          {
            error: err.message,
            planOrderId: updatedPlanOrder._id.toString(),
          }
        );
      });

    logger.info('Plan change revoked successfully', {
      planOrderId: updatedPlanOrder._id,
      restoredInterval: lastHistory.interval,
      restoredIntervalCount: lastHistory.intervalCount,
    });

    return updatedPlanOrder;
  } catch (error) {
    await session.abortTransaction();
    logger.error('Failed to revoke plan change', {
      error: error.message,
      planOrderId: existingPlanOrder._id,
    });
    throw error;
  } finally {
    await session.endSession();
  }
}

exports.revokePlanChangeOrder = async (existingPlanOrder) => {
  // Use plan change specific validation function with error throwing
  validateRevokePlanChangeBuffer(existingPlanOrder, true);

  return handlePlanChangeRevocation(existingPlanOrder);
};

exports.revokeCancelledPlanOrder = async (existingPlanOrder) => {
  // Use common validation function with error throwing
  validateRevokeCancellationBuffer(existingPlanOrder, true);

  // This function only handles CANCELLED plans
  const now = new Date();

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();
  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    // Step 1: Update database FIRST with atomic operation within transaction
    const updatedPlanOrder = await PlanOrderModel.findOneAndUpdate(
      {
        _id: existingPlanOrder._id,
        status: ORDER_STATUS.CANCELLED,
        cancelledAt: { $gt: now }, // Ensure still in grace period
      },
      {
        $set: {
          status: ORDER_STATUS.CURRENT,
          revokedAt: now,
          scheduledCancellation: false,
        },
        $unset: {
          cancelledAt: '',
          unsubscribedAt: '',
          cancellationReasons: '',
        },
      },
      { new: true, session }
    );

    if (!updatedPlanOrder) {
      throw new ParamError(
        'Plan order could not be revoked - status may have changed'
      );
    }

    // Step 2: Check if this is actually a downgrade scenario using proper constant
    let isDowngradeScenario = false;
    if (updatedPlanOrder.nextPlanOrder?.planOrderObjectId) {
      const nextPlanOrder = await PlanOrderModel.findById(
        updatedPlanOrder.nextPlanOrder.planOrderObjectId,
        null,
        { session }
      )
        .read('primary')
        .lean();

      if (
        nextPlanOrder?.tierChangeCategory ===
        TIER_CHANGE_CATEGORY.DOWNGRADE
      ) {
        isDowngradeScenario = true;
        logger.info(
          'Downgrade scenario detected, cancelling pending downgrade plan',
          {
            nextPlanId: nextPlanOrder._id?.toString(),
            entityType: nextPlanOrder.entityType,
            tierChangeCategory: nextPlanOrder.tierChangeCategory,
          }
        );

        // Only process downgrade cancellation
        await PlanOrderModel.findByIdAndUpdate(
          updatedPlanOrder.nextPlanOrder.planOrderObjectId,
          {
            $set: {
              status: ORDER_STATUS.INVALID,
              cancelledAt: now,
              cancellationReason:
                'Cancelled due to revocation of previous plan cancellation',
            },
          },
          { session }
        );

        await PlanOrderModel.findByIdAndUpdate(
          updatedPlanOrder._id,
          {
            $unset: { nextPlanOrder: 1 },
          },
          { session }
        );
      } else if (
        nextPlanOrder?.tierChangeCategory === TIER_CHANGE_CATEGORY.UPGRADE
      ) {
        // This should never happen if flag logic is fixed, but add safeguard
        throw new ParamError('Cannot revoke upgrade plan changes');
      } else {
        logger.warn('Unknown tier change category in nextPlanOrder', {
          tierChangeCategory: nextPlanOrder?.tierChangeCategory,
          planOrderId: nextPlanOrder?._id?.toString(),
        });
      }
    }

    // Step 3: Now do payment provider operations
    await reactivatePaymentProviderSubscription(updatedPlanOrder);

    if (isDowngradeScenario) {
      await cancelPendingPlanAtPaymentProvider(
        updatedPlanOrder.nextPlanOrder.planOrderObjectId
      );
    }

    // Step 4: Mark planHistory entry as revoked to allow future plan changes
    if (
      updatedPlanOrder.planHistory &&
      updatedPlanOrder.planHistory.length > 0
    ) {
      await PlanOrderModel.findByIdAndUpdate(
        updatedPlanOrder._id,
        {
          $set: {
            'planHistory.$[elem].revoked': true,
            'planHistory.$[elem].revokedAt': now,
          },
        },
        {
          arrayFilters: [
            { 'elem.billingCycle': updatedPlanOrder.billingCycle },
          ],
          session,
        }
      );
    }

    await session.commitTransaction();

    // Send notifications (outside transaction)
    notificationService
      .sendPlanRevocationNotification({
        planOrderObjectId: updatedPlanOrder._id,
        communityObjectId: updatedPlanOrder.communityObjectId,
        learnerObjectId: updatedPlanOrder.learnerObjectId,
      })
      .catch((err) => {
        logger.error('Failed to send revocation notification:', {
          error: err.message,
          planOrderId: updatedPlanOrder._id?.toString(),
        });
      });

    logger.info('Successfully revoked plan cancellation:', {
      planOrderId: updatedPlanOrder._id?.toString(),
      communityId: updatedPlanOrder.communityObjectId?.toString(),
      hadPendingPlan: !!updatedPlanOrder.nextPlanOrder?.planOrderObjectId,
    });

    return updatedPlanOrder;
  } catch (error) {
    await session.abortTransaction();
    logger.error('Error in revokeCancelledPlanOrder:', error);
    throw error;
  } finally {
    session.endSession();
  }
};
