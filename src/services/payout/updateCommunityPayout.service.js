const CommunityPayoutModel = require('../../communitiesAPI/models/communityPayouts.model');
const { PAYOUT_STATUS, PAYOUT_CHANNEL } = require('./constants');
const {
  DEFAULT_COMMUNITY_PROFILE_IMAGE,
} = require('../../constants/common');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const CommunityRoleModel = require('../../communitiesAPI/models/communityRole.model');
const { aclRoles } = require('../../communitiesAPI/constants');
const { NOTIFICATION_URL, NOTIFICATION_AUTH } = require('../../config');
const axios = require('../../clients/axios.client');
const { NAS_IO_FRONTEND_URL } = require('../../config');
const { PAYOUT_MAIL_TYPES } = require('../mail/constants');
const {
  processStripeConnectPayout,
} = require('./stripeConnectPayout.service');
const {
  sendPayoutFailedNotification,
} = require('../../communitiesAPI/services/common/payoutDetailsLarkWebhook.service');

const sendPayoutNotification = async (
  payoutId,
  toFinanceOnly = false,
  operator = ''
) => {
  const payout = await CommunityPayoutModel.findById(payoutId).lean();
  if (!payout) {
    throw new Error('No payout found');
  }

  const [community, communityRole] = await Promise.all([
    CommunityModel.findById(payout.communityObjectId).lean(),
    CommunityRoleModel.findOne({
      role: aclRoles.OWNER,
      communityObjectId: payout.communityObjectId,
    }).lean(),
  ]);

  if (!community) {
    throw new Error('No community found');
  }

  if (!communityRole) {
    throw new Error('No community role');
  }

  const communityOwnerEmail = communityRole.email;

  community.communityProfileImage = DEFAULT_COMMUNITY_PROFILE_IMAGE;
  if (community?.thumbnailImgData?.mobileImgData) {
    community.communityProfileImage =
      community?.thumbnailImgData?.mobileImgData?.src;
  }

  const payoutAmount = payout.payoutAmountInLocalCurrency / 100;
  const payoutAmountDisplay = `${
    payout.payoutCurrency
  } ${payoutAmount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;

  const payoutMonth = payout.payoutTitle
    .replace(' - INR', '')
    .slice(0, -4);

  const mailContent = {
    community_name: community.title,
    community_profile_image: community.communityProfileImage,
    payout_month: payoutMonth,
    payout_month_and_year: payout.payoutTitle.replace(' - INR', ''),
    payout_amount: payoutAmountDisplay,
    bank_account_name: payout.bankAccountInfo.accountName,
    bank_account_number: payout.bankAccountInfo.accountNumber,
    iban_number: !payout.bankAccountInfo.iban
      ? '--'
      : payout.bankAccountInfo.iban,
    payout_url: `${NAS_IO_FRONTEND_URL}/portal/money?tab=Payout&activeCommunityId=${community._id}`,
  };

  let toMail = communityOwnerEmail;
  if (toFinanceOnly) {
    toMail = operator;
  }

  const data = {
    mailType: PAYOUT_MAIL_TYPES.COMMUNITY_PAYOUT_NOTIFICATION,
    mailCourse: community.code,
    toMail: [toMail],
    toMailName: [community.By ?? ''],
    requesterServiceName: 'LPBE',
    data: mailContent,
  };
  await axios.post(`${NOTIFICATION_URL}/api/v1/send-email`, data, {
    headers: {
      Authorization: `Bearer ${NOTIFICATION_AUTH}`,
    },
  });
};

const updateCommunityPayoutToPaid = async ({
  payoutId,
  communityCode,
  operator,
}) => {
  if (communityCode) {
    const processingPayouts = await CommunityPayoutModel.find({
      communityCode,
      status: { $ne: PAYOUT_STATUS.PAID },
    }).lean();

    if (processingPayouts.length > 1) {
      throw new Error(`This community has more than 1 processing payouts`);
    } else if (processingPayouts.length === 0) {
      throw new Error(`This community has no processing payouts`);
    }
    // eslint-disable-next-line no-param-reassign
    payoutId = processingPayouts[0]._id;
  }
  const payout = await CommunityPayoutModel.findById(payoutId).lean();
  if (!payout) {
    throw new Error('No payout found');
  }
  if (payout.status === PAYOUT_STATUS.PAID) {
    // Trigger payout to finance only
    await sendPayoutNotification(payoutId, true, operator);
    return;
  }

  const currentDate = new Date();

  switch (payout.payoutChannel) {
    case PAYOUT_CHANNEL.GLOBAL:
    case PAYOUT_CHANNEL.STRIPE_INDIA: {
      await CommunityPayoutModel.updateOne(
        {
          _id: payoutId,
          status: {
            $in: [PAYOUT_STATUS.PROCESSING, PAYOUT_STATUS.PENDING_REPLY],
          },
        },
        {
          $set: {
            status: PAYOUT_STATUS.PAID,
            payoutDate: currentDate,
            payoutStatusTransitionDate: currentDate,
            operator,
          },
        }
      );
      await sendPayoutNotification(payoutId);
      break;
    }
    case PAYOUT_CHANNEL.STRIPE_CONNECT: {
      await processStripeConnectPayout({ payout });
      break;
    }
    default:
      throw new Error('Invalid payout channel');
  }
};

const updateCommunityPayoutToPendingReply = async ({
  payoutId,
  communityCode,
  operator,
}) => {
  if (communityCode) {
    const processingPayout = await CommunityPayoutModel.findOne({
      communityCode,
      status: PAYOUT_STATUS.PROCESSING,
    }).lean();

    if (!processingPayout) {
      throw new Error(`This community has no processing payouts`);
    }

    // Check if there are multiple processing payouts
    const processingCount = await CommunityPayoutModel.countDocuments({
      communityCode,
      status: PAYOUT_STATUS.PROCESSING,
    });

    if (processingCount > 1) {
      throw new Error(`This community has more than 1 processing payouts`);
    }

    // eslint-disable-next-line no-param-reassign
    payoutId = processingPayout._id;
  }

  const payout = await CommunityPayoutModel.findById(payoutId).lean();
  if (!payout) {
    throw new Error('No payout found');
  }

  // Only allow transition from PROCESSING to PENDING_REPLY
  if (payout.status !== PAYOUT_STATUS.PROCESSING) {
    throw new Error(
      `Cannot update payout from ${payout.status} to PENDING_REPLY. Only PROCESSING payouts can be updated to PENDING_REPLY.`
    );
  }

  const updateResult = await CommunityPayoutModel.updateOne(
    {
      _id: payoutId,
      status: PAYOUT_STATUS.PROCESSING,
    },
    {
      $set: {
        status: PAYOUT_STATUS.PENDING_REPLY,
        payoutStatusTransitionDate: new Date(),
        operator,
      },
    }
  );

  if (updateResult.matchedCount === 0) {
    throw new Error(
      `Cannot update payout from ${payout.status} to PENDING_REPLY. Only PROCESSING payouts can be updated to PENDING_REPLY.`
    );
  }

  if (updateResult.modifiedCount === 0) {
    throw new Error('Payout update failed - no changes made');
  }
};

exports.updateCommunityPayout = async (
  payoutIds,
  communityCodes,
  operator
) => {
  const resultList = [];
  if (payoutIds) {
    await Promise.all(
      payoutIds.map(async (payoutId) => {
        try {
          await updateCommunityPayoutToPaid({ payoutId, operator });
        } catch (err) {
          resultList.push(`ID [${payoutId}] error: ${err.toString()}`);
        }
      })
    );
  } else if (communityCodes) {
    await Promise.all(
      communityCodes.map(async (communityCode) => {
        try {
          await updateCommunityPayoutToPaid({ communityCode, operator });
        } catch (err) {
          resultList.push(`[${communityCode}] error: ${err.toString()}`);
        }
      })
    );
  }
  return resultList;
};

exports.updateCommunityPayoutToPendingReply = async (
  payoutIds,
  communityCodes,
  operator
) => {
  const resultList = [];
  if (payoutIds) {
    await Promise.all(
      payoutIds.map(async (payoutId) => {
        try {
          await updateCommunityPayoutToPendingReply({
            payoutId,
            operator,
          });
        } catch (err) {
          resultList.push(`ID [${payoutId}] error: ${err.toString()}`);
        }
      })
    );
  } else if (communityCodes) {
    await Promise.all(
      communityCodes.map(async (communityCode) => {
        try {
          await updateCommunityPayoutToPendingReply({
            communityCode,
            operator,
          });
        } catch (err) {
          resultList.push(`[${communityCode}] error: ${err.toString()}`);
        }
      })
    );
  }
  return resultList;
};

exports.updateCommunityPayoutByWebhook = async (
  payoutObjectId,
  status,
  failureCode,
  failureReason,
  stripePayoutStatus,
  payoutDate
) => {
  const payout = await CommunityPayoutModel.findById(
    payoutObjectId
  ).lean();

  if (!payout) {
    throw new Error(`Invalid payout id: ${payoutObjectId}`);
  }

  if (status === PAYOUT_STATUS.PAYOUT_FAILED) {
    await CommunityPayoutModel.updateOne(
      {
        _id: payoutObjectId,
      },
      {
        $set: {
          status: PAYOUT_STATUS.PAYOUT_FAILED,
          'stripeConnectPayoutDetails.payoutFailureCode': failureCode,
          'stripeConnectPayoutDetails.payoutFailureReason': failureReason,
        },
      }
    );

    // Send lpbe alert
    const header = 'Stripe connect payout failed';
    const message = {
      'Community code: ': payout.communityCode,
      'Payout ID: ': payout._id,
      'Stripe account ID: ': payout.bankAccountInfo.accountId,
      'Failure code: ': failureCode,
      'Failure reason: ': failureReason,
      'Payout status in Stripe dashboard: ': stripePayoutStatus,
    };

    await sendPayoutFailedNotification(header, message);
    return;
  }

  await CommunityPayoutModel.updateOne(
    {
      _id: payoutObjectId,
      status: PAYOUT_STATUS.PENDING_PAYOUT,
    },
    {
      $set: {
        status: PAYOUT_STATUS.PAID,
        payoutDate,
        payoutStatusTransitionDate: payoutDate,
      },
    }
  );

  await sendPayoutNotification(payoutObjectId);
};
