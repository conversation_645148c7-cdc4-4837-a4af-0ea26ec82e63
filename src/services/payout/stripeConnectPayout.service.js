const PaymentBackendRpc = require('../../rpc/paymentBackend');
const {
  PAYMENT_PROVIDER,
  DEFAULT_CURRENCY,
  CURRENCY_WITH_NON_DECIMAL_POINTS,
} = require('../../constants/common');
const { PAYOUT_STATUS, PAYOUT_TARGET_TYPE } = require('./constants');
const logger = require('../logger.service');

const { getConfigByTypeFromCache } = require('../config.service');
const CommunityPayoutModel = require('../../communitiesAPI/models/communityPayouts.model');
const {
  normalizeAndRoundAmountByCurrency,
} = require('../../utils/currency.util');
const {
  sendPayoutFailedNotification,
} = require('../../communitiesAPI/services/common/payoutDetailsLarkWebhook.service');

const findCurrencyConversionFeeByCurrency = async ({ currency }) => {
  const config = await getConfigByTypeFromCache(
    'stripeFXQuoteLockPricing'
  );
  let rateInPercentage = 0;
  for (const group of Object.values(config.value)) {
    if (group.currencyCodes.includes(currency)) {
      rateInPercentage = group.rateInPercentage;
      break;
    }
  }

  if (rateInPercentage === 0 && currency !== 'ARS') {
    throw new Error(
      `Currency conversion fee rate is not available for ${currency}`
    );
  }
  // Fix 1% conversion version fee + fx quote fee by different currency
  return (rateInPercentage + 1) / 100;
};

const getFxQuoteAndTransferAmount = async ({
  paymentBackendRpc,
  payout,
}) => {
  const fxQuoteResult = await paymentBackendRpc.createStripeFXQuote({
    accountId: payout.bankAccountInfo.accountId,
    toCurrency: payout.payoutCurrency,
    paymentProvider: PAYMENT_PROVIDER.STRIPE_US,
  });

  const fxQuoteId = fxQuoteResult.fxQuote.id;

  // Calculate transfer amount
  // we will use base_rate for conversion
  // eq. usd to sgd == 1.30677
  const baseRate =
    fxQuoteResult.fxQuote.rates?.[DEFAULT_CURRENCY.toLowerCase()]
      ?.rate_details?.base_rate;

  // Assume currency conversion fee = 1.1%
  // (amountInStripeAccountCurrency * baseRate) - (amountInStripeAccountCurrency * baseRate * 1.1%) = 10
  // (amountInStripeAccountCurrency * baseRate) * (1-1.1%) = 10
  // amountInStripeAccountCurrency = (10 / (1-1.1%))/ baseRate
  const currencyConversionRate = await findCurrencyConversionFeeByCurrency(
    {
      currency: payout.payoutCurrency,
    }
  ); // 1.1%

  const transferAmount = normalizeAndRoundAmountByCurrency(
    payout.payoutAmountInLocalCurrency /
      (1 - currencyConversionRate) /
      baseRate,
    DEFAULT_CURRENCY
  );

  return { fxQuoteId, transferAmount };
};

const processTransfer = async ({
  paymentBackendRpc,
  payout,
  transferAmount,
  fxQuoteId,
}) => {
  let balanceAmount;
  let transferReceivedAmount;
  let updatedPayout;
  try {
    const transferResult = await paymentBackendRpc.createStripeTransfer({
      accountId: payout.bankAccountInfo.accountId,
      transferCurrency: DEFAULT_CURRENCY,
      transferAmount,
      fxQuoteId,
      paymentProvider: PAYMENT_PROVIDER.STRIPE_US,
    });
    if (!transferResult) {
      throw new Error(`Failed to create transfer`);
    }

    const receivedCurrency = transferResult.receivedCurrency.toUpperCase();
    transferReceivedAmount = CURRENCY_WITH_NON_DECIMAL_POINTS.includes(
      receivedCurrency
    )
      ? transferResult.receivedAmount * 100
      : transferResult.receivedAmount;
    balanceAmount = CURRENCY_WITH_NON_DECIMAL_POINTS.includes(
      receivedCurrency
    )
      ? transferResult.balanceAmount * 100
      : transferResult.balanceAmount;

    const payoutAmount = transferReceivedAmount;

    updatedPayout = await CommunityPayoutModel.findByIdAndUpdate(
      { _id: payout._id },
      {
        status: PAYOUT_STATUS.TRANSFERRED,
        'stripeConnectPayoutDetails.transferId':
          transferResult.transfer.id,
        'stripeConnectPayoutDetails.transferAmount': transferAmount,
        'stripeConnectPayoutDetails.transferCurrency': DEFAULT_CURRENCY,
        'stripeConnectPayoutDetails.receivedAmount':
          transferReceivedAmount,
        'stripeConnectPayoutDetails.receivedCurrency': receivedCurrency,
        'stripeConnectPayoutDetails.payoutAmount': payoutAmount,
        'stripeConnectPayoutDetails.transferTime': new Date(
          transferResult.transfer.created * 1000
        ),
        'stripeConnectPayoutDetails.balanceAmount': balanceAmount,
      },
      { new: true }
    );
  } catch (err) {
    logger.error(
      `Failed to transfer amount to CM's account, ${err.message}`
    );
    await CommunityPayoutModel.updateOne(
      { _id: payout._id },
      {
        status: PAYOUT_STATUS.TRANSFER_FAILED,
        'stripeConnectPayoutDetails.transferFailedReason': err.message,
      }
    );
    throw err;
  }

  if (balanceAmount !== transferReceivedAmount) {
    const header =
      'Stripe connect - account has remaining balance, need tech team to check';
    const message = {
      'Community code: ': payout.communityCode,
      'Payout ID: ': payout._id,
      'Stripe account ID: ': payout.bankAccountInfo.accountId,
    };

    // for tech:
    // verify the remain balance and transfer received amount manually
    // and datafix payoutAmount to be balanceAmount and proceed the payout
    try {
      await sendPayoutFailedNotification(header, message);
    } catch (err) {
      logger.error(
        `Failed to send payout failed notification to lark, ${err.message}`
      );
    }

    throw new Error('Find tech team for this case');
  }
  return updatedPayout;
};

const processPayout = async ({ paymentBackendRpc, payout }) => {
  try {
    let payoutAmount = payout.stripeConnectPayoutDetails.payoutAmount;
    if (payoutAmount !== payout.stripeConnectPayoutDetails.balanceAmount) {
      throw new Error('Need tech team to datafix for this case');
    }

    const metadata = {
      payoutObjectId: payout._id,
      communityCode: payout.communityCode,
      communityObjectId: payout.communityObjectId,
      payoutTargetType: PAYOUT_TARGET_TYPE.COMMUNITY,
    };
    const payoutCurrency =
      payout.bankAccountInfo.payoutCurrency.toUpperCase();

    if (CURRENCY_WITH_NON_DECIMAL_POINTS.includes(payoutCurrency)) {
      // eslint-disable-next-line no-param-reassign
      payoutAmount /= 100;
    }
    const payoutResult = await paymentBackendRpc.createStripePayout({
      accountId: payout.bankAccountInfo.accountId,
      payoutCurrency,
      payoutAmount,
      metadata,
      paymentProvider: PAYMENT_PROVIDER.STRIPE_US,
    });
    if (!payoutResult) {
      throw new Error(`Failed to create payout`);
    }

    await CommunityPayoutModel.updateOne(
      { _id: payout._id },
      {
        status: PAYOUT_STATUS.PENDING_PAYOUT,
        'stripeConnectPayoutDetails.payoutId': payoutResult.payout.id,
        'stripeConnectPayoutDetails.payoutArrivalDate': new Date(
          payoutResult.payout.arrival_date * 1000
        ),
        'stripeConnectPayoutDetails.payoutInitiateTime': new Date(
          payoutResult.payout.created * 1000
        ),
      }
    );
  } catch (err) {
    logger.error(`Failed to payout amount to CM's account`);
    throw err;
  }
};

exports.processStripeConnectPayout = async ({ payout }) => {
  let payoutStatus = payout.status;
  // Update payout to approved
  if (
    [PAYOUT_STATUS.PROCESSING, PAYOUT_STATUS.PENDING_REPLY].includes(
      payoutStatus
    )
  ) {
    const updateQuery = {
      status: PAYOUT_STATUS.APPROVED,
    };
    if (payoutStatus === PAYOUT_STATUS.PENDING_REPLY) {
      updateQuery.payoutStatusTransitionDate = new Date();
    }

    const updateResult = await CommunityPayoutModel.updateOne(
      {
        _id: payout._id,
        status: {
          $in: [PAYOUT_STATUS.PROCESSING, PAYOUT_STATUS.PENDING_REPLY],
        },
      },
      updateQuery
    ).lean();
    payoutStatus = PAYOUT_STATUS.APPROVED;
    if (updateResult.modifiedCount !== 1) {
      throw new Error(
        `Not able to approve this payout because this payout is not in [${PAYOUT_STATUS.PROCESSING}, ${PAYOUT_STATUS.PENDING_REPLY}] status`
      );
    }
  }

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  let transferAmount;
  let fxQuoteId;

  // Transfer amount to CM stripe account, and update payout to transferred
  if (
    [PAYOUT_STATUS.APPROVED, PAYOUT_STATUS.TRANSFER_FAILED].includes(
      payoutStatus
    )
  ) {
    // 1. payout currency == stripe account currency and payout currency != USD,
    //    -> payout amount will be payout.payoutAmountInLocalCurrency
    // 2. payout currency != stripe account currency
    //    -> we will transfer USD amount from NAS account to their account
    //    -> and payout the amount that received in their account
    if (
      payout.bankAccountInfo.payoutCurrency.toUpperCase() ===
        payout.payoutCurrency.toUpperCase() &&
      payout.payoutCurrency !== DEFAULT_CURRENCY
    ) {
      const result = await getFxQuoteAndTransferAmount({
        paymentBackendRpc,
        payout,
      });
      transferAmount = result.transferAmount;
      if (payout.payoutCurrency !== 'ARS') {
        fxQuoteId = result.fxQuoteId;
      }
    } else {
      // payout currency != stripe account currency
      // We will just use USD amount as transfer amount
      transferAmount = payout.payoutAmountInUsd;
    }

    // eslint-disable-next-line no-param-reassign
    payout = await processTransfer({
      paymentBackendRpc,
      payout,
      transferAmount,
      fxQuoteId,
    });
    payoutStatus = PAYOUT_STATUS.TRANSFERRED;
  }

  // Payout to CM's bank account, and update payout to pending payout
  if (
    [PAYOUT_STATUS.TRANSFERRED, PAYOUT_STATUS.PAYOUT_FAILED].includes(
      payoutStatus
    )
  ) {
    await processPayout({ paymentBackendRpc, payout });
  }
};
