const ObjectId = require('mongoose').Types.ObjectId;

const CommunityPayoutModel = require('../../communitiesAPI/models/communityPayouts.model');
const RevenueTransactionModel = require('../../models/revenueTransaction.model');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const CommunityManagerPayoutModel = require('../../communitiesAPI/models/communityManagerPayout.model');
const WalletModel = require('../../models/wallet/wallet.model');
const {
  DEFAULT_CURRENCY,
  PURCHASE_TYPE,
  SUB_WALLET_TYPE,
  PAYMENT_PROVIDER,
  WALLET_TYPE,
  TRANSACTION_TYPE,
} = require('../../constants/common');
const { PAYOUT_STATUS, MINIMUM_PAYOUT_AMOUNT } = require('./constants');
const { ParamError } = require('../../utils/error.util');
const {
  sendErrorNotification,
} = require('../../communitiesAPI/services/common/payoutDetailsLarkWebhook.service');

const getCurrentBalanceByRevenueTransactionResult = async (
  community,
  revenueTransactionResult
) => {
  const bankAccountInfo = await CommunityManagerPayoutModel.findOne({
    communityId: community._id,
  }).lean();

  // Generate payout title
  const payoutTitle = new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
  }).format(new Date());

  const payout = {
    advancePayouts: [],
    applyNewUI: true,
    currency: revenueTransactionResult.localCurrency,
    totalNetAmount: revenueTransactionResult.netAmountInLocalCurrency,
    totalEarningAmount:
      revenueTransactionResult.discountedItemPriceInLocalCurrency,
    whtFee: revenueTransactionResult.feeBreakdownInLocalCurrency?.whtFee,
    nasFeeGst:
      revenueTransactionResult.feeBreakdownInLocalCurrency?.gstOnRevenue -
      revenueTransactionResult.passOnFeeBreakdownInLocalCurrency
        ?.gstOnRevenue,
    paymentGatewayFeeGst:
      revenueTransactionResult.feeBreakdownInLocalCurrency?.gst -
      revenueTransactionResult.passOnFeeBreakdownInLocalCurrency?.gst,
    totalNasFee:
      revenueTransactionResult.revenueShareAmountInLocalCurrency +
      revenueTransactionResult.feeBreakdownInLocalCurrency?.gstOnRevenue,
    totalPaymentGatewayFee:
      revenueTransactionResult.feeBreakdownInLocalCurrency?.processingFee +
      revenueTransactionResult.feeBreakdownInLocalCurrency?.gst +
      revenueTransactionResult.feeBreakdownInLocalCurrency?.gatewayFee +
      (revenueTransactionResult.feeBreakdownInLocalCurrency
        ?.internationalFee ?? 0),
    internationalFee:
      revenueTransactionResult.feeBreakdownInLocalCurrency
        ?.internationalFee ?? 0,
    refundItemPrice:
      revenueTransactionResult.refundBreakdownInLocalCurrency
        ?.discountedItemPrice,
    refundProcessingFee:
      revenueTransactionResult.refundBreakdownInLocalCurrency?.fee
        .refundProcessingFee,
    revenueTransactionInfo: {
      startDate: revenueTransactionResult.transactionStartTime,
      endDate: new Date(),
      count: revenueTransactionResult.revenueTransactionIds.length,
    },
    affiliateCommission:
      revenueTransactionResult.affiliateCommissionInLocalCurrency,
    refundedAffiliateCommission:
      revenueTransactionResult.refundedAffiliateCommissionInLocalCurrency,
    referralReward:
      revenueTransactionResult.referralRewardInLocalCurrency -
      revenueTransactionResult.refundReferralRewardInLocalCurrency,
    bankAccountInfo: {
      name: bankAccountInfo?.accountName,
      accountNumber: bankAccountInfo?.accountNumber,
      bank: bankAccountInfo?.bankName,
    },
    payoutTitle,
  };

  payout.nasFee =
    payout.totalNasFee -
    (revenueTransactionResult.passOnRevenueShareAmountInLocalCurrency +
      revenueTransactionResult.passOnFeeBreakdownInLocalCurrency
        ?.gstOnRevenue);
  payout.paymentGatewayFee =
    payout.totalPaymentGatewayFee -
    (revenueTransactionResult.passOnFeeBreakdownInLocalCurrency
      ?.processingFee +
      revenueTransactionResult.passOnFeeBreakdownInLocalCurrency?.gst +
      revenueTransactionResult.passOnFeeBreakdownInLocalCurrency
        ?.gatewayFee +
      revenueTransactionResult.passOnFeeBreakdownInLocalCurrency
        ?.internationalFee);

  payout.payoutAmount =
    payout.totalEarningAmount + // Discounted item price does not include referral rewards
    payout.referralReward -
    payout.nasFee -
    payout.paymentGatewayFee -
    payout.whtFee -
    payout.refundItemPrice -
    payout.refundProcessingFee -
    payout.affiliateCommission +
    payout.refundedAffiliateCommission;

  return payout;
};

function reorderBalances(balances, baseCurrency) {
  const baseCurrencyBalance = balances.find(
    (balance) => balance.currency === baseCurrency
  );
  const otherBalances = balances.filter(
    (balance) => balance.currency !== baseCurrency
  );

  if (baseCurrencyBalance) {
    return [baseCurrencyBalance, ...otherBalances];
  }

  return otherBalances;
}

const getCurrentBalancePipeline = (community, paymentProviderList) => {
  const pipeline = [
    {
      $match:
        /**
         * query: The query in MQL.
         */
        {
          communityObjectId: community._id,
          payoutObjectId: {
            $exists: false,
          },
          purchaseType: {
            $in: [
              PURCHASE_TYPE.SUBSCRIPTION,
              PURCHASE_TYPE.EVENT,
              PURCHASE_TYPE.FOLDER,
              PURCHASE_TYPE.CHALLENGE,
              PURCHASE_TYPE.REFERRAL_BONUS,
              PURCHASE_TYPE.ONE_DOLLAR_GIFT,
              PURCHASE_TYPE.CAMPAIGN_REWARD,
              PURCHASE_TYPE.SESSION,
              PURCHASE_TYPE.REFUND,
              PURCHASE_TYPE.ADJUSTMENT_ADD,
              PURCHASE_TYPE.ADJUSTMENT_DEDUCT,
              PURCHASE_TYPE.CHARGEBACK,
              PURCHASE_TYPE.ZERO_LINK,
              PURCHASE_TYPE.REFERRAL_REWARD,
            ],
          },
          paymentProvider: { $in: paymentProviderList },
        },
    },
    {
      $lookup: {
        from: 'revenue_transactions',
        let: {
          transactionReferenceId: '$transactionReferenceId',
          paymentProvider: '$paymentProvider',
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  {
                    $eq: ['$$paymentProvider', '$paymentProvider'],
                  },
                  {
                    $eq: [
                      '$$transactionReferenceId',
                      '$refundedTransactionReferenceId',
                    ],
                  },
                ],
              },
            },
          },
          {
            $project: {
              amountBreakdownInUsd: 1,
              amountBreakdownInLocalCurrency: 1,
              amountBreakdownInOriginalCurrency: 1,
            },
          },
        ],
        as: 'inboundData',
      },
    },
    {
      $addFields: {
        inboundData: {
          $first: '$inboundData',
        },
      },
    },
    {
      $project: {
        /**
         * specifications: The fields to
         *   include or exclude.
         */
        purchaseType: 1,
        communityObjectId: 1,
        passOnTakeRate: 1,
        passOnPaymentGatewayFee: 1,
        transactionType: 1,
        transactionCreatedAt: 1,
        amountBreakdownInUsd: 1,
        amountBreakdownInLocalCurrency: {
          $ifNull: [
            '$amountBreakdownInOriginalCurrency',
            '$amountBreakdownInLocalCurrency',
          ],
        },
        inboundAmountBreakdownInUsd: '$inboundData.amountBreakdownInUsd',
        inboundAmountBreakdownInLocalCurrency: {
          $ifNull: [
            '$inboundData.amountBreakdownInOriginalCurrency',
            '$inboundData.amountBreakdownInLocalCurrency',
          ],
        },
      },
    },
    {
      $addFields: {
        // Local Currency
        localCurrency: '$amountBreakdownInLocalCurrency.currency',
        // to cross check final balance
        netAmountInUsd: {
          $cond: {
            if: {
              $eq: ['$transactionType', 'INBOUND'],
            },
            then: '$amountBreakdownInUsd.netAmount',
            else: {
              $multiply: [-1, '$amountBreakdownInUsd.netAmount'],
            },
          },
        },
        netAmountInLocalCurrency: {
          $cond: {
            if: {
              $eq: ['$transactionType', 'INBOUND'],
            },
            then: '$amountBreakdownInLocalCurrency.netAmount',
            else: {
              $multiply: [-1, '$amountBreakdownInLocalCurrency.netAmount'],
            },
          },
        },
        // Get total paid (earning) amount -> discounted item price
        discountedItemPriceInUsd: {
          $cond: {
            if: {
              $and: [
                {
                  $eq: ['$transactionType', TRANSACTION_TYPE.INBOUND],
                },
                {
                  $ne: ['$purchaseType', PURCHASE_TYPE.REFERRAL_REWARD],
                },
              ],
            },
            then: '$amountBreakdownInUsd.discountedItemPrice',
            else: 0,
          },
        },
        discountedItemPriceInLocalCurrency: {
          $cond: {
            if: {
              $and: [
                {
                  $eq: ['$transactionType', TRANSACTION_TYPE.INBOUND],
                },
                {
                  $ne: ['$purchaseType', PURCHASE_TYPE.REFERRAL_REWARD],
                },
              ],
            },
            then: '$amountBreakdownInLocalCurrency.discountedItemPrice',
            else: 0,
          },
        },
        // total refunded discount item price
        refundDiscountedItemPriceInUsd: {
          $cond: {
            if: {
              $and: [
                {
                  $eq: ['$transactionType', TRANSACTION_TYPE.OUTBOUND],
                },
                {
                  $ne: ['$purchaseType', PURCHASE_TYPE.REFERRAL_REWARD],
                },
              ],
            },
            then: '$amountBreakdownInUsd.discountedItemPrice',
            else: 0,
          },
        },
        refundDiscountedItemPriceInLocalCurrency: {
          $cond: {
            if: {
              $and: [
                {
                  $eq: ['$transactionType', TRANSACTION_TYPE.OUTBOUND],
                },
                {
                  $ne: ['$purchaseType', PURCHASE_TYPE.REFERRAL_REWARD],
                },
              ],
            },
            then: '$amountBreakdownInLocalCurrency.discountedItemPrice',
            else: 0,
          },
        },

        // final revenue share after refund
        revenueShareAmountInUsd: {
          $cond: {
            if: {
              $eq: ['$transactionType', 'INBOUND'],
            },
            then: '$amountBreakdownInUsd.revenueShareAmount',
            else: {
              $multiply: ['$amountBreakdownInUsd.revenueShareAmount', -1],
            },
          },
        },
        revenueShareAmountInLocalCurrency: {
          $cond: {
            if: {
              $eq: ['$transactionType', 'INBOUND'],
            },
            then: '$amountBreakdownInLocalCurrency.revenueShareAmount',
            else: {
              $multiply: [
                '$amountBreakdownInLocalCurrency.revenueShareAmount',
                -1,
              ],
            },
          },
        },
        // Referral reward
        referralRewardInUsd: {
          $cond: {
            if: {
              $and: [
                {
                  $eq: ['$transactionType', TRANSACTION_TYPE.INBOUND],
                },
                {
                  $eq: ['$purchaseType', PURCHASE_TYPE.REFERRAL_REWARD],
                },
              ],
            },
            then: '$amountBreakdownInUsd.netAmount',
            else: 0,
          },
        },
        referralRewardInLocalCurrency: {
          $cond: {
            if: {
              $and: [
                {
                  $eq: ['$transactionType', TRANSACTION_TYPE.INBOUND],
                },
                {
                  $eq: ['$purchaseType', PURCHASE_TYPE.REFERRAL_REWARD],
                },
              ],
            },
            then: '$amountBreakdownInLocalCurrency.netAmount',
            else: 0,
          },
        },

        // final passon revenue share after refund
        passOnRevenueShareAmountInUsd: {
          $cond: {
            if: {
              $and: [
                { $eq: ['$passOnTakeRate', true] },
                { $eq: ['$transactionType', 'INBOUND'] },
              ],
            },
            then: '$amountBreakdownInUsd.revenueShareAmount',
            else: {
              $cond: {
                if: {
                  $and: [
                    { $eq: ['$passOnTakeRate', true] },
                    { $eq: ['$transactionType', 'OUTBOUND'] },
                  ],
                },
                then: {
                  $multiply: [
                    -1,
                    '$amountBreakdownInUsd.revenueShareAmount',
                  ],
                },
                else: 0,
              },
            },
          },
        },
        passOnRevenueShareAmountInLocalCurrency: {
          $cond: {
            if: {
              $and: [
                { $eq: ['$passOnTakeRate', true] },
                { $eq: ['$transactionType', 'INBOUND'] },
              ],
            },
            then: '$amountBreakdownInLocalCurrency.revenueShareAmount',
            else: {
              $cond: {
                if: {
                  $and: [
                    { $eq: ['$passOnTakeRate', true] },
                    { $eq: ['$transactionType', 'OUTBOUND'] },
                  ],
                },
                then: {
                  $multiply: [
                    -1,
                    '$amountBreakdownInLocalCurrency.revenueShareAmount',
                  ],
                },
                else: 0,
              },
            },
          },
        },
        // final passon revenue share gst after refund
        passOnFeeBreakdownInUsdGstOnRevenue: {
          $cond: {
            if: {
              $and: [
                { $eq: ['$passOnTakeRate', true] },
                { $eq: ['$transactionType', 'INBOUND'] },
              ],
            },
            then: '$amountBreakdownInUsd.fee.gstOnRevenue',
            else: {
              $cond: {
                if: {
                  $and: [
                    { $eq: ['$passOnTakeRate', true] },
                    { $eq: ['$transactionType', 'OUTBOUND'] },
                  ],
                },
                then: {
                  $multiply: [
                    -1,
                    '$inboundAmountBreakdownInUsd.fee.gstOnRevenue',
                  ],
                },
                else: 0,
              },
            },
          },
        },
        passOnFeeBreakdownInLocalCurrencyGstOnRevenue: {
          $cond: {
            if: {
              $and: [
                { $eq: ['$passOnTakeRate', true] },
                { $eq: ['$transactionType', 'INBOUND'] },
              ],
            },
            then: '$amountBreakdownInLocalCurrency.fee.gstOnRevenue',
            else: {
              $cond: {
                if: {
                  $and: [
                    { $eq: ['$passOnTakeRate', true] },
                    { $eq: ['$transactionType', 'OUTBOUND'] },
                  ],
                },
                then: {
                  $multiply: [
                    -1,
                    '$inboundAmountBreakdownInLocalCurrency.fee.gstOnRevenue',
                  ],
                },
                else: 0,
              },
            },
          },
        },
        // final passon gateway fee after refund
        passOnFeeBreakdownInUsdGatewayFee: {
          $cond: {
            if: {
              $and: [
                { $eq: ['$passOnPaymentGatewayFee', true] },
                { $eq: ['$transactionType', 'INBOUND'] },
              ],
            },
            then: '$amountBreakdownInUsd.fee.gatewayFee',
            else: {
              $cond: {
                if: {
                  $and: [
                    { $eq: ['$passOnPaymentGatewayFee', true] },
                    { $eq: ['$transactionType', 'OUTBOUND'] },
                  ],
                },
                then: {
                  $multiply: [
                    -1,
                    '$inboundAmountBreakdownInUsd.fee.gatewayFee',
                  ],
                },
                else: 0,
              },
            },
          },
        },
        passOnFeeBreakdownInLocalCurrencyGatewayFee: {
          $cond: {
            if: {
              $and: [
                { $eq: ['$passOnPaymentGatewayFee', true] },
                { $eq: ['$transactionType', 'INBOUND'] },
              ],
            },
            then: '$amountBreakdownInLocalCurrency.fee.gatewayFee',
            else: {
              $cond: {
                if: {
                  $and: [
                    { $eq: ['$passOnPaymentGatewayFee', true] },
                    { $eq: ['$transactionType', 'OUTBOUND'] },
                  ],
                },
                then: {
                  $multiply: [
                    -1,
                    '$inboundAmountBreakdownInLocalCurrency.fee.gatewayFee',
                  ],
                },
                else: 0,
              },
            },
          },
        },
        passOnFeeBreakdownInUsdGst: {
          $cond: {
            if: {
              $and: [
                { $eq: ['$passOnPaymentGatewayFee', true] },
                { $eq: ['$transactionType', 'INBOUND'] },
              ],
            },
            then: '$amountBreakdownInUsd.fee.gst',
            else: {
              $cond: {
                if: {
                  $and: [
                    { $eq: ['$passOnPaymentGatewayFee', true] },
                    { $eq: ['$transactionType', 'OUTBOUND'] },
                  ],
                },
                then: {
                  $multiply: [-1, '$inboundAmountBreakdownInUsd.fee.gst'],
                },
                else: 0,
              },
            },
          },
        },
        passOnFeeBreakdownInLocalCurrencyGst: {
          $cond: {
            if: {
              $and: [
                { $eq: ['$passOnPaymentGatewayFee', true] },
                { $eq: ['$transactionType', 'INBOUND'] },
              ],
            },
            then: '$amountBreakdownInLocalCurrency.fee.gst',
            else: {
              $cond: {
                if: {
                  $and: [
                    { $eq: ['$passOnPaymentGatewayFee', true] },
                    { $eq: ['$transactionType', 'OUTBOUND'] },
                  ],
                },
                then: {
                  $multiply: [
                    -1,
                    '$inboundAmountBreakdownInLocalCurrency.fee.gst',
                  ],
                },
                else: 0,
              },
            },
          },
        },
        passOnFeeBreakdownInUsdProcessingFee: {
          $cond: {
            if: {
              $and: [
                { $eq: ['$passOnPaymentGatewayFee', true] },
                { $eq: ['$transactionType', 'INBOUND'] },
              ],
            },
            then: '$amountBreakdownInUsd.fee.processingFee',
            else: {
              $cond: {
                if: {
                  $and: [
                    { $eq: ['$passOnPaymentGatewayFee', true] },
                    { $eq: ['$transactionType', 'OUTBOUND'] },
                  ],
                },
                then: {
                  $multiply: [
                    -1,
                    '$inboundAmountBreakdownInUsd.fee.processingFee',
                  ],
                },
                else: 0,
              },
            },
          },
        },
        passOnFeeBreakdownInLocalCurrencyProcessingFee: {
          $cond: {
            if: {
              $and: [
                { $eq: ['$passOnPaymentGatewayFee', true] },
                { $eq: ['$transactionType', 'INBOUND'] },
              ],
            },
            then: '$amountBreakdownInLocalCurrency.fee.processingFee',
            else: {
              $cond: {
                if: {
                  $and: [
                    { $eq: ['$passOnPaymentGatewayFee', true] },
                    { $eq: ['$transactionType', 'OUTBOUND'] },
                  ],
                },
                then: {
                  $multiply: [
                    -1,
                    '$inboundAmountBreakdownInLocalCurrency.fee.processingFee',
                  ],
                },
                else: 0,
              },
            },
          },
        },
        passOnFeeBreakdownInUsdInternationalFee: {
          $cond: {
            if: {
              $and: [
                { $eq: ['$passOnPaymentGatewayFee', true] },
                { $eq: ['$transactionType', 'INBOUND'] },
              ],
            },
            then: '$amountBreakdownInUsd.fee.internationalFee',
            else: {
              $cond: {
                if: {
                  $and: [
                    { $eq: ['$passOnPaymentGatewayFee', true] },
                    { $eq: ['$transactionType', 'OUTBOUND'] },
                  ],
                },
                then: {
                  $multiply: [
                    -1,
                    '$inboundAmountBreakdownInUsd.fee.internationalFee',
                  ],
                },
                else: 0,
              },
            },
          },
        },
        passOnFeeBreakdownInLocalCurrencyInternationalFee: {
          $cond: {
            if: {
              $and: [
                { $eq: ['$passOnPaymentGatewayFee', true] },
                { $eq: ['$transactionType', 'INBOUND'] },
              ],
            },
            then: '$amountBreakdownInLocalCurrency.fee.internationalFee',
            else: {
              $cond: {
                if: {
                  $and: [
                    { $eq: ['$passOnPaymentGatewayFee', true] },
                    { $eq: ['$transactionType', 'OUTBOUND'] },
                  ],
                },
                then: {
                  $multiply: [
                    -1,
                    '$inboundAmountBreakdownInLocalCurrency.fee.internationalFee',
                  ],
                },
                else: 0,
              },
            },
          },
        },

        // total fee breakdown. For refund it adds up
        feeBreakdownInUsdGatewayFee: {
          $cond: {
            if: {
              $eq: ['$transactionType', 'INBOUND'],
            },
            then: '$amountBreakdownInUsd.fee.gatewayFee',
            else: {
              $multiply: [-1, '$amountBreakdownInUsd.fee.gatewayFee'],
            },
          },
        },
        feeBreakdownInLocalCurrencyGatewayFee: {
          $cond: {
            if: {
              $eq: ['$transactionType', 'INBOUND'],
            },
            then: '$amountBreakdownInLocalCurrency.fee.gatewayFee',
            else: {
              $multiply: [
                -1,
                '$amountBreakdownInLocalCurrency.fee.gatewayFee',
              ],
            },
          },
        },
        feeBreakdownInUsdGst: {
          $cond: {
            if: {
              $eq: ['$transactionType', 'INBOUND'],
            },
            then: '$amountBreakdownInUsd.fee.gst',
            else: {
              $multiply: [-1, '$amountBreakdownInUsd.fee.gst'],
            },
          },
        },
        feeBreakdownInLocalCurrencyGst: {
          $cond: {
            if: {
              $eq: ['$transactionType', 'INBOUND'],
            },
            then: '$amountBreakdownInLocalCurrency.fee.gst',
            else: {
              $multiply: [-1, '$amountBreakdownInLocalCurrency.fee.gst'],
            },
          },
        },
        feeBreakdownInUsdProcessingFee: {
          $cond: {
            if: {
              $eq: ['$transactionType', 'INBOUND'],
            },
            then: '$amountBreakdownInUsd.fee.processingFee',
            else: {
              $multiply: [-1, '$amountBreakdownInUsd.fee.processingFee'],
            },
          },
        },
        feeBreakdownInLocalCurrencyProcessingFee: {
          $cond: {
            if: {
              $eq: ['$transactionType', 'INBOUND'],
            },
            then: '$amountBreakdownInLocalCurrency.fee.processingFee',
            else: {
              $multiply: [
                -1,
                '$amountBreakdownInLocalCurrency.fee.processingFee',
              ],
            },
          },
        },
        feeBreakdownInUsdInternationalFee: {
          $cond: {
            if: {
              $eq: ['$transactionType', 'INBOUND'],
            },
            then: '$amountBreakdownInUsd.fee.internationalFee',
            else: {
              $multiply: [
                -1,
                '$amountBreakdownInUsd.fee.internationalFee',
              ],
            },
          },
        },
        feeBreakdownInLocalCurrencyInternationalFee: {
          $cond: {
            if: {
              $eq: ['$transactionType', 'INBOUND'],
            },
            then: '$amountBreakdownInLocalCurrency.fee.internationalFee',
            else: {
              $multiply: [
                -1,
                '$amountBreakdownInLocalCurrency.fee.internationalFee',
              ],
            },
          },
        },
        feeBreakdownInUsdGstOnRevenue: {
          $cond: {
            if: {
              $eq: ['$transactionType', 'INBOUND'],
            },
            then: '$amountBreakdownInUsd.fee.gstOnRevenue',
            else: {
              $multiply: [-1, '$amountBreakdownInUsd.fee.gstOnRevenue'],
            },
          },
        },
        feeBreakdownInLocalCurrencyGstOnRevenue: {
          $cond: {
            if: {
              $eq: ['$transactionType', 'INBOUND'],
            },
            then: '$amountBreakdownInLocalCurrency.fee.gstOnRevenue',
            else: {
              $multiply: [
                -1,
                '$amountBreakdownInLocalCurrency.fee.gstOnRevenue',
              ],
            },
          },
        },
        feeBreakdownInUsdWhtFee: {
          $cond: {
            if: {
              $eq: ['$transactionType', 'INBOUND'],
            },
            then: '$amountBreakdownInUsd.fee.whtFee',
            else: {
              $multiply: [-1, '$amountBreakdownInUsd.fee.whtFee'],
            },
          },
        },
        feeBreakdownInLocalCurrencyWhtFee: {
          $cond: {
            if: {
              $eq: ['$transactionType', 'INBOUND'],
            },
            then: '$amountBreakdownInLocalCurrency.fee.whtFee',
            else: {
              $multiply: [
                -1,
                '$amountBreakdownInLocalCurrency.fee.whtFee',
              ],
            },
          },
        },
        // Refund referral reward
        refundReferralRewardInUsd: {
          $cond: {
            if: {
              $and: [
                { $eq: ['$transactionType', TRANSACTION_TYPE.OUTBOUND] },
                { $eq: ['$purchaseType', PURCHASE_TYPE.REFERRAL_REWARD] },
              ],
            },
            then: '$amountBreakdownInUsd.netAmount',
            else: 0,
          },
        },
        refundReferralRewardInLocalCurrency: {
          $cond: {
            if: {
              $and: [
                { $eq: ['$transactionType', TRANSACTION_TYPE.OUTBOUND] },
                { $eq: ['$purchaseType', PURCHASE_TYPE.REFERRAL_REWARD] },
              ],
            },
            then: '$amountBreakdownInLocalCurrency.netAmount',
            else: 0,
          },
        },
      },
    },
    {
      $group:
        /**
         * _id: The id of the group.
         * fieldN: The first field name.
         */
        {
          _id: {
            communityObjectId: '$communityObjectId',
            localCurrency: '$localCurrency',
          },
          revenueTransactionIds: {
            $push: '$_id',
          },
          transactionCreatedTime: {
            $push: '$transactionCreatedAt',
          },
          netAmountInUsd: {
            $sum: '$netAmountInUsd',
          },
          netAmountInLocalCurrency: {
            $sum: '$netAmountInLocalCurrency',
          },
          discountedItemPriceInUsd: {
            $sum: '$discountedItemPriceInUsd',
          },
          discountedItemPriceInLocalCurrency: {
            $sum: '$discountedItemPriceInLocalCurrency',
          },
          refundDiscountedItemPriceInUsd: {
            $sum: '$refundDiscountedItemPriceInUsd',
          },
          refundDiscountedItemPriceInLocalCurrency: {
            $sum: '$refundDiscountedItemPriceInLocalCurrency',
          },
          revenueShareAmountInUsd: {
            $sum: '$revenueShareAmountInUsd',
          },
          revenueShareAmountInLocalCurrency: {
            $sum: '$revenueShareAmountInLocalCurrency',
          },
          // Passon info
          passOnRevenueShareAmountInUsd: {
            $sum: '$passOnRevenueShareAmountInUsd',
          },
          passOnRevenueShareAmountInLocalCurrency: {
            $sum: '$passOnRevenueShareAmountInLocalCurrency',
          },
          passOnFeeBreakdownInUsdGstOnRevenue: {
            $sum: '$passOnFeeBreakdownInUsdGstOnRevenue',
          },
          passOnFeeBreakdownInLocalCurrencyGstOnRevenue: {
            $sum: '$passOnFeeBreakdownInLocalCurrencyGstOnRevenue',
          },
          passOnFeeBreakdownInUsdGatewayFee: {
            $sum: '$passOnFeeBreakdownInUsdGatewayFee',
          },
          passOnFeeBreakdownInLocalCurrencyGatewayFee: {
            $sum: '$passOnFeeBreakdownInLocalCurrencyGatewayFee',
          },
          passOnFeeBreakdownInUsdGst: {
            $sum: '$passOnFeeBreakdownInUsdGst',
          },
          passOnFeeBreakdownInLocalCurrencyGst: {
            $sum: '$passOnFeeBreakdownInLocalCurrencyGst',
          },
          passOnFeeBreakdownInUsdProcessingFee: {
            $sum: '$passOnFeeBreakdownInUsdProcessingFee',
          },
          passOnFeeBreakdownInLocalCurrencyProcessingFee: {
            $sum: '$passOnFeeBreakdownInLocalCurrencyProcessingFee',
          },
          passOnFeeBreakdownInUsdInternationalFee: {
            $sum: '$passOnFeeBreakdownInUsdInternationalFee',
          },
          passOnFeeBreakdownInLocalCurrencyInternationalFee: {
            $sum: '$passOnFeeBreakdownInLocalCurrencyInternationalFee',
          },
          // total fee breakdown. For refund it adds up
          feeBreakdownInUsdGatewayFee: {
            $sum: '$feeBreakdownInUsdGatewayFee',
          },
          feeBreakdownInLocalCurrencyGatewayFee: {
            $sum: '$feeBreakdownInLocalCurrencyGatewayFee',
          },
          feeBreakdownInUsdGst: {
            $sum: '$feeBreakdownInUsdGst',
          },
          feeBreakdownInLocalCurrencyGst: {
            $sum: '$feeBreakdownInLocalCurrencyGst',
          },
          feeBreakdownInUsdProcessingFee: {
            $sum: '$feeBreakdownInUsdProcessingFee',
          },
          feeBreakdownInLocalCurrencyProcessingFee: {
            $sum: '$feeBreakdownInLocalCurrencyProcessingFee',
          },
          feeBreakdownInUsdInternationalFee: {
            $sum: '$feeBreakdownInUsdInternationalFee',
          },
          feeBreakdownInLocalCurrencyInternationalFee: {
            $sum: '$feeBreakdownInLocalCurrencyInternationalFee',
          },
          feeBreakdownInUsdGstOnRevenue: {
            $sum: '$feeBreakdownInUsdGstOnRevenue',
          },
          feeBreakdownInLocalCurrencyGstOnRevenue: {
            $sum: '$feeBreakdownInLocalCurrencyGstOnRevenue',
          },
          feeBreakdownInUsdWhtFee: {
            $sum: '$feeBreakdownInUsdWhtFee',
          },
          feeBreakdownInLocalCurrencyWhtFee: {
            $sum: '$feeBreakdownInLocalCurrencyWhtFee',
          },
          affiliateCommissionInUsd: {
            $sum: {
              $cond: {
                if: { $eq: ['$transactionType', 'INBOUND'] },
                then: '$amountBreakdownInUsd.affiliateCommissionAmount',
                else: 0,
              },
            },
          },
          affiliateCommissionInLocalCurrency: {
            $sum: {
              $cond: {
                if: { $eq: ['$transactionType', 'INBOUND'] },
                then: '$amountBreakdownInLocalCurrency.affiliateCommissionAmount',
                else: 0,
              },
            },
          },
          referralRewardInUsd: {
            $sum: '$referralRewardInUsd',
          },
          referralRewardInLocalCurrency: {
            $sum: '$referralRewardInLocalCurrency',
          },

          refundFeeBreakdownInUsdRefundProcessingFee: {
            $sum: {
              $cond: {
                if: { $eq: ['$transactionType', 'OUTBOUND'] },
                then: '$amountBreakdownInUsd.fee.refundProcessingFee',
                else: 0,
              },
            },
          },
          refundFeeBreakdownInLocalCurrencyRefundProcessingFee: {
            $sum: {
              $cond: {
                if: { $eq: ['$transactionType', 'OUTBOUND'] },
                then: '$amountBreakdownInLocalCurrency.fee.refundProcessingFee',
                else: 0,
              },
            },
          },
          refundFeeBreakdownInUsdRefundProcessingTax: {
            $sum: {
              $cond: {
                if: { $eq: ['$transactionType', 'OUTBOUND'] },
                then: '$amountBreakdownInUsd.fee.refundProcessingTax',
                else: 0,
              },
            },
          },
          refundFeeBreakdownInLocalCurrencyRefundProcessingTax: {
            $sum: {
              $cond: {
                if: { $eq: ['$transactionType', 'OUTBOUND'] },
                then: '$amountBreakdownInLocalCurrency.fee.refundProcessingTax',
                else: 0,
              },
            },
          },
          refundedAffiliateCommissionInUsd: {
            $sum: {
              $cond: {
                if: { $eq: ['$transactionType', 'OUTBOUND'] },
                then: '$amountBreakdownInUsd.affiliateCommissionAmount',
                else: 0,
              },
            },
          },
          refundedAffiliateCommissionInLocalCurrency: {
            $sum: {
              $cond: {
                if: { $eq: ['$transactionType', 'OUTBOUND'] },
                then: '$amountBreakdownInLocalCurrency.affiliateCommissionAmount',
                else: 0,
              },
            },
          },
          refundReferralRewardInUsd: {
            $sum: '$refundReferralRewardInUsd',
          },
          refundReferralRewardInLocalCurrency: {
            $sum: '$refundReferralRewardInLocalCurrency',
          },
        },
    },
    {
      $project:
        /**
         * specifications: The fields to
         *   include or exclude.
         */
        {
          localCurrency: '$_id.localCurrency',
          revenueTransactionIds: '$revenueTransactionIds',
          transactionStartTime: {
            $min: '$transactionCreatedTime',
          },
          transactionEndTime: {
            $max: '$transactionCreatedTime',
          },
          discountedItemPriceInUsd: 1,
          discountedItemPriceInLocalCurrency: 1,
          netAmountInUsd: 1,
          netAmountInLocalCurrency: 1,
          // USD
          revenueShareAmountInUsd: 1,
          passOnRevenueShareAmountInUsd: 1,
          passOnFeeBreakdownInUsd: {
            gatewayFee: '$passOnFeeBreakdownInUsdGatewayFee',
            gst: '$passOnFeeBreakdownInUsdGst',
            gstOnRevenue: '$passOnFeeBreakdownInUsdGstOnRevenue',
            processingFee: '$passOnFeeBreakdownInUsdProcessingFee',
            internationalFee: '$passOnFeeBreakdownInUsdInternationalFee',
          },
          feeBreakdownInUsd: {
            gatewayFee: '$feeBreakdownInUsdGatewayFee',
            gst: '$feeBreakdownInUsdGst',
            processingFee: '$feeBreakdownInUsdProcessingFee',
            gstOnRevenue: '$feeBreakdownInUsdGstOnRevenue',
            whtFee: '$feeBreakdownInUsdWhtFee',
            internationalFee: '$feeBreakdownInUsdInternationalFee',
          },
          affiliateCommissionInUsd: 1,
          refundedAffiliateCommissionInUsd: 1,
          refundBreakdownInUsd: {
            discountedItemPrice: '$refundDiscountedItemPriceInUsd',
            fee: {
              refundProcessingFee:
                '$refundFeeBreakdownInUsdRefundProcessingFee',
              refundProcessingTax:
                '$refundFeeBreakdownInUsdRefundProcessingTax',
            },
          },
          // In local currency
          revenueShareAmountInLocalCurrency: 1,
          passOnRevenueShareAmountInLocalCurrency: 1,
          passOnFeeBreakdownInLocalCurrency: {
            gatewayFee: '$passOnFeeBreakdownInLocalCurrencyGatewayFee',
            gst: '$passOnFeeBreakdownInLocalCurrencyGst',
            gstOnRevenue: '$passOnFeeBreakdownInLocalCurrencyGstOnRevenue',
            processingFee:
              '$passOnFeeBreakdownInLocalCurrencyProcessingFee',
            internationalFee:
              '$passOnFeeBreakdownInLocalCurrencyInternationalFee',
          },
          feeBreakdownInLocalCurrency: {
            gatewayFee: '$feeBreakdownInLocalCurrencyGatewayFee',
            gst: '$feeBreakdownInLocalCurrencyGst',
            processingFee: '$feeBreakdownInLocalCurrencyProcessingFee',
            gstOnRevenue: '$feeBreakdownInLocalCurrencyGstOnRevenue',
            whtFee: '$feeBreakdownInLocalCurrencyWhtFee',
            internationalFee:
              '$feeBreakdownInLocalCurrencyInternationalFee',
          },
          refundBreakdownInLocalCurrency: {
            discountedItemPrice:
              '$refundDiscountedItemPriceInLocalCurrency',
            fee: {
              refundProcessingFee:
                '$refundFeeBreakdownInLocalCurrencyRefundProcessingFee',
              refundProcessingTax:
                '$refundFeeBreakdownInLocalCurrencyRefundProcessingTax',
            },
          },
          affiliateCommissionInLocalCurrency: 1,
          refundedAffiliateCommissionInLocalCurrency: 1,
          referralRewardInUsd: 1,
          refundReferralRewardInUsd: 1,
          referralRewardInLocalCurrency: 1,
          refundReferralRewardInLocalCurrency: 1,
        },
    },
  ];

  return pipeline;
};

const getCurrentBalanceByPaymentProvider = async (
  community,
  paymentProviderList
) => {
  const pipeline = getCurrentBalancePipeline(
    community,
    paymentProviderList
  );

  const revenueTransactionResults =
    await RevenueTransactionModel.aggregate(pipeline);

  if (revenueTransactionResults.length === 0) {
    return [];
  }

  // Since we dont update transaction's currency when changing base currency
  // So we will display current balance by different currency here
  const currentBalances = [];
  for await (const revenueTransactionResult of revenueTransactionResults) {
    // Special handling for INR community that has transaction from stripe global
    if (
      community.baseCurrency === 'INR' &&
      paymentProviderList.includes(PAYMENT_PROVIDER.STRIPE)
    ) {
      revenueTransactionResult.localCurrency = DEFAULT_CURRENCY;
      revenueTransactionResult.netAmountInLocalCurrency =
        revenueTransactionResult.netAmountInUsd;
      revenueTransactionResult.discountedItemPriceInLocalCurrency =
        revenueTransactionResult.discountedItemPriceInUsd;
      revenueTransactionResult.revenueShareAmountInLocalCurrency =
        revenueTransactionResult.revenueShareAmountInUsd;
      revenueTransactionResult.passOnRevenueShareAmountInLocalCurrency =
        revenueTransactionResult.passOnRevenueShareAmountInUsd;
      revenueTransactionResult.passOnFeeBreakdownInLocalCurrency =
        revenueTransactionResult.passOnFeeBreakdownInUsd;
      revenueTransactionResult.feeBreakdownInLocalCurrency =
        revenueTransactionResult.feeBreakdownInUsd;
      revenueTransactionResult.refundBreakdownInLocalCurrency =
        revenueTransactionResult.refundBreakdownInUsd;
    }

    const currentBalance =
      await getCurrentBalanceByRevenueTransactionResult(
        community,
        revenueTransactionResult
      );
    currentBalances.push(currentBalance);
  }

  return currentBalances;
};

const validatePayoutAmountWithWalletBalance = async (
  community,
  currentBalances
) => {
  // Get payout amount from wallet balance
  const wallet = await WalletModel.findOne({
    communityObjectId: community._id,
    type: WALLET_TYPE.COMMUNITY,
  }).lean();
  for await (const currentBalance of currentBalances) {
    const payoutAmountFromWallet =
      wallet?.balance?.[currentBalance.currency]?.[
        SUB_WALLET_TYPE.AVAILABLE
      ] ?? 0;
    if (currentBalance.payoutAmount !== payoutAmountFromWallet) {
      await sendErrorNotification(
        `Payout amount in current balance from revenue transaction is not matched with the amount in wallet`,
        {
          'Send from: ': 'LPBE',
          'API: ': '/payouts',
          'Community id: ': community._id,
          'Community code: ': community.code,
          'Total amount from Revenue transaction: ': `${currentBalance.payoutAmount} ${currentBalance.currency}`,
          'Total amount from Wallet: ': `${payoutAmountFromWallet} ${currentBalance.currency}`,
        }
      );
    }
    if (payoutAmountFromWallet > 0) {
      currentBalance.payoutAmount = payoutAmountFromWallet;
    }
  }
};

const getCurrencyBalance = async (community) => {
  const paymentProviderForGlobal = [
    PAYMENT_PROVIDER.STRIPE,
    PAYMENT_PROVIDER.STRIPE_US,
    PAYMENT_PROVIDER.XENDIT,
    PAYMENT_PROVIDER.VOLT,
    PAYMENT_PROVIDER.EBANX,
    PAYMENT_PROVIDER.PAYPAL,
  ];
  const paymentProviderForIndia = [
    PAYMENT_PROVIDER.STRIPE_INDIA,
    PAYMENT_PROVIDER.RAZORPAY,
  ];

  const currentBalances = [];

  const currentBalancesForGlobal =
    await getCurrentBalanceByPaymentProvider(
      community,
      paymentProviderForGlobal
    );
  currentBalances.push(...currentBalancesForGlobal);

  const currentBalancesForIndia = await getCurrentBalanceByPaymentProvider(
    community,
    paymentProviderForIndia
  );
  currentBalances.push(...currentBalancesForIndia);

  // Get balance for community rewards
  // Because now we dont change currency of existing transaction if community change the currency
  // So the campaign reward transaction might have different currency from current base currency when we create payout
  // Here will just add campaign reward currency to the correct balance
  const currentBalancesFromNas = await getCurrentBalanceByPaymentProvider(
    community,
    [PAYMENT_PROVIDER.NAS]
  );
  currentBalancesFromNas.forEach((newBalance) => {
    const balanceIndex = currentBalances.findIndex(
      (balance) => balance.currency === newBalance.currency
    );

    if (balanceIndex !== -1) {
      // Update existing balance with new balance data
      const currentBalance = currentBalances[balanceIndex];
      currentBalance.totalNetAmount += newBalance.totalNetAmount;
      currentBalance.totalEarningAmount += newBalance.totalEarningAmount;
      currentBalance.whtFee += newBalance.whtFee;
      currentBalance.totalNasFee += newBalance.totalNasFee;
      currentBalance.totalPaymentGatewayFee +=
        newBalance.totalPaymentGatewayFee;
      currentBalance.payoutAmount += newBalance.payoutAmount;
      currentBalance.revenueTransactionInfo.count +=
        newBalance.revenueTransactionInfo.count;
      currentBalance.referralReward += newBalance.referralReward;
    } else {
      // If not found, add the new balance object to currentBalances
      currentBalances.push(newBalance);
    }
  });

  if (currentBalances.length === 0) {
    return {
      currentBalances: [
        {
          totalNetAmountInUsd: 0,
          totalNetAmountInLocalCurrency: 0,
          localCurrency: community.baseCurrency,
          currency: community.baseCurrency,
        },
      ],
    };
  }

  // Make the first current balance to be the one in base currency
  const orderedCurrentBalance = reorderBalances(
    currentBalances,
    community.baseCurrency
  );

  await validatePayoutAmountWithWalletBalance(community, currentBalances);

  return {
    currentBalances: orderedCurrentBalance,
  };
};

const getNextPayoutDate = () => {
  const now = new Date();
  const nextMonth = new Date(
    Date.UTC(now.getUTCFullYear(), now.getUTCMonth() + 1, 1)
  );
  return nextMonth.toISOString();
};

exports.retrievePayouts = async ({ communityId, pageNo, pageSize }) => {
  const [payouts, totalPayoutSize, totalPayoutAmountByPayoutCurrency] =
    await Promise.all([
      CommunityPayoutModel.find({
        communityObjectId: communityId,
      })
        .sort({ _id: -1 })
        .skip((pageNo - 1) * pageSize)
        .limit(pageSize)
        .lean(),
      CommunityPayoutModel.countDocuments({
        communityObjectId: communityId,
      }),
      CommunityPayoutModel.aggregate([
        {
          $match: {
            communityObjectId: new ObjectId(communityId),
            status: PAYOUT_STATUS.PAID,
          },
        },
        {
          $addFields: {
            payoutAmount: {
              $cond: {
                if: {
                  $eq: ['$payoutCurrency', 'USD'],
                },
                then: '$payoutAmountInUsd',
                else: '$payoutAmountInLocalCurrency',
              },
            },
          },
        },
        {
          $group: {
            _id: '$payoutCurrency',
            payoutAmount: {
              $sum: '$payoutAmount',
            },
          },
        },
        {
          $project: {
            _id: 0,
            payoutCurrency: '$_id',
            payoutAmount: '$payoutAmount',
          },
        },
        {
          $sort: {
            payoutCurrency: 1,
          },
        },
      ]),
    ]);

  const payoutList = [];

  for (const payoutObject of payouts) {
    let revenueTransactionInfo = {};

    // With stripe connect, we will multiple different status of our payout object
    // But to prevent those status that might confuse CMs,
    // we will just keep payout status to be Paid and Processing
    let payoutStatus = payoutObject.status;
    switch (payoutObject.status) {
      case PAYOUT_STATUS.PAYOUT_FAILED:
      case PAYOUT_STATUS.APPROVED:
      case PAYOUT_STATUS.TRANSFERRED:
      case PAYOUT_STATUS.TRANSFER_FAILED:
      case PAYOUT_STATUS.PENDING_PAYOUT:
        payoutStatus = PAYOUT_STATUS.PROCESSING;
        break;
      default:
    }
    if (payoutObject.linkedRevenueTransactions?.ids) {
      revenueTransactionInfo = {
        count: payoutObject.linkedRevenueTransactions.ids.length,
        startDate: payoutObject.linkedRevenueTransactions.startDate,
        endDate: payoutObject.linkedRevenueTransactions.endDate,
      };
    }
    const createdAtString = payoutObject.createdAt.toISOString();
    const applyNewUI =
      createdAtString >= new Date('2024-04-02').toISOString();
    const payout = {
      applyNewUI,
      payoutId: payoutObject._id,
      payoutTitle: payoutObject.payoutTitle,
      payoutType: payoutObject.type,
      status: payoutStatus,
      adjustments: payoutObject.adjustments,
      advancePayouts: payoutObject.advancePayouts ?? [],
      payoutDate:
        payoutObject.payoutStatusTransitionDate ??
        payoutObject.payoutDate ??
        payoutObject.createdAt,
      payoutReason: payoutObject.payoutReason,
      bankAccountInfo: {
        name: payoutObject.bankAccountInfo?.accountName,
        accountNumber: payoutObject.bankAccountInfo?.accountNumber,
        bank: payoutObject.bankAccountInfo?.bankName,
      },
      revenueTransactionInfo,
    };

    if (payoutObject.payoutCurrency !== DEFAULT_CURRENCY) {
      payout.currency = payoutObject.localCurrency;
      payout.totalEarningAmount = payoutObject.totalAmountInLocalCurrency;
      payout.totalEarningAmountInUsd = payoutObject.totalAmountInUsd;
      payout.revenueShareAmount =
        payoutObject.revenueShareAmountInLocalCurrency;
      payout.paymentGatewayFee =
        payoutObject.paymentGatewayFeeInLocalCurrency -
        payoutObject.feeBreakdownInLocalCurrency?.whtFee;
      payout.whtFee = payoutObject.feeBreakdownInLocalCurrency?.whtFee;
      payout.refundAmount = payoutObject.refundAmountInLocalCurrency;
      payout.payoutAmount = payoutObject.payoutAmountInLocalCurrency;
      payout.payoutAmountInUsd = payoutObject.payoutAmountInUsd;

      if (applyNewUI) {
        const totalNasFee =
          payoutObject.revenueShareAmountInLocalCurrency +
          payoutObject.feeBreakdownInLocalCurrency?.gstOnRevenue;

        const totalPaymentGatewayFee =
          payoutObject.feeBreakdownInLocalCurrency?.processingFee +
          payoutObject.feeBreakdownInLocalCurrency?.gst +
          payoutObject.feeBreakdownInLocalCurrency?.gatewayFee +
          payoutObject.feeBreakdownInLocalCurrency?.internationalFee;

        const nonPassOnNasFee =
          totalNasFee -
          (payoutObject.passOnRevenueShareAmountInLocalCurrency +
            payoutObject.passOnFeeBreakdownInLocalCurrency?.gstOnRevenue);

        const nonPassOnPaymentGatewayFee =
          totalPaymentGatewayFee -
          (payoutObject.passOnFeeBreakdownInLocalCurrency?.processingFee +
            payoutObject.passOnFeeBreakdownInLocalCurrency?.gst +
            payoutObject.passOnFeeBreakdownInLocalCurrency?.gatewayFee +
            (payoutObject.passOnFeeBreakdownInLocalCurrency
              ?.internationalFee ?? 0));

        payout.nasFee = nonPassOnNasFee;
        payout.paymentGatewayFee = nonPassOnPaymentGatewayFee;
        payout.nasFeeGst =
          payoutObject.feeBreakdownInLocalCurrency?.gstOnRevenue -
          payoutObject.passOnFeeBreakdownInLocalCurrency?.gstOnRevenue;
        payout.paymentGatewayFeeGst =
          payoutObject.feeBreakdownInLocalCurrency?.gst -
          payoutObject.passOnFeeBreakdownInLocalCurrency?.gst;
        payout.totalNasFee = totalNasFee;
        payout.totalPaymentGatewayFee = totalPaymentGatewayFee;
        payout.refundItemPrice =
          payoutObject.refundBreakdownInLocalCurrency?.discountedItemPrice;
        payout.refundProcessingFee =
          payoutObject.refundBreakdownInLocalCurrency?.fee?.refundProcessingFee;
        payout.affiliateCommission =
          payoutObject.affiliateCommission?.affiliateCommissionInLocalCurrency;
        payout.refundedAffiliateCommission =
          payoutObject.affiliateCommission?.refundedAffiliateCommissionInLocalCurrency;
        payout.referralReward =
          (payoutObject.referralReward?.referralRewardInLocalCurrency ??
            0) -
          (payoutObject.referralReward
            ?.refundedReferralRewardInLocalCurrency ?? 0);
      }
    } else {
      payout.currency = DEFAULT_CURRENCY;
      payout.totalEarningAmount = payoutObject.totalAmountInUsd;
      payout.revenueShareAmount = payoutObject.revenueShareAmountInUsd;
      payout.paymentGatewayFee = payoutObject.paymentGatewayFeeInUsd;
      payout.refundAmount = payoutObject.refundAmountInUsd;
      payout.payoutAmount = payoutObject.payoutAmountInUsd;
      const formattedPayout = [];
      for (const item of payout.advancePayouts) {
        formattedPayout.push({
          ...item,
          amountInLocalCurrency: item?.amountInUsd,
        });
      }
      payout.advancePayouts = formattedPayout;
      if (applyNewUI) {
        const totalNasFee =
          payoutObject.revenueShareAmountInUsd +
          payoutObject.feeBreakdownInUsd?.gstOnRevenue;
        const totalPaymentGatewayFee =
          payoutObject.feeBreakdownInUsd?.processingFee +
          payoutObject.feeBreakdownInUsd?.gst +
          payoutObject.feeBreakdownInUsd?.gatewayFee +
          payoutObject.feeBreakdownInUsd?.internationalFee;

        const nonPassOnNasFee =
          totalNasFee -
          (payoutObject.passOnRevenueShareAmountInUsd +
            payoutObject.passOnFeeBreakdownInUsd?.gstOnRevenue);

        const nonPassOnPaymentGatewayFee =
          totalPaymentGatewayFee -
          (payoutObject.passOnFeeBreakdownInUsd?.processingFee +
            payoutObject.passOnFeeBreakdownInUsd?.gst +
            payoutObject.passOnFeeBreakdownInUsd?.gatewayFee +
            (payoutObject.passOnFeeBreakdownInUsd?.internationalFee ?? 0));

        payout.nasFee = nonPassOnNasFee;
        payout.paymentGatewayFee = nonPassOnPaymentGatewayFee;
        payout.nasFeeGst =
          payoutObject.feeBreakdownInUsd?.gstOnRevenue -
          payoutObject.passOnFeeBreakdownInUsd?.gstOnRevenue;
        payout.paymentGatewayFeeGst =
          payoutObject.feeBreakdownInUsd?.gst -
          payoutObject.passOnFeeBreakdownInUsd?.gst;
        payout.totalNasFee = totalNasFee;
        payout.totalPaymentGatewayFee = totalPaymentGatewayFee;
        payout.refundItemPrice =
          payoutObject.refundBreakdownInUsd?.discountedItemPrice;
        payout.refundProcessingFee =
          payoutObject.refundBreakdownInUsd?.fee?.refundProcessingFee;
        payout.affiliateCommission =
          payoutObject.affiliateCommission?.affiliateCommissionInUsd;
        payout.refundedAffiliateCommission =
          payoutObject.affiliateCommission?.refundedAffiliateCommissionInUsd;
        payout.referralReward =
          (payoutObject.referralReward?.referralRewardInUsd ?? 0) -
          (payoutObject.referralReward?.refundedReferralRewardInUsd ?? 0);
      }
    }
    payoutList.push(payout);
  }

  const metadata = {
    total: totalPayoutSize,
    limit: pageSize,
    page: pageNo,
    pages: Math.ceil(totalPayoutSize / pageSize),
  };

  // Get current balance
  const community = await CommunityModel.findById(communityId).lean();
  const { currentBalance, currentBalances } = await getCurrencyBalance(
    community
  );

  if (!community) {
    throw new ParamError('Invalid communityId');
  }

  const index = totalPayoutAmountByPayoutCurrency.findIndex(
    (totalPayout) => totalPayout.payoutCurrency === community.baseCurrency
  );

  if (index !== -1) {
    const [elementToMove] = totalPayoutAmountByPayoutCurrency.splice(
      index,
      1
    );

    totalPayoutAmountByPayoutCurrency.unshift(elementToMove);
  }

  // Only return nextPayoutDate if minimum payout amount is met
  let meetsMinimum = false;

  const minimumPayoutAmount = {};

  currentBalances.map((balance) => {
    const minimumAmount = MINIMUM_PAYOUT_AMOUNT[balance.currency];
    if (minimumAmount) {
      meetsMinimum = meetsMinimum || balance.payoutAmount >= minimumAmount;
      minimumPayoutAmount[balance.currency] = minimumAmount;
    }
    return null;
  });

  return {
    payoutList,
    payoutListMetadata: metadata,
    totalPayoutAmountByPayoutCurrency,
    currentBalance,
    currentBalances,
    minimumPayoutAmount,
    nextPayoutDate: meetsMinimum ? getNextPayoutDate() : null,
  };
};
