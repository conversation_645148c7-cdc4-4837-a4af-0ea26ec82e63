const logger = require('../logger.service');
const CommunityPayoutModel = require('../../communitiesAPI/models/communityPayouts.model');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const WalletModel = require('../../models/wallet/wallet.model');
const {
  PAYOUT_TYPE,
  PAYOUT_CHANNEL,
  COMMUNITY_PAYOUT_ACCOUNT_STATUS,
} = require('./constants');
const { ParamError } = require('../../utils/error.util');
const {
  SUB_WALLET_TYPE,
  PAYMENT_PROVIDER,
  PAYOUT_GATEWAY,
} = require('../../constants/common');
const CommunityManagerPayoutModel = require('../../communitiesAPI/models/communityManagerPayout.model');
const CommunityPayoutAccountModel = require('../../communitiesAPI/models/communityPayoutAccount.model');
const RevenueTransactionModel = require('../../models/revenueTransaction.model');
const CreatePayoutTransactionServices = require('./createPayoutTransaction.service');
const {
  generateQueryPipeline,
  formatPayout,
} = require('./generatePayout');
const PrimaryMongooseConnection = require('../../rpc/primaryMongooseConnection');
const { getConfigByTypeFromCache } = require('../config.service');

const createPayout = async (payout) => {
  logger.info(
    `payout for ${payout.communityCode}: ${
      payout.payoutAmountInLocalCurrency / 100
    } ${payout.payoutCurrency}`
  );

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    const [result] = await CommunityPayoutModel.create([payout], {
      session,
    });
    const payoutObject = result.toObject();
    const payoutObjectId = payoutObject._id;

    const transactionUpdateResult =
      await RevenueTransactionModel.updateMany(
        {
          _id: { $in: payout.linkedRevenueTransactions.ids },
          payoutObjectId: { $exists: false },
        },
        {
          $set: {
            payoutObjectId,
          },
        },
        {
          session,
        }
      );
    logger.info(
      `transactionUpdateResult: ${JSON.stringify(transactionUpdateResult)}`
    );

    // Create payout transaction
    await CreatePayoutTransactionServices.createPayoutTransaction({
      payout: payoutObject,
      dbSession: session,
    });

    await session.commitTransaction();
  } catch (err) {
    logger.error(
      `Failed to create payout: ${payout.communityCode}, ${err.message}`
    );
    await session.abortTransaction();
    throw err;
  } finally {
    await session.endSession();
  }
};

exports.createAdvancePayout = async (
  communityCode,
  currency,
  transactionCreatedFrom,
  transactionCreatedTo,
  reason,
  operator
) => {
  // Verify community code
  const community = await CommunityModel.findOne({
    code: communityCode,
  }).lean();
  if (!community) {
    throw new ParamError(`community code is invalid: ${communityCode}`);
  }
  if (community.restrictedInfo?.checkout) {
    throw new ParamError(`this community has been flagged as fraud`);
  }

  let payoutChannel = PAYOUT_CHANNEL.GLOBAL;
  let paymentProviderList = [
    PAYMENT_PROVIDER.STRIPE,
    PAYMENT_PROVIDER.STRIPE_US,
    PAYMENT_PROVIDER.XENDIT,
    PAYMENT_PROVIDER.VOLT,
    PAYMENT_PROVIDER.EBANX,
    PAYMENT_PROVIDER.PAYPAL,
    PAYMENT_PROVIDER.NAS,
  ];
  if (currency === 'INR') {
    payoutChannel = PAYOUT_CHANNEL.STRIPE_INDIA;
    paymentProviderList = [
      PAYMENT_PROVIDER.STRIPE_INDIA,
      PAYMENT_PROVIDER.RAZORPAY,
      PAYMENT_PROVIDER.NAS,
    ];
  }

  // Get total amount by transaction create date
  const pipeline = generateQueryPipeline(
    transactionCreatedTo,
    community,
    paymentProviderList
  );
  const revenueTransactionResult = await RevenueTransactionModel.aggregate(
    pipeline
  );

  if (revenueTransactionResult.length === 0) {
    logger.info(
      `There's no revenue for ${community.code} through [${paymentProviderList}]`
    );
    return null;
  }

  let revenueResult = null;
  for await (const result of revenueTransactionResult) {
    if (result.localCurrency === currency) {
      revenueResult = result;
    }
  }

  if (!revenueResult) {
    throw new Error(
      `Cannot find any revenue in ${currency} for this community`
    );
  }
  try {
    // generate payout title
    const payoutTitle = new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
    }).format(new Date());

    const payout = formatPayout({
      community,
      revenueResult,
      payoutCurrency: currency,
      payoutType: PAYOUT_TYPE.ADVANCE,
      payoutChannel,
      payoutTitle: `${payoutTitle} - Advance payout`,
      endOfMonth: transactionCreatedTo,
    });
    payout.payoutReason = reason;
    payout.operator = operator;

    // Verify if wallet has available balance
    const wallet = await WalletModel.findOne({
      communityObjectId: community._id,
    }).lean();
    if (
      wallet.balance?.[currency]?.[SUB_WALLET_TYPE.AVAILABLE] <
      payout.payoutAmountInLocalCurrency
    ) {
      throw new ParamError(
        `Not enough balance for advance payout: current balance=${
          wallet.balance?.[currency]?.[SUB_WALLET_TYPE.AVAILABLE]
        }, advance payout amount=${payout.payoutAmountInLocalCurrency}`
      );
    }

    // Get bank account info
    // TODO: get bank account by currency
    const bypassStripeConnectCommunityListConfig =
      await getConfigByTypeFromCache('bypassStripeConnectCommunityList');
    const communityCodesThatBypassStripeConnect =
      bypassStripeConnectCommunityListConfig.value?.communityCodes ?? [];

    let bankAccountInfo;
    const payoutAccount = await CommunityPayoutAccountModel.findOne({
      communityObjectId: community._id,
      payoutGateway: PAYOUT_GATEWAY.STRIPE_US,
      status: COMMUNITY_PAYOUT_ACCOUNT_STATUS.CONNECTED,
    }).lean();
    if (
      payoutAccount &&
      !communityCodesThatBypassStripeConnect.includes(community.code)
    ) {
      const firstValidAccount =
        payoutAccount.accountDetails.external_accounts?.data?.find(
          (account) =>
            ['new', 'validated', 'verified'].includes(account.status)
        );
      if (!firstValidAccount) {
        throw new Error(
          `No valid bank account info for ${community.code}, payout wont be created`
        );
      }
      bankAccountInfo = {
        _id: payoutAccount._id,
        accountId: payoutAccount.payoutGatewayAccountId,
        externalAccountId: firstValidAccount.id,
        country: firstValidAccount.country,
        payoutCurrency: firstValidAccount.currency.toUpperCase(),
        bankName: firstValidAccount.bank_name,
        accountName: firstValidAccount.account_holder_name,
        accountNumber: `****${firstValidAccount.last4}`,
        email: payoutAccount.email,
      };
      payout.payoutChannel = PAYOUT_CHANNEL.STRIPE_CONNECT;
    }

    if (!bankAccountInfo) {
      // TODO: remove this part after grace period
      bankAccountInfo = await CommunityManagerPayoutModel.findOne({
        communityId: community._id,
      }).lean();
    }

    if (!bankAccountInfo) {
      throw new Error(
        `No bank account info for ${community.code}, payout wont be created`
      );
    }
    payout.bankAccountInfo = bankAccountInfo;

    logger.info(`Advance payout: ${JSON.stringify(payout)}`);
    await createPayout(payout);
  } catch (err) {
    logger.error(`Failed to create advance payout, error = ${err}`);
    throw new Error(
      `Failed to create advance payout, error = ${err.message}`
    );
  }

  return {};
};
