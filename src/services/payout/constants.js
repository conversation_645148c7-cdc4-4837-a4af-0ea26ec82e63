const PAYOUT_TYPE = {
  GENERAL: 'General',
  ADVANC<PERSON>: 'Advance',
};

const PAYOUT_STATUS = {
  PROCESSING: 'Processing',
  PENDING_REPLY: 'Pending_Reply',
  PAID: 'Paid',
  APPROVED: 'Approved',
  TRANSFERRED: 'Transferred',
  TRANSFER_FAILED: 'Transfer_Failed',
  PENDING_PAYOUT: 'Pending_Payout',
  PAYOUT_FAILED: 'Payout_Failed',
};

const PAYOUT_CHANNEL = {
  GLOBAL: 'GLOBAL',
  STRIPE_INDIA: 'STRIPE_INDIA',
  STRIPE_CONNECT: 'STRIPE_CONNECT',
};

const PAYOUT_ADJUSTMENT_TYPE = {
  ADJUSTMENT_ADD: 'ADJUSTMENT_ADD',
  ADJUSTMENT_DEDUCT: 'ADJUSTMENT_DEDUCT',
};

const PAYOUT_TARGET_TYPE = {
  COMMUNITY: 'COMMUNITY',
  MEMBER: 'MEMBER',
};

const COMMUNITY_PAYOUT_ACCOUNT_STATUS = {
  INIT: 'init',
  CONNECTED: 'connected',
  REJECTED: 'rejected',
  RESTRICTED: 'restricted',
  PENDING_VERIFICATION: 'pending_verification',
};

const COMMUNITY_PAYOUT_ACCOUNT_TYPE = {
  STRIPE_CONNECT: 'STRIPE_CONNECT',
  BANK_ACCOUNT: 'BANK_ACCOUNT',
};

const STRIPE_CONNECT_NOT_SUPPORTED_COUNTRIES = [
  'Brazil',
  'Andorra',
  'San Marino',
  'Vatican City',
  'Papua New Guinea',
  'Nicaragua',
  'United Arab Emirates',
  'Venezuela',
];

const MINIMUM_PAYOUT_AMOUNT = Object.freeze({
  AED: 4000, // 40.00 AED
  ARS: 900000, // 9000.00 ARS
  AUD: 1600, // 16.00 AUD
  BRL: 5500, // 55.00 BRL
  CAD: 1400, // 14.00 CAD
  CLP: 2300000, // 23000.00 CLP
  COP: ********, // 140000.00 COP
  EUR: 1000, // 10.00 EUR
  GBP: 900, // 9.00 GBP
  HUF: 400000, // 4000.00 HUF
  IDR: ********, // 170000.00 IDR
  ILS: 4000, // 40.00 ILS
  INR: 85000, // 850.00 INR
  JPY: 160000, // 1600.00 JPY
  MXN: 18000, // 180.00 MXN
  MYR: 13300, // 133.00 MYR
  PEN: 3900, // 39.00 PEN
  PHP: 65000, // 650.00 PHP
  SGD: 1400, // 14.00 SGD
  USD: 1000, // 10.00 USD
  VND: ********, // 250000.00 VND
});

module.exports = {
  PAYOUT_TYPE,
  PAYOUT_STATUS,
  PAYOUT_CHANNEL,
  PAYOUT_ADJUSTMENT_TYPE,
  PAYOUT_TARGET_TYPE,
  COMMUNITY_PAYOUT_ACCOUNT_STATUS,
  COMMUNITY_PAYOUT_ACCOUNT_TYPE,
  STRIPE_CONNECT_NOT_SUPPORTED_COUNTRIES,
  MINIMUM_PAYOUT_AMOUNT,
};
