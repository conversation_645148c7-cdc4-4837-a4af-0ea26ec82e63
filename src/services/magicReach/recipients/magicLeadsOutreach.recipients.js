const { ObjectId } = require('mongoose').Types;

const IcpLeadMatchModel = require('@/src/models/magicLeads/icpLeadMatches.model');
const Learner = require('../../../models/learners.model');
const EnrichedLeadModel = require('@/src/models/magicLeads/enrichedLeads.model');
const CommunityModel = require('@/src/communitiesAPI/models/community.model');
const { BUCKET_NAMES } = require('../constants');
const { env } = require('@/src/config');
const { PRODUCTION } = require('@/src/constants/common');

async function retrieveEmailMatchFilter({
  leadObjectId,
  icpProfileObjectId,
  communityObjectId,
}) {
  const matchFilter = {
    leadObjectId: new ObjectId(leadObjectId),
    icpProfileObjectId: new ObjectId(icpProfileObjectId),
    communityObjectId: new ObjectId(communityObjectId),
  };

  const icpLeadMatch = await IcpLeadMatchModel.findOne(matchFilter).lean();

  if (!icpLeadMatch) {
    throw new Error('ICP Lead Match not found');
  }

  return {
    _id: matchFilter.leadObjectId,
    isActive: true,
  };
}

exports.countMagicLeadsOutreach = async ({
  filters,
  excludeEmails,
  excludePhoneNumbers,
}) => {
  const excludedEmails = await Learner.distinct('email', {
    $or: [
      { email: { $in: excludeEmails } },
      { phoneNumber: { $in: excludePhoneNumbers } },
    ],
  });

  const filterQuery = await retrieveEmailMatchFilter({
    leadObjectId: filters.leadObjectId,
    icpProfileObjectId: filters.icpProfileObjectId,
    communityObjectId: filters.communityId,
  });

  filterQuery.email = { $nin: excludedEmails };

  const count = await EnrichedLeadModel.countDocuments(filterQuery);
  return count;
};

const getMagicLeadsOutreachBucketMeta = async () => {
  return {};
};

async function buildMagicLeadsOutreachPipeline({
  icpProfileObjectId,
  leadObjectId,
  communityObjectId,
  excludeEmails,
  skip,
  limit,
}) {
  const matchQuery = await retrieveEmailMatchFilter({
    icpProfileObjectId,
    leadObjectId,
    communityObjectId,
  });

  matchQuery.email = matchQuery.email || {};
  matchQuery.email.$nin = excludeEmails;

  const pipeline = [
    {
      $match: matchQuery,
    },
    {
      $project: {
        firstName: 1,
        lastName: 1,
        email: 1,
        profileImage: { $first: '$socialProfiles.profileImageUrl' },
        _id: 1,
      },
    },
    {
      $facet: {
        meta: [{ $count: 'total' }],
        data: [{ $skip: skip }, { $limit: limit }],
      },
    },
    { $unwind: '$meta' },
  ];

  return pipeline;
}

const fillEnrichedLeadsAndSingleBucket = async ({
  identifier,
  values,
  bucket,
  originalEntryList,
  communityOwnerEmail,
}) => {
  if (!values?.length) return [];

  // Only use the real enriched lead email in production
  const useEnrichedLeadEmail = env === PRODUCTION;

  // Build a map for quick lookup
  const leadMap = {};
  values.forEach((lead) => {
    leadMap[lead[identifier]] = lead;
  });

  // Decide which list to enrich
  const results = originalEntryList
    ? [...originalEntryList] // shallow copy if mutating is not desired
    : values.map((lead) => ({ [identifier]: lead[identifier] }));

  // Enrich each entry
  for (const entry of results) {
    entry.inBuckets = [bucket];
    const idValue = entry[identifier];
    if (idValue && leadMap[idValue]) {
      Object.assign(entry, {
        _id: leadMap[idValue]._id,
        firstName: leadMap[idValue].firstName,
        lastName: leadMap[idValue].lastName,
        email: useEnrichedLeadEmail
          ? leadMap[idValue].email
          : communityOwnerEmail,
        profileImage: leadMap[idValue].profileImage,
      });
    }
  }

  return results;
};

exports.getMagicLeadsOutreach = async ({
  filters,
  excludeEmails,
  skip,
  limit,
}) => {
  const bucketMeta = await getMagicLeadsOutreachBucketMeta();

  const aggregationPipeline = await buildMagicLeadsOutreachPipeline({
    icpProfileObjectId: filters.icpProfileObjectId,
    leadObjectId: filters.leadObjectId,
    communityObjectId: filters.communityId,
    excludeEmails,
    skip,
    limit,
  });

  const [results, community] = await Promise.all([
    EnrichedLeadModel.aggregate(aggregationPipeline),
    CommunityModel.findOne(
      { _id: filters.communityId },
      { createdBy: 1 }
    ).lean(),
  ]);

  if (!community) {
    throw new Error('Invalid communityId');
  }

  if (!(results?.length && results[0]?.data?.length)) {
    return {
      users: [],
      meta: {
        total: 0,
        bucketMeta,
      },
    };
  }

  const leads = results[0].data;

  return {
    users: await fillEnrichedLeadsAndSingleBucket({
      identifier: '_id',
      values: leads,
      bucket: BUCKET_NAMES.MAGIC_LEADS_OUTREACH,
      communityOwnerEmail: community.createdBy,
    }),
    meta: {
      ...results[0].meta,
      bucketMeta,
    },
  };
};
