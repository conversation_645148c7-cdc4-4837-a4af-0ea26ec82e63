const axios = require('../../../clients/axios.client');

const logger = require('../../logger.service');
const {
  ENROLMENT_MAIL_TYPES,
  MAIL_TYPES_COMMON_HEADER_SET,
} = require('../../mail/constants');
const ProductNode = require('./ProductNode');
const ButtonNode = require('./ButtonNode');
const DivNode = require('./DivNode');
const ImageNode = require('./ImageNode');
const LineBreakNode = require('./LineBreakNode');
const LinkNode = require('./LinkNode');
const ListItemNode = require('./ListItemNode');
const ListNode = require('./ListNode');
const ParagraphNode = require('./ParagraphNode');
const HeadingNode = require('./HeadingNode');
const DividerNode = require('./DividerNode');
const RootNode = require('./RootNode');
const TextNode = require('./TextNode');

class MagicReachNodeWrapper {
  constructor(node) {
    this._fullNode = node;
    this._variablesData = node.variablesData ?? {};
    this._localisedKeys = node.localisedKeys ?? {};
    if (node.variablesData?.mail_subject) {
      // eslint-disable-next-line no-param-reassign
      node.variablesData.mail_subject = this.replaceVariablesData(
        node.variablesData.mail_subject
      );
      this._variablesData = node.variablesData;
    }

    switch (node.type) {
      case 'heading':
        this._node = new HeadingNode(node);
        break;
      case 'paragraph':
        this._node = new ParagraphNode(node);
        break;
      case 'variable':
        this._node = new TextNode(node);
        break;
      case 'text':
        this._node = new TextNode(node);
        break;
      case 'horizontalrule':
        this._node = new DividerNode(node);
        break;
      case 'list':
        this._node = new ListNode(node);
        break;
      case 'listitem':
        this._node = new ListItemNode(node);
        break;
      case 'image':
        this._node = new ImageNode(node);
        break;
      case 'button':
        this._node = new ButtonNode(node);
        break;
      case 'link':
        this._node = new LinkNode(node);
        break;
      case 'autolink':
        this._node = new LinkNode(node);
        break;
      case 'root':
        this._node = new RootNode(node);
        break;
      case 'linebreak':
        this._node = new LineBreakNode(node);
        break;
      case 'product':
        this._node = new ProductNode(node);
        break;
      default:
        this._node = new DivNode(node);
        break;
    }
  }

  getNode() {
    return this._node;
  }

  getMailSubject() {
    return this._variablesData?.mail_subject ?? '';
  }

  replaceVariablesData(text) {
    let processedText = text;
    for (const key of Object.keys(this._variablesData)) {
      const regex = new RegExp(`{${key}}`, 'g');
      processedText = processedText.replace(
        regex,
        this._variablesData[key]
      );
    }
    return processedText;
  }

  getOptimizedDiscordMessage() {
    const unOptimizedMessage = this.getDiscordMessage();
    const optimizedMessage = [];
    unOptimizedMessage.forEach((message) => {
      if (!message) {
        return;
      }
      if (message.type === 'text') {
        const lastMessage = optimizedMessage[optimizedMessage.length - 1];
        if (lastMessage && lastMessage.type === 'text') {
          const messageLength = message.content.length;
          const lastMessageLength = lastMessage.content.length;
          const totalLength = messageLength + lastMessageLength;
          if (totalLength <= 2000) {
            lastMessage.content += '\n' + message.content;
          } else {
            optimizedMessage.push(message);
          }
        } else {
          optimizedMessage.push(message);
        }
      } else {
        optimizedMessage.push(message);
      }
    });
    return optimizedMessage;
  }

  getDiscordMessage() {
    const result = [];
    if (this._fullNode.children) {
      const stringArray = [];
      this._fullNode.children.forEach((node) => {
        const child = new MagicReachNodeWrapper({
          ...node,
          localisedKeys: this._localisedKeys,
          variablesData: this._variablesData,
        });
        const messages = child.getDiscordMessage();
        if (typeof messages === 'string') {
          stringArray.push(messages);
        } else if (Array.isArray(messages)) {
          const filteredMessges = messages.filter((message) => !!message);
          const isAllChildrenString = filteredMessges.every(
            (message) => typeof message === 'string'
          );
          if (isAllChildrenString) {
            result.push({
              type: 'text',
              content: filteredMessges.join(''),
            });
          } else {
            result.push(...filteredMessges);
          }
        } else {
          result.push(messages);
        }
      });
      if (stringArray.length > 0) {
        result.push({
          type: 'text',
          content: stringArray.join(''),
        });
      }
      return result;
    }
    return this.getNode().writeDiscordMessage();
  }

  getWhatsappMessage() {
    const result = [];
    if (this._fullNode.type === 'root') {
      result.push(this.getNode().writeWhatsappMessage());
    }

    if (!this._fullNode.children || this._fullNode.children.length === 0) {
      return this.getNode().writeWhatsappMessage();
    }

    const stringArray = [];
    this._fullNode.children.forEach((node) => {
      const child = new MagicReachNodeWrapper({
        ...node,
        localisedKeys: this._localisedKeys,
        variablesData: this._variablesData,
      });
      const messages = child.getWhatsappMessage();

      if (messages === '\n' && node.type !== 'linebreak') {
        result.push({
          type: 'text',
          text: {
            body: '\n',
          },
        });
      } else if (typeof messages === 'string') {
        stringArray.push(messages);
        // result.text.body = (result?.text?.body ?? '') + messages;
      } else if (Array.isArray(messages)) {
        const filteredMessages = messages.filter((message) => !!message);
        const isAllChildrenString = filteredMessages.every(
          (message) => typeof message === 'string'
        );
        if (isAllChildrenString) {
          if (filteredMessages.join('\n').length > 0) {
            result.push({
              type: 'text',
              text: {
                body: filteredMessages.join('\n'),
              },
            });
          }
        } else {
          filteredMessages.forEach((message) => {
            if (typeof message === 'string') {
              result.push({
                type: 'text',
                text: {
                  body: message,
                },
              });
            } else {
              result.push(message);
            }
          });
        }
      } else {
        result.push(messages);
      }
    });
    if (stringArray.length > 0) {
      if (stringArray.join('').length > 0) {
        const body = stringArray.join('');
        if (this._fullNode.type === 'link') {
          return this.getNode().writeWhatsappMessage(body);
        }
        if (this._fullNode.type === 'autolink') {
          return body;
        }
        result.push({
          type: 'text',
          text: {
            body,
          },
        });
      }
    }
    return result;
  }

  getOptimizedWhatsappMessage() {
    const unOptimizedMessage = this.getWhatsappMessage();

    const optimizedMessage = [];
    const imageMessage = [];
    unOptimizedMessage.forEach((message) => {
      if (message) {
        if (message.type === 'text') {
          const editedMessage = message;
          if (message?.text?.body)
            editedMessage.text.body = this.replaceVariablesData(
              message?.text?.body
            );
          const lastMessage =
            optimizedMessage[optimizedMessage.length - 1];
          if (lastMessage && lastMessage.type === 'text') {
            lastMessage.text.preview_url = true;
            const messageLength = editedMessage?.text?.body.length;
            const lastMessageLength = lastMessage?.text?.body.length;
            const totalLength = messageLength + lastMessageLength;
            if (totalLength <= 4032) {
              const newLine =
                editedMessage?.text?.body === '\n' ? '' : '\n';
              lastMessage.text.body += `${newLine}${editedMessage?.text?.body}`;
            } else {
              optimizedMessage.push(editedMessage);
            }
          } else {
            optimizedMessage.push(editedMessage);
          }
        } else if (message.type === 'image') {
          imageMessage.push(message);
        } else {
          optimizedMessage.push(message);
        }
      }
    });
    const newMessage = optimizedMessage.concat(imageMessage);
    return newMessage;
  }

  getMessageForFraudCheck() {
    let result = '';
    if (this._fullNode.children && this._fullNode.children.length > 0) {
      if (this._fullNode.type === 'listitem') {
        result = result.concat('- ');
      }
      this._fullNode.children.forEach((node) => {
        const child = new MagicReachNodeWrapper(node);
        result = result.concat(child.getMessageForFraudCheck());
      });
      result = result.concat('\n');
      return result;
    }
    switch (this._fullNode.type) {
      case 'paragraph':
        return '\n';
      case 'text':
        return this._fullNode.text;
      case 'image':
        return '<image>\n';
      case 'link':
        return this._fullNode.label + '(' + this._fullNode.link + ')';
      default:
        return '';
    }
  }

  getLinks() {
    const links = [];
    if (this._fullNode.children && this._fullNode.children.length > 0) {
      this._fullNode.children.forEach((node) => {
        const child = new MagicReachNodeWrapper(node);
        const childLinks = child.getLinks();
        links.push(...childLinks);
      });
    }
    switch (this._fullNode.type) {
      case 'link': {
        const link = this._fullNode.link ?? this._fullNode.url;
        if (link) {
          links.push(link);
        }
        break;
      }
      case 'text': {
        const linkRegex = /(https?:\/\/[^\s]+)/g;
        const linksFromText = this._fullNode.text.match(linkRegex);
        if (linksFromText) {
          links.push(...linksFromText);
        }
        break;
      }
      case 'button':
        links.push(this._fullNode.link);
        break;
      default:
        break;
    }
    return links;
  }

  // This employs recursion to make sure that new MagicReachNodeWrappers are made in a DFS approach
  getHTML(mailType = null) {
    let result = '';
    let openingTag;
    let closingTag;
    if (
      MAIL_TYPES_COMMON_HEADER_SET.has(mailType) &&
      this._fullNode.type === 'root'
    ) {
      openingTag = this.getNode().writeCommonMailOpeningTags();
      if (
        mailType ===
          ENROLMENT_MAIL_TYPES.MEMBER_COMMUNITY_ENROLMENT_APPLICATION_APPROVED_FOR_WHATSAPP ||
        mailType ===
          ENROLMENT_MAIL_TYPES.MEMBER_COMMUNITY_ENROLMENT_WITHOUT_APPLICATION_FOR_WHATSAPP
      ) {
        closingTag = this.getNode().writeWhatsappMailClosingTags();
      } else {
        closingTag = this.getNode().writeCommonMailClosingTags();
      }
    } else {
      openingTag = this.getNode().writeOpeningTags({
        isCustomEmail: !!mailType,
      });
      closingTag = this.getNode().writeClosingTags();
    }

    result = result.concat(openingTag);
    if (this._fullNode.children) {
      result = result.concat(
        ...this._fullNode.children.map((node) => {
          const child = new MagicReachNodeWrapper({
            ...node,
            localisedKeys: this._localisedKeys,
            variablesData: this._variablesData,
          });
          return child.getHTML(mailType);
        })
      );
    }
    result = result.concat(closingTag);
    return this.replaceVariablesData(result);
  }

  async initTags(languagePreference, hasPostForMembers) {
    let processedLanguagePreference = '';
    if (languagePreference && languagePreference !== 'en') {
      processedLanguagePreference = `_${languagePreference.replace(
        '-',
        '_'
      )}`;
    }
    try {
      let magicReachOpeningTemplateS3Path = `https://s3.ap-southeast-1.amazonaws.com/storage.nas.academy/email_assets/html/community/COMMON/template/magic_reach_opening_template_v1${processedLanguagePreference}.html`;

      let magicReachClosingTemplateS3Path = `https://s3.ap-southeast-1.amazonaws.com/storage.nas.academy/email_assets/html/community/COMMON/template/magic_reach_closing_template_v1${processedLanguagePreference}.html`;
      if (hasPostForMembers) {
        // view post button
        magicReachClosingTemplateS3Path = `https://s3.ap-southeast-1.amazonaws.com/storage.nas.academy/email_assets/html/community/COMMON/template/magic_reach_closing_post_template_v1${processedLanguagePreference}.html`;
        magicReachOpeningTemplateS3Path = `https://s3.ap-southeast-1.amazonaws.com/storage.nas.academy/email_assets/html/community/COMMON/template/magic_reach_opening_post_template_v1${processedLanguagePreference}.html`;
      }
      const promises = [];
      promises.push(
        axios({
          method: 'get',
          url: magicReachOpeningTemplateS3Path,
          data: {},
          responseType: 'text',
          responseEncoding: 'utf8',
        })
      );
      promises.push(
        axios({
          method: 'get',
          url: magicReachClosingTemplateS3Path,
          data: {},
          responseType: 'text',
          responseEncoding: 'utf8',
        })
      );
      const responses = await Promise.all(promises);
      if (responses[0].status === 200) {
        this._node.setDefaultOpeningTag(responses[0].data.toString());
      } else {
        logger.error('initRootOpeningTag error', responses[0]);
        throw new Error('Unable to retrieve opening tag from S3');
      }
      if (responses[1].status === 200) {
        this._node.setDefaultClosingTag(responses[1].data.toString());
      } else {
        logger.error('initRootClosingTag error', responses[1]);
        throw new Error('Unable to retrieve closing tag from S3');
      }
      return;
    } catch (err) {
      logger.error('initTags error', err, err.stack);
    }

    this._node.setDefaultOpeningTag(
      MagicReachNodeWrapper.getFallbackOpeningTemplate()
    );
    this._node.setDefaultClosingTag(
      MagicReachNodeWrapper.getFallbackClosingTemplate()
    );
  }

  getPreviewText() {
    let preview = '';
    if (this._fullNode.children && this._fullNode.children.length > 0) {
      if (this._fullNode.type === 'listitem') {
        preview = preview.concat(' ');
      }
      for (const node of this._fullNode.children) {
        node.variablesData = this._variablesData;
        const child = new MagicReachNodeWrapper(node);
        preview = preview.concat(child.getPreviewText());
        if (preview.length > 200) {
          return preview;
        }
      }

      if (
        this._fullNode.type === 'heading' ||
        this._fullNode.type === 'paragraph'
      ) {
        preview = preview.concat(' ');
      }
      return this.replaceVariablesData(preview);
    }
    switch (this._fullNode.type) {
      case 'heading':
        return ' ';
      case 'paragraph':
        return ' ';
      case 'text':
        return this.replaceVariablesData(this._fullNode.text);
      case 'image':
        return ' ';
      case 'link':
        return this.replaceVariablesData(this._fullNode.label);
      case 'variable':
        return this.replaceVariablesData(this._fullNode.variable);
      default:
        return ' ';
    }
  }

  static getFallbackOpeningTemplate() {
    return `<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!--<![endif]-->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type="text/css">
    #outlook a{padding:0;}body{margin:0;padding:0;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%;}table,td{border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;}img{border:0;height:auto;line-height:100%;outline:none;text-decoration:none;-ms-interpolation-mode:bicubic;}p{display:block;margin:0;}
    </style>
    <!--[if mso]> <noscript><xml><o:OfficeDocumentSettings><o:AllowPNG/><o:PixelsPerInch>96</o:PixelsPerInch></o:OfficeDocumentSettings></xml></noscript>
    <![endif]-->
    <!--[if lte mso 11]>
    <style type="text/css">
    .ogf{width:100% !important;}
    </style>
    <![endif]-->
    <style type="text/css">
    @media only screen and (min-width:599px){.xc536{width:536px!important;max-width:536px;}.pc100{width:100%!important;max-width:100%;}}
    </style>
    <style media="screen and (min-width:599px)">.moz-text-html .xc536{width:536px!important;max-width:536px;}.moz-text-html .pc100{width:100%!important;max-width:100%;}
    </style>
    <style type="text/css">
    @media only screen and (max-width:599px){table.fwm{width:100%!important;}td.fwm{width:auto!important;}}noinput.mn-checkbox{display:block!important;max-height:none!important;visibility:visible!important;}
    @media only screen and (max-width:599px){.mn-checkbox[type="checkbox"]~.il{display:none!important;}.mn-checkbox[type="checkbox"]:checked~.il,.mn-checkbox[type="checkbox"]~.mn-trigger{display:block!important;max-width:none!important;max-height:none!important;font-size:inherit!important;}.mn-checkbox[type="checkbox"]~.il>a{display:block!important;}.mn-checkbox[type="checkbox"]:checked~.mn-trigger .mn-icon-close{display:block!important;}.mn-checkbox[type="checkbox"]:checked~.mn-trigger .mn-icon-open{display:none!important;}}
    </style>
    <style type="text/css">
    u+.nas-io-magic-reach-mail .gs{background:#000;mix-blend-mode:screen;display:inline-block;padding:0;margin:0;}u+.nas-io-magic-reach-mail .gd{background:#000;mix-blend-mode:difference;display:inline-block;padding:0;margin:0;}u+.nas-io-magic-reach-mail a,#MessageViewBody a,a[x-apple-data-detectors]{color:inherit!important;text-decoration:none!important;font-size:inherit!important;font-family:inherit!important;font-weight:inherit!important;line-height:inherit!important;}span.MsoHyperlink{mso-style-priority:99;color:inherit;}span.MsoHyperlinkFollowed{mso-style-priority:99;color:inherit;}td.b .klaviyo-image-block{display:inline;vertical-align:middle;}
    @media only screen and (max-width:599px){.nas-io-magic-reach-mail{height:100%!important;margin:0!important;padding:0!important;width:100%!important;}u+.nas-io-magic-reach-mail .glist{margin-left:1em!important;}td.ico.v>div.il>a.l.m,td.ico.v .mn-label{padding-right:0!important;padding-bottom:16px!important;}td.x{padding-left:0!important;padding-right:0!important;}.fwm img{max-width:100%!important;height:auto!important;}.aw img{width:auto!important;margin-left:auto!important;margin-right:auto!important;}.ah img{height:auto!important;}td.b.nw>table,td.b.nw a{width:auto!important;}td.stk{border:0!important;}td.u{height:auto!important;}br.sb{display:none!important;}.thd-1 .i-thumbnail{display:inline-block!important;height:auto!important;overflow:hidden!important;}.hd-1{display:block!important;height:auto!important;overflow:visible!important;}.ht-1{display:table!important;height:auto!important;overflow:visible!important;}.hr-1{display:table-row!important;height:auto!important;overflow:visible!important;}.hc-1{display:table-cell!important;height:auto!important;overflow:visible!important;}div.r.pr-32>table>tbody>tr>td,div.r.pr-32>div>table>tbody>tr>td{padding-right:32px!important}div.r.pl-32>table>tbody>tr>td,div.r.pl-32>div>table>tbody>tr>td{padding-left:32px!important}td.x.fs-28 span,td.x.fs-28>div,td.x.fs-28{font-size:28px!important}div.r.pr-40>table>tbody>tr>td,div.r.pr-40>div>table>tbody>tr>td{padding-right:40px!important}div.r.pl-40>table>tbody>tr>td,div.r.pl-40>div>table>tbody>tr>td{padding-left:40px!important}td.b.fw-1>table{width:100%!important}td.fw-1>table>tbody>tr>td>a{display:block!important;width:100%!important;padding-left:0!important;padding-right:0!important;}td.b.fw-1>table{width:100%!important}td.fw-1>table>tbody>tr>td{width:100%!important;padding-left:0!important;padding-right:0!important;}td.v.s-8>div.il>a.l.m{padding-right:8px!important;}td.v.ico.s-8>div.il>a.l.m,td.v.ico.s-8 .mn-label{padding-bottom:8px!important;padding-right:0!important;}}
    </style>
    <meta name="color-scheme" content="light dark">
    <meta name="supported-color-schemes" content="light dark">
    <!--[if gte mso 9]>
    <style>li{text-indent:-1em;}
    </style>
    <![endif]-->
    <!--[if mso]><!-- -->
    <style>.cr->table>tbody>tr>td,.c-r>table{border-collapse:collapse;}
    </style>
    <!--<![endif]-->
            
    <style>
       .product-template .entity-image {
            width: 100%;
            max-width: 128px;
            height: auto;
            border-radius: 8px;
            margin-bottom: 12px;
        }
        /* Media query for desktop */
        @media screen and (min-width: 500px) {
          .product-template  .entity-image {
              width: 128px;
              margin-right: 16px;
              margin-bottom: 0;
          }
          .product-template .entity-details {
              flex-grow: 1;
          }
          .product-template .entity-first-column {
              display: flex;
              align-items: center;
          }
        }
    </style>
    </head><body lang="en" link="#DD0000" vlink="#DD0000" class="nas-io-magic-reach-mail"
    style="mso-line-height-rule:exactly;word-spacing:normal;background-color:#fffffe !important;">
    <div class="bg" style="background-color:#fffffe !important;">
        <!--[if mso | IE]>
<table align="center" border="0" cellpadding="0" cellspacing="0" class="r-outlook -outlook pr-32-outlook pl-32-outlook -outlook" role="none" style="width:600px;" width="600"><tr><td style="line-height:0;font-size:0;mso-line-height-rule:exactly;">
<![endif]-->
        <div class="r  pr-32 pl-32"
            style="background:#fffffe;background-color:#fffffe;margin:0px auto;max-width:600px;">
            <table align="center" border="0" cellpadding="0" cellspacing="0" role="none"
                style="background:#fffffe;background-color:#fffffe;width:100%;">
                <tbody>
                    <tr>
                        <td style="border:none;direction:ltr;font-size:0;padding:12px 32px 12px 32px;text-align:left;">
                            <!--[if mso | IE]>
<table role="none" border="0" cellpadding="0" cellspacing="0"><tr><td class="c-outlook -outlook -outlook" style="vertical-align:middle;width:536px;">
<![endif]-->
                            <div class="xc536 ogf c"
                                style="font-size:0;text-align:left;direction:ltr;display:inline-block;vertical-align:middle;width:100%;">
                                <a href="{community_link}" target="_blank" style="decoration:none;text-decoration:none;color:inherit;">
                                <table border="0" cellpadding="0" cellspacing="0" role="none"
                                    style="border:none;vertical-align:middle;" width="100%">
                                    <tbody>
                                        <tr>
                                            <td align="left" class="a"
                                                style="background:transparent;font-size:0;padding:0;word-break:break-word;">
                                                <table cellpadding="0" cellspacing="0" width="100%" border="0"
                                                    style="color:#000000;font-family:Arial,sans-serif;font-size:13px;line-height:22px;table-layout:fixed;width:100%;border:none;">
                                                    <tr class="q ">
                                                        <td align="left" class="u"
                                                            style="padding:0;height:40px;word-wrap:break-word;vertical-align:middle;"
                                                            width="40">
                                                            <table border="0" cellpadding="0" cellspacing="0"
                                                                width="100%">
                                                                <tr>
                                                                    <td class="i  " align="left" width="100%"><img
                                                                            width="40" height="auto"
                                                                            style="display:block;width:40px;height:40px;border-radius:8px;"
                                                                            src="{community_profile_image}>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                        <td class="tgtr"
                                                            style="vertical-align:middle;color:transparent;font-size:0;"
                                                            width="12">
                                                        </td>
                                                        <td align="left" class="u"
                                                            style="padding:0;height:17px;word-wrap:break-word;vertical-align:middle;"
                                                            width="auto">
                                                            <table border="0" cellpadding="0" cellspacing="0"
                                                                width="100%">
                                                                <tr>
                                                                    <td class="x  " align="left" width="100%">
                                                                        <p style="Margin:0;text-align:left;"><span
                                                                                style="font-size:14px;font-family:Helvetica,Arial,sans-serif;font-weight:700;color:#706f6b;line-height:121%;">{community_name}</span>
                                                                        </p>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                </a>
                            </div>
                            <!--[if mso | IE]>
</td></tr></table>
<![endif]-->
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!--[if mso | IE]>
</td></tr></table>
<table align="center" border="0" cellpadding="0" cellspacing="0" class="r-outlook -outlook pr-32-outlook pl-32-outlook -outlook" role="none" style="width:600px;" width="600"><tr><td style="line-height:0;font-size:0;mso-line-height-rule:exactly;">
<![endif]-->
        <div class="r  pr-32 pl-32"
            style="background:#fffffe;background-color:#fffffe;margin:0px auto;max-width:600px;">
            <table align="center" border="0" cellpadding="0" cellspacing="0" role="none"
                style="background:#fffffe;background-color:#fffffe;width:100%;">
                <tbody>
                    <tr>
                        <td style="border:none;direction:ltr;font-size:0;padding:24px 32px 8px 32px;text-align:left;">
                            <!--[if mso | IE]>
<table role="none" border="0" cellpadding="0" cellspacing="0"><tr><td class="c-outlook -outlook -outlook" style="vertical-align:middle;width:536px;">
<![endif]-->
                            <div class="xc536 ogf c"
                                style="font-size:0;text-align:left;direction:ltr;display:inline-block;vertical-align:middle;width:100%;">
                                <table border="0" cellpadding="0" cellspacing="0" role="none"
                                    style="border:none;vertical-align:middle;" width="100%">
                                    <tbody>
                                        <tr>
                                            <td align="left" class="x  m"
                                                style="font-size:0;padding-bottom:8px;word-break:break-word;">
                                                <div style="text-align:left;">
                                                    <p style="Margin:0;text-align:left;mso-line-height-alt:157%"><span
                                                            style="font-size:14px;font-family:Helvetica,Arial,sans-serif;font-weight:400;color:#91918d;line-height:157%;">{sentOn}</span>
                                                    </p>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="left" class="x  fs-28"
                                                style="font-size:0;padding-bottom:0;word-break:break-word;">
                                                <div style="text-align:left;">
                                                    <p style="Margin:0;text-align:left;mso-line-height-alt:119%"><span
                                                            style="font-size:32px;font-family:Helvetica,Arial,sans-serif;font-weight:700;color:#1b1b18;line-height:119%;letter-spacing:-1px;">{mail_subject}</span>
                                                    </p>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <!--[if mso | IE]>
</td></tr></table>
<![endif]-->
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!--[if mso | IE]>
</td></tr></table>
<table align="center" border="0" cellpadding="0" cellspacing="0" class="r-outlook -outlook pr-32-outlook pl-32-outlook -outlook" role="none" style="width:600px;" width="600"><tr><td style="line-height:0;font-size:0;mso-line-height-rule:exactly;">
<![endif]-->
        <div class="r  pr-32 pl-32"
            style="background:#fffffe;background-color:#fffffe;margin:0px auto;max-width:600px;">
            <table align="center" border="0" cellpadding="0" cellspacing="0" role="none"
                style="background:#fffffe;background-color:#fffffe;width:100%;">
                <tbody>
                    <tr>
                        <td style="border:none;direction:ltr;font-size:0;padding:16px 32px 16px 32px;text-align:left;">
                            <!--[if mso | IE]>
<table role="none" border="0" cellpadding="0" cellspacing="0"><tr><td class="c-outlook -outlook -outlook" style="vertical-align:middle;width:536px;">
<![endif]-->
                            <div class="xc536 ogf c"
                                style="font-size:0;text-align:left;direction:ltr;display:inline-block;vertical-align:middle;width:100%;">
                                <table border="0" cellpadding="0" cellspacing="0" role="none"
                                    style="border:none;vertical-align:middle;" width="100%">
                                    <tbody>
                                        <tr>
                                            <td align="left" class="a"
                                                style="background:transparent;font-size:0;padding:0;word-break:break-word;">
                                                <table cellpadding="0" cellspacing="0" width="100%" border="0"
                                                    style="color:#000000;font-family:Arial,sans-serif;font-size:13px;line-height:22px;table-layout:fixed;width:100%;border:none;">
                                                    <tr class="q ">
                                                        <td align="right" class="u"
                                                            style="padding:0;height:32px;word-wrap:break-word;vertical-align:middle;"
                                                            width="32">
                                                            <table border="0" cellpadding="0" cellspacing="0"
                                                                width="100%">
                                                                <tr>
                                                                    <td class="i  " align="right" width="100%"><img
                                                                            width="32" height="auto"
                                                                            style="display:block;width:32px;height:32px;border-radius:50%;"
                                                                            src="{host_profile_image}">
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                        <td class="tgtr"
                                                            style="vertical-align:middle;color:transparent;font-size:0;"
                                                            width="12">
                                                        </td>
                                                        <td align="left" class="u"
                                                            style="padding:0;height:22px;word-wrap:break-word;vertical-align:middle;"
                                                            width="auto">
                                                            <table border="0" cellpadding="0" cellspacing="0"
                                                                width="100%">
                                                                <tr>
                                                                    <td class="x  " align="left" width="100%">
                                                                        <p style="Margin:0;text-align:left;"><span
                                                                                style="font-size:14px;font-family:Helvetica,Arial,sans-serif;font-weight:400;color:#1b1b18;line-height:157%;">{community_host}</span>
                                                                        </p>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <!--[if mso | IE]>
</td></tr></table>
<![endif]-->
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!--[if mso | IE]>
</td></tr></table>
<table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="none" style="width:600px;background-color: #fffffe;" width="600"><tr><td style="line-height:0;font-size:0;mso-line-height-rule:exactly;">
<![endif]-->
        <div class="" style="margin:0px auto;max-width:600px; background-color: #fffffe;">
            <table align="center" border="0" cellpadding="0" cellspacing="0" role="none" style="width:100%;">
                <tbody>
                    <tr>
                        <td style="direction:ltr;font-size:0;padding:0;text-align:center;">
                            <!--[if mso | IE]>
<table role="none" border="0" cellpadding="0" cellspacing="0"><tr><td class="" style="vertical-align:top;width:600px;">
<![endif]-->
                            <div class="pc100 ogf"
                                style="font-size:0;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
                                <table border="0" cellpadding="0" cellspacing="0" role="none" width="100%">
                                    <tbody>
                                        <tr>
                                            <td style="vertical-align:top;padding:0;">
                                                <table border="0" cellpadding="0" cellspacing="0" role="none"
                                                    width="100%">
                                                    <tbody>
                                                        <tr>
                                                            <td align="left" class="i"
                                                                style="font-size:0;padding:0;word-break:break-word;">
                                                                <table border="0" cellpadding="0" cellspacing="0"
                                                                    role="none"
                                                                    style="border-collapse:collapse;border-spacing:0;">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td style="width:600px; border-top:1 solid #E2E2DF">
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <!--[if mso | IE]>
</td></tr></table>
<![endif]-->
                        </td>
                    </tr>
                </tbody>
            </table>
      
            <!--[if mso | IE]>
    </td></tr></table>
    <table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="none" style="width:600px; background-color: #fffffe;" width="600"><tr><td style="line-height:0;font-size:0;mso-line-height-rule:exactly;">
    <![endif]-->
            <div class="" style="margin:0px auto;max-width:600px;background-color: #fffffe;">
                <table align="center" border="0" cellpadding="0" cellspacing="0" role="none" style="width:100%;">
                    <tbody>
                        <tr>
                            <td style="direction:ltr;font-size:0;padding:0;text-align:center;">
                                <!--[if mso | IE]>
    <table role="none" border="0" cellpadding="0" cellspacing="0"><tr><td class="" style="vertical-align:top;width:600px;">
    <![endif]-->
                                <div class="pc100 ogf"
                                    style="font-size:0;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
                                    <table border="0" cellpadding="0" cellspacing="0" role="none" width="100%">
                                        <tbody>
                                            <tr>
                                                <td style="vertical-align:top;padding:0;">
                                                    <table border="0" cellpadding="0" cellspacing="0" role="none"
                                                        width="100%">
                                                        <tbody>
                                                            <tr>
                                                                <td align="center" class="i"
                                                                    style="font-size:0;padding:0;word-break:break-word;">
                                                                    <table border="0" cellpadding="0" cellspacing="0"
                                                                        role="none"
                                                                        style="border-collapse:collapse;border-spacing:0;">
                                                                        <tbody>
                                                                            <tr>
                                                                                <td style="width:536px;"><hr style="border-top: 1px solid #E2E2DF;"/>
                                                                                </td>
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <!--[if mso | IE]>
    </td></tr></table>
    <![endif]-->
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
      <section style="margin:24px auto; max-width:600px;padding:0 32px">`;
  }

  static getFallbackClosingTemplate() {
    // eslint-disable-next-line class-methods-use-this
    return `</section>

    <!--[if mso | IE]>
    </td></tr></table>
    <table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="none" style="width:600px; background:#fffffe" width="600"><tr><td style="line-height:0;font-size:0;mso-line-height-rule:exactly;">
    <![endif]-->
            <div class="" style="margin:0px auto;max-width:600px; background:#fffffe">
                <table align="center" border="0" cellpadding="0" cellspacing="0" role="none" style="width:100%;">
                    <tbody>
                        <tr>
                            <td style="direction:ltr;font-size:0;padding:0;text-align:center;">
                                <!--[if mso | IE]>
    <table role="none" border="0" cellpadding="0" cellspacing="0"><tr><td class="" style="vertical-align:top;width:600px;">
    <![endif]-->
                                <div class="pc100 ogf"
                                    style="font-size:0;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
                                    <table border="0" cellpadding="0" cellspacing="0" role="none" width="100%">
                                        <tbody>
                                            <tr>
                                                <td style="vertical-align:top;padding:0;">
                                                    <table border="0" cellpadding="0" cellspacing="0" role="none"
                                                        width="100%">
                                                        <tbody>
                                                            <tr>
                                                                <td align="center" class="i"
                                                                    style="font-size:0;padding:0;word-break:break-word;">
                                                                    <table border="0" cellpadding="0" cellspacing="0"
                                                                        role="none"
                                                                        style="border-collapse:collapse;border-spacing:0;">
                                                                        <tbody>
                                                                            <tr>
                                                                                <td style="width:536px;"><hr style="border-top: 1px solid #E2E2DF;"/>
                                                                                </td>
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <!--[if mso | IE]>
    </td></tr></table>
    <![endif]-->
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
    </div>
    <footer style="padding: 24px 0; text-align: center;background:#fffffe">
    <!--[if mso | IE]>
    </td></tr></table>
    <table align="center" border="0" cellpadding="0" cellspacing="0" class="r-outlook -outlook pr-32-outlook pl-32-outlook -outlook" role="none" style="width:600px;" width="600"><tr><td style="line-height:0;font-size:0;mso-line-height-rule:exactly;">
    <![endif]-->
            <div class="r  pr-32 pl-32"
                style="background:#fffffe;background-color:#fffffe;margin:0px auto;max-width:600px;">
                <table align="center" border="0" cellpadding="0" cellspacing="0" role="none"
                    style="background:#fffffe;background-color:#fffffe;width:100%;">
                    <tbody>
                        <tr>
                            <td style="border:none;direction:ltr;font-size:0;padding:16px 32px 16px 32px;text-align:left;">
                                <!--[if mso | IE]>
    <table role="none" border="0" cellpadding="0" cellspacing="0"><tr><td class="" style="width:536px;">
    <![endif]-->
                                <div class="pc100 ogf"
                                    style="font-size:0;line-height:0;text-align:left;display:inline-block;width:100%;direction:ltr;">
                                    <!--[if mso | IE]>
    <table border="0" cellpadding="0" cellspacing="0" role="none"><tr><td style="vertical-align:middle;width:536px;">
    <![endif]-->
                                    <div class="pc100 ogf c"
                                        style="font-size:0;text-align:left;direction:ltr;display:inline-block;vertical-align:middle;width:100.0000%;">
                                        <table border="0" cellpadding="0" cellspacing="0" role="none"
                                            style="border:none;vertical-align:middle;" width="100%">
                                            <tbody>
                                                <tr>
                                                    <td align="center" class="x  m"
                                                        style="font-size:0;padding-bottom:4px;word-break:break-word;">
                                                        <div style="text-align:center;">
                                                            <p style="Margin:0;text-align:center;mso-line-height-alt:121%">
                                                                <span
                                                                    style="font-size:14px;font-family:Helvetica,Arial,sans-serif;font-weight:700;color:#1b1b18;line-height:121%;">It&rsquo;s
                                                                    10x better with the Nas.io app</span></p>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td align="center" class="x  m"
                                                        style="font-size:0;padding-bottom:4px;word-break:break-word;">
                                                        <div style="text-align:center;">
                                                            <p style="Margin:0;text-align:center;mso-line-height-alt:158%">
                                                                <span
                                                                    style="font-size:12px;font-family:Helvetica,Arial,sans-serif;font-weight:400;color:#706f6b;line-height:158%;">Stay
                                                                    up to date with {community_name}</span></p>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td align="center" class="o"
                                                        style="font-size:0;padding:8px 0px 0px 0px;padding-bottom:0;word-break:break-word;">
                                                        <!--[if mso | IE]>
    <table align="center" border="0" cellpadding="0" cellspacing="0" role="none"><tr><td>
    <![endif]-->
                                                        <table align="center" border="0" cellpadding="0" cellspacing="0"
                                                            role="none" style="float:none;display:inline-table;">
                                                            <tbody>
                                                                <tr class="e  m">
                                                                    <td style="padding:0 16px 0 0;vertical-align:middle;">
                                                                        <table border="0" cellpadding="0" cellspacing="0"
                                                                            role="none" style="width:120px;">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td
                                                                                        style="font-size:0;height:40px;vertical-align:middle;width:120px;">
                                                                                        <a href="https://apps.apple.com/il/app/nas-communities-for-everyone/id1624529593"
                                                                                            target="_blank"> <img
                                                                                                alt="Apple" title="ios"
                                                                                                height="40"
                                                                                                src="https://s3.ap-southeast-1.amazonaws.com/image-assets.nasdaily.com/email/png/app-store.png"
                                                                                                style="display:block;"
                                                                                                width="120"></a>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        <!--[if mso | IE]>
    </td><td>
    <![endif]-->
                                                        <table align="center" border="0" cellpadding="0" cellspacing="0"
                                                            role="none" style="float:none;display:inline-table;">
                                                            <tbody>
                                                                <tr class="e  ">
                                                                    <td style="padding:0;vertical-align:middle;">
                                                                        <table border="0" cellpadding="0" cellspacing="0"
                                                                            role="none" style="width:136px;">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td
                                                                                        style="font-size:0;height:40px;vertical-align:middle;width:136px;">
                                                                                        <a href="https://play.google.com/store/apps/details?id=com.nas.academy"
                                                                                            target="_blank"> <img
                                                                                                alt="Google" title="play store"
                                                                                                height="40"
                                                                                                src="https://s3.ap-southeast-1.amazonaws.com/image-assets.nasdaily.com/email/png/play-store.png"
                                                                                                style="display:block;"
                                                                                                width="136"></a>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        <!--[if mso | IE]>
    </td></tr></table>
    <![endif]-->
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <!--[if mso | IE]>
    </td></tr></table>
    <![endif]-->
                                </div>
                                <!--[if mso | IE]>
    </td></tr></table>
    <![endif]-->
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <!--[if mso | IE]>
    </td></tr></table>
    <table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="none" style="width:600px;" width="600"><tr><td style="line-height:0;font-size:0;mso-line-height-rule:exactly;">
    <![endif]-->
            <div class="" style="margin:0px auto;max-width:600px;">
                <table align="center" border="0" cellpadding="0" cellspacing="0" role="none" style="width:100%;">
                    <tbody>
                        <tr>
                            <td style="direction:ltr;font-size:0;padding:0;text-align:center;">
                                <!--[if mso | IE]>
    <table role="none" border="0" cellpadding="0" cellspacing="0"><tr><td class="" style="vertical-align:top;width:600px;">
    <![endif]-->
                                <div class="pc100 ogf"
                                    style="font-size:0;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
                                    <table border="0" cellpadding="0" cellspacing="0" role="none" width="100%">
                                        <tbody>
                                            <tr>
                                                <td style="vertical-align:top;padding:0;">
                                                    <table border="0" cellpadding="0" cellspacing="0" role="none"
                                                        width="100%">
                                                        <tbody>
                                                            <tr>
                                                                <td align="center" class="i"
                                                                    style="font-size:0;padding:0;word-break:break-word;">
                                                                    <table border="0" cellpadding="0" cellspacing="0"
                                                                        role="none"
                                                                        style="border-collapse:collapse;border-spacing:0;">
                                                                        <tbody>
                                                                            <tr>
                                                                                <td style="width:536px;"><hr style="border-top: 1px solid #E2E2DF;"/>
                                                                                </td>
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <!--[if mso | IE]>
    </td></tr></table>
    <![endif]-->
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <!--[if mso | IE]>
    </td></tr></table>
    <table align="center" border="0" cellpadding="0" cellspacing="0" class="r-outlook -outlook pr-32-outlook pl-32-outlook -outlook" role="none" style="width:600px;" width="600"><tr><td style="line-height:0;font-size:0;mso-line-height-rule:exactly;">
    <![endif]-->
            <div class="r  pr-32 pl-32"
                style="background:#fffffe;background-color:#fffffe;margin:0px auto;max-width:600px;">
                <table align="center" border="0" cellpadding="0" cellspacing="0" role="none"
                    style="background:#fffffe;background-color:#fffffe;width:100%;">
                    <tbody>
                        <tr>
                            <td style="border:none;direction:ltr;font-size:0;padding:24px 32px 40px 32px;text-align:left;">
                                <!--[if mso | IE]>
    <table role="none" border="0" cellpadding="0" cellspacing="0"><tr><td class="c-outlook -outlook -outlook" style="vertical-align:top;width:536px;">
    <![endif]-->
                                <div class="xc536 ogf c"
                                    style="font-size:0;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
                                    <table border="0" cellpadding="0" cellspacing="0" role="none"
                                        style="border:none;vertical-align:top;" width="100%">
                                        <tbody>
                                            <tr>
                                                <td align="center" class="x  m"
                                                    style="font-size:0;padding-bottom:4px;word-break:break-word;">
                                                    <div style="text-align:center;">
                                                        <p style="Margin:0;text-align:center;mso-line-height-alt:117%"><span
                                                                style="font-size:12px;font-family:Helvetica,Arial,sans-serif;font-weight:400;color:#91918d;line-height:117%;">Sent
                                                                with</span></p>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="center" class="i  m"
                                                    style="font-size:0;padding:0;padding-bottom:4px;word-break:break-word;">
                                                    <table border="0" cellpadding="0" cellspacing="0" role="none"
                                                        style="border-collapse:collapse;border-spacing:0;">
                                                        <tbody>
                                                            <tr>
                                                                <td style="width:96px;"> <img alt="Nas IO" height="auto"
                                                                        src="https://s3-ap-southeast-1.amazonaws.com/image-assets.nasdaily.com/email/png/nasio_logo_light.png"
                                                                        style="border:0;display:block;outline:none;text-decoration:none;height:auto;width:100%;font-size:13px;"
                                                                        title="" width="96">
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="s  m"
                                                    style="font-size:0;padding:0;padding-bottom:4px;word-break:break-word;">
                                                    <div style="height:4px;line-height:4px;">&#8202;</div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="center" class="v  s-8"
                                                    style="font-size:0;padding-bottom:0;word-break:break-word;">
                                                    <div class="il">
                                                        <!--[if mso | IE]>
    <table role="none" border="0" cellpadding="0" cellspacing="0" align="center"><tr><td style="padding:0;padding-top:0;padding-left:0;padding-right:8px;padding-bottom:0;" class="l-outlook -outlook m-outlook">
    <![endif]--> <a class="l  m" href="https://nas.io/start-community" target="_blank"
                                                            style="display:inline-block;color:#000000;font-family:Helvetica,Arial,sans-serif;font-size:13px;font-weight:normal;line-height:0;text-decoration:none;text-transform:none;padding:0;padding-top:0;padding-left:0;padding-right:8px;padding-bottom:0;">
                                                            <span
                                                                style="font-size:12px;font-family:Helvetica,Arial,sans-serif;font-weight:400;color:#91918d;line-height:117%;text-decoration:underline;">Start
                                                                your business</span></a>
                                                        <!--[if mso | IE]>
    </td><td style="padding:0;padding-top:0;padding-left:0;padding-right:0;padding-bottom:0;" class="l-outlook -outlook -outlook">
    <![endif]--> <a class="l  " href="{unsubscribed_link}" target="_blank"
                                                            style="display:inline-block;color:#000000;font-family:Helvetica,Arial,sans-serif;font-size:13px;font-weight:normal;line-height:0;text-decoration:none;text-transform:none;padding:0;padding-top:0;padding-left:0;padding-right:0;padding-bottom:0;">
                                                            <span
                                                                style="font-size:12px;font-family:Helvetica,Arial,sans-serif;font-weight:400;color:#91918d;line-height:117%;text-decoration:underline;">Unsubscribe</span>
                                                        </a>
                                                        <!--[if mso | IE]>
    </td></tr></table>
    <![endif]-->
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <!--[if mso | IE]>
    </td></tr></table>
    <![endif]-->
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
  </footer>
  </div></body></html>`;
  }
}

module.exports = MagicReachNodeWrapper;
