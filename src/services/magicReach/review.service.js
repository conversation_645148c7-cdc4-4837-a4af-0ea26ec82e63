/* eslint-disable no-else-return */
const Community = require('../../communitiesAPI/models/community.model');
const CommunityMagicReachEmail = require('../../models/magicReach/communityMagicReachEmail.model');
const CommunityDraftTemplate = require('../../models/notificationBackend/communityDraftTemplate.model');
const LearnerModel = require('@/src/models/learners.model');
const UserModel = require('@/src/models/users.model');
const EnrichedLeadModel = require('@/src/models/magicLeads/enrichedLeads.model');
const PlanOrderModel = require('@/src/models/plan/communityPlanOrder.model');
const CommunityRoleModel = require('@/src/communitiesAPI/models/communityRole.model');
const ProductModel = require('@/src/models/product/communityProduct.model');
const IcpLeadMatchModel = require('@/src/models/magicLeads/icpLeadMatches.model');
const IcpProfileModel = require('@/src/models/magicLeads/icpProfiles.model');
const outreachEmailService = require('@/src/services/magicLeads/email.service');

const { transformPaginatedParam } = require('../../utils/pagination.util');

const { getMessage } = require('./compose.service');
const { processSendingMessage } = require('./send.service');
const {
  notifyManagerOnBlocked,
  notifyManagerOnApproved,
} = require('./send/fraud');

const { getMessageFilter } = require('./list.service');

const { ParamError } = require('../../utils/error.util');
const logger = require('../logger.service');

const {
  FRAUD_CHECK_STATUS,
  MAGIC_REACH_MESSAGE_STATUS,
  BUCKET_NAMES,
  REVIEW_STATUS,
} = require('./constants');
const { ORDER_STATUS } = require('@/src/services/plan/constants');
const { aclRoles } = require('@/src/communitiesAPI/constants');
const { MAGIC_LEADS_STATUS } = require('@/src/constants/common');

async function getCommunity(communityId) {
  const community = await Community.findById(communityId);
  if (!community) {
    throw new ParamError('Invalid communityId');
  }
  return community;
}

/**
 * Reject the in review message
 *  Send a notification to CM
 * @param {*} param0
 */
const rejectInReviewMessage = async ({
  messageId,
  operator,
  localizedReason,
}) => {
  const message = await getMessage(messageId);
  if (!message) {
    throw new ParamError('Magic Reach message not found');
  }
  message.fraudInfo.rejectedBy = operator;
  message.fraudInfo.reviewedBy = operator;
  message.fraudInfo.status = FRAUD_CHECK_STATUS.REJECTED;

  if (localizedReason) {
    message.fraudInfo.reviewStatus = REVIEW_STATUS.REJECTED;
    message.fraudInfo.localizedReason = localizedReason;
  }

  message.status = MAGIC_REACH_MESSAGE_STATUS.BLOCKED;
  message.fraudInfo.reviewedDate = new Date();

  await message.save();

  const isOutreachEmail =
    !!message.bucketFilters?.[BUCKET_NAMES.MAGIC_LEADS_OUTREACH];

  if (!isOutreachEmail) {
    await notifyManagerOnBlocked({
      title: message.title,
      authorUserObjectId: message.author,
    });
  }

  if (isOutreachEmail) {
    const leadObjectId =
      message.bucketFilters[BUCKET_NAMES.MAGIC_LEADS_OUTREACH]
        .leadObjectId;
    const icpProfileObjectId =
      message.bucketFilters[BUCKET_NAMES.MAGIC_LEADS_OUTREACH]
        .icpProfileObjectId;
    const communityObjectId = message.communityId;

    await IcpLeadMatchModel.updateOne(
      {
        icpProfileObjectId,
        leadObjectId,
        communityObjectId,
      },
      {
        $set: {
          status: MAGIC_LEADS_STATUS.REJECTED,
        },
      }
    );
  }
};

/**
 * Approve an in review message
 *  Update the status
 *  Send email asynchronously
 * @param {*} param
 */
const approveInReviewMessage = async ({
  messageId,
  operator,
  approveReason,
  localizedReason,
}) => {
  const message = await getMessage(messageId);
  if (!message) {
    throw new ParamError('Magic Reach message not found');
  }
  const community = await getCommunity(message.communityId);
  if (!community) {
    throw new ParamError('Community not found');
  }

  const isOutreachEmail =
    !!message.bucketFilters?.[BUCKET_NAMES.MAGIC_LEADS_OUTREACH];

  if (isOutreachEmail) {
    const leadObjectId =
      message.bucketFilters[BUCKET_NAMES.MAGIC_LEADS_OUTREACH]
        .leadObjectId;

    const enrichedLead = await EnrichedLeadModel.findOne({
      _id: leadObjectId,
      isActive: true,
    }).lean();

    if (!enrichedLead) {
      throw new ParamError('Lead not found or lead is inactive');
    }

    if (
      enrichedLead.reachInfo?.optOutFromEmail?.communityCodes?.includes(
        community.code
      )
    ) {
      throw new ParamError('Lead has opted out of email');
    }
  }

  message.fraudInfo.status = FRAUD_CHECK_STATUS.APPROVED;
  message.fraudInfo.approvedBy = operator;
  message.fraudInfo.reviewedBy = operator;

  if (isOutreachEmail) {
    message.fraudInfo.reviewStatus = REVIEW_STATUS.APPROVED;
    message.fraudInfo.localizedReason = localizedReason;
  }

  if (approveReason) {
    message.fraudInfo.approveReason = approveReason;
  }

  message.fraudInfo.reviewedDate = new Date();

  await message.save();

  if (!isOutreachEmail) {
    await notifyManagerOnApproved({
      title: message.title,
      authorUserObjectId: message.author,
    });
  }

  const messageData = message.toObject();
  const sentMessage = await processSendingMessage({
    community,
    messageId,
    messageData,
  });

  if (isOutreachEmail) {
    const leadObjectId =
      message.bucketFilters[BUCKET_NAMES.MAGIC_LEADS_OUTREACH]
        .leadObjectId;
    const icpProfileObjectId =
      message.bucketFilters[BUCKET_NAMES.MAGIC_LEADS_OUTREACH]
        .icpProfileObjectId;
    const communityObjectId = message.communityId;

    const [icpLeadMatch, icpProfile] = await Promise.all([
      IcpLeadMatchModel.findOne({
        icpProfileObjectId,
        leadObjectId,
        communityObjectId,
      }).lean(),
      IcpProfileModel.findOne({
        _id: icpProfileObjectId,
        communityObjectId,
      }).lean(),
    ]);

    await outreachEmailService.postSendOutreachEmail({
      message: sentMessage,
      icpProfile,
      icpLeadMatch,
    });
  }
};

/**
 * Get details of the paginated messages to return
 * @param {*} messages
 */
async function getMessageDetails(messages) {
  const communityIds = messages.map((message) => message.communityId);
  const messageIds = messages.map((message) => message._id.toString());
  const userObjectIds = messages.map((message) =>
    message.author.toString()
  );
  const leadObjectIds = messages
    .map((message) =>
      message.bucketFilters?.[
        BUCKET_NAMES.MAGIC_LEADS_OUTREACH
      ]?.leadObjectId?.toString()
    )
    .filter(Boolean);

  const entityObjectIds = messages
    .map((message) =>
      message.bucketMetas?.[
        BUCKET_NAMES.MAGIC_LEADS_OUTREACH
      ]?.entityObjectId?.toString()
    )
    .filter(Boolean);

  const [
    communities,
    templates,
    leads,
    activePlanOrder,
    users,
    communityOwnerRole,
    products,
  ] = await Promise.all([
    Community.find({
      _id: { $in: communityIds },
    }).lean(),
    CommunityDraftTemplate.find({
      draftId: { $in: messageIds },
    }).lean(),
    EnrichedLeadModel.find(
      {
        _id: { $in: leadObjectIds },
      },
      { email: 1, socialProfiles: 1, location: 1 }
    ).lean(),
    PlanOrderModel.find({
      communityObjectId: { $in: communityIds },
      $or: [
        {
          status: ORDER_STATUS.CURRENT,
        },
        {
          status: ORDER_STATUS.CANCELLED,
          cancelledAt: {
            $gt: new Date(),
          },
        },
      ],
    }).lean(),
    UserModel.find(
      {
        _id: { $in: userObjectIds },
      },
      { learner: 1 }
    ).lean(),
    CommunityRoleModel.find({
      communityObjectId: { $in: communityIds },
      role: aclRoles.OWNER,
    }).lean(),
    ProductModel.find({
      entityObjectId: { $in: entityObjectIds },
    }).lean(),
  ]);

  const learners = await LearnerModel.find({
    _id: { $in: users.map((user) => user.learner) },
  }).lean();

  const learnerMap = learners.reduce((acc, learner) => {
    acc[learner._id] = learner;
    return acc;
  }, {});

  const userToLearnerMap = users.reduce((acc, user) => {
    const learner = learnerMap[user.learner];
    acc[user._id] = learner;
    return acc;
  }, {});

  const communityMap = communities.reduce((acc, community) => {
    acc[community._id] = community;
    return acc;
  }, {});

  const templateMap = templates.reduce((acc, template) => {
    acc[template.draftId] = template;
    return acc;
  }, {});

  const leadMap = leads.reduce((acc, lead) => {
    acc[lead._id] = lead;
    return acc;
  }, {});

  const activePlanOrderMap = activePlanOrder.reduce((acc, planOrder) => {
    acc[planOrder.communityObjectId] = planOrder;
    return acc;
  }, {});

  const communityOwnerRoleMap = communityOwnerRole.reduce((acc, role) => {
    acc[role.communityObjectId] = role;
    return acc;
  }, {});

  const productMap = products.reduce((acc, product) => {
    acc[product.entityObjectId] = product;
    return acc;
  }, {});

  for await (const message of messages) {
    try {
      if (message.communityId in communityMap) {
        const community = communityMap[message.communityId];
        message.communityCode = community.code;
        message.planType = community.config?.planType ?? 'FREE';
        message.communityTitle = community.title;
        message.communityLink = community.link;
      }

      if (message.communityId in communityOwnerRoleMap) {
        const ownerRole = communityOwnerRoleMap[message.communityId];
        message.ownerEmail = ownerRole.email;
        message.ownerUserObjectId = ownerRole.userObjectId;
      }

      if (message._id in templateMap) {
        message.messageHTMLLink = templateMap[message._id].templateLink;
      }

      if (
        message.bucketFilters[
          BUCKET_NAMES.MAGIC_LEADS_OUTREACH
        ]?.leadObjectId?.toString() in leadMap
      ) {
        const lead =
          leadMap[
            message.bucketFilters[BUCKET_NAMES.MAGIC_LEADS_OUTREACH]
              .leadObjectId
          ];
        message.recipientEmail = lead.email;
        message.leadObjectId = lead._id;
        message.leadSocialProfiles = lead.socialProfiles;
        message.leadCountryName = lead.location?.countryName ?? '';
        message.optOutFromEmail =
          lead.reachInfo?.optOutFromEmail?.communityCodes?.includes(
            message.communityCode
          ) ?? false;
      }

      if (message.communityId in activePlanOrderMap) {
        const planOrder = activePlanOrderMap[message.communityId];
        message.planBillingCycle = planOrder.billingCycle;
        message.planStartDate = planOrder.createdAt;
        message.planInterval = planOrder.interval;
        message.planIntervalCount = planOrder.intervalCount;
      }

      if (message.author in userToLearnerMap) {
        message.senderEmail = userToLearnerMap[message.author].email;
        message.senderLanguagePreference =
          userToLearnerMap[message.author].languagePreference ?? 'en';
      }

      if (
        message.bucketMetas?.[
          BUCKET_NAMES.MAGIC_LEADS_OUTREACH
        ]?.entityObjectId?.toString() in productMap
      ) {
        const product =
          productMap[
            message.bucketMetas[
              BUCKET_NAMES.MAGIC_LEADS_OUTREACH
            ].entityObjectId.toString()
          ];
        message.productTitle = product.title;
        message.productLink = product.slug;
      }
    } catch (error) {
      logger.error(
        `Error in getting magic reach message details for COPS`,
        `messageId=${message._id}|`,
        `error=${error}|`,
        `trace=${error.stack}`
      );
    }
  }
  return messages;
}

/**
 * Return magic reach message for COPS portal
 * @param {*} param
 */
const getMessages = async ({
  messageStatus,
  fraudStatus,
  communitySearchString,
  messageSearchString,
  isOutreachEmail = false,
  communityCode,
  creatorEmail,
  recipientEmail,
  planType,
  messageObjectId,
  reviewStatus,
  sentDateFrom,
  sentDateTo,
  createdAtDateFrom,
  createdAtDateTo,
  reviewedDateFrom,
  reviewedDateTo,
  pageNo,
  pageSize,
  sortBy = 'createdAt',
  sortOrder = -1,
}) => {
  const messageFilter = await getMessageFilter({
    messageStatus,
    fraudStatus,
    communitySearchString,
    messageSearchString,
    isOutreachEmail,
    communityCode,
    creatorEmail,
    recipientEmail,
    planType,
    messageObjectId,
    reviewStatus,
    sentDateFrom,
    sentDateTo,
    createdAtDateFrom,
    createdAtDateTo,
    reviewedDateFrom,
    reviewedDateTo,
  });
  if (messageFilter === null) {
    return {
      messages: [],
      meta: {
        total: 0,
      },
    };
  }

  const { skip, limit } = transformPaginatedParam(pageNo, pageSize);
  const [messages, count] = await Promise.all([
    CommunityMagicReachEmail.find(messageFilter, {
      sentPlatforms: 1,
      status: 1,
      title: 1,
      author: 1,
      selectedUsers: 1,
      selectedBuckets: 1,
      unselectedUsers: 1,
      sentOn: 1,
      communityId: 1,
      analyticsData: 1,
      createdAt: 1,
      toSendCount: 1,
      fraudInfo: 1,
      bucketFilters: 1,
      bucketMetas: 1,
    })
      .sort({ [sortBy]: Number(sortOrder) })
      .skip(skip)
      .limit(limit)
      .lean(),
    CommunityMagicReachEmail.countDocuments(messageFilter),
  ]);

  const messagesWithDetails = await getMessageDetails(messages);
  const results = {
    messages: messagesWithDetails,
    meta: {
      total: count,
      page: pageNo,
      pages: Math.ceil(count / pageSize),
    },
  };

  return results;
};

module.exports = {
  approveInReviewMessage,
  rejectInReviewMessage,
  getMessages,
};
