/* eslint-disable no-unused-vars */
const crypto = require('crypto');
const ObjectId = require('mongoose').Types.ObjectId;

const CommunityDraftTemplateModel = require('../../../models/notificationBackend/communityDraftTemplate.model');
const CommunityMagicReachEmailRecipientsModel = require('../../../models/magicReach/communityMagicReachEmailRecipients.model');

const MagicReachNodeWrapper = require('../contentFormatter/MagicReachNodeWrapper');
const commonService = require('./common');
const logger = require('../../logger.service');
const { getConfigByTypeFromCache } = require('../../config.service');
const { getInvalidTestEmails } = require('../../mail/index');
const { getUploadedFileLink } = require('../../upload');

const { sendMessageToSQSQueue } = require('../../../handlers/sqs.handler');

const { ToUserError, ParamError } = require('../../../utils/error.util');
const { getFullName } = require('../../user/utils/learner.utils');
const {
  getAuthorDetails,
  getReplyToMailName,
} = require('../utils/learner.utils');
const localizationUtils = require('../../../utils/localization.utils');

const {
  GENERIC_REPLY_TO_NAME,
  FALLBACK_REPLY_TO,
  DB_INSERT_BATCH_SIZE,
  SEND_TO_QUEUE_BATCH_SIZE,
  BUCKET_NAMES,
} = require('../constants');
const {
  IMAGEASSETBUCKET,
  CONFIG_TYPES,
} = require('../../../constants/common');
const { GENERIC_ERROR } = require('../../../constants/errorCode');

const config = require('../../../config');

const PrimaryMongooseConnection = require('../../../rpc/primaryMongooseConnection');

const hasBase64Content = (str) => {
  const base64Regex =
    /data:image\/(png|jpg|jpeg|gif|webp|heic|heif|avif);base64,/g;
  return base64Regex.test(JSON.stringify(str || {}));
};

function generateHashForRecipients(draftId, email) {
  return crypto
    .createHash('md5')
    .update(`${draftId}_${email}`)
    .digest('hex')
    .slice(0, 24);
}

/**
 * Create recipients in DB using upsert with transaction for easier error handling
 * @returns inserted and failed recipient records to be used in send stage
 */
async function upsertRecipientsInDB({ recipients, messageId }) {
  const dbIdtoRecipientMap = {};
  const dbOperations = recipients.map((recipient) => {
    const { email, phoneNumber, inBuckets, profileImage } = recipient;
    const fullName = getFullName(recipient);
    const _id = new ObjectId(generateHashForRecipients(messageId, email));
    dbIdtoRecipientMap[_id] = recipient;
    return {
      updateOne: {
        filter: { _id },
        update: {
          $set: {
            draftId: messageId,
            email,
            fullName,
            phoneNumber,
            profileImage,
            sendGridStatus: 'false',
            inBucketsWhenSent: inBuckets,
          },
        },
        upsert: true,
      },
    };
  });
  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();

  session.startTransaction();
  let results;
  try {
    results = await CommunityMagicReachEmailRecipientsModel.bulkWrite(
      dbOperations,
      {
        ordered: true,
        batchSzie: DB_INSERT_BATCH_SIZE,
        session,
      }
    );
    await session.commitTransaction();
  } catch (error) {
    logger.error(
      `Failed to create recipients for Magic Reach message in DB using bulkWrite|` +
        `message_id=${messageId}|` +
        `error=${error}`
    );
    await session.abortTransaction();
    return {
      insertedRecipients: [],
      failedRecipients: Object.values(dbIdtoRecipientMap),
    };
  } finally {
    await session.endSession();
  }

  logger.info(
    `Finished upserting email recipients for Magic Reach message in DB using bulkwrite|` +
      `messageId=${messageId}` +
      `results=${JSON.stringify(results)}`
  );

  return {
    insertedRecipients: Object.values(dbIdtoRecipientMap),
    failedRecipients: [],
  };
}

/**
 * Create recipients in DB
 * @returns inserted and failed recipient records to be used in send stage
 */
async function createRecipientsInDB({ recipients, messageId }) {
  const dbIdtoRecipientMap = {};
  const dbModels = recipients.map((recipient) => {
    const { email, fullName, phoneNumber, inBuckets } = recipient;
    const _id = new ObjectId(generateHashForRecipients(messageId, email));
    dbIdtoRecipientMap[_id] = recipient;
    return {
      _id,
      draftId: messageId,
      email,
      fullName,
      phoneNumber,
      sendGridStatus: 'false',
      inBucketsWhenSent: inBuckets,
    };
  });
  try {
    const results =
      await CommunityMagicReachEmailRecipientsModel.insertMany(dbModels, {
        limit: DB_INSERT_BATCH_SIZE,
        ordered: true,
      });

    logger.info(
      `Finished upserting email recipients for Magic Reach message in DB using insertMany|` +
        `messageId=${messageId}` +
        `results=${JSON.stringify(results)}`
    );

    if (results?.length !== dbModels.length) {
      const allIds = dbModels.map((model) => model._id);
      const insertedIds = results.insertedIds ? results.insertedId : [];
      const failedIds = allIds.filter((id) => !insertedIds.includes(id));
      const insertedRecipients = insertedIds.map(
        (id) => dbIdtoRecipientMap[id]
      );
      const failedRecipients = failedIds.map(
        (id) => dbIdtoRecipientMap[id]
      );
      return {
        insertedRecipients,
        failedRecipients,
      };
    }
    return {
      insertedRecipients: Object.values(dbIdtoRecipientMap),
      failedRecipients: [],
    };
  } catch (error) {
    logger.error(
      `Failed to create email recipients for message in DB using insertMany|` +
        `message_id=${messageId}|` +
        `error=${error}`
    );
    return {
      insertedRecipients: [],
      failedRecipients: Object.values(dbIdtoRecipientMap),
    };
  }
}

function getS3KeyName({ messageId, language }) {
  if (!language || language === 'en') {
    return `${messageId}.html`;
  }
  return `${messageId}_${language}.html`;
}

/**
 * Create template document
 */
const getOrCreateTemplateDocument = async ({
  community,
  messageId,
  messageData,
  isPreview,
  postInfo = null,
}) => {
  if (hasBase64Content(messageData?.content?.root)) {
    throw new ParamError(
      `MessageData contains base64 info. [${messageId}] cannot be sent`
    );
  }
  let templateDocument = await CommunityDraftTemplateModel.findOne({
    draftId: messageId,
    isPreview,
  });
  const author = await getAuthorDetails(messageData.author);
  const replyToMail =
    author.length > 0 ? author[0].email : FALLBACK_REPLY_TO;
  const replyToMailName =
    author.length > 0
      ? getReplyToMailName(author[0])
      : GENERIC_REPLY_TO_NAME;
  const authorProfileImage =
    author.length > 0 ? author[0].profileImage : null;
  const authorFirstName = author?.[0]?.firstName ?? GENERIC_REPLY_TO_NAME;
  const authorLanguagePreference =
    author?.[0]?.authorLanguagePreference ?? 'en';

  const localisedKeys =
    await commonService.getMentionedProductsLocalisedKeys(
      authorLanguagePreference,
      messageData.mentionedProducts
    );

  const rootPayload = {
    ...messageData?.content?.root,
    localisedKeys,
    variablesData: {
      author_language_pref: authorLanguagePreference,
      author_timezone: author?.[0]?.timezone,
      community_id: community._id.toString(),
      community_code: community.code,
      community_name: community.title,
      community_profile_image:
        community.thumbnailImgData?.mobileImgData?.src,
      community_host: replyToMailName,
      sentOn: new Intl.DateTimeFormat('en-SG', {
        month: 'short',
        day: 'numeric',
      }).format(new Date()),
      community_link: `${config.NAS_IO_FRONTEND_URL}/${community.link}`,
      host_profile_image: authorProfileImage,
      mail_subject: messageData.title,
    },
  };

  const hasPostForMembers =
    postInfo &&
    messageData.selectedBuckets.includes(BUCKET_NAMES.MEMBERS) &&
    postInfo?.slug;

  if (hasPostForMembers) {
    rootPayload.variablesData.likePostLink = `${config.NAS_IO_FRONTEND_URL}${community.link}/feed${postInfo.slug}?likePost=true`;
    rootPayload.variablesData.commentPostLink = `${config.NAS_IO_FRONTEND_URL}${community.link}/feed${postInfo.slug}?openCommentInput=true`;
    rootPayload.variablesData.postLink = `${config.NAS_IO_FRONTEND_URL}${community.link}/feed${postInfo.slug}`;
  }

  const htmlGen = new MagicReachNodeWrapper(rootPayload);
  const mailSubject = htmlGen.getMailSubject();
  await htmlGen.initTags(authorLanguagePreference, hasPostForMembers);
  let htmlData = htmlGen.getHTML();
  const previewText = htmlGen.getPreviewText();
  htmlData = htmlData.replace('{{preview_text}}', `${previewText}...`);
  const destinationUrl = `/nasIO/portal/draft-email-templates/${community.code}`;

  const location = await getUploadedFileLink(
    IMAGEASSETBUCKET,
    destinationUrl,
    `${messageId}.html`,
    htmlData,
    'text/html'
  );

  const templateDocumentData = {
    draftId: messageId,
    communityId: community._id,
    communityCode: community.code,
    templateLink: location,
    fromMail:
      community.code === 'LEGENDS_COMMUNITY'
        ? '<EMAIL>'
        : `${community.link.replace('/', '')}-<EMAIL>`,
    fromMailName: `${authorFirstName} | ${community.title}`,
    replyToMail,
    replyToMailName,
    subject: isPreview ? `${mailSubject} - PREVIEW` : mailSubject,
    isPreview,
    authorLanguagePreference,
  };

  if (templateDocument) {
    templateDocument = await CommunityDraftTemplateModel.findOneAndUpdate(
      { _id: templateDocument._id },
      { $set: { ...templateDocumentData } },
      { new: true }
    );
  } else {
    templateDocument = await CommunityDraftTemplateModel.create(
      templateDocumentData
    );
  }

  const languages = await localizationUtils.getLanguagesInSnakeCase();
  languages.forEach(async (language) => {
    if (language !== authorLanguagePreference) {
      const languageSpecificNode = new MagicReachNodeWrapper(rootPayload);
      await languageSpecificNode.initTags(language, postInfo);
      let languageSpecificHTMLData = languageSpecificNode.getHTML();
      languageSpecificHTMLData = languageSpecificHTMLData.replace(
        '{{preview_text}}',
        `${previewText}...`
      );

      await getUploadedFileLink(
        IMAGEASSETBUCKET,
        destinationUrl,
        getS3KeyName({
          messageId,
          language,
        }),
        languageSpecificHTMLData,
        'text/html'
      );
    }
  });

  return templateDocument;
};

async function getSendToQueueBatchSize() {
  const lpbeConfig = await getConfigByTypeFromCache(
    CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
  );
  const configName = 'MAGIC_REACH_SEND_EMAIL_TO_QUEUE_BATCH_SIZE';
  const configValue =
    lpbeConfig?.envVarData?.[configName] || SEND_TO_QUEUE_BATCH_SIZE;
  return configValue;
}

const sendToQueueV2 = async ({
  communityCode,
  communityId,
  messageId,
  draftTemplateId,
  searchData,
  isStressTest = false,
  isTest = false,
}) => {
  const messageBody = {
    draftTemplateId,
    messageId,
    communityCode,
    communityId,
    searchData,
    isStressTest,
    isTest,
    isMarketing: true,
  };
  await sendMessageToSQSQueue({
    queueUrl: config.MAGIC_REACH_SEND_EMAIL_QUEUE_URL,
    messageBody,
  });
};

/**
 * Send to queue by chunk
 * @returns pushed and failed to pushed count
 */
const sendToQueue = async ({
  communityCode,
  messageId,
  templateDocument,
  recipients,
  // dbPage,
  isStressTest = false,
  isTest = false,
}) => {
  const chunks = [];
  const batchSize = await getSendToQueueBatchSize();
  for (let i = 0; i < recipients.length; i += batchSize) {
    chunks.push(recipients.slice(i, i + batchSize));
  }

  const sendToQueuePromises = chunks.map(async (chunk, index) => {
    try {
      const recipientCount = chunk.length;
      const toEmails = [];
      const toNames = [];
      const processedRecipients = [];

      chunk.forEach((recipient) => {
        toEmails.push(recipient.email);
        toNames.push(getFullName(recipient));
        processedRecipients.push({
          email: recipient.email,
          fullName: getFullName(recipient),
          phoneNumber: recipient.phoneNumber,
          profileImage: recipient.profileImage,
          inBucketsWhenSent: recipient.inBuckets,
        });
      });

      const messageBody = {
        draftTemplateId: templateDocument._id,
        messageId,
        communityCode,
        toEmails,
        toNames,
        recipients: processedRecipients,
        isStressTest,
        isTest,
        isMarketing: true,
        index,
      };

      await sendMessageToSQSQueue({
        queueUrl: config.MAGIC_REACH_SEND_EMAIL_QUEUE_URL,
        messageBody,
      });

      logger.info(
        `Finished sending Magic reach message to email queue|` +
          `queueUrl=${config.MAGIC_REACH_SEND_EMAIL_QUEUE_URL}|` +
          `recipientCount=${recipientCount}|` +
          // `dbPage=${dbPage}|` +
          `innerChunkIndex=${index}`
      );

      return recipientCount;
    } catch (error) {
      logger.error(
        `Failed to send Magic reach message to email queue|` +
          `queueUrl=${config.MAGIC_REACH_SEND_EMAIL_QUEUE_URL}|` +
          `recipientsSize=${SEND_TO_QUEUE_BATCH_SIZE}|` +
          // `dbPage=${dbPage}|` +
          `innerChunkIndex=${index}|` +
          `error=${error}|` +
          `trace=${error.stack}`
      );
      return 0;
    }
  });

  const results = await Promise.allSettled(sendToQueuePromises);
  const successCount = results
    .filter((result) => result.status === 'fulfilled')
    .map((result) => result.value)
    .reduce((total, count) => total + count, 0);
  return {
    queuePushed: successCount,
    queueFailed: recipients.length - successCount,
  };
};

const createRecipientsAndSendEmails = async ({
  community,
  messageId,
  templateDocument,
  recipients,
  pageNo,
}) => {
  const queueResults = await sendToQueue({
    communityCode: community.code,
    messageId,
    templateDocument,
    recipients,
    // dbPage: pageNo,
  });
  const queuePushed = queueResults.queuePushed;
  const queueFailed = queueResults.queueFailed;

  logger.info(
    `Magic Reach emails pushed to queue|` +
      `messageId=${messageId}|` +
      `page=${pageNo}|` +
      `pushed=${queuePushed}|` +
      `failed=${queueFailed}`
  );

  return {
    queuePushed,
    queueFailed,
  };
};

const sendTestEmail = async ({
  community,
  messageId,
  messageData,
  recipients,
  isStressTest = false,
  isTest = false,
}) => {
  const emails = recipients.map((recipient) => recipient.email);
  const invalidEmails = await getInvalidTestEmails({
    community,
    emails,
  });

  if (invalidEmails.length > 0) {
    throw new ToUserError(
      `Test recipients must be current members of community.`,
      GENERIC_ERROR.INVALID_TEST_RECIPIENT_ERROR
    );
  }

  const templateDocument = await getOrCreateTemplateDocument({
    community,
    messageId,
    messageData,
    isPreview: true,
  });

  const queueResult = await sendToQueue({
    communityCode: community.code,
    messageId,
    templateDocument,
    recipients,
    // dbPage: 0,
    isStressTest,
    isTest,
  });
  return queueResult.queueFailed === 0;
};

module.exports = {
  hasBase64Content,
  sendTestEmail,
  createRecipientsAndSendEmails,
  getOrCreateTemplateDocument,
  sendToQueueV2,
};
