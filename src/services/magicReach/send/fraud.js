const axios = require('../../../clients/axios.client');

const MagicReachNodeWrapper = require('../contentFormatter/MagicReachNodeWrapper');

const logger = require('../../logger.service');

const {
  getAuthorDetails,
  getReplyToMailName,
} = require('../utils/learner.utils');

const {
  NOTIFICATION_URL,
  NOTIFICATION_AUTH,
  env,
} = require('../../../config');

const {
  MAIL_TYPES,
  GENERIC_REPLY_TO_NAME,
  AI_TEMPLATE_PROMPT_TYPE,
  REVIEW_STATUS,
} = require('../constants');

const { createChatComletion } = require('../../../clients/openai.client');
const openaiClient = require('@/src/clients/openaiV2.client');
const fraudService = require('../../fraud');
const promptService = require('@/src/services/aiCofounder/ai/prompt');
const {
  TEMPLATE_SOURCE_TYPE,
  PRODUCTION,
} = require('@/src/constants/common');

async function createChatForFraudProbability(content) {
  const prompt = await fraudService.getFraudOpenAIPrompt(
    fraudService.PROMPT_NAMES.MAGIC_REACH
  );

  return `${prompt} 
  Give your answer in JSON format with the following structure and instructions:
  {
    "probability": 0-100,
    "reason": "Your reason here, in English, for the user input being suspicious or the scraped content from external links being suspicious.",
  }

  Following is the content:
  ${content}`;
}

function outreachEmailFraudCheckFunctionCallSchema() {
  return {
    type: 'function',
    name: 'outreach_email_fraud_check',
    description:
      'Evaluate whether an outreach email message is potentially fraudulent.',
    parameters: {
      type: 'object',
      properties: {
        fraudReason: {
          type: 'object',
          description:
            'Specific reason the message is flagged as fraudulent (e.g., phishing attempt, impersonation, suspicious link).',
          properties: {
            en: {
              type: 'string',
              description:
                'Reason in English for the message being flagged as fraudulent.',
            },
            es_mx: {
              type: 'string',
              description:
                'Reason in Spanish (Mexico) for the message being flagged as fraudulent.',
            },
            pt_br: {
              type: 'string',
              description:
                'Reason in Portuguese (Brazil) for the message being flagged as fraudulent.',
            },
            ja: {
              type: 'string',
              description:
                'Reason in Japanese for the message being flagged as fraudulent.',
            },
          },
          required: ['en', 'es_mx', 'pt_br', 'ja'],
          additionalProperties: false,
        },
        aiSummary: {
          type: 'string',
          description:
            'Summary of the message being flagged as fraudulent by AI in english.',
        },
        reviewStatus: {
          type: 'string',
          description:
            'Fraud review status of the message (e.g. pending_review, approved, rejected).',
          enum: [
            REVIEW_STATUS.PENDING_REVIEW,
            REVIEW_STATUS.APPROVED_BY_AI,
            REVIEW_STATUS.REJECTED_BY_AI,
          ],
        },
      },
      required: ['fraudReason', 'aiSummary', 'reviewStatus'],
      additionalProperties: false,
    },
  };
}

async function generateFraudCheckForOutreachEmail({
  title,
  formattedMessage,
  aiContextInput,
  outreachPurpose,
}) {
  const promptTemplate = await promptService.getProcessedPromptAndModel({
    templateType: AI_TEMPLATE_PROMPT_TYPE.OUTREACH_EMAIL_FRAUD_CHECK,
    source: TEMPLATE_SOURCE_TYPE.MAGIC_LEADS,
  });

  const input = [...(aiContextInput ?? [])];

  if (outreachPurpose) {
    input.push({
      role: 'system',
      content: `Outreach purpose: ${outreachPurpose}`,
    });
  }

  if (title) {
    input.push({
      role: 'system',
      content: `Email title: ${title}`,
    });
  }

  if (formattedMessage) {
    input.push({
      role: 'system',
      content: `Email content: ${formattedMessage}`,
    });
  }

  const schema = outreachEmailFraudCheckFunctionCallSchema();

  const response = await openaiClient.responses.create({
    model: promptTemplate.aiModel?.model ?? 'gpt-4.1-mini',
    instructions: promptTemplate.prompt,
    input,
    tools: [schema],
    tool_choice: 'required',
    parallel_tool_calls: false,
    max_tool_calls: 1,
    max_output_tokens: 10000,
    truncation: 'auto',
  });

  const responseOutput = response?.output?.find(
    (output) => output.type === 'function_call'
  );

  if (!responseOutput) {
    throw new Error('Failed to generate outreach email template');
  }

  const args = JSON.parse(responseOutput.arguments);
  const callId = responseOutput.call_id;
  const responseId = response.id;

  return {
    fraudReason: args.fraudReason,
    reviewStatus: args.reviewStatus,
    aiSummary: args.aiSummary,
    callId,
    responseId,
  };
}

async function getAuthor(authorUserObjectId) {
  const author = await getAuthorDetails(authorUserObjectId);
  const authorEmail = author.length > 0 ? author[0].email : undefined;
  if (!authorEmail) {
    throw new Error('CM Email is undefined');
  }
  const authorName =
    author.length > 0
      ? getReplyToMailName(author[0])
      : GENERIC_REPLY_TO_NAME;

  return {
    authorEmail,
    authorName,
  };
}

const checkForExternalLink = async ({ content, title }) => {
  const rootPayload = {
    ...content?.root,
    title,
  };
  const links = new MagicReachNodeWrapper(rootPayload).getLinks();

  const hostname = env === PRODUCTION ? 'nas.io' : 'dev-nas.io';

  for (const link of links) {
    try {
      const parsedUrl = new URL(link);
      if (parsedUrl.hostname !== hostname) {
        return true;
      }
    } catch (error) {
      logger.info(
        'Invalid URL encountered for external link check',
        link,
        error.message
      );
      return true;
    }
  }

  return false;
};

const consultOpenAPIForFraudProbability = async ({ content, title }) => {
  const rootPayload = {
    ...content?.root,
    title,
  };
  const formattedMessage = new MagicReachNodeWrapper(
    rootPayload
  ).getMessageForFraudCheck();

  try {
    const blacklistKeywordResult =
      await fraudService.hasBlacklistedKeywords({
        content: `${title}, ${formattedMessage}`,
      });
    if (blacklistKeywordResult.hasBlacklistedKeyword) {
      return {
        probability: 100,
        reason: `The message contains a blacklisted keyword: ${blacklistKeywordResult.blacklistedKeyword}`,
      };
    }
    const chatContent = await createChatForFraudProbability(
      formattedMessage
    );
    const probabilityResponse = await createChatComletion([
      {
        role: 'user',
        content: chatContent,
      },
    ]);
    const resultString =
      probabilityResponse?.choices?.[0]?.message?.content
        ?.replace('```json\n', '')
        ?.replace('\n```', '');
    const result = JSON.parse(resultString);
    const probability = result.probability;
    const reason = result.reason;
    return {
      probability,
      reason,
    };
  } catch (error) {
    if (error.response) {
      logger.error(
        `Error in calling OpenAI chatCompletion|`,
        `errorStatus=${error.response.status}|`,
        `errorData=${error.response.data}`
      );
    } else {
      logger.error(`Error with OpenAI API request|error=${error.message}`);
    }
    return {
      probability: 0,
    };
  }
};

const consultOpenAPIForOutreachEmailFraudCheck = async ({
  content,
  title,
  hasExternalLinks,
  aiContextInput,
  outreachPurpose,
}) => {
  const rootPayload = {
    ...(content?.root ?? {}),
    title,
  };
  const formattedMessage = new MagicReachNodeWrapper(
    rootPayload
  ).getMessageForFraudCheck();

  try {
    const blacklistKeywordResult =
      await fraudService.hasBlacklistedKeywords({
        content: `${title}, ${formattedMessage}`,
      });

    if (blacklistKeywordResult.hasBlacklistedKeyword) {
      const fraudReason = {
        en: `The message contains a blacklisted keyword: ${blacklistKeywordResult.blacklistedKeyword}`,
        es_mx: `El mensaje contiene una palabra clave en la lista negra: ${blacklistKeywordResult.blacklistedKeyword}`,
        pt_br: `A mensagem contém uma palavra-chave na lista negra: ${blacklistKeywordResult.blacklistedKeyword}`,
        ja: `メッセージにはブラックリストに登録されたキーワードが含まれています: ${blacklistKeywordResult.blacklistedKeyword}`,
      };

      return {
        reviewStatus: REVIEW_STATUS.REJECTED_BY_AI,
        fraudReason,
        aiSummary: fraudReason.en,
        reviewedDate: new Date(),
      };
    }

    if (hasExternalLinks) {
      const fraudReason = {
        en: 'The message contains external links',
        es_mx: 'El mensaje contiene enlaces externos',
        pt_br: 'A mensagem contém links externos',
        ja: 'メッセージには外部リンクが含まれています',
      };

      return {
        reviewStatus: REVIEW_STATUS.REJECTED_BY_AI,
        fraudReason,
        aiSummary: fraudReason.en,
        reviewedDate: new Date(),
      };
    }

    const fraudCheckResult = await generateFraudCheckForOutreachEmail({
      title,
      formattedMessage,
      aiContextInput,
      outreachPurpose,
    });

    return {
      reviewStatus: fraudCheckResult.reviewStatus,
      fraudReason: fraudCheckResult.fraudReason,
      aiSummary: fraudCheckResult.aiSummary,
      reviewedDate: new Date(),
      callId: fraudCheckResult.callId,
      responseId: fraudCheckResult.responseId,
    };
  } catch (error) {
    logger.error(
      `consultOpenAPIForOutreachEmailFraudCheck error: ${error.message}`
    );

    return {
      reviewStatus: REVIEW_STATUS.APPROVED,
    };
  }
};

/**
 * Send an email to COPs mailbox notifying a potential fraud Magic Reach message
 * @param {*} param
 */
const notifyCOPSOnPendingApproval = async ({
  community,
  reason,
  title,
}) => {
  const emailData = {
    reason,
    communityName: community.title,
    title,
    communityCode: community.code,
    env,
  };
  try {
    const data = {
      mailType: MAIL_TYPES.PENDING_APPROVAL_NOTIFY_COPS,
      mailSubject: `[${env.toLocaleUpperCase()}] Pending approval message from ${
        community.title
      } community (code ${community.code})`,
      requesterServiceName: 'LPBE',
      data: emailData,
    };
    await axios.post(`${NOTIFICATION_URL}/api/v1/send-email`, data, {
      headers: {
        Authorization: `Bearer ${NOTIFICATION_AUTH}`,
      },
    });
  } catch (error) {
    logger.error(
      'Error on axios post request to notify COPS on pending approval Magic Reach|',
      `error=${error}|`,
      `trace=${error.stack}`
    );
  }
};

/**
 * Send an email to COPs mailbox notifying a potential fraud Magic Reach message
 * @param {*} param
 */
const notifyCOPSOnWarning = async ({ community, reason, title }) => {
  const emailData = {
    reason,
    communityName: community.title,
    title,
    communityCode: community.code,
    env,
  };
  try {
    const data = {
      mailType: MAIL_TYPES.WARNING_NOTIFY_COPS,
      mailSubject: `[${env.toLocaleUpperCase()}] Magic Reach potential fraud message warning from ${
        community.title
      } community (code ${community.code}) `,
      requesterServiceName: 'LPBE',
      data: emailData,
    };
    await axios.post(`${NOTIFICATION_URL}/api/v1/send-email`, data, {
      headers: {
        Authorization: `Bearer ${NOTIFICATION_AUTH}`,
      },
    });
  } catch (error) {
    logger.error(
      'Error on axios post request to notify COPS on pending approval Magic Reach|',
      `error=${error}|`,
      `trace=${error.stack}`
    );
  }
};

/**
 * Send an email to COPs mailbox notifying a potential fraud Magic Reach message that needs approval
 * @param {*} param
 */
const notifyCMOnPendingApproval = async ({
  title,
  authorUserObjectId,
}) => {
  const { authorEmail, authorName } = await getAuthor(authorUserObjectId);
  try {
    const emailData = {
      magicReachMessageTitle: title,
    };

    const data = {
      mailType: MAIL_TYPES.PENDING_APPROVAL_NOTIFY_CM,
      mailSubject: `Your latest Magic Reach message is in review`,
      toMail: [authorEmail],
      toMailName: [authorName],
      requesterServiceName: 'LPBE',
      data: emailData,
    };
    await axios.post(`${NOTIFICATION_URL}/api/v1/send-email`, data, {
      headers: {
        Authorization: `Bearer ${NOTIFICATION_AUTH}`,
      },
    });
  } catch (error) {
    logger.error(
      'Error on axios post request to notify CM on pending approval Magic Reach|',
      `error=${error}|`,
      `trace=${error.stack}`
    );
  }
};

/**
 * Send an email to CM notifying that a Magic reach message is blocked due to fraud
 * @param {*} param0
 */
const notifyManagerOnApproved = async ({ title, authorUserObjectId }) => {
  const { authorEmail, authorName } = await getAuthor(authorUserObjectId);
  const emailData = {
    magicReachMessageTitle: title,
  };
  try {
    const data = {
      mailType: MAIL_TYPES.APPROVED_NOTIFY_MANAGER,
      mailSubject: 'Your magic reach message is approved',
      toMail: [authorEmail],
      toMailName: [authorName],
      requesterServiceName: 'LPBE',
      data: emailData,
    };
    await axios.post(`${NOTIFICATION_URL}/api/v1/send-email`, data, {
      headers: {
        Authorization: `Bearer ${NOTIFICATION_AUTH}`,
      },
    });
  } catch (error) {
    logger.error(
      'Error on axios post request to notify CM on Magic Reach message approved|',
      `error=${error}|`,
      `trace=${error.stack}`
    );
  }
};

/**
 * Send an email to CM notifying that a Magic reach message is blocked due to fraud
 * @param {*} param0
 */
const notifyManagerOnBlocked = async ({ title, authorUserObjectId }) => {
  const { authorEmail, authorName } = await getAuthor(authorUserObjectId);
  const emailData = {
    magicReachMessageTitle: title,
  };
  try {
    const data = {
      mailType: MAIL_TYPES.BLOCKED_NOTIFY_MANAGER,
      mailSubject: 'Your magic reach message is blocked',
      toMail: [authorEmail],
      toMailName: [authorName],
      requesterServiceName: 'LPBE',
      data: emailData,
    };
    await axios.post(`${NOTIFICATION_URL}/api/v1/send-email`, data, {
      headers: {
        Authorization: `Bearer ${NOTIFICATION_AUTH}`,
      },
    });
  } catch (error) {
    logger.error(
      'Error on axios post request to notify CM on Magic Reach message being blocked|',
      `error=${error}|`,
      `trace=${error.stack}`
    );
  }
};

module.exports = {
  consultOpenAPIForFraudProbability,
  consultOpenAPIForOutreachEmailFraudCheck,
  checkForExternalLink,
  notifyCMOnPendingApproval,
  notifyCOPSOnPendingApproval,
  notifyCOPSOnWarning,
  notifyManagerOnBlocked,
  notifyManagerOnApproved,
};
