/* eslint-disable no-await-in-loop */
/* eslint-disable no-else-return */
const ROUTE_API_KEY = process.env.ROUTE_API_KEY;

const WhitelistedCommunity = require('../../models/fraud/whitelistedCommunity.model');
const Community = require('../../communitiesAPI/models/community.model');
const CommunityMagicReachEmailModel = require('../../models/magicReach/communityMagicReachEmail.model');
const Learner = require('../../models/learners.model');
const EmailUnsubscribe = require('../../communitiesAPI/models/emailUnsubscribe.model');
const CommunityMagicReachStats = require('../../models/magicReach/communityMagicReachStats.model');
const EmailScheduledJob = require('../../models/mail/emailScheduledJob.model');
const PrimaryMongooseConnection = require('../../rpc/primaryMongooseConnection');

const {
  JOB_STATUS,
  RESULT_TYPE,
  WORKER_TYPES,
  NO_MAIL_TYPES,
} = require('../mail/constants');

const {
  submitJob,
  updateJob,
  reportJobResult,
} = require('../mail/scheduledEmail.service');
const {
  addTrackingEvents,
} = require('../platform/genericTracking/genericTracking.service');

const {
  updateMessage,
  searchRecipients,
  getToCountBuckets,
  countRecipients,
  filterSelectedUsers,
} = require('./compose.service');
// const { getFormattedMentionedProducts } = require('./common');
const {
  consultOpenAPIForOutreachEmailFraudCheck,
  consultOpenAPIForFraudProbability,
  notifyCMOnPendingApproval,
  notifyCOPSOnPendingApproval,
  notifyCOPSOnWarning,
  checkForExternalLink,
} = require('./send/fraud');
const {
  hasBase64Content,
  createRecipientsAndSendEmails,
  sendTestEmail,
  getOrCreateTemplateDocument,
  sendToQueueV2,
} = require('./send/sendEmail');
const {
  createRecipientsAndSendWhatsappInitiateMessage,
  sendTestWhatsappInitiateMessage,
  sendWhatsappReplyMessages,
  sendToQueueInitiateMessageV2,
} = require('./send/sendWhatsapp');
const sendDiscordService = require('./send/sendDiscord');
const sendPostService = require('./send/sendPost');
const { getOptOutWhatsappByCommunity } = require('./utils/optInOut.util');
const {
  extractCommunitySendLimitConfig,
  isEnforcingSendLimit,
} = require('./utils/communityLimit.util');

const {
  validateMongoosePayload,
  ParamError,
  ToUserError,
} = require('../../utils/error.util');
const stringUtils = require('../../utils/string_handling');
const logger = require('../logger.service');
const { getConfigByTypeFromCache } = require('../config.service');

const {
  LEARN_BACKEND_URL,
  MAGIC_REACH_FRAUD_PROBABILITY_THRESHOLD,
  MAGIC_REACH_BLOCK_ON_FRAUD,
  MAGIC_REACH_SEND_COUNT_BLOCK_THRESHOLD,
} = require('../../config');

const {
  SEARCH_RECIPIENT_DB_PAGE_SIZE,
  PLATFORMS,
  FRAUD_CHECK_STATUS,
  MAGIC_REACH_MESSAGE_STATUS,
  BUCKET_NAMES,
  NO_LIMIT,
  WHATSAPP_DEFAULT_TEMPLATE_ID,
  BUCKET_NAME_TO_BATCH_METADATA_MODEL_TYPE,
  REVIEW_STATUS,
} = require('./constants');
const { CONFIG_TYPES } = require('../../constants/common');

const {
  MAGIC_REACH_ERROR,
  GENERIC_ERROR,
} = require('../../constants/errorCode');
const membershipService = require('../membership');
const batchMetadataService = require('../batchMetadata');
const MagicReachUsageService = require('../featurePermissions/magicReachUsage.service');

async function getCommunity(communityId) {
  const community = await Community.findById(communityId).lean();
  if (!community) {
    throw new ParamError('Invalid communityId');
  }
  return community;
}

async function getSearchDBBatchSize() {
  const lpbeConfig = await getConfigByTypeFromCache(
    CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
  );
  const configName = 'MAGIC_REACH_SEARCH_DB_BATCH_SIZE';
  const configValue =
    lpbeConfig?.envVarData?.[configName] || SEARCH_RECIPIENT_DB_PAGE_SIZE;
  return configValue;
}

/**
 * Get unique emails that have unsubscribed from Magic Reach
 * @param {ObjectId} communityId
 * @returns {Promise<{Set<string>}>}
 */
async function getEmailsToAvoidSet(communityId, communityCode) {
  const emails = await EmailUnsubscribe.find({
    communityObjectId: { $in: [communityId, null] },
  }).distinct('email');

  const query = {
    isActive: true,
    [`notificationsPreferences.${NO_MAIL_TYPES.ALL}.optOutForCommunities`]:
      communityCode,
  };

  let learnerEmails = await Learner.find(query).select('email').lean();

  learnerEmails = learnerEmails.map((learner) => learner.email);
  if (!emails?.length && !learnerEmails.length) {
    return new Set();
  }
  return new Set([...emails, ...learnerEmails]);
}

async function getPhoneNumbersToAvoidSet(communityId) {
  const numbers = await getOptOutWhatsappByCommunity(communityId);
  if (!numbers?.length) {
    return new Set();
  }
  return new Set(numbers);
}

/**
 * Get recessary maps for processing from recipients selection in Magic Reach message
 * @param {*} messageData
 * @returns maps is email -> User
 */
function getFilteringMaps(messageData) {
  const selectedUser = messageData.selectedUsers || [];
  const unselectedUsers = messageData.unselectedUsers || [];
  const remainedSelectedUserMapByEmail = selectedUser.reduce(
    (acc, user) => {
      if (user.email) {
        acc[user.email] = user;
      }
      return acc;
    },
    {}
  );
  const remainedSelectedUserMapByPhone = selectedUser.reduce(
    (acc, user) => {
      if (user.phoneNumber) {
        acc[user.phoneNumber] = user;
      }
      return acc;
    },
    {}
  );
  const unselectedUserMapByEmail = unselectedUsers.reduce((acc, user) => {
    if (user.email) {
      acc[user.email] = user;
    }
    return acc;
  }, {});
  const unselectedUserMapByPhone = unselectedUsers.reduce((acc, user) => {
    if (user.phoneNumber) {
      acc[user.phoneNumber] = user;
    }
    return acc;
  }, {});
  return {
    remainedSelectedUserMapByEmail,
    remainedSelectedUserMapByPhone,
    unselectedUserMapByEmail,
    unselectedUserMapByPhone,
  };
}

/**
 * Log and aggregate all create in DB & send to queue result for a platform
 * @param {*} param0
 */
function processPromiseResult({
  promiseResult,
  messageId,
  platform,
  bucket,
  currentPage,
  originalCount,
}) {
  if (promiseResult.status === 'rejected') {
    logger.error(
      `Failed to send Magic Reach message|` +
        `messageId=${messageId}|` +
        `platform=${platform}|` +
        `bucket=${bucket}|` +
        `page=${currentPage}|` +
        `result=${JSON.stringify(promiseResult)}`
    );
    return {
      dbInserted: 0,
      dbFailed: originalCount,
      queuePushed: 0,
      queueFailed: originalCount,
    };
  } else if (promiseResult.status === 'fulfilled') {
    logger.info(
      `Finished sending Magic Reach message|` +
        `messageId=${messageId}|` +
        `platform=${platform}|` +
        `bucket=${bucket}|` +
        `page=${currentPage}|` +
        `result=${JSON.stringify(promiseResult.value)}`
    );
    return {
      dbInserted: promiseResult.value.dbInserted,
      dbFailed: promiseResult.value.dbFailed,
      queuePushed: promiseResult.value.queuePushed,
      queueFailed: promiseResult.value.queueFailed,
    };
  }
  return {
    dbInserted: 0,
    dbFailed: originalCount,
    queuePushed: 0,
    queueFailed: originalCount,
  };
}

/**
 * Create recipients in DB and push to send queue, concurrently by platform
 * @param {*} param
 * @returns new total result map
 */
async function createRecipientsAndSend({
  totalResultMap,
  community,
  messageId,
  messageData,
  emailTemplateDocument,
  bucket,
  platformsToExecute,
  currentEmailBatch,
  currentPhoneBatch,
  currentPage,
}) {
  const newTotalResultMap = totalResultMap;
  const toExecutePromise = [];
  const originalCounts = [];
  const platformsExecuted = [];
  if (
    currentEmailBatch.length > 0 &&
    platformsToExecute.includes(PLATFORMS.EMAIL)
  ) {
    toExecutePromise.push(
      createRecipientsAndSendEmails({
        community,
        messageId,
        messageData,
        templateDocument: emailTemplateDocument,
        recipients: currentEmailBatch,
        pageNo: currentPage,
      })
    );
    originalCounts.push(currentEmailBatch.length);
    platformsExecuted.push(PLATFORMS.EMAIL);
  }
  if (
    currentPhoneBatch.length > 0 &&
    platformsToExecute.includes(PLATFORMS.WHATSAPP)
  ) {
    toExecutePromise.push(
      createRecipientsAndSendWhatsappInitiateMessage({
        community,
        messageId,
        messageData,
        recipients: currentPhoneBatch,
        pageNo: currentPage,
      })
    );
    originalCounts.push(currentPhoneBatch.length);
    platformsExecuted.push(PLATFORMS.WHATSAPP);
  }

  const promiseResults = await Promise.allSettled(toExecutePromise);
  for (let i = 0; i < platformsExecuted.length; i++) {
    const platform = platformsExecuted[i];
    const promiseResult = promiseResults[i];
    const result = processPromiseResult({
      promiseResult,
      platform,
      messageId,
      bucket,
      currentPage,
      originalCount: originalCounts[i],
    });
    // newTotalResultMap[platform].dbInserted += result.dbInserted;
    // newTotalResultMap[platform].dbFailed += result.dbFailed;
    newTotalResultMap[platform].queuePushed += result.queuePushed;
    newTotalResultMap[platform].queueFailed += result.queueFailed;
  }
  return newTotalResultMap;
}

async function sendDiscord({ community, messageId, messageData }) {
  const discordResult =
    await sendDiscordService.formatAndSendDiscordMessage({
      community,
      messageData,
    });
  await CommunityMagicReachEmailModel.updateOne(
    {
      _id: messageId,
    },
    {
      [`sentResults.${PLATFORMS.DISCORD_V2}`]: discordResult,
    }
  );
}

async function sendPost({ community, messageId, messageData }) {
  const { bucketFilters = {} } = messageData;
  const challengeBucket =
    bucketFilters[BUCKET_NAMES.CHALLENGE_PARTICIPANTS];
  let postResult;
  // If the message targets a challenge bucket, delegate to the challenge helper
  if (challengeBucket?.challengeId) {
    postResult = await sendPostService.formatAndSendChallengePost({
      community,
      messageData,
      programId: challengeBucket.challengeId,
    });
  } else {
    postResult = await sendPostService.formatAndSendPostMessage({
      community,
      messageData,
    });
  }

  const { announcementInfo, ...rest } = postResult;
  await CommunityMagicReachEmailModel.updateOne(
    {
      _id: messageId,
    },
    {
      [`sentResults.${PLATFORMS.ANNOUNCEMENT_V2}`]: rest,
    }
  );

  return announcementInfo;
}

async function* retrieveBatchMetadataList({
  community,
  batchMetadataModelType,
  entityObjectId,
}) {
  let lastBatchNumber;

  while (true) {
    const batchMetadataList =
      await batchMetadataService.retrieveBatchMetadataList({
        batchMetadataModelType,
        entityObjectId,
        communityObjectId: community._id,
        lastBatchNumber,
      });

    if (!batchMetadataList?.length) {
      break;
    }

    yield batchMetadataList;

    lastBatchNumber =
      batchMetadataList[batchMetadataList.length - 1].batchNumber;
  }
}

async function populateQueueMessages({
  community,
  bucket,
  messageData,
  baseQueueMessage,
  pageSize,
  recipientCountByBucketMap,
  filteringDataByBucketMap,
  queueMessages,
}) {
  const { filters, queryParams, otherFilters } =
    filteringDataByBucketMap[bucket];

  const batchMetadataModelType =
    BUCKET_NAME_TO_BATCH_METADATA_MODEL_TYPE[bucket];
  const isBatchMetadataEnabled =
    community.config?.batchMetadataEnabledType?.[batchMetadataModelType];

  const hasSearchString =
    filters.searchString != null && filters.searchString !== '';

  const entityObjectId = filters.entityObjectId ?? community._id;

  const existsBatchMetadataList =
    await batchMetadataService.existsBatchMetadataList({
      communityObjectId: community._id,
      batchMetadataModelType,
      entityObjectId,
    });

  if (
    isBatchMetadataEnabled &&
    existsBatchMetadataList &&
    !hasSearchString
  ) {
    for await (const batchMetadataList of retrieveBatchMetadataList({
      community,
      batchMetadataModelType,
      entityObjectId,
    })) {
      for (const batchMetadata of batchMetadataList) {
        const queueMessage = { ...baseQueueMessage };
        const { startObjectId, endObjectId, batchNumber } = batchMetadata;

        queueMessage.searchData = {
          bucketName: bucket,
          filters,
          searchString: filters.searchString,
          pageNo: batchNumber,
          pageSize,
          communityRole: queryParams.role || [],
          status: queryParams.status || [],
          otherFilters,
          unselectedUsers: messageData.unselectedUsers,
          startObjectId,
          endObjectId,
        };

        queueMessages.push(queueMessage);
      }
    }
  } else {
    const recipientCount = recipientCountByBucketMap[bucket];
    const numberOfPages = Math.ceil(recipientCount / pageSize);

    for (
      let currentPage = 1;
      currentPage <= numberOfPages;
      currentPage++
    ) {
      const queueMessage = { ...baseQueueMessage };

      queueMessage.searchData = {
        bucketName: bucket,
        filters,
        searchString: filters.searchString,
        pageNo: currentPage,
        pageSize,
        communityRole: queryParams.role || [],
        status: queryParams.status || [],
        otherFilters,
        unselectedUsers: messageData.unselectedUsers,
      };

      queueMessages.push(queueMessage);
    }
  }
}

async function retrieveQueueMessages({
  community,
  baseQueueMessage,
  messageData,
  recipientCountByBucketMap,
  filteringDataByBucketMap,
  buckets,
}) {
  const pageSize = await getSearchDBBatchSize();

  const queueMessages = [];

  for await (const bucket of buckets) {
    await populateQueueMessages({
      community,
      bucket,
      messageData,
      baseQueueMessage,
      pageSize,
      recipientCountByBucketMap,
      filteringDataByBucketMap,
      queueMessages,
    });
  }

  if (messageData.selectedUsers?.length > 0) {
    queueMessages.push({
      ...baseQueueMessage,
      searchData: {
        selectedUsers: messageData.selectedUsers,
      },
    });
  }

  return queueMessages;
}

async function submitSendEmailJobs({
  community,
  messageId,
  messageData,
  postInfo,
  recipientCountByBucketMap,
  filteringDataByBucketMap,
  buckets,
}) {
  const emailTemplateDocument = await getOrCreateTemplateDocument({
    community,
    messageId,
    messageData,
    postInfo,
    isPreview: false,
  });

  if (!emailTemplateDocument) {
    throw new ParamError(`Email template not found for ${messageId}`);
  }

  const baseQueueMessage = {
    communityId: community._id,
    communityCode: community.code,
    messageId,
    draftTemplateId: emailTemplateDocument._id,
  };

  const queueMessages = await retrieveQueueMessages({
    community,
    baseQueueMessage,
    messageData,
    recipientCountByBucketMap,
    filteringDataByBucketMap,
    buckets,
  });

  await Promise.all(
    queueMessages.map(async (queueMessage) => {
      try {
        await sendToQueueV2(queueMessage);
      } catch (error) {
        logger.error(
          `Failed to send to Email queue`,
          error,
          error.stack,
          JSON.stringify(queueMessage)
        );
      }
    })
  );

  await CommunityMagicReachEmailModel.updateOne(
    {
      _id: messageId,
    },
    {
      $set: {
        [`pushedToQueueResults.${PLATFORMS.EMAIL}.queuePushed`]: true,
      },
    }
  );
}

async function submitSendWhatsappJobs({
  community,
  messageId,
  messageData,
  buckets,
  recipientCountByBucketMap,
  filteringDataByBucketMap,
}) {
  const baseQueueMessage = {
    community,
    messageId,
    messageData,
    templateObjectId:
      messageData.templateObjectId?.[PLATFORMS.WHATSAPP] ??
      WHATSAPP_DEFAULT_TEMPLATE_ID,
  };

  const queueMessages = await retrieveQueueMessages({
    community,
    baseQueueMessage,
    messageData,
    recipientCountByBucketMap,
    filteringDataByBucketMap,
    buckets,
  });

  await Promise.all(
    queueMessages.map(async (queueMessage) => {
      try {
        await sendToQueueInitiateMessageV2(queueMessage);
      } catch (error) {
        logger.error(
          `Failed to send to Whatsapp queue`,
          error,
          error.stack,
          JSON.stringify(queueMessage)
        );
      }
    })
  );

  await CommunityMagicReachEmailModel.updateOne(
    {
      _id: messageId,
    },
    {
      $set: {
        [`pushedToQueueResults.${PLATFORMS.WHATSAPP}.queuePushed`]: true,
      },
    }
  );
}

// eslint-disable-next-line no-unused-vars
const processSendingMessage = async ({
  community,
  messageId,
  messageData,
}) => {
  const selectedBuckets = messageData.selectedBuckets || [];
  const toCountBuckets = getToCountBuckets(selectedBuckets);
  const emailsToAvoidSet = await getEmailsToAvoidSet(
    community._id,
    community.code
  );
  const phoneNumbersToAvoidSet = await getPhoneNumbersToAvoidSet(
    community._id
  );
  const optOutEmails = Array.from(emailsToAvoidSet);
  const optOutWhatsapp = Array.from(phoneNumbersToAvoidSet);

  let postInfo;
  const platformsToExecute = messageData.sentPlatforms || [];
  if (platformsToExecute.includes(PLATFORMS.ANNOUNCEMENT_V2)) {
    // here need to handle to the bucket filters and send the post or send challenge feed entry
    postInfo = await sendPost({ community, messageId, messageData });
  }
  if (platformsToExecute.includes(PLATFORMS.DISCORD_V2)) {
    await sendDiscord({ community, messageId, messageData });
  }

  const toSendEmail = platformsToExecute.includes(PLATFORMS.EMAIL);
  const toSendWhatsapp = platformsToExecute.includes(PLATFORMS.WHATSAPP);

  const recipientCountByBucketMap = {};
  const filteringDataByBucketMap = {};
  if (toSendEmail || toSendWhatsapp) {
    for (const bucketName of toCountBuckets) {
      const filters = messageData.bucketFilters?.[bucketName] || {};
      let searchString;
      if (filters.searchString) {
        searchString = filters.searchString;
      }
      const { queryParams, otherFilters } =
        // eslint-disable-next-line no-await-in-loop
        await membershipService.membershipSearchUtils.processGetMembersQueryParams(
          { ...filters, bucketName }
        );
      const sampleSearch = await searchRecipients({
        communityId: community._id,
        bucketName,
        searchString,
        filters,
        pageNo: 0,
        pageSize: 1,
        communityRole: queryParams.role || [],
        status: queryParams.status || [],
        otherFilters,
        additionalData: {
          community,
          optOutEmails,
          optOutWhatsapp,
        },
      });
      const recipientCount = sampleSearch.meta.total;
      recipientCountByBucketMap[bucketName] = recipientCount;
      filteringDataByBucketMap[bucketName] = {
        filters,
        queryParams,
        otherFilters,
      };
    }

    if (toSendEmail) {
      await submitSendEmailJobs({
        community,
        messageId,
        messageData,
        postInfo,
        buckets: toCountBuckets,
        recipientCountByBucketMap,
        filteringDataByBucketMap,
        optOutEmails,
        optOutWhatsapp,
      });
    }
    if (toSendWhatsapp) {
      await submitSendWhatsappJobs({
        community,
        messageId,
        messageData,
        recipientCountByBucketMap,
        filteringDataByBucketMap,
        buckets: toCountBuckets,
        optOutEmails,
        optOutWhatsapp,
      });
    }
  }

  const updatedMessage =
    await CommunityMagicReachEmailModel.findOneAndUpdate(
      {
        _id: messageId,
      },
      {
        status: MAGIC_REACH_MESSAGE_STATUS.SENT,
      },
      {
        new: true,
      }
    ).lean();

  try {
    await addTrackingEvents([
      {
        entity: 'magic_reach_message',
        collectionName: 'community_magic_reach_email',
        entityObjectId: messageId,
        event: 'sent',
        trackingInfo: {
          communityObjectId: community._id,
          communityCode: community.code,
          messageTitle: updatedMessage.title,
          selectedBuckets: updatedMessage.selectedBuckets,
          sentPlatforms: updatedMessage.sentPlatforms,
          createdAt: updatedMessage.createdAt,
          author: updatedMessage.author,
          status: updatedMessage.status,
        },
      },
    ]);
  } catch (error) {
    logger.error(
      'Failed to add tracking events',
      `error=${error}`,
      `stack=${error.stack}`
    );
  }

  return updatedMessage;
};

/**
 * Search, create recipients in DB and push to SQS queue sending task
 * @param {*} param0
 */
const searchForAndCreateRecipientsAndSend = async ({
  community,
  messageId,
  messageData,
}) => {
  const communityId = community._id;
  try {
    const selectedBuckets = messageData.selectedBuckets || [];
    const {
      remainedSelectedUserMapByEmail,
      remainedSelectedUserMapByPhone,
      unselectedUserMapByEmail,
      unselectedUserMapByPhone,
    } = getFilteringMaps(messageData);
    const toCountBuckets = getToCountBuckets(selectedBuckets);
    const emailRecipientMap = {};
    const phoneRecipientMap = {};
    const emailsToAvoidSet = await getEmailsToAvoidSet(
      communityId,
      community.code
    );
    const phoneNumbersToAvoidSet = await getPhoneNumbersToAvoidSet(
      communityId
    );
    let totalResultMap = {};
    const sentResults = {};
    const platformsToExecute = [];
    for (const platform of messageData.sentPlatforms) {
      totalResultMap[platform] = {
        // dbInserted: 0,
        // dbFailed: 0,
        queuePushed: 0,
        queueFailed: 0,
      };
      platformsToExecute.push(platform);
    }

    let postInfo;
    // check for platforms over here
    if (platformsToExecute.includes(PLATFORMS.ANNOUNCEMENT_V2)) {
      const postResult = await sendPostService.formatAndSendPostMessage({
        community,
        messageData,
      });
      const { announcementInfo, ...rest } = postResult;
      sentResults[PLATFORMS.ANNOUNCEMENT_V2] = rest;
      // eslint-disable-next-line no-unused-vars
      postInfo = announcementInfo;
    }
    if (platformsToExecute.includes(PLATFORMS.DISCORD_V2)) {
      const discordResult =
        await sendDiscordService.formatAndSendDiscordMessage({
          community,
          messageData,
        });
      sentResults[PLATFORMS.DISCORD_V2] = discordResult;
    }
    let emailTemplateDocument;
    if (platformsToExecute.includes(PLATFORMS.EMAIL)) {
      emailTemplateDocument = await getOrCreateTemplateDocument({
        community,
        messageId,
        messageData,
        isPreview: false,
        postInfo,
      });
    }

    for (const bucket of toCountBuckets) {
      let currentPage = 0;
      let hasMore = true;
      do {
        const currentEmailBatch = [];
        const currentPhoneBatch = [];
        const filters = messageData.bucketFilters?.[bucket] || {};
        let searchString;
        if (filters.searchString) {
          searchString = filters.searchString;
        }
        const { queryParams, otherFilters } =
          // eslint-disable-next-line no-await-in-loop
          await membershipService.membershipSearchUtils.processGetMembersQueryParams(
            { ...filters, bucketName: bucket }
          );
        // eslint-disable-next-line no-await-in-loop
        const searchResults = await searchRecipients({
          communityId,
          bucketName: bucket,
          searchString,
          filters,
          pageNo: currentPage,
          pageSize: SEARCH_RECIPIENT_DB_PAGE_SIZE,
          communityRole: queryParams.role || [],
          status: queryParams.status || [],
          otherFilters,
          additionalData: {
            community,
            optOutEmails: Array.from(emailsToAvoidSet),
            optOutWhatsapp: Array.from(phoneNumbersToAvoidSet),
          },
        });
        if (!searchResults?.users?.length) {
          break;
        }
        if (searchResults.users.length < SEARCH_RECIPIENT_DB_PAGE_SIZE) {
          hasMore = false;
        }

        for (const user of searchResults.users) {
          if (
            user.email &&
            !(user.email in emailRecipientMap) &&
            !emailsToAvoidSet.has(user.email) &&
            !(user.email in unselectedUserMapByEmail)
          ) {
            emailRecipientMap[user.email] = user;
            currentEmailBatch.push(user);
            delete remainedSelectedUserMapByEmail[user.email];
          }
          if (
            user.phoneNumber &&
            !(user.phoneNumber in phoneRecipientMap) &&
            !phoneNumbersToAvoidSet.has(user.phoneNumber) &&
            !(user.phoneNumber in unselectedUserMapByPhone)
          ) {
            phoneRecipientMap[user.phoneNumber] = user;
            currentPhoneBatch.push(user);
            delete remainedSelectedUserMapByPhone[user.phoneNumber];
          }
        }

        // eslint-disable-next-line no-await-in-loop
        totalResultMap = await createRecipientsAndSend({
          totalResultMap,
          community,
          messageId,
          messageData,
          emailTemplateDocument,
          bucket,
          platformsToExecute,
          currentEmailBatch,
          currentPhoneBatch,
          currentPage,
        });

        currentPage++;
      } while (hasMore);
    }

    emailsToAvoidSet.forEach((email) => {
      if (email in remainedSelectedUserMapByEmail) {
        delete remainedSelectedUserMapByEmail[email];
      }
    });
    phoneNumbersToAvoidSet.forEach((phoneNumber) => {
      if (phoneNumber in remainedSelectedUserMapByPhone) {
        delete remainedSelectedUserMapByPhone[phoneNumber];
      }
    });

    totalResultMap = await createRecipientsAndSend({
      totalResultMap,
      community,
      messageId,
      messageData,
      emailTemplateDocument,
      bucket: 'Remained Selected Users',
      platformsToExecute,
      currentEmailBatch: Object.values(remainedSelectedUserMapByEmail),
      currentPhoneBatch: Object.values(remainedSelectedUserMapByPhone),
      currentPage: 'NA',
    });

    const toDBPayload = messageData;
    toDBPayload.status = MAGIC_REACH_MESSAGE_STATUS.SENT;
    delete toDBPayload.analyticsData;
    const updatedMessage = await updateMessage({
      communityId,
      messageId,
      messageData: {
        ...toDBPayload,
        pushedToQueueResults: totalResultMap,
        sentResults,
      },
    });

    try {
      await addTrackingEvents([
        {
          entity: 'magic_reach_message',
          collectionName: 'community_magic_reach_email',
          entityObjectId: messageId,
          event: 'sent',
          trackingInfo: {
            communityObjectId: communityId,
            communityCode: community.code,
            messageTitle: updatedMessage.title,
            selectedBuckets: updatedMessage.selectedBuckets,
            sentPlatforms: updatedMessage.sentPlatforms,
            createdAt: updatedMessage.createdAt,
            author: updatedMessage.author,
            status: updatedMessage.status,
          },
        },
      ]);
    } catch (error) {
      logger.error(
        'Failed to add tracking events',
        `error=${error}`,
        `stack=${error.stack}`
      );
    }
  } catch (error) {
    logger.error(
      `Failed to asynchronously send Magic Reach Message|` +
        `communityId=${communityId}|` +
        `messageId=${messageId}|` +
        `error=${error}|` +
        `trace=${error.stack}`
    );
  }
};

/**
 * Legacy check for sending limit, throw ToUserError if limit exceeds
 * @param {Object} community - Community object
 * @param {Array} sentPlatforms - Platforms to send to
 * @param {Object} toSendCounts - Count of messages to send
 * @returns {Promise<void>}
 */
async function checkSendLimitLegacy({
  community,
  sentPlatforms,
  toSendCounts,
}) {
  await Promise.all(
    sentPlatforms.map(async (platform) => {
      const sendLimit = await extractCommunitySendLimitConfig({
        community,
        platform,
      });

      if (!sendLimit || sendLimit.total === -1) {
        return;
      }

      const communityStats = await CommunityMagicReachStats.findOne({
        communityId: community._id,
        platform,
        window: sendLimit.window,
      });

      const currentStats = {
        totalSent: 0,
        memberSent: 0,
        nonMemberSent: 0,
      };

      if (communityStats) {
        currentStats.totalSent = communityStats.totalSent ?? 0;
        currentStats.memberSent = communityStats.memberSent ?? 0;
        currentStats.nonMemberSent = communityStats.nonMemberSent ?? 0;
      }

      for (const stat of ['total', 'member', 'nonMember']) {
        if (sendLimit[stat] && sendLimit[stat] !== NO_LIMIT) {
          if (
            currentStats[`${stat}Sent`] + toSendCounts[stat] >
            sendLimit[stat]
          ) {
            const formattedStatName = stringUtils.capitalizeAllWords(
              stringUtils.camelToSpace(stat)
            );
            throw new ToUserError(
              `You have reached your ${formattedStatName} send quota of ${sendLimit[stat]} messages. Please upgrade to send more messages.`,
              MAGIC_REACH_ERROR.WA_DM_LIMIT_REACHED,
              {
                quotaName: formattedStatName,
                limit: sendLimit[stat],
              }
            );
          }
        }
      }
    })
  );
}

/**
 * Check for sending limit using feature permissions, throw ToUserError if limit exceeds
 * @param {Object} community - Community object
 * @param {Array} sentPlatforms - Platforms to send to
 * @param {Object} toSendCounts - Count of messages to send
 * @returns {Promise<void>}
 */
async function checkSendLimitWithFeaturePermissions({
  community,
  sentPlatforms,
  toSendCounts,
}) {
  // Check WhatsApp platform specifically with feature permissions
  if (sentPlatforms.includes(PLATFORMS.WHATSAPP)) {
    const limitCheck = await MagicReachUsageService.checkSendLimit(
      community._id,
      toSendCounts.total
    );

    if (!limitCheck.allowed) {
      throw new ToUserError(
        limitCheck.message,
        MAGIC_REACH_ERROR.WA_DM_LIMIT_REACHED,
        {
          quotaName: 'WhatsApp Messages',
          limit: limitCheck.limit,
          currentUsage: limitCheck.currentUsage,
        }
      );
    }
  }

  // For non-WhatsApp platforms, fall back to the legacy system
  const nonWhatsappPlatforms = sentPlatforms.filter(
    (platform) => platform !== PLATFORMS.WHATSAPP
  );
  if (nonWhatsappPlatforms.length > 0) {
    await checkSendLimitLegacy({
      community,
      sentPlatforms: nonWhatsappPlatforms,
      toSendCounts,
    });
  }
}

// Keep the old function name for backward compatibility
const checkSendLimit = checkSendLimitWithFeaturePermissions;

async function checkFraud({ messageData }) {
  const toSendCount = messageData.toSendCount;
  const openAIResult = await consultOpenAPIForFraudProbability({
    content: messageData.content,
    title: messageData.title,
    threshold: MAGIC_REACH_FRAUD_PROBABILITY_THRESHOLD,
  });

  const hasExternalLinks = await checkForExternalLink({
    content: messageData.content,
    title: messageData.title,
  });

  let isPotentialFraud = false;
  let sendOnDetection;
  if (
    openAIResult.probability >= MAGIC_REACH_FRAUD_PROBABILITY_THRESHOLD
  ) {
    isPotentialFraud = true;
  }

  // eslint-disable-next-line no-unused-vars
  const invitedSelectedUserCount = messageData.selectedUsers?.filter(
    (user) => user.inBuckets?.includes(BUCKET_NAMES.INVITED)
  ).length;

  // eslint-disable-next-line no-unused-vars
  const customFilterWithInvited =
    messageData.bucketFilters?.[
      BUCKET_NAMES.CUSTOM_FILTERS
    ]?.status?.includes('invited') ||
    messageData.bucketFilters?.[BUCKET_NAMES.CUSTOM_FILTERS]?.tabFilter ===
      'invited' ||
    messageData.bucketFilters?.[BUCKET_NAMES.CUSTOM_FILTERS]?.segment ===
      'all' ||
    messageData.bucketFilters?.[BUCKET_NAMES.CUSTOM_FILTERS]?.segment ===
      'invited';

  if (parseInt(MAGIC_REACH_BLOCK_ON_FRAUD, 10) === 0) {
    sendOnDetection = true;
  } else if (toSendCount >= MAGIC_REACH_SEND_COUNT_BLOCK_THRESHOLD) {
    sendOnDetection = false;
  } else {
    sendOnDetection = true;
  }

  const reasons = [];
  if (isPotentialFraud) {
    reasons.push(openAIResult.reason);
  }
  if (hasExternalLinks) {
    reasons.push('Message contains external links');
  }
  const reason = reasons.join(' ');

  return {
    openAIResult,
    isPotentialFraud,
    hasExternalLinks,
    sendOnDetection,
    reason,
  };
}

async function checkOutreachEmailFraud({ messageData }) {
  const hasExternalLinks = await checkForExternalLink({
    content: messageData.content,
    title: messageData.title,
  });

  const openAIResult = await consultOpenAPIForOutreachEmailFraudCheck({
    content: messageData.content,
    title: messageData.title,
    hasExternalLinks,
    aiContextInput:
      messageData.bucketMetas?.[BUCKET_NAMES.MAGIC_LEADS_OUTREACH]
        ?.aiContextInput,
    outreachPurpose:
      messageData.bucketMetas?.[BUCKET_NAMES.MAGIC_LEADS_OUTREACH]
        ?.outreachPurpose,
  });

  const isPotentialFraud = ![
    REVIEW_STATUS.APPROVED,
    REVIEW_STATUS.APPROVED_BY_AI,
  ].includes(openAIResult.reviewStatus);

  const sendOnDetection = false;

  return {
    openAIResult,
    isPotentialFraud,
    hasExternalLinks,
    sendOnDetection,
    reason: null,
  };
}

async function isCommunityRestricted({ community }) {
  if (community.restrictedInfo?.magicReach === true) {
    return true;
  }
  return false;
}

async function isCommunityWhitelisted({ community }) {
  const magicReachWhitelisted =
    community.magicReachFraudConfig?.whitelisted === true;
  const whitelistedCommunity = await WhitelistedCommunity.findOne({
    code: community.code,
  }).lean();
  const isManaged = community.currentAccountManagerEmail;
  return magicReachWhitelisted || whitelistedCommunity || isManaged;
}

async function checkOutreachMessageForFraud({ community, messageData }) {
  let isPotentialFraud = false;
  let sendOnDetection;
  let hasExternalLinks = false;
  let reason = '';
  let openAIResult = {};

  const isRestricted =
    community.restrictedInfo?.magicLeadOutreachEmail === true;

  if (isRestricted) {
    return {
      isRestricted: true,
    };
  }

  try {
    const fraudResult = await checkOutreachEmailFraud({
      messageData,
    });
    openAIResult = fraudResult.openAIResult;
    isPotentialFraud = fraudResult.isPotentialFraud;
    hasExternalLinks = fraudResult.hasExternalLinks;
    sendOnDetection = fraudResult.sendOnDetection;
    reason = fraudResult.reason;
  } catch (error) {
    logger.error(`Error in fraud check`, error, error.stack);
  }

  return {
    openAIResult,
    isPotentialFraud,
    hasExternalLinks,
    sendOnDetection,
    reason,
    whitelisted: false,
    enforceInReviewProcess: false,
    isOutreachEmail: true,
  };
}

async function checkMessageForFraud({ community, messageData }) {
  let isPotentialFraud = false;
  let sendOnDetection;
  let hasExternalLinks = false;
  let reason = '';
  let openAIResult = {};
  const enforceInReviewProcess =
    community.magicReachFraudConfig?.enforceInReviewProcess ?? false;

  const whitelisted = await isCommunityWhitelisted({ community });

  if (enforceInReviewProcess || !whitelisted) {
    const isRestricted = await isCommunityRestricted({
      community,
    });
    if (isRestricted) {
      return {
        isRestricted: true,
      };
    }

    try {
      const fraudResult = await checkFraud({
        messageData,
      });
      openAIResult = fraudResult.openAIResult;
      isPotentialFraud = fraudResult.isPotentialFraud;
      hasExternalLinks = fraudResult.hasExternalLinks;
      sendOnDetection = fraudResult.sendOnDetection;
      reason = fraudResult.reason;
    } catch (error) {
      logger.error(`Error in fraud check`, error, error.stack);
    }
  }
  return {
    openAIResult,
    isPotentialFraud,
    hasExternalLinks,
    sendOnDetection,
    reason,
    whitelisted,
    enforceInReviewProcess,
  };
}

async function processFraudResultAndSend({
  community,
  messageId,
  messageData,
  fraudCheckResult,
  jobObjectId = null,
}) {
  const toDBMessage = messageData;
  const toSendCount = toDBMessage.toSendCount;
  const {
    whitelisted,
    openAIResult = {},
    isPotentialFraud,
    sendOnDetection,
    reason,
    enforceInReviewProcess,
    isOutreachEmail = false,
  } = fraudCheckResult;
  try {
    let toSend = true;

    if (enforceInReviewProcess) {
      toDBMessage.fraudInfo = {
        ...openAIResult,
        status: FRAUD_CHECK_STATUS.REVIEWING,
      };
      toDBMessage.status = MAGIC_REACH_MESSAGE_STATUS.REVIEWING;

      // create template document so COPS can view the content
      await getOrCreateTemplateDocument({
        community,
        messageId,
        messageData,
        isPreview: false,
      });

      await notifyCOPSOnPendingApproval({
        community,
        reason,
        title: toDBMessage.title,
      });

      toSend = false;
    } else if (whitelisted) {
      toDBMessage.fraudInfo = {
        ...openAIResult,
        status: FRAUD_CHECK_STATUS.APPROVED,
      };
    } else if (isPotentialFraud && !sendOnDetection) {
      logger.info(
        `Message detected as potential fraud, needs review|messageId=${messageId}`
      );

      toDBMessage.fraudInfo = {
        ...openAIResult,
        status: FRAUD_CHECK_STATUS.REVIEWING,
      };

      toDBMessage.status = MAGIC_REACH_MESSAGE_STATUS.REVIEWING;

      if (
        isOutreachEmail &&
        [REVIEW_STATUS.REJECTED_BY_AI, REVIEW_STATUS.REJECTED].includes(
          openAIResult.reviewStatus
        )
      ) {
        toDBMessage.fraudInfo.status = FRAUD_CHECK_STATUS.REJECTED;
        toDBMessage.status = MAGIC_REACH_MESSAGE_STATUS.BLOCKED;
      }

      // create template document so COPS can view the content
      await getOrCreateTemplateDocument({
        community,
        messageId,
        messageData,
        isPreview: false,
      });

      if (!isOutreachEmail) {
        await notifyCOPSOnPendingApproval({
          community,
          reason,
          title: toDBMessage.title,
        });
      }

      toSend = false;
    } else if (isPotentialFraud && sendOnDetection) {
      logger.info(
        `Message detected as potential fraud but send on detection|messageId=${messageId}|`,
        `toSendCount=${toSendCount}`
      );
      toDBMessage.fraudInfo = {
        ...openAIResult,
        status: FRAUD_CHECK_STATUS.SENT_ON_DETECTION,
      };
      await notifyCOPSOnWarning({
        community,
        reason,
        title: toDBMessage.title,
      });
    } else {
      toDBMessage.fraudInfo = {
        ...openAIResult,
        status: FRAUD_CHECK_STATUS.APPROVED,
      };
    }

    let updatedMessage = await updateMessage({
      communityId: community._id,
      messageId,
      messageData: toDBMessage,
    });

    if (toSend) {
      logger.info(
        `Not fraud message or not blocking fraud message. Sending..`
      );
      updatedMessage = await processSendingMessage({
        community,
        messageId,
        messageData: toDBMessage,
      });
      if (jobObjectId) {
        // update the job status to completed
        await reportJobResult({
          jobObjectId,
          resultType: RESULT_TYPE.SINGLE,
          jobStatus: JOB_STATUS.COMPLETED,
          results: {
            status: JOB_STATUS.COMPLETED,
            message: 'Scehduled MR sent to all participants',
          },
        });
      }
    } else if (!isOutreachEmail) {
      await notifyCMOnPendingApproval({
        title: toDBMessage.title,
        authorUserObjectId: toDBMessage.author,
      });
    }

    return updatedMessage;
  } catch (error) {
    logger.error(
      'checkFraudAndSend unexpected error|',
      `error=${error}|`,
      `trace=${error.stack}`
    );
    toDBMessage.status = MAGIC_REACH_MESSAGE_STATUS.PROCESSING_FAILED;
    await updateMessage({
      communityId: community._id,
      messageId,
      messageData: toDBMessage,
    });
    if (jobObjectId) {
      // update the job status to completed
      await reportJobResult({
        jobObjectId,
        resultType: RESULT_TYPE.SINGLE,
        jobStatus: JOB_STATUS.FAILED,
        results: {
          status: JOB_STATUS.FAILED,
          message: `Scheduled MR processing failed - ${error.message}`,
        },
      });
    }

    return null;
  }
}

const getInitialAnalyticsData = (platforms) => {
  const analyticsData = {};
  if (platforms.includes(PLATFORMS.EMAIL)) {
    analyticsData[PLATFORMS.EMAIL] = {
      totalOpens: 0,
      uniqueOpens: 0,
      nasCTAClicks: 0,
      optOutClicks: 0,
      totalClicks: 0,
      uniqueClicks: 0,
      sentRecipients: 0,
      bounceRecipients: 0,
      failedRecipients: 0,
    };
  }
  if (platforms.includes(PLATFORMS.WHATSAPP)) {
    analyticsData[PLATFORMS.WHATSAPP] = {
      initiateMessage: {
        sentRecipients: 0,
        failedRecipients: 0,
        readRecipients: 0,
        deliveredRecipients: 0,
      },
      lastReplyMessage: {
        sentRecipients: 0,
        failedRecipients: 0,
        readRecipients: 0,
        deliveredRecipients: 0,
      },
    };
  }
  return analyticsData;
};

async function validateSendingMessage(messageId) {
  const message = await CommunityMagicReachEmailModel.findOne(
    {
      _id: messageId,
    },
    { status: 1 }
  );
  if (!message) {
    throw new ParamError(`Message not found for ${messageId}`);
  }
  if (
    ![
      MAGIC_REACH_MESSAGE_STATUS.DRAFT,
      MAGIC_REACH_MESSAGE_STATUS.SCHEDULED,
    ].includes(message.status)
  ) {
    throw new ParamError(`Message has already been sent for ${messageId}`);
  }
}

const sendMessage = async ({ communityId, messageId, messageData }) => {
  const community = await getCommunity(communityId);
  await validateSendingMessage(messageId);
  const { jobObjectId } = messageData;
  const toDBMessage = messageData;
  toDBMessage.status = MAGIC_REACH_MESSAGE_STATUS.PROCESSING;
  toDBMessage.isDraft = false;
  toDBMessage.sentOn = new Date();
  toDBMessage.analyticsData = getInitialAnalyticsData(
    toDBMessage.sentPlatforms
  );
  toDBMessage.selectedUsers = await filterSelectedUsers(
    toDBMessage.selectedUsers,
    communityId
  );
  const toSendCounts = await countRecipients({
    communityId: community._id,
    selectedBuckets: toDBMessage.selectedBuckets,
    selectedUsers: toDBMessage.selectedUsers,
    unselectedUsers: toDBMessage.unselectedUsers,
    bucketFilters: toDBMessage.bucketFilters,
  });
  toDBMessage.toSendCount = toSendCounts?.total;

  if (toDBMessage.toSendCount === 0) {
    throw new ParamError(`No recipients selected for ${messageId}`);
  }

  // TODO: TO cleanup later - Comment this out since FE wont use
  // toDBMessage.mentionedProducts = await getFormattedMentionedProducts(
  //   toDBMessage.mentionedProducts,
  //   true
  // );
  if (await isEnforcingSendLimit()) {
    await checkSendLimit({
      community,
      sentPlatforms: messageData.sentPlatforms,
      toSendCounts,
    });
  }

  const updatedMessage = await updateMessage({
    communityId,
    messageId,
    messageData: toDBMessage,
  });

  if (hasBase64Content(updatedMessage)) {
    throw new ParamError(
      `Updated Message contains base64 info. [${messageId}] cannot be sent`
    );
  }

  const isOutreachEmail = toDBMessage.selectedBuckets.includes(
    BUCKET_NAMES.MAGIC_LEADS_OUTREACH
  );

  let fraudCheckResult;

  if (isOutreachEmail) {
    fraudCheckResult = await checkOutreachMessageForFraud({
      community,
      messageData,
    });
  } else {
    fraudCheckResult = await checkMessageForFraud({
      community,
      messageData,
    });
  }

  if (fraudCheckResult.isRestricted === true) {
    await CommunityMagicReachEmailModel.updateOne(
      {
        _id: messageId,
      },
      {
        status: MAGIC_REACH_MESSAGE_STATUS.BLOCKED,
      }
    );
    throw new ToUserError(
      'Community is restricted for Magic Reach',
      GENERIC_ERROR.RISKY_ACTION_ERROR
    );
  }

  const message = await processFraudResultAndSend({
    community,
    messageId,
    messageData: toDBMessage,
    fraudCheckResult,
    jobObjectId,
  });

  return message;
};

/**
 * Send a test message to selected platforms with selected recipients
 * @param {*} param0
 */
const sendTestMessage = async ({
  communityId,
  messageId,
  messageData,
  platform,
  recipients,
}) => {
  let result;
  const community = await getCommunity(communityId);
  switch (platform) {
    case PLATFORMS.EMAIL: {
      const toSendRecipients = [];
      recipients.forEach((recipient) => {
        if (recipient.email) {
          const fullName = recipient.fullName || 'TEST RECIPIENT';
          const firstName = fullName.split(' ')[0];

          toSendRecipients.push({
            firstName,
            lastName: fullName.replace(firstName, '').trim(),
            fullName,
            email: recipient.email,
          });
        }
      });
      result = await sendTestEmail({
        community,
        messageId,
        messageData,
        recipients: toSendRecipients,
        isTest: true,
      });
      if (!result) {
        throw new Error('Failed to send test email');
      }
      break;
    }
    case PLATFORMS.WHATSAPP: {
      const toSendRecipients = [];
      recipients.forEach((recipient) => {
        if (recipient.phoneNumber) {
          toSendRecipients.push({
            fullName: recipient.fullName || 'TEST RECIPIENT',
            phoneNumber: recipient.phoneNumber,
          });
        }
      });
      result = await sendTestWhatsappInitiateMessage({
        community,
        messageId,
        messageData,
        recipients: toSendRecipients,
        isTest: true,
      });
      if (!result) {
        throw new Error('Failed to send test Whatsapp 1st message');
      }
      break;
    }
    default:
      throw new ParamError('Unsupported platform');
  }
};

const stressTestMessage = async ({
  numberOfRecipients,
  platform,
  communityId,
  messageId,
  messageData,
  testEmailPrefix,
  testEmailDomain,
  testPhoneNumbers,
}) => {
  let result;
  const community = await getCommunity(communityId);
  const recipients = [];
  if (platform === PLATFORMS.EMAIL) {
    for (let i = 0; i < numberOfRecipients; i++) {
      recipients.push({
        email: `${testEmailPrefix}+${i}@${testEmailDomain}`,
        fullName: `STRESS TEST RECIPIENT ${i}`,
      });
    }
    result = await sendTestEmail({
      community,
      messageId,
      messageData,
      recipients,
      isStressTest: true,
    });
    if (!result) {
      throw new Error('Failed to send stress test email');
    }
  }

  if (platform === PLATFORMS.WHATSAPP) {
    for (let i = 0; i < numberOfRecipients; i++) {
      for (const phoneNumber of testPhoneNumbers) {
        recipients.push({
          phoneNumber,
          fullName: `STRESS TEST RECIPIENT ${i}`,
        });
      }
    }
    result = await sendTestWhatsappInitiateMessage({
      community,
      messageId,
      messageData,
      recipients,
      isStressTest: true,
    });
    if (!result) {
      throw new Error('Failed to send stress test whatsapp 1st message');
    }
  }
};

/**
 * Schedule a Magic Reach message in DB
 * @param {*} param messageData should be valid Mongo model payload
 * @returns {Promise<ObjectId>}
 */
const scheduleMessage = async ({
  communityId,
  messageData,
  messageId,
}) => {
  const { dueAt } = messageData;
  const toDBMessage = messageData;
  toDBMessage.status = MAGIC_REACH_MESSAGE_STATUS.SCHEDULED;
  toDBMessage.isDraft = false;
  toDBMessage.communityId = communityId;
  toDBMessage.schedule = {
    recurring: false,
    dueAt,
  };
  const toSendCounts = await countRecipients({
    communityId,
    selectedBuckets: toDBMessage.selectedBuckets,
    selectedUsers: toDBMessage.selectedUsers,
    unselectedUsers: toDBMessage.unselectedUsers,
    bucketFilters: toDBMessage.bucketFilters,
  });
  toDBMessage.toSendCount = toSendCounts.total;

  if (toDBMessage.toSendCount === 0) {
    throw new ParamError(`No recipients selected for ${messageId}`);
  }

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();
  try {
    const message = new CommunityMagicReachEmailModel(toDBMessage);
    await validateMongoosePayload({
      operation: 'Update Magic Reach Message',
      document: message,
      payload: toDBMessage,
    });
    const updatedMessage =
      await CommunityMagicReachEmailModel.findOneAndUpdate(
        { _id: messageId },
        toDBMessage,
        { upsert: false, new: true },
        { session }
      ).lean();

    if (!updatedMessage) {
      throw new Error(
        `Update Magic Reach message in DB returned undefined. Most likely Magic Reach message [${messageId}] doesn't exist`
      );
    }
    const emailSchedule = await EmailScheduledJob.findOne({
      entityObjectId: messageId,
    }).lean();

    if (emailSchedule) {
      // update the email schedule
      await updateJob({
        filter: {
          _id: emailSchedule._id,
        },
        set: {
          dueAt,
          entityObjectId: messageId,
          entityParentObjectId: null,
          'workerDetails.bodyParam': messageData,
        },
        options: { session },
      });
    } else {
      // add the email schedule
      await submitJob(
        {
          dueAt,
          useCase: 'magic_reach_schedule',
          workerType: WORKER_TYPES.CUSTOM_API,
          workerDetails: {
            path: `${LEARN_BACKEND_URL}api/v1/communities/${communityId}/magic-reach/message/${messageId}/send-scheduled`,
            headers: {
              'api-key': `${ROUTE_API_KEY}`,
            },
            bodyParam: messageData,
          },
          entityObjectId: messageId,
          entityParentObjectId: null,
          resultType: RESULT_TYPE.SINGLE,
        },
        { session }
      );
    }
    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    logger.error(`scheduleMessage error|error=${error}`);
    throw error;
  } finally {
    await session.endSession();
  }
};

module.exports = {
  searchForAndCreateRecipientsAndSend,
  processSendingMessage,
  sendMessage,
  sendTestMessage,
  stressTestMessage,
  sendWhatsappReplyMessages,
  scheduleMessage,
};
