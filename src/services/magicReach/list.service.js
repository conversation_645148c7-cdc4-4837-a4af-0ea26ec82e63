const aws = require('aws-sdk');
const mongoose = require('mongoose');
const ObjectId = require('mongoose').Types.ObjectId;

const Community = require('../../communitiesAPI/models/community.model');
const CommunityMagicReachEmail = require('../../models/magicReach/communityMagicReachEmail.model');
const CommunityMagicReachEmailRecipients = require('../../models/magicReach/communityMagicReachEmailRecipients.model');
const CommunityMagicReachWhatsappRecipients = require('../../models/magicReach/communityMagicReachWhatsappRecipients.model');
const EnrichedLeadModel = require('../../models/magicLeads/enrichedLeads.model');
const User = require('../../models/users.model');

const { transformPaginatedParam } = require('../../utils/pagination.util');

const productService = require('../common/communityProducts.service');
const logger = require('../logger.service');

const { PURCHASE_TYPE } = require('../../constants/common');
const {
  ENTITY_TYPE: PLAN_TYPE,
} = require('@/src/services/plan/constants');
const {
  MAGIC_REACH_MESSAGE_STATUS,
  PLATFORMS,
  EMAIL_RECIPIENT_FILTER,
  WHATSAPP_RECIPIENT_FILTER,
  EARNING_RECIPIENT_FILTER,
  CLICK_RECIPIENT_FILTER,
  BUCKET_NAMES,
} = require('./constants');

const {
  storageBucketName,
  S3_RECIPIENT_BASE_PATH,
  awsRegion,
  awsSecretKey,
  awsAccessKey,
} = require('../../config');

/**
 * Get match stage mongo filtering based on query params
 * @param {*} param0
 */
async function getMessageFilter({
  communityId,
  messageStatus,
  fraudStatus,
  communitySearchString,
  messageSearchString,
  isOutreachEmail = false,
  communityCode,
  creatorEmail,
  recipientEmail,
  planType,
  messageObjectId,
  reviewStatus,
  sentDateFrom,
  sentDateTo,
  createdAtDateFrom,
  createdAtDateTo,
  reviewedDateFrom,
  reviewedDateTo,
}) {
  const messageFilter = {};

  if (messageObjectId) {
    messageFilter._id = new ObjectId(messageObjectId);
  }

  if (sentDateFrom || sentDateTo) {
    messageFilter.sentOn = {};
    if (sentDateFrom) {
      messageFilter.sentOn.$gte = new Date(sentDateFrom);
    }
    if (sentDateTo) {
      messageFilter.sentOn.$lte = new Date(sentDateTo);
    }
  }

  if (createdAtDateFrom || createdAtDateTo) {
    messageFilter.createdAt = {};
    if (createdAtDateFrom) {
      messageFilter.createdAt.$gte = new Date(createdAtDateFrom);
    }
    if (createdAtDateTo) {
      messageFilter.createdAt.$lte = new Date(createdAtDateTo);
    }
  }

  if (reviewedDateFrom || reviewedDateTo) {
    messageFilter['fraudInfo.reviewedDate'] = {};
    if (reviewedDateFrom) {
      messageFilter['fraudInfo.reviewedDate'].$gte = new Date(
        reviewedDateFrom
      );
    }

    if (reviewedDateTo) {
      messageFilter['fraudInfo.reviewedDate'].$lte = new Date(
        reviewedDateTo
      );
    }
  }

  if (isOutreachEmail) {
    messageFilter.selectedBuckets = BUCKET_NAMES.MAGIC_LEADS_OUTREACH;
    messageFilter.status = {
      $in: [
        MAGIC_REACH_MESSAGE_STATUS.SENT,
        MAGIC_REACH_MESSAGE_STATUS.REVIEWING,
        MAGIC_REACH_MESSAGE_STATUS.BLOCKED,
      ],
    };
  } else {
    messageFilter.selectedBuckets = {
      $ne: BUCKET_NAMES.MAGIC_LEADS_OUTREACH,
    };
  }

  let communityFilter;
  if (communityId) {
    messageFilter.communityId = new ObjectId(communityId);
  } else if (communitySearchString) {
    const communityRegex = {
      $regex: new RegExp(`${communitySearchString}`, 'i'),
    };
    communityFilter = {
      $or: [
        {
          code: communityRegex,
        },
        {
          name: communityRegex,
        },
      ],
    };
    const communities = await Community.find(communityFilter, { _id: 1 });
    if (communities?.length > 0) {
      messageFilter.communityId = {
        $in: communities.map((community) => community._id),
      };
    } else {
      return null;
    }
  }

  // For outreach email filter
  if (communityCode || creatorEmail || planType) {
    const matchFilter = {};

    if (communityCode) {
      matchFilter.code = communityCode;
    }

    if (creatorEmail) {
      matchFilter.createdBy = creatorEmail;
    }

    if ([PLAN_TYPE.PRO, PLAN_TYPE.PLATINUM].includes(planType)) {
      matchFilter['config.planType'] = planType;
    } else if (planType) {
      matchFilter['config.planType'] = { $exists: false };
    }

    const communities = await Community.find(matchFilter, {
      _id: 1,
    }).lean();

    if (communities.length > 0) {
      messageFilter.communityId = {
        $in: communities.map((community) => community._id),
      };
    }
  }

  if (recipientEmail) {
    const recipients = await CommunityMagicReachEmailRecipients.find(
      {
        email: recipientEmail,
      },
      { draftId: 1 }
    ).lean();

    if (recipients.length > 0) {
      messageFilter._id = {
        $in: recipients.map((recipient) => recipient.draftId),
      };
    }

    if (isOutreachEmail) {
      const enrichedLeads = await EnrichedLeadModel.find(
        {
          email: recipientEmail,
        },
        { _id: 1 }
      ).lean();

      if (enrichedLeads.length > 0) {
        messageFilter[
          `bucketFilters.${BUCKET_NAMES.MAGIC_LEADS_OUTREACH}.leadObjectId`
        ] = {
          $in: enrichedLeads.map((enrichedLead) => enrichedLead._id),
        };
      }
    }
  }

  if (messageStatus) {
    if (messageStatus === MAGIC_REACH_MESSAGE_STATUS.SENT) {
      messageFilter.isDraft = false;
      messageFilter.status = {
        $nin: [
          MAGIC_REACH_MESSAGE_STATUS.BLOCKED,
          MAGIC_REACH_MESSAGE_STATUS.REVIEWING,
          MAGIC_REACH_MESSAGE_STATUS.PROCESSING,
        ],
      };
    } else if (messageStatus === MAGIC_REACH_MESSAGE_STATUS.DRAFT) {
      messageFilter.isDraft = true;
    } else {
      messageFilter.status = messageStatus;
    }
  } else if (!isOutreachEmail) {
    messageFilter.status = {
      $ne: MAGIC_REACH_MESSAGE_STATUS.DRAFT,
    };
  }

  if (fraudStatus) {
    messageFilter['fraudInfo.status'] = fraudStatus;
  }

  if (reviewStatus) {
    messageFilter['fraudInfo.reviewStatus'] = reviewStatus;
  }

  if (messageSearchString) {
    const messageRegex = {
      $regex: new RegExp(`${messageSearchString}`, 'i'),
    };
    messageFilter.title = messageRegex;
  }

  return messageFilter;
}

/**
 * Get mongo pipeline for messages
 * @param {*} param0
 * @returns
 */
function getMessagePipeline({ messageFilter, project, skip, limit }) {
  const pipeline = [{ $match: messageFilter }];
  pipeline.push({ $sort: { createdAt: -1 } });
  pipeline.push({
    $project: project,
  });
  pipeline.push({
    $facet: {
      meta: [{ $count: 'total' }],
      messages: [{ $skip: skip }, { $limit: limit }],
    },
  });
  pipeline.push({ $unwind: '$meta' });
  return pipeline;
}

/**
 * Get details of the paginated messages to return
 * @param {*} messages
 */
// eslint-disable-next-line no-unused-vars
async function getMessageDetails(messages) {
  const userIds = messages.map((message) => message.author);
  const usersWithLearners = await User.aggregate([
    { $match: { _id: { $in: userIds } } },
    {
      $lookup: {
        from: 'learners',
        localField: 'learner',
        foreignField: '_id',
        as: 'learner',
      },
    },
    {
      $unwind: {
        path: '$learner',
        preserveNullAndEmptyArrays: false,
      },
    },
  ]);
  const usersWithLearnersMap = usersWithLearners.reduce((acc, entry) => {
    acc[entry._id] = entry;
    return acc;
  }, {});

  for await (const message of messages) {
    try {
      if (message.author in usersWithLearnersMap) {
        message.authorUserDetails = usersWithLearnersMap[message.author];
      }
    } catch (error) {
      logger.error(
        `Error in getting magic reach message details`,
        `messageId=${message._id}|`,
        `error=${error}|`,
        `trace=${error.stack}`
      );
    }
  }
  return messages;
}

/**
 * Return magic reach message for CMP portal
 * @param {*} param
 */
const getMessages = async ({
  communityId,
  messageStatus,
  messageSearchString,
  pageNo,
  pageSize,
}) => {
  const messageFilter = await getMessageFilter({
    communityId,
    messageStatus,
    messageSearchString,
  });
  if (messageFilter === null) {
    return {
      messages: [],
      meta: {
        total: 0,
      },
    };
  }

  const { skip, limit } = transformPaginatedParam(pageNo, pageSize);

  const [messages, count] = await Promise.all([
    CommunityMagicReachEmail.find(messageFilter, {
      sentPlatforms: 1,
      status: 1,
      title: 1,
      author: 1,
      selectedUsers: 1,
      selectedBuckets: 1,
      unselectedUsers: 1,
      sentOn: 1,
      communityId: 1,
      analyticsData: 1,
      createdAt: 1,
    })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit),
    CommunityMagicReachEmail.countDocuments(messageFilter),
  ]);

  const results = {
    messages,
    meta: {
      total: count,
      page: pageNo,
      pages: Math.ceil(count / pageSize),
    },
  };
  return results;
};

function getShardModelByName(shardName) {
  return mongoose.model(
    shardName,
    CommunityMagicReachEmailRecipients.schema,
    shardName
  );
}

/**
 * Sort by isSent -> isOpened -> isClicked if status is not isBounced
 */
async function getEmailRecipients({
  message,
  requestQueryParams,
  pageNo,
  pageSize,
  shardName,
}) {
  const recipientFilter = {
    draftId: message._id,
  };

  const statusFilterObject = {};
  for (const filter of EMAIL_RECIPIENT_FILTER) {
    if (filter in requestQueryParams) {
      statusFilterObject[filter] = requestQueryParams[filter];
    }
  }

  if (Object.keys(statusFilterObject).length) {
    for (const key of Object.keys(statusFilterObject)) {
      if (statusFilterObject[key] === 'true') {
        recipientFilter[key] = { $in: [true, 'true'] };
      } else {
        recipientFilter[key] = { $exists: false };
      }
    }
  }

  for (const filter of CLICK_RECIPIENT_FILTER) {
    if (filter in requestQueryParams) {
      recipientFilter[`clickInfo.${filter}`] = new ObjectId(
        requestQueryParams[filter]
      );
    }
  }

  for (const filter of EARNING_RECIPIENT_FILTER) {
    if (
      filter in requestQueryParams &&
      (requestQueryParams[filter] === 'true' ||
        requestQueryParams[filter] === true)
    ) {
      recipientFilter[`clickInfo.paidInfo.revenueTransactionObjectId`] = {
        $exists: true,
      };
    }
  }

  const { skip, limit } = transformPaginatedParam(pageNo, pageSize);

  const pipeline = [
    {
      $match: recipientFilter,
    },
    {
      $sort: {
        isClicked: -1,
        isOpened: -1,
        isDelivered: -1,
        email: -1,
      },
    },
    {
      $project: {
        email: 1,
        profileImage: 1,
        fullName: 1,
        clickInfo: 1,
        isClicked: 1,
        isDelivered: 1,
        isOpened: 1,
        isBounced: 1,
      },
    },
    {
      $facet: {
        meta: [{ $count: 'total' }],
        recipients: [{ $skip: skip }, { $limit: limit }],
      },
    },
    { $unwind: '$meta' },
  ];
  let recipientModel = CommunityMagicReachEmailRecipients;
  if (message.shardRecipientsResult?.shardModel) {
    recipientModel = getShardModelByName(
      message.shardRecipientsResult?.shardModel
    );
  }
  const data = await recipientModel.aggregate(pipeline);

  const results = { recipients: [], meta: {} };
  if (data?.[0]?.recipients?.length) {
    results.recipients = data[0].recipients;
  }

  if (requestQueryParams.linkObjectId) {
    results.recipients = results.recipients.map((recipient) => {
      const final = recipient;
      const clickInfo = recipient.clickInfo ?? [];
      final.clickInfo = clickInfo.filter(
        (info) =>
          info.linkObjectId.toString() === requestQueryParams.linkObjectId
      );
      return final;
    });
  }

  if (data?.[0]?.meta?.total) {
    results.meta.total = data[0].meta.total;
    results.meta.page = pageNo;
    results.meta.pages = Math.ceil(data[0].meta.total / pageSize);
  }
  return results;
}

/**
 * Sort by isSent -> isDelivered -> isClicked if status is not isBounced
 */
async function getWhatsappRecipients({
  message,
  requestQueryParams,
  messageType,
  pageNo,
  pageSize,
}) {
  const recipientFilter = {
    magicReachMessageId: message._id,
  };

  const statusFilterObject = {};
  for (const filter of WHATSAPP_RECIPIENT_FILTER) {
    if (filter in requestQueryParams) {
      statusFilterObject[filter] = requestQueryParams[filter];
    }
  }

  if (Object.keys(statusFilterObject).length) {
    for (const key of Object.keys(statusFilterObject)) {
      if (statusFilterObject[key] === 'true') {
        recipientFilter[`${messageType}.${key}`] = { $in: [true, 'true'] };
      } else {
        recipientFilter[`${messageType}.${key}`] = { $exists: false };
      }
    }
  }

  const { skip, limit } = transformPaginatedParam(pageNo, pageSize);

  const pipeline = [
    {
      $match: recipientFilter,
    },
    {
      $sort: {
        [`${messageType}.isSent`]: -1,
        [`${messageType}.isDelivered`]: -1,
        [`${messageType}.isRead`]: -1,
      },
    },
    {
      $facet: {
        meta: [{ $count: 'total' }],
        recipients: [{ $skip: skip }, { $limit: limit }],
      },
    },
    { $unwind: '$meta' },
  ];
  const data = await CommunityMagicReachWhatsappRecipients.aggregate(
    pipeline
  );

  const results = { recipients: [], meta: {} };
  if (data?.[0]?.recipients?.length) {
    results.recipients = data[0].recipients;
  }
  if (data?.[0]?.meta?.total) {
    results.meta.total = data[0].meta.total;
    results.meta.page = pageNo;
    results.meta.pages = Math.ceil(data[0].meta.total / pageSize);
  }
  return results;
}

/**
 * Return the lists of links that has been clicked or has earnings
 * @param {*} param0
 * @returns
 */
async function getLinks({
  magicReachEmail,
  hasPaid,
  platform,
  pageNo,
  pageSize,
}) {
  const { skip, limit } = transformPaginatedParam(pageNo, pageSize);

  let links = [];
  switch (platform.toLowerCase()) {
    case PLATFORMS.EMAIL.toLowerCase():
    case PLATFORMS.WHATSAPP.toLowerCase(): {
      links = magicReachEmail.analyticsData?.[platform]?.links ?? [];
      break;
    }
    default:
      break;
  }

  if (hasPaid) {
    links = links.filter((link) => link.salesCount > 0);
  }

  // eslint-disable-next-line no-unused-vars
  links = links.sort((a, b) => {
    if (!a.productInfo?.productObjectId) return 1;
    return -1;
  });

  const results = {
    links: [],
    meta: {
      total: links.length,
      page: pageNo,
      pages: Math.ceil(links.length / pageSize),
    },
  };
  const end =
    skip + limit > results.meta.total ? results.meta.total : skip + limit;

  const finalLinks = links.slice(skip, end);
  const subscriptionObjectIds = [];
  const eventObjectIds = [];
  const productObjectIds = [];
  const programObjectIds = [];

  finalLinks.forEach((link) => {
    const product = link.productInfo;
    switch (product?.type) {
      case PURCHASE_TYPE.SUBSCRIPTION:
        subscriptionObjectIds.push(product.productObjectId);
        break;
      case PURCHASE_TYPE.CHALLENGE:
        programObjectIds.push(product.productObjectId);
        break;
      case PURCHASE_TYPE.EVENT:
        eventObjectIds.push(product.productObjectId);
        break;
      case PURCHASE_TYPE.SESSION:
      case PURCHASE_TYPE.FOLDER:
        productObjectIds.push(product.productObjectId);
        break;
      default:
        break;
    }
  });

  const entitiesCache = await productService.retrieveEntitiesCache({
    subscriptionObjectIds,
    eventObjectIds,
    productObjectIds,
    programObjectIds,
    source: productService.SOURCE_TYPES.MAGIC_REACH,
  });

  results.links = finalLinks.map((link) => {
    let productInfo = link.productInfo ?? {};
    if (productInfo.productObjectId) {
      const key = `${productInfo.type}-${productInfo.productObjectId}`;
      productInfo = entitiesCache.get(key) ?? {};
    }
    return {
      ...link,
      productInfo: {
        ...link.productInfo,
        ...productInfo,
      },
    };
  });

  return results;
}

/**
 * Return the recipient list
 * @param {*} param0
 * @returns
 */
const getRecipients = async ({
  message,
  platform,
  requestQueryParams,
  messageType,
  pageNo,
  pageSize,
}) => {
  switch (platform.toLowerCase()) {
    case PLATFORMS.EMAIL.toLowerCase():
      return getEmailRecipients({
        message,
        requestQueryParams,
        pageNo,
        pageSize,
      });
    case PLATFORMS.WHATSAPP.toLowerCase():
      return getWhatsappRecipients({
        message,
        requestQueryParams,
        pageNo,
        pageSize,
        messageType,
      });
    default:
      return {
        recipients: [],
        meta: {
          total: 0,
        },
      };
  }
};

const getRecipientsCSVStream = async ({ messageId }) => {
  const config = awsSecretKey
    ? {
        secretAccessKey: awsSecretKey,
        accessKeyId: awsAccessKey,
      }
    : { credentialProvider: new aws.CredentialProviderChain() };

  aws.config.update({
    region: awsRegion,
    ...config,
  });
  const s3 = new aws.S3();
  const key = `${S3_RECIPIENT_BASE_PATH}/${messageId}/email.csv`;
  const params = {
    Bucket: storageBucketName,
    Key: key,
  };
  const stream = await s3.getObject(params).createReadStream();

  return stream;
};

module.exports = {
  getMessages,
  getMessageFilter,
  getMessagePipeline,
  getRecipients,
  getRecipientsCSVStream,
  getLinks,
};
