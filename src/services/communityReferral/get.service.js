const ObjectId = require('mongoose').Types.ObjectId;
const { DateTime } = require('luxon');

const CommunityModel = require('../../communitiesAPI/models/community.model');
const CommunityReferralRewardModel = require('../../models/communityReferral/communityReferralReward.model');
const EarningAnalyticsModel = require('../../models/earningAnalytics.model');
const PredictedEarningAnalyticsModel = require('../../models/predictedEarningAnalytics.model');
const RevenueTransactionModel = require('../../models/revenueTransaction.model');

const PaymentBackendRpc = require('../../rpc/paymentBackend');

const planService = require('../plan/plan.service');
const planPricingService = require('../plan/pricing.service');
const {
  getCountryInfoByCountryFromDB,
} = require('../countryInfoMapping/countryInfoMapping.service');

const { generateUniqueCode } = require('../../utils/codeGeneration.util');
const { ParamError, InternalError } = require('../../utils/error.util');

const {
  DEFAULT_COUNTRY,
  DEFAULT_CURRENCY,
  TRANSACTION_TYPE,
  PURCHASE_TYPE,
} = require('../../constants/common');
const {
  COMMUNITY_REFERRAL_REWARD_PLAN_TYPE,
} = require('../communityReferralRewardTemplate/constants');
const currencyUtils = require('../../utils/currency.util');

function sortBasedOnInterval(inputArray, sortOrder = 1) {
  const formattedPrice = inputArray.map((price) => {
    let intervalValue = price.intervalCount;
    switch (price.interval) {
      case 'year':
        intervalValue *= 365;
        break;
      case 'month':
        intervalValue *= 30;
        break;
      case 'week':
        intervalValue *= 14;
        break;
      case 'day':
      default:
        break;
    }
    return { ...price, intervalValue };
  });

  if (sortOrder <= -1) {
    return formattedPrice.sort(
      (a, b) => b.intervalValue - a.intervalValue
    );
  }
  return formattedPrice.sort((a, b) => a.intervalValue - b.intervalValue);
}

function formatRewardIntervalBasedOnCurrency(
  rewardIntervalMap = new Map(),
  recurringReward = {},
  fieldName,
  baseCurrency = DEFAULT_CURRENCY
) {
  const recurringRewardKeys = Object.keys(recurringReward);

  recurringRewardKeys.forEach((key) => {
    const defaultReward = recurringReward[key].find(
      (currency) => currency.currency === DEFAULT_CURRENCY
    );
    const reward = recurringReward[key].find(
      (currency) => currency.currency === baseCurrency
    );

    const rewardInterval = rewardIntervalMap.get(key);
    const finalRewardInterval = {
      ...rewardInterval,
      [fieldName]: {
        currency: reward ? reward.currency : defaultReward.currency,
        amount: reward ? reward.amount : defaultReward.amount,
        amountInUsd: defaultReward.amount,
      },
    };
    rewardIntervalMap.set(key, finalRewardInterval);
  });
}

function formatRewardsBasedOnTemplate(template, currency) {
  const rewardIntervalMap = new Map();
  const {
    refereeRecurringReward = {},
    referrerRecurringReward = {},
    referrerUpfrontReward = {},
  } = template;
  formatRewardIntervalBasedOnCurrency(
    rewardIntervalMap,
    refereeRecurringReward,
    'refereeRecurringReward',
    currency
  );
  formatRewardIntervalBasedOnCurrency(
    rewardIntervalMap,
    referrerRecurringReward,
    'referrerRecurringReward',
    currency
  );
  formatRewardIntervalBasedOnCurrency(
    rewardIntervalMap,
    referrerUpfrontReward,
    'referrerUpfrontReward',
    currency
  );

  let useDefaultCurrency = false;
  rewardIntervalMap.forEach((value) => {
    if (
      value.refereeRecurringReward.currency !== currency ||
      value.referrerRecurringReward.currency !== currency ||
      value.referrerUpfrontReward.currency !== currency
    ) {
      useDefaultCurrency = true;
    }
  });
  const rewards = [];
  rewardIntervalMap.forEach((value, key) => {
    const [interval, intervalCount] = key.split('-');
    rewards.push({
      currency: useDefaultCurrency ? DEFAULT_CURRENCY : currency,
      interval,
      intervalCount,
      refereeRecurringReward: {
        amount: useDefaultCurrency
          ? value.refereeRecurringReward.amountInUsd
          : value.refereeRecurringReward.amount,
        currency: useDefaultCurrency
          ? DEFAULT_CURRENCY
          : value.refereeRecurringReward.currency,
      },
      referrerRecurringReward: {
        amount: useDefaultCurrency
          ? value.referrerRecurringReward.amountInUsd
          : value.referrerRecurringReward.amount,
        currency: useDefaultCurrency
          ? DEFAULT_CURRENCY
          : value.referrerRecurringReward.currency,
      },
      referrerUpfrontReward: {
        amount: useDefaultCurrency
          ? value.referrerUpfrontReward.amountInUsd
          : value.referrerUpfrontReward.amount,
        currency: useDefaultCurrency
          ? DEFAULT_CURRENCY
          : value.referrerUpfrontReward.currency,
      },
    });
  });
  const sortedRewards = sortBasedOnInterval(rewards, 1);
  return sortedRewards;
}

async function retrievePlanRecurringPrices({
  plan,
  countryInfo = null,
  community = null,
  paymentBackendRpc,
}) {
  const nextBillingPrice = await planService.retrieveNextBillingPlan({
    plan,
  });

  const prices = await planPricingService.retrievePrices({
    plan: nextBillingPrice ?? plan,
    countryInfo,
    community,
    paymentBackendRpc,
  });
  return prices;
}

async function retrieveReferralRewards(community, planType) {
  const paymentBackendRpc = new PaymentBackendRpc();

  const [defaultPlan, { referralRewardTemplate }] = await Promise.all([
    planService.retrieveDefaultPlan({
      entityType: planType,
    }),
    planService.retrieveCommunityReferralDetails({
      communityReferralCode: community.communityReferralCode,
      referrerCommunity: community,
      planType,
    }),
    paymentBackendRpc.init(),
  ]);

  const formattedRewards = formatRewardsBasedOnTemplate(
    referralRewardTemplate,
    community.baseCurrency
  );

  const defaultPriceMap = new Map();
  let defaultPrices = [];
  if (formattedRewards?.[0]?.currency === community.baseCurrency) {
    defaultPrices = await retrievePlanRecurringPrices({
      plan: defaultPlan,
      paymentBackendRpc,
      community,
    });
    defaultPrices.forEach((price) => {
      const { interval, intervalCount } = price;
      defaultPriceMap.set(`${interval}-${intervalCount}`, price);
    });
  } else {
    const countryInfo = await getCountryInfoByCountryFromDB(
      DEFAULT_COUNTRY
    );
    defaultPrices = await retrievePlanRecurringPrices({
      plan: defaultPlan,
      paymentBackendRpc,
      countryInfo,
    });
  }
  defaultPrices.forEach((price) => {
    const { interval, intervalCount } = price;
    defaultPriceMap.set(`${interval}-${intervalCount}`, price);
  });
  const final = formattedRewards.map((reward) => {
    const {
      interval,
      intervalCount,
      refereeRecurringReward,
      referrerRecurringReward,
    } = reward;
    const intervalKey = `${interval}-${intervalCount}`;
    const defaultPrice = defaultPriceMap.get(intervalKey);
    if (!defaultPrice) {
      throw new InternalError(`Missing default price for ${intervalKey}`);
    }
    return {
      interval,
      intervalCount,
      planType,
      referrer: {
        currency: referrerRecurringReward.currency,
        earningAmount: referrerRecurringReward.amount,
      },
      referee: {
        currency: refereeRecurringReward.currency,
        savingAmount:
          defaultPrice.checkoutAmount - refereeRecurringReward.amount,
        originalAmount: defaultPrice.checkoutAmount,
        checkoutAmount: refereeRecurringReward.amount,
      },
    };
  });

  return final;
}

async function retrieveReferrerTotalEarnings(community) {
  const rewards = await CommunityReferralRewardModel.find(
    {
      referrerCommunityObjectId: community._id,
    },
    {
      earningAnalytics: 1,
      currency: 1,
    }
  ).lean();

  let totalEarnings = 0;
  let totalQuantity = 0;

  if (!rewards.length) {
    return {
      currency: community.baseCurrency,
      amount: totalEarnings,
      quantity: totalQuantity,
    };
  }

  rewards.forEach((reward) => {
    totalEarnings += reward.earningAnalytics.revenueInLocalCurrency;
    totalQuantity += reward.earningAnalytics.quantity;
  });

  return {
    currency: rewards[0].currency,
    amount: totalEarnings,
    quantity: totalQuantity,
  };
}

async function retrieveReferrerPredictedEarnings(community) {
  const currentDateInUtc = DateTime.utc();
  const startOfMonth = currentDateInUtc.startOf('month');
  const endOfMonth = currentDateInUtc.endOf('month');
  const dayAfterEndOfMonth = endOfMonth.plus({ days: 1 }).startOf('day');

  const paymentBackendRpc = new PaymentBackendRpc();

  const [
    earningAnalytics,
    predictedEarningAnalytics,
    referralRewardReversalTransactions,
  ] = await Promise.all([
    EarningAnalyticsModel.find(
      {
        communityObjectId: community._id,
        date: { $gte: startOfMonth, $lte: currentDateInUtc },
      },
      {
        referralRewards: 1,
      }
    ).lean(),
    PredictedEarningAnalyticsModel.find(
      {
        communityObjectId: community._id,
        date: { $gt: currentDateInUtc, $lt: dayAfterEndOfMonth },
      },
      {
        referralRewards: 1,
      }
    ).lean(),
    RevenueTransactionModel.find(
      {
        communityObjectId: community._id,
        transactionCreatedAt: {
          $gte: startOfMonth,
          $lte: currentDateInUtc,
        },
        transactionType: TRANSACTION_TYPE.OUTBOUND,
        purchaseType: PURCHASE_TYPE.REFERRAL_REWARD,
      },
      {
        amountBreakdownInLocalCurrency: 1,
      }
    ).lean(),
    paymentBackendRpc.init(),
  ]);

  if (
    !earningAnalytics.length &&
    !predictedEarningAnalytics.length &&
    !referralRewardReversalTransactions.length
  ) {
    return {
      currency: community.baseCurrency,
      amount: 0,
      date: currentDateInUtc,
    };
  }

  let totalEarnings = 0;
  let localCurrency = community.baseCurrency;

  earningAnalytics.forEach((analytic) => {
    const referralRewards = analytic.referralRewards ?? [];
    referralRewards.forEach((referral) => {
      totalEarnings += referral.revenueInLocalCurrency;
      localCurrency = referral.localCurrency;
    });
  });

  predictedEarningAnalytics.forEach((analytic) => {
    const referralRewards = analytic.referralRewards ?? [];
    referralRewards.forEach((referral) => {
      totalEarnings += referral.revenueInLocalCurrency;
      localCurrency = referral.localCurrency;
    });
  });

  const conversionRateCache = new Map();

  await Promise.all(
    referralRewardReversalTransactions.map(async (transaction) => {
      const { amountBreakdownInLocalCurrency } = transaction;

      let conversionRate = 1;

      const isDifferentCurrency =
        amountBreakdownInLocalCurrency.currency !== community.baseCurrency;

      if (isDifferentCurrency) {
        if (
          conversionRateCache.has(amountBreakdownInLocalCurrency.currency)
        ) {
          conversionRate = conversionRateCache.get(
            amountBreakdownInLocalCurrency.currency
          );
        } else {
          const conversionRateDataFromLocalCurrencyToBaseCurrency =
            await paymentBackendRpc.getConversionRate(
              amountBreakdownInLocalCurrency.currency,
              community.baseCurrency
            );

          conversionRate =
            conversionRateDataFromLocalCurrencyToBaseCurrency.conversionRate;

          conversionRateCache.set(
            amountBreakdownInLocalCurrency.currency,
            conversionRate
          );
        }
      }

      totalEarnings -= currencyUtils.normalizeAndRoundAmountByCurrency(
        amountBreakdownInLocalCurrency.netAmount * conversionRate,
        community.baseCurrency
      );
    })
  );

  return {
    currency: localCurrency,
    amount: totalEarnings,
    date: currentDateInUtc,
  };
}

exports.retrieveReferrerRewards = async ({
  communityReferralCode,
  planType,
  refereeCommunityObjectId,
}) => {
  const referralDetails =
    await planService.retrieveCommunityReferralDetails({
      communityReferralCode,
      planType,
      refereeCommunityObjectId,
    });

  const {
    referrerCommunity,
    plan: referralPlan,
    referralRewardTemplate,
    defaultReferralRewardTemplate,
  } = referralDetails;

  if (!referralPlan || !referralRewardTemplate) {
    return null;
  }

  return {
    referrerCommunityObjectId: referrerCommunity._id,
    referrerRewardTemplateObjectId: referralRewardTemplate._id,
    planType: referralPlan.entityType,
    referrerRecurringReward:
      referralRewardTemplate.referrerRecurringReward,
    referrerUpfrontReward: referralRewardTemplate.referrerUpfrontReward,
    defaultReferrerRecurringReward:
      defaultReferralRewardTemplate.referrerRecurringReward,
    defaultReferrerUpfrontReward:
      defaultReferralRewardTemplate.referrerUpfrontReward,
  };
};

exports.getReferralCodeDetails = async (
  communityReferralCode,
  planType,
  communityObjectId = null,
  country = DEFAULT_COUNTRY
) => {
  const paymentBackendRpc = new PaymentBackendRpc();

  const [countryInfo, referralDetails, defaultPlan] = await Promise.all([
    getCountryInfoByCountryFromDB(country),
    planService.retrieveCommunityReferralDetails({
      communityReferralCode,
      planType,
      refereeCommunityObjectId: communityObjectId,
    }),
    planService.retrieveDefaultPlan({
      entityType: planType,
    }),
    paymentBackendRpc.init(),
  ]);

  const { referrerCommunity, plan: referralPlan } = referralDetails;
  if (!referralPlan) {
    throw new ParamError(
      `Referral code ${communityReferralCode} can't be applied`
    );
  }

  const referralPrices = await retrievePlanRecurringPrices({
    plan: referralPlan,
    countryInfo,
    paymentBackendRpc,
  });

  const finalReferralCurrency =
    referralPrices?.[0]?.currency?.toUpperCase();
  let defaultPriceCountryInfo = countryInfo;
  if (finalReferralCurrency !== countryInfo?.currencyCode?.toUpperCase()) {
    if (finalReferralCurrency === DEFAULT_CURRENCY) {
      defaultPriceCountryInfo = await getCountryInfoByCountryFromDB(
        DEFAULT_COUNTRY
      );
    }
    throw new ParamError(
      `Referral price does not have price in  ${countryInfo.currency} and ${DEFAULT_CURRENCY}`
    );
  }
  const defaultPrices = await retrievePlanRecurringPrices({
    plan: defaultPlan,
    countryInfo: defaultPriceCountryInfo,
    paymentBackendRpc,
  });

  const referralPrice = sortBasedOnInterval(referralPrices, -1)?.[0];

  const defaultPrice = defaultPrices.find(
    (price) =>
      price.interval === referralPrice.interval &&
      price.intervalCount === referralPrice.intervalCount
  );

  const savingAmount =
    defaultPrice.checkoutAmount - referralPrice.checkoutAmount;

  return {
    referrer: {
      title: referrerCommunity.title,
      link: referrerCommunity.link,
      profileImage:
        referrerCommunity.thumbnailImgData?.desktopImgData?.src,
    },
    savingAmount,
    originalAmount: defaultPrice.checkoutAmount,
    checkoutAmount: referralPrice.checkoutAmount,
    currency: referralPrice.currency,
    interval: referralPrice.interval,
    intervalCount: referralPrice.intervalCount,
  };
};

exports.retrieveOrCreateCommunityReferralCode = async (communityId) => {
  const community = await CommunityModel.findById(
    communityId,

    { _id: 1, title: 1, code: 1, communityReferralCode: 1, config: 1 }
  ).lean();

  if (!community) {
    throw new ParamError('Community not found');
  }
  let finalCommunityReferralCode = community.communityReferralCode;
  let showCommunityReferralLink =
    community.config?.showCommunityReferralLink;
  if (!finalCommunityReferralCode) {
    finalCommunityReferralCode = await generateUniqueCode(
      community.title,
      CommunityModel,
      'communityReferralCode'
    );
    await CommunityModel.updateOne(
      { _id: community._id },
      {
        communityReferralCode: finalCommunityReferralCode,
        communityReferralCodeCreatedAt: new Date(),
        'config.showCommunityReferralLink': true,
      }
    );
    showCommunityReferralLink = true;
  }
  return {
    communityReferralCode: finalCommunityReferralCode,
    showCommunityReferralLink,
  };
};

exports.getReferrerRewardSummary = async (communityId) => {
  const community = await CommunityModel.findOne({
    _id: new ObjectId(communityId),
    isActive: true,
  }).lean();

  if (!community) {
    throw new ParamError('Community not found');
  }

  const [proRewards, platinumRewards, totalEarnings, estimatedEarnings] =
    await Promise.all([
      retrieveReferralRewards(
        community,
        COMMUNITY_REFERRAL_REWARD_PLAN_TYPE.PRO
      ),
      retrieveReferralRewards(
        community,
        COMMUNITY_REFERRAL_REWARD_PLAN_TYPE.PLATINUM
      ),
      retrieveReferrerTotalEarnings(community),
      retrieveReferrerPredictedEarnings(community),
    ]);

  const totalRewards = [...proRewards, ...platinumRewards];

  return {
    rewards: totalRewards,
    totalEarnings,
    estimatedEarnings,
  };
};
