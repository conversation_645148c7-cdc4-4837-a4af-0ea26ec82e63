const { DateTime } = require('luxon');
const { REROUTE_MEMBER_LINK } = require('../../config/index');
const { DEFAULT_IMAGES } = require('./constants');

// constants
const { NAS_IO_FRONTEND_URL } = require('../../config');
const {
  PRICE_TYPE,
  PURCHASE_TYPE,
  EVENT_TYPES,
  SIGNUP_REQUESTOR,
} = require('../../constants/common');
const { PRODUCT_TYPE } = require('@/src/services/product/constants');

const nameUtils = require('../../utils/name.util');

const {
  formatLegacyInPersonLocationToMetadata,
  buildCalendarURL,
} = require('../../utils/event.util');

const { formatTimezone } = require('../../utils/date.util');
const priceUtil = require('../../utils/price.util');

const UTC = 'UTC';

// const getCalendarDateDetails = (input, timezone) => {
//   const date = DateTime.fromISO(input.toISOString(), {
//     zone: timezone ?? UTC,
//   });
//   const year = date.year;
//   const month = date.month;
//   const day = date.day;
//   const hours = date.hour;
//   const minutes = date.minute;

//   return { year, month, day, hours, minutes };
// };

exports.formatICSDateTimeString = (date) => {
  if (!date) {
    return '';
  }
  const dateTime = DateTime.fromISO(date.toISOString(), {
    zone: UTC,
  }).toISO({ format: 'basic' });
  const dateTimeArr = dateTime.split('.');

  return `${dateTimeArr[0]}Z`;
};

exports.getEventTimeInfo = ({ start, end, timezone }) => {
  if (!start || !end) {
    return {};
  }
  const startTimeISOString = start.toISOString();
  const endTimeISOString = end.toISOString();
  const startTime = DateTime.fromISO(startTimeISOString, {
    zone: timezone ?? UTC,
  });
  const endTime = DateTime.fromISO(endTimeISOString, {
    zone: timezone ?? UTC,
  });
  const duration = endTime.diff(startTime, 'minutes');

  let timezoneOffset = '+0000';
  const ratio = Math.abs(startTime.offset) / 60;
  const hours = Math.trunc(ratio);
  const mins = 60 * (ratio - hours);

  if (startTime.offset !== 0) {
    timezoneOffset = `${startTime.offset < 0 ? '-' : '+'}${
      hours < 10 ? '0' : ''
    }${hours}${mins < 10 ? '0' : ''}${mins}`;
  }

  return {
    startTimeISOString,
    endTimeISOString,
    startTime,
    endTime,
    duration,
    timezoneOffset,
  };
};
exports.formatCommunityData = ({ community, emailToken = null }) => {
  const data = {
    community_code: community.code,
    community_name: community.title,
    community_link: `${REROUTE_MEMBER_LINK}?&activeCommunityId=${community._id}&memberExperience=1&accessToken=${emailToken}`,
    community_profile_image:
      community?.thumbnailImgData?.mobileImgData?.src ??
      DEFAULT_IMAGES.COMMUNITY_PROFILE_IMAGE,
  };
  return data;
};

exports.formatEventData = ({
  event,
  community,
  owner,
  emailToken,
  timezone = UTC,
  languagePreference = 'en',
}) => {
  const eventLink = community?.link
    ? `${NAS_IO_FRONTEND_URL}${community?.link}/events${event.slug}`
    : NAS_IO_FRONTEND_URL || '';

  const {
    startTimeISOString,
    endTimeISOString,
    duration,
    timezoneOffset,
  } = this.getEventTimeInfo({
    start: event?.startTime,
    end: event?.endTime,
    timezone,
  });

  const formattedTimezone = formatTimezone(timezone);

  const eventStartDateInTimezone = DateTime.fromISO(startTimeISOString, {
    zone: timezone,
  });

  const eventEndDateInTimezone = DateTime.fromISO(endTimeISOString, {
    zone: timezone,
  });

  // Tue, 20 April
  const eventStartDateTextFormat = eventStartDateInTimezone.toFormat(
    'ccc, dd LLL',
    {
      locale: languagePreference,
    }
  );

  const eventStartTimeInFormat = eventStartDateInTimezone.toFormat(
    'h:mm a',
    {
      locale: languagePreference,
    }
  );

  // 17 May, 10:00 AM

  const eventEndDateTextFormat = eventEndDateInTimezone.toFormat(
    'dd LLL, h:mm a',
    {
      locale: languagePreference,
    }
  );

  const eventEndTimeInFormat = eventEndDateInTimezone.toFormat('h:mm a', {
    locale: languagePreference,
  });

  const addToCalGoogleLink = buildCalendarURL(event, 'google');
  const addToCalOutlookLink = buildCalendarURL(event, 'outlook');
  const addToCalAppleLink = eventLink; // Will need to setup a page on FE to handle this. For now, we can use the event link

  const data = {
    event_image: event?.bannerImg,
    event_name: event?.title,
    manage_event_more_opt_link: `${NAS_IO_FRONTEND_URL}/portal/events/manage/${event?._id}?activeCommunityId=${community?._id}&scrollTo=EVENT_ACCORDION_MORE_OPTS`,
    manage_event_link: `${NAS_IO_FRONTEND_URL}/portal/events/manage/${event?._id}?activeCommunityId=${community?._id}`,
    view_event_link: `${eventLink}?accessToken=${emailToken}`,
    event_link: eventLink,

    event_description: event?.description ?? '',
    event_calendar_link: event?.icsFileLink ?? '',
    event_type: event?.type,
    event_in_person_location: event?.inPersonLocation,
    event_in_person_location_metadata:
      event?.type === EVENT_TYPES.INPERSON
        ? event?.inPersonLocationMetadata ??
          formatLegacyInPersonLocationToMetadata(event?.inPersonLocation)
        : {},
    event_live_link: event?.liveLink,
    event_hide_location: event.hideLocation ?? true,

    event_duration: duration?.minutes,
    event_start_date_iso: startTimeISOString,
    event_end_date_iso: endTimeISOString,
    timezone: formattedTimezone,
    timezoneOffset,

    ics_uid: event?._id,
    ics_sequence: event?._version || 0,
    ics_organizer_name:
      owner?.name ?? community['By'] ?? `Community Manager`,
    ics_organizer_email:
      `${community?.link.replace('/', '')}-<EMAIL>` ??
      `${event?._id}-<EMAIL>`,
    ics_summary: event?.title,
    ics_event_start: this.formatICSDateTimeString(event?.startTime),
    ics_event_end: this.formatICSDateTimeString(event?.endTime),
    ics_description: event?.title,
    ics_alarm_description: event?.title,
    add_to_cal_google_link: addToCalGoogleLink,
    add_to_cal_outlook_link: addToCalOutlookLink,
    add_to_cal_apple_link: addToCalAppleLink,

    event_host_first_name: event?.host?.firstName,
    event_host_last_name: event?.host?.lastName,
    event_host_profile_image:
      event?.host?.profileImage ??
      DEFAULT_IMAGES.COMMUNITY_HOST_PROFILE_IMAGE,
    event_host_description: event?.host?.description ?? 'Community Host',
    eventStartDateTextFormat,
    eventStartTimeInFormat,
    eventEndDateTextFormat,
    eventEndTimeInFormat,
    event_start_date_time: `${eventStartDateTextFormat} ${eventStartTimeInFormat}`,

    product_title: event?.title,
    product_in_person_location: event?.inPersonLocation,
    product_live_link: event?.liveLink,
    product_start_date_iso: startTimeISOString,
    product_end_date_iso: endTimeISOString,
    price: priceUtil.getPriceDisplayForMail(
      event?.amount,
      event?.currency
    ),
  };

  if (event?.type === EVENT_TYPES.LIVE) {
    data.ics_location = event?.liveLink ?? '';
    data.event_location = event?.liveLink ?? '';
    delete data.event_in_person_location;
  } else if (event?.type === EVENT_TYPES.INPERSON) {
    const inPersonLocationStr = event.inPersonLocationMetadata
      ? [
          event.inPersonLocationMetadata.name,
          event.inPersonLocationMetadata.formatted_address ?? '',
        ].join(', ')
      : event.inPersonLocation ?? '';
    data.ics_location = inPersonLocationStr;
    data.event_location = inPersonLocationStr;
    delete data.event_live_link;
  }

  return data;
};

exports.getManagerEventRsvpAdditionalFields = ({
  learner,
  eventAttendeeGoing,
  eventAttendeeNotGoing,
}) => {
  const name = `${learner?.firstName ?? ''} ${
    learner?.lastName ?? ''
  }`.trim();
  return {
    rsvp_attendee_name: name || '',
    rsvp_attendee_email: learner?.email || '',
    no_of_attendee_going: eventAttendeeGoing || 0,
    no_of_attendee_not_going: eventAttendeeNotGoing || 0,
  };
};

exports.getMemberEventRsvpAdditionalFields = ({
  addonTransactionData,
  eventAttendeeObjectId,
  eventAttendeePurchaseType,
  ticketReference,
  ticketReferences = [],
  quantity,
}) => {
  return {
    member_paid_amount: (
      (addonTransactionData?.amount ?? 0) / 100
    ).toFixed(2),
    member_paid_currency: addonTransactionData?.currency ?? '',
    member_paid_local_amount: (
      (addonTransactionData?.local_amount ?? 0) / 100
    ).toFixed(2),
    member_paid_local_currency: addonTransactionData?.local_currency ?? '',
    event_attendee_object_id: eventAttendeeObjectId,
    purchase_type: eventAttendeePurchaseType,
    ticket_reference: ticketReference,
    ticket_references: ticketReferences,
    ticket_meta_data: ticketReferences,
    quantity: addonTransactionData?.quantity ?? quantity,
  };
};

exports.getSampleAttendeeEventRsvpAdditionalFields = ({
  event,
  learner,
}) => {
  const eventAttendeePurchaseType = event.type;
  const sampleTicketReference = 'sample_ticket';
  const sampleTicketReferences = [
    {
      ticketReference: sampleTicketReference,
      qrCodeSrc:
        'https://d2oi1rqwb0pj00.cloudfront.net/nasio/events/tickets/sample/qr-sample_ticket_id-1739269712839.png', // sample v1 qr code
      isCheckedIn: false,
      updatedAt: '2025-02-10T12:54:42.397+00:00',
      updatedByLearnerObjectId: '677e92e32f27fc06b7c7d53d',
    },
  ];

  return {
    event_attendee_profile_image: learner?.profileImage ?? '',
    purchase_type: eventAttendeePurchaseType,
    ticket_reference: sampleTicketReference,
    ticket_references: sampleTicketReferences,
    ticket_meta_data: sampleTicketReferences,
    quantity: 1,
  };
};

function getEntityInfoPrice(entityInfo) {
  const { priceType, priceDetails, entityType } = entityInfo;

  const { amount, currency, interval, intervalCount } = priceDetails;

  const entityPrice =
    priceType === PRICE_TYPE.FLEXIBLE
      ? 'Pay as you wish'
      : `${currency} ${new Intl.NumberFormat('en-US', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }).format(amount / 100)}`;

  const getPlanInterval = {
    'month-1': `${interval}`,
    'month-3': `${intervalCount} ${interval}s`,
    'month-6': `${intervalCount} ${interval}s`,
    'month-12': `year`,
    'year-1': `year`,
  };

  if (entityType === PURCHASE_TYPE.SUBSCRIPTION) {
    const planInterval = getPlanInterval[`${interval}-${intervalCount}`];
    return `${entityPrice}/${planInterval}`;
  }

  return entityPrice;
}

function getEntityInfoTime(entityInfo, timezone) {
  const {
    startTime,
    endTime,
    entityType,
    durationIntervalInMinutes,
    locationType,
    hostLearner,
  } = entityInfo;

  const startDate = DateTime.fromJSDate(startTime ?? new Date()).setZone(
    timezone
  );
  const endDate = DateTime.fromJSDate(endTime ?? new Date()).setZone(
    timezone
  );

  const timezoneAbbreviation = startDate.toFormat('ZZZZ');

  let entityTime;

  switch (entityType) {
    case PURCHASE_TYPE.EVENT:
      entityTime = `${startDate.toFormat(
        'dd LLL, hh:mm a'
      )} - ${endDate.toFormat(
        'dd LLL, hh:mm a'
      )} ${timezoneAbbreviation} ${
        locationType === EVENT_TYPES.LIVE ? 'Online' : 'In-person'
      }`;
      break;
    case PURCHASE_TYPE.SESSION:
      entityTime = `${durationIntervalInMinutes}min with ${nameUtils.getName(
        hostLearner?.firstName,
        hostLearner?.lastName
      )}`;
      break;
    case PURCHASE_TYPE.CHALLENGE:
      entityTime = `${startDate.toFormat('dd LLL')} - ${endDate.toFormat(
        'dd LLL'
      )} ${timezoneAbbreviation}`;
      break;
    default:
      break;
  }

  return entityTime;
}

function generateAdditionalParamsForCheckout(additionalParams) {
  if (!additionalParams || Object.keys(additionalParams).length === 0) {
    return '';
  }

  return `&${Object.entries(additionalParams)
    .map(([key, value]) => `${key}=${value}`)
    .join('&')}`;
}

function generateEntityInfoCheckoutLink(
  entityInfo,
  discount,
  additionalParams,
  requestor = SIGNUP_REQUESTOR.SIGNUP
) {
  const {
    communityObjectId,
    communityCode,
    communityLink,
    slug,
    entityType,
    entityObjectId,
  } = entityInfo;

  const additionalParameters =
    generateAdditionalParamsForCheckout(additionalParams);

  const baseCheckoutLink = `${NAS_IO_FRONTEND_URL}/checkout-global?communityId=${communityObjectId}&communityCode=${communityCode}&requestor=${requestor}${additionalParameters}`;

  const generateEntityCheckoutLink = (type) => {
    const discountCodeParam = discount?.code
      ? `&entityDiscountCode=${discount.code}`
      : '';

    return `${baseCheckoutLink}&sourceInfoType=${type}&sourceInfoOrigin=${entityObjectId}${discountCodeParam}`;
  };

  const checkoutLinkGenerators = {
    [PURCHASE_TYPE.SUBSCRIPTION]: () =>
      discount?.code
        ? `${baseCheckoutLink}?discountCode=${discount.code}`
        : baseCheckoutLink,
    [PURCHASE_TYPE.EVENT]: () => generateEntityCheckoutLink('event'),
    [PURCHASE_TYPE.FOLDER]: () => generateEntityCheckoutLink('folder'),
    [PRODUCT_TYPE.COURSE]: () => generateEntityCheckoutLink('folder'),
    [PRODUCT_TYPE.DIGITAL_FILES]: () =>
      generateEntityCheckoutLink('folder'),
    [PURCHASE_TYPE.SESSION]: () => {
      const discountCodeParam = discount?.code
        ? `?discountCode=${discount.code}`
        : '';

      return `${NAS_IO_FRONTEND_URL}${communityLink}/sessions${slug}${discountCodeParam}${additionalParameters}`;
    },
    [PURCHASE_TYPE.CHALLENGE]: () =>
      generateEntityCheckoutLink('challenge'),
  };

  const checkoutLinkGenerator = checkoutLinkGenerators[entityType];

  if (!checkoutLinkGenerator) {
    throw new Error(`${entityType} is not supported`);
  }

  return checkoutLinkGenerator();
}

function getEntityInfoImage(entityInfo) {
  const { thumbnail, entityType, hostLearner } = entityInfo;

  if (entityType === PURCHASE_TYPE.SUBSCRIPTION) {
    return hostLearner.profileImage;
  }

  return thumbnail;
}

exports.formatUpsellEntityData = ({
  sourceParentEntity,
  upsell,
  timezone,
  languagePreference,
}) => {
  const {
    entityInfo,
    title: upsellTitle,
    description: upsellDescription,
    discount,
    upsellIdentityCode,
  } = upsell;

  const { title, entityType } = entityInfo;

  const entityPrice = getEntityInfoPrice(entityInfo);

  const entityTime = getEntityInfoTime(
    entityInfo,
    timezone,
    languagePreference
  );

  const additionalParams = { upsellIdentityCode };

  const entityCheckoutLink = generateEntityInfoCheckoutLink(
    entityInfo,
    discount,
    additionalParams,
    SIGNUP_REQUESTOR.UPSELL_EMAIL
  );

  const entityImage = getEntityInfoImage(entityInfo);

  const data = {
    entity_name: title,
    entity_image: entityImage,
    entity_time: entityTime,
    entity_price: entityPrice,
    entity_checkout_link: entityCheckoutLink,
    parent_source_entity_name: sourceParentEntity.title,
    is_subscription_entity_type: entityType === PURCHASE_TYPE.SUBSCRIPTION,
    discount_value: discount?.value ?? 0,
    discount_effective_end_date: discount?.effectiveTimeEnd
      ? DateTime.fromJSDate(discount.effectiveTimeEnd)
          .setZone(timezone)
          .toFormat('d LLL yyyy, hh:mm a ZZZZ')
      : '',
    upsell_title: upsellTitle,
    upsell_description: upsellDescription,
  };

  return data;
};

exports.formatMemberData = ({ learner }) => {
  const { email, firstName, lastName, profileImage, phoneNumber } =
    learner;

  const memberName = nameUtils.getName(firstName, lastName);

  const data = {
    member_name: memberName,
    member_email: email,
    member_image: profileImage,
    member_phone_number: phoneNumber,
  };

  return data;
};

exports.formatOwnerData = ({ owner }) => {
  const { email, firstName, lastName, profileImage } = owner;

  const ownerName = nameUtils.getName(firstName, lastName);

  const data = {
    owner_name: ownerName !== '' ? ownerName : 'Community Manager',
    owner_email: email,
    owner_image: profileImage,
  };

  return data;
};

exports.formatMembershipData = ({ subscription, languagePreference }) => {
  const { createdAt } = subscription;

  const currentDate = DateTime.utc();
  const memberJoinedDate = DateTime.fromJSDate(createdAt);
  const diffInDays = Math.floor(
    currentDate.diff(memberJoinedDate, 'days').days
  );

  const data = {
    member_joined_date: memberJoinedDate.toFormat(
      languagePreference === 'ja' ? 'yyyy年LLL月dd日' : 'dd LLL yyyy',
      { locale: languagePreference }
    ),
    member_duration_in_days: diffInDays,
  };

  return data;
};

exports.formatAwardOrResetPointData = ({
  community,
  program,
  programItem,
  point,
  reason,
}) => {
  const data = {
    challenge_title: program.title,
    checkpoint_title: programItem.title,
    challenge_image: program.cover,
    reason,
    point,
    challenge_link: `${NAS_IO_FRONTEND_URL}${community.link}/challenges${program.slug}`,
  };

  return data;
};

exports.formatZeroLinkData = ({ zeroLink }) => {
  const data = {
    zero_link_title: zeroLink.title,
    zero_link_cover_img: zeroLink.coverImg,
    zero_link_message: zeroLink.message,
    zero_link_redirect_link: zeroLink.redirectLink,
    zero_link_detail_page: `${NAS_IO_FRONTEND_URL}/portal/money?tab=zero_links&zeroLinkId=${zeroLink._id.toString()}`,
  };

  return data;
};
