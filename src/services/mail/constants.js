const { LEARN_BACKEND_URL } = require('../../config');

const DEFAULT_MAIL_COURSE_OR_MAIL_COURSE_OFFER = 'All';

const MAIL_RECORD_TYPE = {
  GENERAL: 'general',
  OVERRIDE: 'override',
};

const EMAIL_FALLBACK = {
  MANAGER_EMAIL: '<EMAIL>',
  MANAGER_NAME: 'Community Manager',
  COMMUNITY_FROM_MAIL: '<EMAIL>',
};

const DEFAULT_IMAGES = {
  COMMUNITY_HOST_PROFILE_IMAGE:
    'https://d2oi1rqwb0pj00.cloudfront.net/na-portal/default-community-host-profile.png',
  COMMUNITY_PROFILE_IMAGE:
    'https://d2oi1rqwb0pj00.cloudfront.net/na-website/community-product-page/nas-io-homepage/NA+logo.jpeg',
};

const CONTENT_DIVIDER = '<!-- Add Content Here -->';

const MAIL_TEMPLATE_LINK = {
  MEMBER_WELCOME_STANDARD_COMMUNITY:
    'https://s3.ap-southeast-1.amazonaws.com/storage.nas.academy/email_assets/html/community/COMMON/template/member_welcome_standard_community_template.html',
  MEMBER_WELCOME_WHATSAPP_COMMUNITY:
    'https://s3.ap-southeast-1.amazonaws.com/storage.nas.academy/email_assets/html/community/COMMON/template/member_welcome_whatsapp_community_template.html',
};

const ENROLMENT_MAIL_TYPES = {
  MEMBER_COMMUNITY_ENROLMENT_WITHOUT_APPLICATION:
    'COMMUNITY_ENROLMENT_WITHOUT_APPLICATION',
  MEMBER_COMMUNITY_ENROLMENT_WITHOUT_APPLICATION_FOR_WHATSAPP:
    'COMMUNITY_ENROLMENT_WITHOUT_APPLICATION_FOR_WHATSAPP',
  MEMBER_COMMUNITY_ENROLMENT_APPLICATION_APPROVED:
    'COMMUNITY_ENROLMENT_APPLICATION_APPROVED',
  MEMBER_COMMUNITY_ENROLMENT_APPLICATION_APPROVED_FOR_WHATSAPP:
    'COMMUNITY_ENROLMENT_APPLICATION_APPROVED_FOR_WHATSAPP',
  MEMBER_COMMUNITY_ENROLMENT_APPLICATION:
    'COMMUNITY_ENROLMENT_APPLICATION',
  MEMBER_COMMUNITY_ENROLMENT_APPLICATION_REJECTED:
    'COMMUNITY_ENROLMENT_APPLICATION_REJECTED',
  MEMBER_COMMUNITY_MEMBER_REMOVED: 'COMMUNITY_MEMBER_REMOVED',
  MEMBER_REVOKE_CANCELLATION_MAIL: 'MEMBER_REVOKE_CANCELLATION_MAIL',
};

const AWS_MAIL_TYPES = [
  ENROLMENT_MAIL_TYPES.MEMBER_COMMUNITY_ENROLMENT_WITHOUT_APPLICATION,
  ENROLMENT_MAIL_TYPES.MEMBER_COMMUNITY_ENROLMENT_WITHOUT_APPLICATION_FOR_WHATSAPP,
  ENROLMENT_MAIL_TYPES.MEMBER_COMMUNITY_ENROLMENT_APPLICATION_APPROVED,
  ENROLMENT_MAIL_TYPES.MEMBER_COMMUNITY_ENROLMENT_APPLICATION_APPROVED_FOR_WHATSAPP,
  ENROLMENT_MAIL_TYPES.MEMBER_COMMUNITY_ENROLMENT_APPLICATION,
];

const ZERO_LINK_MAIL_TYPES = {
  MANAGER_ZERO_LINK_PAYMENT_SUCCESS: 'MANAGER_ZERO_LINK_PAYMENT_SUCCESS',
  MEMBER_ZERO_LINK_PAYMENT_SUCCESS: 'MEMBER_ZERO_LINK_PAYMENT_SUCCESS',
};

const CALENDAR_MAIL_TYPES = {
  MANAGER_GOOGLE_CALENDAR_RECONNECTION:
    'MANAGER_GOOGLE_CALENDAR_RECONNECTION',
};

const EVENT_MAIL_TYPES = {
  MANAGER_COMMUNITY_EVENT_CREATED: 'MANAGER_COMMUNITY_EVENT_CREATED',
  MANAGER_COMMUNITY_EVENT_CREATED_V2: 'MANAGER_COMMUNITY_EVENT_CREATED_V2',
  MANAGER_COMMUNITY_EVENT_RSVP: 'MANAGER_COMMUNITY_EVENT_RSVP',
  MANAGER_COMMUNITY_EVENT_UPDATE: 'MANAGER_COMMUNITY_EVENT_UPDATE',
  MANAGER_COMMUNITY_EVENT_UPDATE_V2: 'MANAGER_COMMUNITY_EVENT_UPDATE_V2',
  MANAGER_COMMUNITY_EVENT_CANCELLED: 'MANAGER_COMMUNITY_EVENT_CANCELLED',
  MANAGER_COMMUNITY_EVENT_CANCELLED_V2:
    'MANAGER_COMMUNITY_EVENT_CANCELLED_V2',
  MEMBER_COMMUNITY_EVENT_RSVP: 'MEMBER_COMMUNITY_EVENT_RSVP',
  MEMBER_COMMUNITY_EVENT_PENDING_APPROVAL:
    'MEMBER_COMMUNITY_EVENT_PENDING_APPROVAL',
  MEMBER_COMMUNITY_EVENT_APPROVED: 'MEMBER_COMMUNITY_EVENT_APPROVED',
  MEMBER_COMMUNITY_EVENT_REJECTED: 'MEMBER_COMMUNITY_EVENT_REJECTED',
  MEMBER_COMMUNITY_EVENT_UPDATE: 'MEMBER_COMMUNITY_EVENT_UPDATE',
  MEMBER_COMMUNITY_EVENT_UPDATE_V2: 'MEMBER_COMMUNITY_EVENT_UPDATE_V2',
  MEMBER_COMMUNITY_EVENT_CANCELLED: 'MEMBER_COMMUNITY_EVENT_CANCELLED',
  MEMBER_COMMUNITY_EVENT_REMINDER_24H:
    'MEMBER_COMMUNITY_EVENT_REMINDER_24H',
  MEMBER_COMMUNITY_EVENT_REMINDER_1H: 'MEMBER_COMMUNITY_EVENT_REMINDER_1H',
  MEMBER_COMMUNITY_EVENT_PUBLISHED: 'MEMBER_COMMUNITY_EVENT_PUBLISHED',
};

const FOLDER_MAIL_TYPES = {
  COMMUNITY_FOLDER_PURCHASE_MEMBER: 'COMMUNITY_FOLDER_PURCHASE_MEMBER',
};

const SESSION_MAIL_TYPES = {
  MEMBER_COMMUNITY_SESSION_CONFIRMATION:
    'MEMBER_COMMUNITY_SESSION_CONFIRMATION',
  MANAGER_COMMUNITY_SESSION_CONFIRMED:
    'MANAGER_COMMUNITY_SESSION_CONFIRMED',
  MEMBER_CANCELLATION_SESSION_NOTIFICATION:
    'MEMBER_CANCELLATION_SESSION_NOTIFICATION',
  MANAGER_CANCELLATION_SESSION_NOTIFICATION:
    'MANAGER_CANCELLATION_SESSION_NOTIFICATION',
  MEMBER_COMMUNITY_SESSION_REMINDER_24H:
    'MEMBER_COMMUNITY_SESSION_REMINDER_24H',
  MEMBER_COMMUNITY_SESSION_UPDATED: 'MEMBER_COMMUNITY_SESSION_UPDATED',
  MANAGER_COMMUNITY_SESSION_UPDATED: 'MANAGER_COMMUNITY_SESSION_UPDATED',
  MANAGER_COMMUNITY_SESSION_UPDATED_V2:
    'MANAGER_COMMUNITY_SESSION_UPDATED_V2',
  MANAGER_COMMUNITY_SESSION_CANCELLED_V2:
    'MANAGER_COMMUNITY_SESSION_CANCELLED_V2',
};

// TODO: review with team if we need to change the label(s) for the following
const CHALLENGE_MAIL_TYPES = {
  CHALLENGE_PARTICIPANT_ADDED_MEMBER: 'CHALLENGE_PARTICIPANT_ADDED_MEMBER',
  CHALLENGE_PARTICIPANT_ADDED_MANAGER:
    'CHALLENGE_PARTICIPANT_ADDED_MANAGER',
  CHALLENGE_PARTICIPANT_REMOVED_NOTIFICATION_MEMBER:
    'CHALLENGE_PARTICIPANT_REMOVED_NOTIFICATION_MEMBER',
  CHALLENGE_START_REMINDER_72H: 'CHALLENGE_START_REMINDER_72H',
  CHALLENGE_WINNER_DECLARATION_NOTIFICATION_MEMBER:
    'CHALLENGE_WINNER_DECLARATION_NOTIFICATION_MEMBER',
  CHALLENGE_PARTICIPANT_CHECKPOINT_REMINDER:
    'CHALLENGE_PARTICIPANT_CHECKPOINT_REMINDER',
  CHALLENGE_PARTICIPANT_CHECKPOINT_70_PERCENT_REMINDER:
    'CHALLENGE_PARTICIPANT_CHECKPOINT_70_PERCENT_REMINDER',
  CHALLENGE_PARTICIPANT_CHECKPOINT_ENDING_IN_1_HOUR_REMINDER:
    'CHALLENGE_PARTICIPANT_CHECKPOINT_ENDING_IN_1_HOUR_REMINDER',
  MEMBER_CHALLENGE_AWARD_POINT: 'MEMBER_CHALLENGE_AWARD_POINT',
  MEMBER_CHALLENGE_RESET_POINT: 'MEMBER_CHALLENGE_RESET_POINT',
};

const CHECKPOINT_MAIL_TYPES = [
  CHALLENGE_MAIL_TYPES.CHALLENGE_PARTICIPANT_CHECKPOINT_REMINDER,
  CHALLENGE_MAIL_TYPES.CHALLENGE_PARTICIPANT_CHECKPOINT_70_PERCENT_REMINDER,
  CHALLENGE_MAIL_TYPES.CHALLENGE_PARTICIPANT_CHECKPOINT_ENDING_IN_1_HOUR_REMINDER,
];

const CHALLENGE_CHECKPOINT_EVENT_MAIL_TYPES = {
  MEMBER_CHALLENGE_CHECKPOINT_EVENT_REMINDER_NOW:
    'MEMBER_CHALLENGE_CHECKPOINT_EVENT_REMINDER_NOW',
  MEMBER_CHALLENGE_CHECKPOINT_EVENT_REMINDER_1H:
    'MEMBER_CHALLENGE_CHECKPOINT_EVENT_REMINDER_1H',
  MEMBER_CHALLENGE_CHECKPOINT_EVENT_REMINDER_24H:
    'MEMBER_CHALLENGE_CHECKPOINT_EVENT_REMINDER_24H',
};

const ABANDONED_CART_MAIL_TYPES = {
  ABANDONED_CART_REMINDER_1HR: 'ABANDONED_CART_REMINDER_1HR',
  ABANDONED_CART_REMINDER_24HR: 'ABANDONED_CART_REMINDER_24HR',
  ABANDONED_CART_MEMBERSHIP_REMINDER_1HR:
    'ABANDONED_CART_MEMBERSHIP_REMINDER_1HR',
  ABANDONED_CART_MEMBERSHIP_REMINDER_24HR:
    'ABANDONED_CART_MEMBERSHIP_REMINDER_24HR',
};

const UPSELL_MAIL_TYPE = {
  MEMBER_UPSELL: 'MEMBER_UPSELL',
};

const AFFILIATE_MAIL_TYPE = {
  MANAGER_NEW_AFFILIATE: 'MANAGER_NEW_AFFILIATE',
  MEMBER_NEW_AFFILIATE: 'MEMBER_NEW_AFFILIATE',
  MEMBER_AFFILIATE_COMMISSION_CHANGES:
    'MEMBER_AFFILIATE_COMMISSION_CHANGES',
  MEMBER_AFFILIATE_REMOVED: 'MEMBER_AFFILIATE_REMOVED',
  MANAGER_AFFILIATE_NEW_SALE: 'MANAGER_AFFILIATE_NEW_SALE',
  MEMBER_AFFILIATE_NEW_SALE: 'MEMBER_AFFILIATE_NEW_SALE',
};

const PAYOUT_MAIL_TYPES = {
  COMMUNITY_PAYOUT_NOTIFICATION: 'COMMUNITY_PAYOUT_NOTIFICATION',
  MEMBER_PAYOUT_NOTIFICATION: 'MEMBER_PAYOUT_NOTIFICATION',
};

const PLAN_MAIL_TYPE = {
  NAS_IO_PLATINUM_CANCEL_MAIL: 'NAS_IO_PLATINUM_CANCEL_MAIL',
  NAS_IO_PRO_WELCOME_MAIL: 'NAS_IO_PRO_WELCOME_MAIL',
  NAS_IO_PRO_CANCEL_MAIL: 'NAS_IO_PRO_CANCEL_MAIL',
  NAS_IO_PRO_RENEWAL_FAILURE_MAIL_1ST:
    'NAS_IO_PRO_RENEWAL_FAILURE_MAIL_1ST',
  NAS_IO_PRO_RENEWAL_FAILURE_MAIL_2ND:
    'NAS_IO_PRO_RENEWAL_FAILURE_MAIL_2ND',
  NAS_IO_PRO_RENEWAL_FAILURE_MAIL_3RD:
    'NAS_IO_PRO_RENEWAL_FAILURE_MAIL_3RD',
  NAS_IO_PRO_RENEWAL_FAILURE_MAIL_4TH:
    'NAS_IO_PRO_RENEWAL_FAILURE_MAIL_4TH',
  NAS_IO_PLATINUM_RENEWAL_FAILURE_MAIL_1ST:
    'NAS_IO_PLATINUM_RENEWAL_FAILURE_MAIL_1ST',
  NAS_IO_PLATINUM_RENEWAL_FAILURE_MAIL_2ND:
    'NAS_IO_PLATINUM_RENEWAL_FAILURE_MAIL_2ND',
  NAS_IO_PLATINUM_RENEWAL_FAILURE_MAIL_3RD:
    'NAS_IO_PLATINUM_RENEWAL_FAILURE_MAIL_3RD',
  NAS_IO_PLATINUM_RENEWAL_FAILURE_MAIL_4TH:
    'NAS_IO_PLATINUM_RENEWAL_FAILURE_MAIL_4TH',
  TO_REFERRER_FOR_SUCCESSFUL_PAYMENT_VIA_COMMUNITY_REFERRAL_LINK:
    'TO_REFERRER_FOR_SUCCESSFUL_PAYMENT_VIA_COMMUNITY_REFERRAL_LINK',
  NAS_IO_PLATINUM_WELCOME_MAIL: 'NAS_IO_PLATINUM_WELCOME_MAIL',
  NAS_IO_PRO_REVOKE_CANCELLATION_MAIL:
    'NAS_IO_PRO_REVOKE_CANCELLATION_MAIL',
  NAS_IO_PLATINUM_REVOKE_CANCELLATION_MAIL:
    'NAS_IO_PLATINUM_REVOKE_CANCELLATION_MAIL',
  TO_REFERRER_FOR_SUCCESSFUL_PAYMENT_VIA_COMMUNITY_REFERRAL_LINK_PLATINUM:
    'TO_REFERRER_FOR_SUCCESSFUL_PAYMENT_VIA_COMMUNITY_REFERRAL_LINK_PLATINUM',
};

const FEED_MAIL_TYPES = {
  MEMBER_COMMUNITY_POST_CREATED_ANNOUNCEMENT:
    'MEMBER_COMMUNITY_POST_CREATED_ANNOUNCEMENT',
  MEMBER_COMMUNITY_POST_APPROVED_ANNOUNCEMENT:
    'MEMBER_COMMUNITY_POST_APPROVED_ANNOUNCEMENT',
  MEMBER_COMMUNITY_POST_REJECTED_ANNOUNCEMENT:
    'MEMBER_COMMUNITY_POST_REJECTED_ANNOUNCEMENT',
  CM_COMMUNITY_POST_PENDING_APPROVAL_ANNOUNCEMENT:
    'CM_COMMUNITY_POST_PENDING_APPROVAL_ANNOUNCEMENT',
};
const NO_MAIL_TYPES = {
  MAGIC_REACH: 'COMMUNITY_MAGIC_REACH',
  ALL: 'ALL_MAIL_TYPES',
};

const MAIL_TYPES_COMMON_HEADER_SET = new Set([
  ENROLMENT_MAIL_TYPES.MEMBER_COMMUNITY_ENROLMENT_APPLICATION_APPROVED,
  ENROLMENT_MAIL_TYPES.MEMBER_COMMUNITY_ENROLMENT_APPLICATION_APPROVED_FOR_WHATSAPP,
  ENROLMENT_MAIL_TYPES.MEMBER_COMMUNITY_ENROLMENT_WITHOUT_APPLICATION,
  ENROLMENT_MAIL_TYPES.MEMBER_COMMUNITY_ENROLMENT_WITHOUT_APPLICATION_FOR_WHATSAPP,
]);

const FOLDER_NAMES = {
  ENROLMENT: 'enrolment',
  EVENT: 'event',
  SESSION: 'session',
  CHALLENGE: 'challenge',
};

const WORKER_TYPES = {
  CUSTOM_API: 'custom_api',
  SQS: 'sqs',
  AWS_BATCH: 'aws_batch',
  NOTI_SERVICE: 'noti_service',
  REDIS_CACHE: 'redis_cache',
};

const RESULT_TYPE = {
  SINGLE: 'single',
  BATCH: 'batch',
};

const JOB_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
};

const USE_CASES = {
  CHECKPOINT_EMAIL_24H: 'checkpoint_email_24h_schedule',
  CHECKPOINT_EMAIL_1H: 'checkpoint_email_1h_schedule',
  CHECKPOINT_EMAIL_NOW: 'checkpoint_email_now_schedule',
  CHALLENGE_START_REMINDER_72H: 'challenge_start_reminder_72h_schedule',
  ABANDONED_CARTS_REMINDER: 'abandoned_cart_reminder',
  ABANDONED_CHECKOUT_UPDATE: 'abandoned_checkout_update',
  PAYPAL_UNSUBSCRIPTION_REMINDER: 'paypal_unsubscription_reminder',
  FLUSH_REDIS_CACHE: 'flush_redis_cache',
};

const LIMITATION_MAIL_NOTIFICATION_TYPES = {
  FREE_PRO_STORAGE_LIMIT_HIT_LESS_THAN_100:
    'FREE_PRO_STORAGE_LIMIT_HIT_LESS_THAN_100',
  PLATINUM_STORAGE_LIMIT_HIT_LESS_THAN_100:
    'PLATINUM_STORAGE_LIMIT_HIT_LESS_THAN_100',
  FREE_PRO_STORAGE_LIMIT_HIT_100: 'FREE_PRO_STORAGE_LIMIT_HIT_100',
  PLATINUM_STORAGE_LIMIT_HIT_100: 'PLATINUM_STORAGE_LIMIT_HIT_100',
};

const CHECKPOINT_REMINDER_API_PATH = `${LEARN_BACKEND_URL}api/v1/challenge/send-checkpoint-reminder`;

const PRODUCT_MAIL_TYPES = {
  MEMBER_COMMUNITY_CHALLENGE_PUBLISHED:
    'MEMBER_COMMUNITY_CHALLENGE_PUBLISHED',
  MEMBER_COMMUNITY_SESSION_PUBLISHED: 'MEMBER_COMMUNITY_SESSION_PUBLISHED',
  MEMBER_COMMUNITY_DIGITAL_FILE_PUBLISHED:
    'MEMBER_COMMUNITY_DIGITAL_FILE_PUBLISHED',
  MEMBER_COMMUNITY_COURSE_PUBLISHED: 'MEMBER_COMMUNITY_COURSE_PUBLISHED',
};

module.exports = {
  EMAIL_FALLBACK,
  DEFAULT_IMAGES,
  CONTENT_DIVIDER,
  MAIL_TEMPLATE_LINK,
  ENROLMENT_MAIL_TYPES,
  ZERO_LINK_MAIL_TYPES,
  CALENDAR_MAIL_TYPES,
  EVENT_MAIL_TYPES,
  CHALLENGE_CHECKPOINT_EVENT_MAIL_TYPES,
  FOLDER_NAMES,
  MAIL_TYPES_COMMON_HEADER_SET,
  DEFAULT_MAIL_COURSE_OR_MAIL_COURSE_OFFER,
  LIMITATION_MAIL_NOTIFICATION_TYPES,
  MAIL_RECORD_TYPE,
  NO_MAIL_TYPES,
  SESSION_MAIL_TYPES,
  CHALLENGE_MAIL_TYPES,
  UPSELL_MAIL_TYPE,
  JOB_STATUS,
  WORKER_TYPES,
  RESULT_TYPE,
  ABANDONED_CART_MAIL_TYPES,
  AFFILIATE_MAIL_TYPE,
  USE_CASES,
  CHECKPOINT_MAIL_TYPES,
  CHECKPOINT_REMINDER_API_PATH,
  PAYOUT_MAIL_TYPES,
  AWS_MAIL_TYPES,
  FEED_MAIL_TYPES,
  PLAN_MAIL_TYPE,
  FOLDER_MAIL_TYPES,
  PRODUCT_MAIL_TYPES,
};
