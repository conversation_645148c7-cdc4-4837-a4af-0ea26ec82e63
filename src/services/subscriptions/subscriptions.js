const ObjectId = require('mongoose').Types.ObjectId;
const moment = require('moment');
const CommunitySubscriptionModel = require('../../communitiesAPI/models/communitySubscriptions.model');
const CommunityDiscountModel = require('../../communitiesAPI/models/communityDiscounts.model');
const RawTransactionModel = require('../../models/rawTransaction.model');
const UserModel = require('../../models/users.model');
const CommunityRoleModel = require('../../communitiesAPI/models/communityRole.model');
const commonSubscriptionService = require('../common/subscription.service');
const {
  COMMUNITY_SUBSCRIPTION_STATUSES,
  PAYMENT_PROVIDER,
  PURCHASE_TYPE,
  DISCOUNT_TYPE,
  TRANSACTION_TYPE,
} = require('../../constants/common');
const {
  StripePaymentMethodService,
  StripeIndiaPaymentMethodService,
  EbanxPaymentMethodService,
  RazorpayPaymentMethodService,
  PaypalPaymentMethodService,
} = require('./paymentMethod');
const {
  PAYMENT_STATUSES,
  aclRoles,
} = require('../../communitiesAPI/constants');
const {
  ParamError,
  ResourceNotFoundError,
} = require('../../utils/error.util');
const { getBillingHistory } = require('./billingHistory');
const { PAYPAL_DASHBOARD_DOMAIN } = require('../../config');
const PaymentBackendRpc = require('../../rpc/paymentBackend');

const getCurrentPaymentMethod = async (subscription) => {
  const isRecurringPurchase =
    subscription.paymentDetails?.recurringPurchase ?? true;

  if (!subscription.stripeSubscriptionId) {
    return {};
  }
  switch (subscription.paymentProvider) {
    case PAYMENT_PROVIDER.STRIPE:
    case PAYMENT_PROVIDER.STRIPE_US:
      return StripePaymentMethodService.getCurrentMethodOfSubscription(
        subscription.stripeSubscriptionId,
        subscription.paymentProvider
      );
    case PAYMENT_PROVIDER.STRIPE_INDIA:
      return StripeIndiaPaymentMethodService.getCurrentMethodOfSubscription(
        subscription.stripeSubscriptionId,
        isRecurringPurchase
      );
    case PAYMENT_PROVIDER.RAZORPAY:
      return RazorpayPaymentMethodService.getCurrentMethodOfSubscription(
        subscription.stripeSubscriptionId
      );
    case PAYMENT_PROVIDER.EBANX:
      return EbanxPaymentMethodService.getCurrentMethodOfSubscription(
        subscription
      );
    case PAYMENT_PROVIDER.PAYPAL:
      return PaypalPaymentMethodService.getCurrentMethodOfSubscription(
        subscription
      );
    default:
  }
};

const getDiscountInfo = async (subscription) => {
  // 1. Discount forever: return discounted price, and original price
  // 2. Discount with interval: return discounted price, and original price
  // -> special handling: need to return the end date of discount period

  if (!subscription.purchase_transaction?.discountObjectId) {
    return null;
  }

  const discount = await CommunityDiscountModel.findById(
    subscription.purchase_transaction.discountObjectId
  ).lean();

  if (!discount) {
    throw new ResourceNotFoundError('Discount not found');
  }

  const discountDetails = {
    percentage: discount.type === 'percentage' ? discount.value : 0,
  };

  // Display discounted amount when there is recently change plan
  const hasRecentChangePlan = subscription.planHistory?.find(
    (planHistory) => planHistory.billingCycle === subscription.billingCycle
  );

  if (discount.intervalCount && discount.intervalCount > 0) {
    if (
      !hasRecentChangePlan &&
      (subscription.nextBillingDate < new Date() ||
        subscription.billingCycle > 1)
    ) {
      // Return null when the discount interval is over
      return null;
    }

    // Discount with interval
    discountDetails.discountType = DISCOUNT_TYPE.INTERVAL;
    discountDetails.discountEndDay = subscription.nextBillingDate;
    discountDetails.isUnderDiscountPeriod =
      subscription.nextBillingDate >= new Date();
    discountDetails.fullAmount =
      subscription.purchase_transaction.full_local_amount;
    discountDetails.discountedAmount =
      subscription.purchase_transaction.local_amount;
  } else if (discount.trialDays != null && discount.trialDays > 0) {
    if (subscription.purchase_transaction?.isFreeTrial) {
      // Only display free trial info when its still under trial period
      discountDetails.discountType = DISCOUNT_TYPE.FREE_TRIAL;
      discountDetails.trialDays = discount.trialDays;
      discountDetails.isFreeTrial = true;
    } else {
      return null;
    }
  } else {
    // Discount forever
    discountDetails.discountType = DISCOUNT_TYPE.FOREVER;
    discountDetails.fullAmount =
      subscription.purchase_transaction.full_local_amount;
    discountDetails.discountedAmount =
      subscription.purchase_transaction.local_amount;
  }
  return discountDetails;
};

const getGracePeriodEndDate = async (subscription) => {
  const gracePeriodEndDate = moment(subscription.nextBillingDate);
  switch (subscription.paymentProvider) {
    case PAYMENT_PROVIDER.STRIPE:
    case PAYMENT_PROVIDER.STRIPE_US:
    case PAYMENT_PROVIDER.STRIPE_INDIA:
    case PAYMENT_PROVIDER.EBANX:
      gracePeriodEndDate.add(7, 'day');
      break;
    case PAYMENT_PROVIDER.PAYPAL:
      gracePeriodEndDate.add(10, 'day');
      break;
    case PAYMENT_PROVIDER.RAZORPAY: {
      const paymentMethod = await getCurrentPaymentMethod(subscription);
      if (paymentMethod?.upiId) {
        gracePeriodEndDate.add(1, 'day');
      } else {
        gracePeriodEndDate.add(3, 'day');
      }
      break;
    }
    default:
      throw new Error(
        `Invalid payment provider: ${subscription.paymentProvider}`
      );
  }
  return gracePeriodEndDate.toDate();
};

const getChangeMethodLink = (subscription) => {
  switch (subscription.paymentProvider) {
    case PAYMENT_PROVIDER.PAYPAL:
      // eslint-disable-next-line no-param-reassign
      subscription.changePaymentMethodLink = `${PAYPAL_DASHBOARD_DOMAIN}/myaccount/autopay/connect/${subscription.stripeSubscriptionId}/funding`;
      break;
    default:
      break;
  }
  return subscription;
};

exports.getSubscriptions = async (
  learnerObjectId,
  subscriptionId = null
) => {
  const matchQuery = {
    learnerObjectId: new ObjectId(learnerObjectId),
    $or: [
      {
        status: COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT,
      },
      {
        status: COMMUNITY_SUBSCRIPTION_STATUSES.CANCELLED,
        cancelledAt: {
          $gt: new Date(),
        },
      },
    ],
  };

  if (subscriptionId) {
    matchQuery._id = new ObjectId(subscriptionId);
  }

  const user = await UserModel.findOne(
    { learner: learnerObjectId, isActive: true },
    { _id: 1 }
  ).lean();

  if (!user) {
    throw new ResourceNotFoundError('User not found');
  }

  const [subscriptions, communityMangerRoles] = await Promise.all([
    CommunitySubscriptionModel.aggregate([
      { $match: matchQuery },
      {
        $lookup: {
          from: 'community_purchase_transactions',
          localField: '_id',
          foreignField: 'subscriptionObjectId',
          as: 'purchase_transaction',
        },
      },
      {
        $unwind: {
          path: '$purchase_transaction',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'communities',
          localField: 'communityCode',
          foreignField: 'code',
          as: 'communities',
        },
      },
      {
        $unwind: {
          path: '$communities',
        },
      },
    ]),
    CommunityRoleModel.find(
      { userObjectId: user._id, role: aclRoles.MANAGER },
      { communityCode: 1 }
    ).lean(),
  ]);

  const managerRoleSet = communityMangerRoles.reduce(
    (acc, { communityCode }) => {
      acc.add(communityCode);
      return acc;
    },
    new Set()
  );

  const activeSubscriptions = [];
  const activeSubscriptionsPurchaseTransaction = [];
  for await (const subscription of subscriptions) {
    // For member that change plan, there will have two purchase transaction under same subscription
    // And subscription.communitySignupId is always the latest one, we only return latest one
    const notExistSamePurchaseTransaction =
      subscription.communitySignupId &&
      subscription.communitySignupId !==
        subscription.purchase_transaction._id.toString();

    if (
      notExistSamePurchaseTransaction ||
      managerRoleSet.has(subscription.communityCode) // NOTE: Manager is not included in membership list (by PM)
    ) {
      // eslint-disable-next-line no-continue
      continue;
    }

    const billingHistory = [];
    const activeSubscription = {
      subscriptionId: subscription._id,
      purchaseTransactionId: subscription.purchase_transaction?._id,
      learnerObjectId: subscription.learnerObjectId,
      stripeSubscriptionId: subscription.stripeSubscriptionId,
      interval: subscription.interval,
      intervalCount: subscription.intervalCount,
      nextBillingDate: subscription.nextBillingDate,
      paymentProvider: subscription.paymentProvider,
      stripeProductProvider: subscription.stripeProductProvider,
      status: subscription.status,
      stripeProductId: subscription.stripeProductId,
      amount:
        subscription.purchase_transaction?.full_local_amount ??
        subscription.purchase_transaction?.local_amount ??
        0,
      currency: subscription.purchase_transaction?.local_currency,
      paymentDetails: subscription.purchase_transaction?.payment_details,
      community: {
        communityId: subscription.communities._id,
        fullScreenBannerImgData:
          subscription.communities.fullScreenBannerImgData,
        title: subscription.communities.title,
        thumbnailImgData: subscription.communities.thumbnailImgData,
        code: subscription.communities.code,
        link: subscription.communities.link,
        payment_methods: subscription.communities.payment_methods,
        stripeProductId: subscription.communities.stripeProductId,
        baseCurrency: subscription.communities.baseCurrency,
      },
      planHistory: subscription.planHistory,
      billingCycle: subscription.billingCycle,
      billingHistory,
      createdAt: subscription.createdAt,
    };

    // Add revocation flags
    const now = new Date();

    // canRevokePlanChange: For active subscriptions with plan history in current billing cycle
    let canRevokePlanChange = false;

    if (subscription.status === COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT) {
      // Check if there's plan history for the current billing cycle
      const currentBillingCycle = subscription.billingCycle || 1;
      const relevantPlanHistory =
        subscription.planHistory?.filter(
          (history) => history.billingCycle === currentBillingCycle
        ) || [];

      if (relevantPlanHistory.length > 0 && subscription.nextBillingDate) {
        // Check billing date buffer (24 hours) - plan change uses billing date
        const PLAN_BUFFER_MS = 24 * 60 * 60 * 1000; // 24 hours
        const billingDeadline = new Date(
          subscription.nextBillingDate.getTime() - PLAN_BUFFER_MS
        );
        canRevokePlanChange = now < billingDeadline;
      }
      // If no nextBillingDate, canRevokePlanChange remains false
    }

    // canRevokeCancellation: For cancelled subscriptions (scheduled cancellation)
    let canRevokeCancellation = false;
    if (
      subscription.status === COMMUNITY_SUBSCRIPTION_STATUSES.CANCELLED &&
      subscription.cancelledAt &&
      subscription.cancelledAt > now
    ) {
      // Check cancellation buffer (2 hours) - cancellation revoke uses cancelledAt
      const CANCELLATION_BUFFER_MS = 2 * 60 * 60 * 1000; // 2 hours
      const cancellationDeadline = new Date(
        subscription.cancelledAt.getTime() - CANCELLATION_BUFFER_MS
      );
      canRevokeCancellation = now < cancellationDeadline;
    }

    activeSubscription.canRevokePlanChange = canRevokePlanChange;

    activeSubscription.canRevokeCancellation = canRevokeCancellation;

    const discountDetails = await getDiscountInfo(subscription);
    if (discountDetails) {
      activeSubscription.discountDetails = discountDetails;
    }

    if (
      activeSubscription.paymentDetails?.status ===
      PAYMENT_STATUSES.RENEWAL_FAILED
    ) {
      // For renewal failed case, we need to manually calculate grace period end date
      activeSubscription.paymentDetails.gracePeriodEndDate =
        await getGracePeriodEndDate(subscription);
    }

    activeSubscriptions.push(activeSubscription);

    activeSubscriptionsPurchaseTransaction.push(
      subscription.purchase_transaction
    );
  }

  if (subscriptionId) {
    const subscription = activeSubscriptions[0];
    const purchaseTransaction = activeSubscriptionsPurchaseTransaction[0];

    if (!subscription) {
      throw new ParamError('No active subscription');
    }

    // Because when user change subscription plan, we will create new purchase transaction.
    // So the subscription.communitySignupId wont have prev info.
    // Use community object id and learner id to fetch past transaction data
    const rawTransactions = await RawTransactionModel.find({
      purchaseType: PURCHASE_TYPE.SUBSCRIPTION,
      communityObjectId: subscription.community.communityId,
      learnerObjectId: subscription.learnerObjectId,
      transactionType: TRANSACTION_TYPE.INBOUND,
      status: 'Success',
    })
      .sort({ transactionCreatedAt: -1 })
      .lean();

    const existsRawTransactionForPurchaseTransaction =
      rawTransactions.find(
        (rawTransaction) =>
          rawTransaction.purchasedId.toString() ===
          purchaseTransaction?._id?.toString()
      );

    // Return date, payment method, amount
    subscription.billingHistory = await getBillingHistory(rawTransactions);

    // Get all active pricing from stripe
    if (subscription.stripeSubscriptionId) {
      const currentPaymentMethod = await getCurrentPaymentMethod(
        subscription
      );
      subscription.currentPaymentMethod = currentPaymentMethod;

      const paymentBackendRpc = new PaymentBackendRpc();
      await paymentBackendRpc.init();

      const canChangePlan =
        await commonSubscriptionService.checkIfCanChangePlan({
          subscription,
          purchaseTransaction,
          community: subscription.community,
          currentPaymentMethod,
          existsRawTransactionForPurchaseTransaction,
          throwError: false,
        });

      subscription.canChangePlan = canChangePlan;

      const canChangePaymentMethod =
        commonSubscriptionService.checkIfCanChangePaymentMethod({
          subscription,
          purchaseTransaction,
          throwError: false,
        });

      subscription.canChangePaymentMethod = canChangePaymentMethod;
      getChangeMethodLink(subscription);
    }

    return subscription;
  }

  return activeSubscriptions;
};
