const logger = require('../logger.service');
const fraudService = require('../fraud');
const { createChatComletion } = require('../../clients/openai.client');
const { ParamError } = require('../../utils/error.util');
const {
  MAGIC_REACH_FRAUD_PROBABILITY_THRESHOLD,
} = require('../../config');

/**
 * Check for external links in plain text message
 * @param {String} message - Plain text message to check
 * @returns {Boolean} True if external links found
 */
const checkPlainTextForExternalLinks = (message) => {
  // URL regex pattern to find URLs in plain text
  const urlRegex = /(https?:\/\/[^\s]+)/gi;
  const urls = message.match(urlRegex);

  if (!urls) {
    return false;
  }

  // Check if any URL is not from nas.io
  for (const url of urls) {
    try {
      const parsedUrl = new URL(url);
      if (parsedUrl.hostname !== 'nas.io') {
        return true;
      }
    } catch (error) {
      logger.info(
        'Invalid URL encountered for external link check',
        url,
        error.message
      );
      return true; // Treat invalid URLs as external links
    }
  }

  return false;
};

/**
 * Create fraud check prompt for plain text
 * @param {String} content - Plain text content to check
 * @returns {String} OpenAI prompt
 */
const createFraudCheckPromptForPlainText = async (content) => {
  const prompt = await fraudService.getFraudOpenAIPrompt(
    fraudService.PROMPT_NAMES.MAGIC_REACH
  );

  return `${prompt} 
  Give your answer in JSON format with the following structure and instructions:
  {
    "probability": 0-100,
    "reason": "Your reason here, in English, for the user input being suspicious.",
  }

  Following is the content:
  ${content}`;
};

/**
 * Check plain text for fraud using OpenAI
 * @param {String} message - Plain text message to check
 * @returns {Promise<Object>} Fraud check result
 */
const consultOpenAPIForPlainTextFraud = async (message) => {
  try {
    // First check for blacklisted keywords
    const blacklistKeywordResult =
      await fraudService.hasBlacklistedKeywords({
        content: message,
      });

    if (blacklistKeywordResult.hasBlacklistedKeyword) {
      return {
        probability: 100,
        reason: `The message contains a blacklisted keyword: ${blacklistKeywordResult.blacklistedKeyword}`,
      };
    }

    // Create OpenAI prompt for plain text
    const chatContent = await createFraudCheckPromptForPlainText(message);

    const probabilityResponse = await createChatComletion([
      {
        role: 'user',
        content: chatContent,
      },
    ]);

    const resultString =
      probabilityResponse?.choices?.[0]?.message?.content
        ?.replace('```json\n', '')
        ?.replace('\n```', '');

    if (!resultString) {
      throw new Error('OpenAI response is empty or invalid');
    }

    let result;
    try {
      result = JSON.parse(resultString);
    } catch (parseError) {
      throw new Error(
        `Failed to parse OpenAI response: ${parseError.message}`
      );
    }

    // Validate the parsed result has required fields
    if (
      typeof result.probability !== 'number' ||
      typeof result.reason !== 'string'
    ) {
      throw new Error(
        'OpenAI response missing required fields (probability, reason)'
      );
    }

    return {
      probability: result.probability,
      reason: result.reason,
    };
  } catch (error) {
    if (error.response) {
      logger.error(
        `Error in calling OpenAI chatCompletion for plain text|`,
        `errorStatus=${error.response.status}|`,
        `errorData=${error.response.data}`
      );
    } else {
      logger.error(
        `Error with OpenAI API request for plain text|error=${error.message}`
      );
    }
    return {
      probability: 0,
      reason: 'OpenAI fraud check failed',
    };
  }
};

/**
 * Perform fraud check on product change notification message (plain text)
 * @param {Object} params
 * @param {String} params.notifyMessage - The notification message to check
 * @returns {Promise<Object>} Fraud check result
 */
const checkProductNotificationFraud = async ({ notifyMessage }) => {
  if (!notifyMessage) {
    return { isPotentialFraud: false, reason: 'No message to check' };
  }
  try {
    // Check for external links first - reject immediately if found
    const hasExternalLinks = checkPlainTextForExternalLinks(notifyMessage);

    if (hasExternalLinks) {
      throw new ParamError(
        'Product notification messages cannot contain links. Please use plain text only.'
      );
    }

    // Perform OpenAI fraud probability check on plain text
    const openAIResult = await consultOpenAPIForPlainTextFraud(
      notifyMessage
    );

    // Determine if potentially fraudulent
    const isPotentialFraud =
      openAIResult.probability >= MAGIC_REACH_FRAUD_PROBABILITY_THRESHOLD;

    const reasons = [];
    if (isPotentialFraud) {
      reasons.push(openAIResult.reason);
    }

    return {
      openAIResult,
      isPotentialFraud,
      hasExternalLinks: false,
      reason: reasons.join(' '),
      fraudCheckStatus: isPotentialFraud ? 'FLAGGED' : 'APPROVED',
    };
  } catch (error) {
    // If it's a ParamError about links, re-throw it
    if (error instanceof ParamError) {
      throw error;
    }

    logger.error('Error in product notification fraud check', error);
    return {
      openAIResult: {},
      isPotentialFraud: false,
      hasExternalLinks: false,
      reason: 'Fraud check failed - allowing message',
      fraudCheckStatus: 'ERROR',
    };
  }
};

module.exports = {
  checkPlainTextForExternalLinks,
  createFraudCheckPromptForPlainText,
  consultOpenAPIForPlainTextFraud,
  checkProductNotificationFraud,
};
