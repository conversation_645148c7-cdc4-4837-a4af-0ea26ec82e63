const logger = require('../logger.service');
const CommunityProductChangeLogsModel = require('../../models/product/communityProductChangeLogs.model');
const CommunityProductModel = require('../../models/product/communityProduct.model');
const {
  PRODUCT_CHANGE_NOTI_STATUS,
  PRODUCT_CHANGE_LOG_TYPE,
  PRODUCT_TYPE,
  PRODUCT_STATUS,
} = require('./constants');
const { CHANGE_LOG_NOTIFICATION_QUEUE_URL } = require('../../config');
const { sendMessageToSQSQueue } = require('../../handlers/sqs.handler');
const { handlers } = require('./handlers');
const CommunitySubscriptionModel = require('../../communitiesAPI/models/communitySubscriptions.model');
const {
  COMMUNITY_SUBSCRIPTION_STATUSES,
} = require('../../constants/common');
const { checkProductNotificationFraud } = require('./fraud');
const { ParamError } = require('../../utils/error.util');

/**
 * Log community product changes for auditing (local version)
 * @param {Object} params
 * @param {String} params.productType
 * @param {String|ObjectId} params.entityObjectId
 * @param {Object} params.fieldsChanged - The fieldsChanged object
 * @param {Object} params.beforeData - The product data before update
 * @param {Object} params.afterData - The product data after update
 * @param {String|ObjectId} params.operatorLearnerObjectId
 * @param {String} params.notiStatus
 * @param {Object} [params.metadata] - Additional metadata (optional)
 */
exports.addCommunityProductLog = async ({
  communityObjectId,
  communityCode,
  productType,
  entityObjectId,
  changeLogType,
  fieldsChanged = [],
  beforeData = {},
  afterData = {},
  operatorLearnerObjectId,
  metadata,
}) => {
  // Check if this product has member signup
  let product = await CommunityProductModel.findOne({
    productType,
    entityObjectId,
  }).lean();
  if (!product) {
    // in case the product has deleted before we handle the change log,
    // the previous product data is stored in the metadata
    product = metadata.product;
  }

  if (changeLogType === PRODUCT_CHANGE_LOG_TYPE.PRODUCT_PUBLISHED) {
    const hasMember = await CommunitySubscriptionModel.exists({
      communityCode,
      $or: [
        { status: COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT },
        {
          $and: [
            { status: COMMUNITY_SUBSCRIPTION_STATUSES.CANCELLED },
            { cancelledAt: { $gte: new Date() } },
          ],
        },
      ],
    });
    if (!hasMember) {
      return {};
    }
  } else if (product.status === PRODUCT_STATUS.PUBLISHED) {
    if (
      // if its session, we don't need to check total member count,
      // because we always rely on bookings that in upcoming days to send notification
      productType !== PRODUCT_TYPE.SESSION
    ) {
      const handler = handlers[productType];
      if (!handler) {
        throw new Error(
          `No handler found for product type: ${productType}`
        );
      }
      // For product update/cancel, we only log the change if there is member signup for this product
      const memberCount = await handler.getMemberCount(entityObjectId);

      if (memberCount <= 0) {
        return {};
      }
    }
  } else {
    // For unpublished product, we don't need to record change log
    return {};
  }

  // Add change details for the fields that changed
  const changeDetails = new Map(
    fieldsChanged.map((field) => [
      field,
      { before: beforeData[field], after: afterData[field] },
    ])
  );

  // This log will be used for notification that triggered by CMs
  const changeLog = await CommunityProductChangeLogsModel.create({
    communityObjectId,
    productType,
    entityObjectId,
    changeLogType,
    operatorLearnerObjectId,
    notiStatus: PRODUCT_CHANGE_NOTI_STATUS.DRAFT,
    metadata,
    changeDetails,
  });

  if (changeLogType === PRODUCT_CHANGE_LOG_TYPE.EVENT_RECORD_ADDED) {
    await this.notifyByChangeLogType({ changeLogId: changeLog._id });
  }

  return changeLog;
};

exports.notifyByChangeLogType = async ({
  changeLogId,
  notifyMessage = '',
}) => {
  const changeLog = await CommunityProductChangeLogsModel.findById(
    changeLogId
  ).lean();
  if (!changeLog) throw new Error('Change log not found');
  if (changeLog.notiStatus !== PRODUCT_CHANGE_NOTI_STATUS.DRAFT) {
    throw new ParamError('Change log notification message has processed');
  }

  // Perform fraud check on notify message
  const fraudResult = await checkProductNotificationFraud({
    notifyMessage,
  });

  // Determine if message should be blocked
  let notiStatus = PRODUCT_CHANGE_NOTI_STATUS.PROCESSING;
  if (fraudResult?.isPotentialFraud) {
    notiStatus = PRODUCT_CHANGE_NOTI_STATUS.REVIEWING;
    logger.warn(
      `Product notification flagged for fraud review|changeLogId=${changeLogId}|reason=${fraudResult.reason}`
    );
  }

  await CommunityProductChangeLogsModel.updateOne(
    { _id: changeLogId },
    {
      notiStatus,
      notifyMessage,
      fraudCheckResult: {
        probability: fraudResult?.openAIResult?.probability ?? 0,
        reason: fraudResult?.reason ?? '',
        status: fraudResult?.fraudCheckStatus ?? '',
        checkedAt: new Date(),
      },
    }
  );

  // Only send to queue if not flagged for fraud
  if (!fraudResult.isPotentialFraud) {
    const messageBody = {
      changeLogId,
    };

    try {
      await sendMessageToSQSQueue({
        queueUrl: CHANGE_LOG_NOTIFICATION_QUEUE_URL,
        messageBody,
      });
    } catch (sqsError) {
      logger.error(
        `Failed to send notification to SQS queue|changeLogId=${changeLogId}|error=${sqsError.message}`
      );
      // Update status to indicate SQS failure
      await CommunityProductChangeLogsModel.updateOne(
        { _id: changeLogId },
        { notiStatus: PRODUCT_CHANGE_NOTI_STATUS.DRAFT }
      );
      throw sqsError;
    }
  }

  return {
    success: true,
    fraudCheck: fraudResult,
  };
};
