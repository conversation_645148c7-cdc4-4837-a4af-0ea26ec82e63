const PRODUCT_STATUS = {
  UNPUBLISHED: 'unpublished', // for draft/unpublished
  PUBLISHED: 'published',
  DELETED: 'deleted',
};

const PRODUCT_TYPE = {
  EVENT: 'EVENT',
  DIGITAL_FILES: 'DIGITAL_FILES',
  COURSE: 'COURSE',
  CHALLENGE: 'CHALLENGE',
  SESSION: 'SESSION',
  MEMBERSHIP: 'MEMBERSHIP',
};

const PRODUCT_PRICE_TYPE = {
  FREE: 'FREE',
  PAID: 'PAID',
  FLEXIBLE: 'FLEXIBLE',
};

const PRODUCT_DATA_QUERY_GROUP = {
  UPCOMING: 'UPCOMING',
  ALWAYS_ON: 'ALWAYS_ON',
  PAST: 'PAST',
  UNPUBLISHED: 'UNPUBLISHED',
  BY_PRODUCT_TYPE: 'BY_PRODUCT_TYPE',
};

const PRODUCT_SORT_FIELDS = {
  TITLE: 'title',
  PRODUCT_TYPE: 'productType',
  EARNINGS: 'earnings',
};

const PRODUCT_CHANGE_NOTI_STATUS = {
  DRAFT: 'DRAFT',
  SENT: 'SENT',
  PROCESSING: 'PROCESSING',
  FAILED: 'FAILED',
  REJECTED: 'REJECTED',
  REVIEWING: 'REVIEWING',
};

const PRODUCT_CHANGE_LOG_TYPE = {
  FIELD_UPDATED: 'FIELD_UPDATED',
  PRODUCT_PUBLISHED: 'PRODUCT_PUBLISHED',
  PRODUCT_DELETED: 'PRODUCT_DELETED',
  EVENT_RECORD_ADDED: 'EVENT_RECORD_ADDED',
  CHALLENGE_UPDATED: 'CHALLENGE_UPDATED',
};

const FRAUD_REVIEW_OPERATION = {
  APPROVE: 'approve',
  REJECT: 'reject',
};

module.exports = {
  PRODUCT_STATUS,
  PRODUCT_TYPE,
  PRODUCT_PRICE_TYPE,
  PRODUCT_DATA_QUERY_GROUP,
  PRODUCT_SORT_FIELDS,
  PRODUCT_CHANGE_NOTI_STATUS,
  PRODUCT_CHANGE_LOG_TYPE,
  FRAUD_REVIEW_OPERATION,
};
