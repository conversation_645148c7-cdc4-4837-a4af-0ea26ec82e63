const { handlers } = require('./handlers');
const CommunityProductModel = require('../../models/product/communityProduct.model');
const logger = require('../logger.service');
const { PRODUCT_STATUS } = require('./constants');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const LearnerModel = require('../../models/learners.model');
const { trackFacebookEvent } = require('../../utils/fbEventTracking.util');

async function handleFirstProductPublishTracking({
  communityObjectId,
  productData,
  productType,
  existingProductWasPublished,
  productIsPublished,
  session,
}) {
  if (!productIsPublished) return;
  if (existingProductWasPublished) return;

  try {
    const publishedProductsCount =
      await CommunityProductModel.countDocuments({
        communityObjectId,
        status: PRODUCT_STATUS.PUBLISHED,
      })
        .session(session)
        .setOptions({ readPreference: 'primary' });

    if (publishedProductsCount === 1) {
      // Get community and learner information
      const community = await CommunityModel.findById(communityObjectId, {
        createdBy: 1,
        trackingData: 1,
        config: 1,
      }).lean();

      if (!community) {
        logger.error(
          'Community not found for first product publish tracking'
        );
        return;
      }

      const learner = await LearnerModel.findOne({
        email: community.createdBy,
        isActive: true,
      }).lean();

      if (!learner) {
        logger.error(
          'Learner not found for first product publish tracking',
          {
            communityObjectId,
            createdBy: community.createdBy,
          }
        );
        return;
      }

      await trackFacebookEvent({
        learner,
        eventName: 'first_published_product',
        eventId: communityObjectId.toString(),
        trackingData: community?.trackingData || {},
        customData: {
          productType,
          communityObjectId: communityObjectId.toString(),
          entityObjectId: productData?.entityObjectId?.toString(),
          planType: community.config?.planType,
          currency: productData?.currency,
          amount: productData?.amount ? productData.amount / 100 : 0,
        },
      });
    }
  } catch (error) {
    logger.error(
      '[handleFirstProductPublishTracking] Failed to track first product publish event:',
      error
    );
  }
}

exports.syncProductData = async ({ productType, entity, session }) => {
  const handler = handlers[productType];
  if (!handler) {
    throw new Error(`No handler found for product type: ${productType}`);
  }
  logger.info(`Sync data to product table: ${productType}-${entity._id}`);

  // Handler processes the entity and returns update data
  const updateData = await handler.processEntity(entity);

  const { entityObjectId, ...otherUpdateFields } = updateData;

  const existedProduct = await CommunityProductModel.findOne({
    entityObjectId,
    productType,
  }).lean();

  if (existedProduct) {
    if (otherUpdateFields.status === PRODUCT_STATUS.DELETED) {
      // Since we will always have data in the original product collections (event,folder,..)
      // For delete status, we will just delete the record from this unified product collection
      await CommunityProductModel.deleteOne(
        { _id: existedProduct._id },
        { session }
      );
      return existedProduct;
    }

    await CommunityProductModel.updateOne(
      { _id: existedProduct._id },
      otherUpdateFields,
      { session }
    );
  } else {
    await CommunityProductModel.create([updateData], {
      session,
    });
  }

  // Track first product publish
  const existingProductWasPublished =
    existedProduct?.status === PRODUCT_STATUS.PUBLISHED;
  const productIsPublished =
    otherUpdateFields.status === PRODUCT_STATUS.PUBLISHED;

  if (!existingProductWasPublished && productIsPublished) {
    await handleFirstProductPublishTracking({
      communityObjectId: updateData.communityObjectId,
      productData: updateData,
      productType,
      existingProductWasPublished,
      productIsPublished,
      session,
    });
  }
};

exports.bulkCreateData = async ({ productType, entityList }) => {
  const handler = handlers[productType];
  if (!handler) {
    throw new Error(`No handler found for product type: ${productType}`);
  }

  // Handler processes the entity and returns update data
  const dataList = await Promise.all(
    entityList.map(async (entity) => {
      const updateData = await handler.processEntity(entity);
      return (
        updateData && {
          insertOne: {
            document: updateData,
          },
        }
      );
    })
  );

  await CommunityProductModel.bulkWrite(dataList);
};
