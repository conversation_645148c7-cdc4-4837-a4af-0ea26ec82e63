// const mongoose = require('mongoose'); // Currently unused but may be needed for ObjectId operations
const logger = require('../logger.service');
const CommunityProductChangeLogsModel = require('../../models/product/communityProductChangeLogs.model');
const {
  PRODUCT_CHANGE_NOTI_STATUS,
  FRAUD_REVIEW_OPERATION,
} = require('./constants');
const { CHANGE_LOG_NOTIFICATION_QUEUE_URL } = require('../../config');
const { sendMessageToSQSQueue } = require('../../handlers/sqs.handler');
const { ParamError } = require('../../utils/error.util');

/**
 * Get all change logs in REVIEWING status for fraud review
 * @param {Object} params
 * @param {Number} params.page - Page number (default: 1)
 * @param {Number} params.limit - Items per page (default: 15)
 * @returns {Promise<Object>} Paginated fraud review logs with metadata
 */
exports.getReviewingLogs = async ({ page = 1, limit = 15 }) => {
  const skip = (page - 1) * limit;

  const filter = { notiStatus: PRODUCT_CHANGE_NOTI_STATUS.REVIEWING };
  const matchStage = {
    $match: filter,
  };

  const lookupStages = [
    {
      $lookup: {
        from: 'community_product',
        let: {
          entityId: '$entityObjectId',
          prodType: '$productType',
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$entityObjectId', '$$entityId'] },
                  { $eq: ['$productType', '$$prodType'] },
                ],
              },
            },
          },
        ],
        as: 'product',
      },
    },
    {
      $lookup: {
        from: 'communities',
        localField: 'communityObjectId',
        foreignField: '_id',
        as: 'community',
      },
    },
  ];

  const projectStage = {
    $project: {
      changeLogId: '$_id',
      productTitle: { $arrayElemAt: ['$product.title', 0] },
      communityCode: { $arrayElemAt: ['$community.code', 0] },
      communityObjectId: '$communityObjectId',
      communityLink: { $arrayElemAt: ['$community.link', 0] },
      productType: 1,
      productLink: { $arrayElemAt: ['$product.slug', 0] },
      changeLogType: 1,
      notifyMessage: 1,
      fraudReason: '$fraudCheckResult.reason',
      fraudProbability: '$fraudCheckResult.probability',
      createdAt: 1,
    },
  };

  const sortStage = { $sort: { createdAt: -1 } };

  // Get total count
  const countResult = await CommunityProductChangeLogsModel.countDocuments(
    filter
  );
  const total = countResult || 0;

  // Get paginated data
  const dataPipeline = [
    matchStage,
    ...lookupStages,
    projectStage,
    sortStage,
    { $skip: skip },
    { $limit: limit },
  ];

  const data = await CommunityProductChangeLogsModel.aggregate(
    dataPipeline
  );

  const pages = Math.ceil(total / limit);

  return {
    reviewingLogs: data,
    meta: {
      limit,
      page,
      pages,
      total,
    },
  };
};

/**
 * Process fraud review action (approve or reject)
 * @param {Object} params
 * @param {String} params.changeLogId - ID of the change log to review
 * @param {String} params.action - Action to take (approve|reject)
 * @param {String} params.operatorEmail - Email of the operator performing the review
 * @returns {Promise<Object>} Review result with status and queue information
 */
exports.processFraudReview = async ({
  changeLogId,
  action,
  operatorEmail,
}) => {
  const changeLog = await CommunityProductChangeLogsModel.findById(
    changeLogId
  );

  if (!changeLog) {
    throw new ParamError('Change log not found');
  }

  if (changeLog.notiStatus !== PRODUCT_CHANGE_NOTI_STATUS.REVIEWING) {
    throw new ParamError('Change log is not in reviewing status');
  }

  let newStatus;
  let queueSent = false;

  if (action === FRAUD_REVIEW_OPERATION.APPROVE) {
    newStatus = PRODUCT_CHANGE_NOTI_STATUS.PROCESSING;

    // Send to SQS queue
    try {
      await sendMessageToSQSQueue({
        queueUrl: CHANGE_LOG_NOTIFICATION_QUEUE_URL,
        messageBody: { changeLogId },
      });
      queueSent = true;

      logger.info(
        `Fraud review approved and sent to SQS|changeLogId=${changeLogId}|operator=${operatorEmail}`
      );
    } catch (error) {
      logger.error(
        `Failed to send approved message to SQS|changeLogId=${changeLogId}|operator=${operatorEmail}|error=${error.message}`
      );
      newStatus = PRODUCT_CHANGE_NOTI_STATUS.DRAFT; // Revert to draft for retry
      throw error;
    }
  } else if (action === FRAUD_REVIEW_OPERATION.REJECT) {
    newStatus = PRODUCT_CHANGE_NOTI_STATUS.REJECTED;

    logger.info(
      `Fraud review rejected|changeLogId=${changeLogId}|operator=${operatorEmail}`
    );
  } else {
    throw new ParamError(
      `Invalid action. Must be one of: ${Object.values(
        FRAUD_REVIEW_OPERATION
      ).join(', ')}`
    );
  }

  // Update change log status
  await CommunityProductChangeLogsModel.updateOne(
    { _id: changeLogId },
    {
      notiStatus: newStatus,
      reviewedAt: new Date(),
      reviewedBy: operatorEmail,
    }
  );

  return {
    changeLogId,
    newStatus,
    queueSent,
  };
};
