/* eslint-disable no-param-reassign */
const ObjectId = require('mongoose').Types.ObjectId;
const CommunityProductModel = require('../../models/product/communityProduct.model');
const CommunityModel = require('@/src/communitiesAPI/models/community.model');

const {
  PRODUCT_TYPE,
  PRODUCT_STATUS,
  PRODUCT_DATA_QUERY_GROUP,
  PRODUCT_SORT_FIELDS,
} = require('./constants');
const folderViewerBatchMetadataModel = require('@/src/models/batchMetadata/folderViewerBatchMetadata.model');
const eventAttendeeBatchMetadataModel = require('@/src/models/batchMetadata/eventAttendeeBatchMetadata.model');
const programParticipantBatchMetadataModel = require('@/src/models/batchMetadata/programParticipantBatchMetadata.model');
const sessionAttendeeBatchMetadataModel = require('@/src/models/batchMetadata/sessionAttendeeBatchMetadata.model');
const {
  hgetAsync,
  hsetAsync,
  expireAsync,
} = require('@/src/awsRedisClient');
const { PROGRAM_CHALLENGE_TYPE } = require('../program/constants');
const { handlers } = require('./handlers');
const regexUtils = require('../../utils/regex.util');
const PublishProductUsageService = require('../featurePermissions/publishProductUsage.service');

const getSortQuery = ({ sortBy, sortOrder }) => {
  if (!sortBy || !sortOrder) {
    return null;
  }
  if (sortBy === PRODUCT_SORT_FIELDS.EARNINGS) {
    return { 'earningAnalytics.revenueInLocalCurrency': sortOrder };
  }
  return { [sortBy]: sortOrder };
};

const enrichResults = async (results) => {
  // For each result, enrich with memberCount if type is EVENT
  const enrichedResults = await Promise.all(
    results.map(async (item) => {
      const handler = handlers[item.productType];
      const memberCount = await handler.getMemberCount(
        item.entityObjectId
      );
      const otherMetadata = await handler.getMetadata(item);
      return {
        ...item,
        memberCount,
        // if item has isVisible field, use it; otherwise default to true
        ...otherMetadata,
      };
    })
  );
  return enrichedResults;
};

const getQueryByPurchaseType = ({ productType, timingStatus, now }) => {
  // Notes: always sort by status, then time (updateTime, startTime)
  // To always return published items first
  switch (productType) {
    case PRODUCT_TYPE.COURSE:
    case PRODUCT_TYPE.DIGITAL_FILES:
    case PRODUCT_TYPE.SESSION:
      return {
        query: {
          productType,
        },
        sort: { status: 1, updatedAt: -1 },
      };

    case PRODUCT_TYPE.EVENT:
      if (timingStatus === PRODUCT_DATA_QUERY_GROUP.PAST) {
        return {
          query: {
            productType,
            'metadata.endTime': { $lt: now },
          },
          sort: { status: 1, 'metadata.startTime': -1 },
        };
      }
      return {
        query: {
          productType,
          'metadata.endTime': { $gte: now },
        },
        sort: { status: 1, 'metadata.startTime': 1 },
      };
    case PRODUCT_TYPE.CHALLENGE:
      if (timingStatus === PRODUCT_DATA_QUERY_GROUP.PAST) {
        return {
          query: {
            productType,
            'metadata.challengeType': PROGRAM_CHALLENGE_TYPE.FIXED,
            'metadata.endTime': { $lt: now },
          },
          sort: { status: 1, 'metadata.startTime': -1 },
        };
      }
      if (timingStatus === PRODUCT_DATA_QUERY_GROUP.UPCOMING) {
        return {
          query: {
            $or: [
              {
                productType,
                'metadata.challengeType': PROGRAM_CHALLENGE_TYPE.ALWAYS_ON,
              },
              {
                productType,
                'metadata.challengeType': PROGRAM_CHALLENGE_TYPE.FIXED,
                'metadata.endTime': { $gte: now },
              },
            ],
          },
        };
      }

      return {
        query: {
          productType,
          'metadata.challengeType': PROGRAM_CHALLENGE_TYPE.ALWAYS_ON,
        },
        sort: { status: 1, updatedAt: -1 },
      };

    default:
      throw new Error(`Invalid product type: ${productType}`);
  }
};
const getQueriesByGroup = async ({
  communityObjectId,
  productType,
  status,
  priceTypes,
  timingStatus,
  now,
  sortBy,
  sortOrder,
  isVisible,
}) => {
  const baseQuery = {
    communityObjectId: new ObjectId(communityObjectId),
    ...(priceTypes?.length && { priceType: { $in: priceTypes } }),
    ...(status && { status }),
    ...(isVisible && { isVisible: { $ne: false } }),
  };
  const sortQuery = getSortQuery({ sortBy, sortOrder });

  let queriesByGroup;
  if (productType) {
    const queryByPurchaseType = getQueryByPurchaseType({
      productType,
      timingStatus,
      now,
    });
    queriesByGroup = {
      [PRODUCT_DATA_QUERY_GROUP.BY_PRODUCT_TYPE]: {
        query: {
          ...baseQuery,
          ...queryByPurchaseType.query,
        },
        sort: sortQuery || queryByPurchaseType.sort,
      },
    };
  } else if (sortBy && sortBy) {
    queriesByGroup = {
      [PRODUCT_DATA_QUERY_GROUP.BY_PRODUCT_TYPE]: {
        query: {
          ...baseQuery,
        },
        sort: sortQuery,
      },
    };
  } else {
    queriesByGroup = {
      [PRODUCT_DATA_QUERY_GROUP.UPCOMING]: {
        query: {
          ...baseQuery,
          status: PRODUCT_STATUS.PUBLISHED,
          $or: [
            { productType: PRODUCT_TYPE.EVENT },
            {
              productType: PRODUCT_TYPE.CHALLENGE,
              'metadata.challengeType': PROGRAM_CHALLENGE_TYPE.FIXED,
            },
          ],
          'metadata.endTime': { $gte: now },
        },
        sort: { 'metadata.startTime': 1 },
      },
      [PRODUCT_DATA_QUERY_GROUP.ALWAYS_ON]: {
        query: {
          ...baseQuery,
          status: PRODUCT_STATUS.PUBLISHED,
          $or: [
            {
              productType: {
                $in: [
                  PRODUCT_TYPE.COURSE,
                  PRODUCT_TYPE.DIGITAL_FILES,
                  PRODUCT_TYPE.SESSION,
                ],
              },
            },
            {
              productType: PRODUCT_TYPE.CHALLENGE,
              'metadata.challengeType': PROGRAM_CHALLENGE_TYPE.ALWAYS_ON,
            },
          ],
        },
        sort: { updatedAt: -1 },
      },
      [PRODUCT_DATA_QUERY_GROUP.PAST]: {
        query: {
          ...baseQuery,
          status: PRODUCT_STATUS.PUBLISHED,
          $or: [
            { productType: PRODUCT_TYPE.EVENT },
            {
              productType: PRODUCT_TYPE.CHALLENGE,
              'metadata.challengeType': PROGRAM_CHALLENGE_TYPE.FIXED,
            },
          ],
          'metadata.endTime': { $lt: now },
        },
        sort: { 'metadata.startTime': -1 },
      },
      [PRODUCT_DATA_QUERY_GROUP.UNPUBLISHED]: {
        query: {
          ...baseQuery,
          status: PRODUCT_STATUS.UNPUBLISHED,
        },
        sort: { updatedAt: -1 },
      },
    };
    if (status === PRODUCT_STATUS.PUBLISHED) {
      delete queriesByGroup[PRODUCT_DATA_QUERY_GROUP.UNPUBLISHED];
    } else if (status === PRODUCT_STATUS.UNPUBLISHED) {
      delete queriesByGroup[PRODUCT_DATA_QUERY_GROUP.UPCOMING];
      delete queriesByGroup[PRODUCT_DATA_QUERY_GROUP.PAST];
      delete queriesByGroup[PRODUCT_DATA_QUERY_GROUP.ALWAYS_ON];
    }
  }

  return queriesByGroup;
};

const buildProductSearchStage = async ({
  communityObjectId,
  searchString,
  status,
}) => {
  const searchStage = {
    index: 'unified_product_title_index',
    compound: {
      should: [],
      filter: [
        {
          equals: {
            path: 'communityObjectId',
            value: new ObjectId(communityObjectId),
          },
        },
      ],
    },
  };

  if (searchString) {
    const searchWithEscapedRegexSign =
      regexUtils.escapeRegExp(searchString);
    searchStage.compound.should.push(
      // For the title with lots of words case, ngram might not work fine to get correct data
      // So we add phrase search with higher boost score to get more accurate data

      // Exact phrase match (works better with your nGram setup)
      {
        phrase: {
          path: 'title',
          query: searchWithEscapedRegexSign,
          slop: 0, // No word gaps allowed
          score: {
            boost: { value: 10 },
          },
        },
      },
      // Text search - normal priority
      {
        text: {
          path: 'title',
          query: searchWithEscapedRegexSign,
          score: {
            boost: { value: 1.0 }, // Normal importance
          },
        },
      }
    );
  }

  if (status) {
    searchStage.compound.filter.push({
      text: {
        path: 'status',
        query: status,
      },
    });
  }

  return searchStage;
};

const queryProductWithSearchString = async ({
  communityObjectId,
  pageNo,
  pageSize,
  sortBy,
  sortOrder,
  searchString,
  productType,
  status,
  priceTypes,
  timingStatus,
}) => {
  const now = new Date();
  const skip = (pageNo - 1) * pageSize;
  const searchStage = await buildProductSearchStage({
    communityObjectId,
    searchString,
    status,
  });
  let queryByPurchaseType = {
    query: {},
    sort: { status: 1, updatedAt: -1 },
  };
  if (productType) {
    queryByPurchaseType = getQueryByPurchaseType({
      productType,
      timingStatus,
      now,
    });
  }
  if (priceTypes && priceTypes.length > 0) {
    queryByPurchaseType.query = {
      ...queryByPurchaseType.query,
      priceType: { $in: priceTypes },
    };
  }

  const searchQuery = [
    {
      $search: searchStage,
    },
    {
      $addFields: {
        searchScore: { $meta: 'searchScore' },
      },
    },
    {
      $match: {
        // Filter out low-scoring matches
        searchScore: { $gte: 5 }, // Adjust based on testing
      },
    },
    ...(queryByPurchaseType
      ? [{ $match: queryByPurchaseType.query }]
      : []),
  ];
  const sortQuery = getSortQuery({ sortBy, sortOrder });

  const sort = sortQuery || queryByPurchaseType?.sort;
  const pipeline = [
    ...searchQuery,
    {
      $sort: sort,
    },
    {
      $skip: skip,
    },
    {
      $limit: pageSize,
    },
  ];

  const results = await CommunityProductModel.aggregate(pipeline);
  // For each result, enrich with memberCount, folderItemCounts
  const enrichedResults = await enrichResults(results);

  if (enrichedResults.length === 0) {
    return {
      meta: {
        limit: pageSize,
        page: pageNo,
        pages: 0,
        total: 0,
      },
      products: [],
    };
  }

  const totalCountResult = await CommunityProductModel.aggregate([
    ...searchQuery,
    {
      $count: 'total',
    },
  ]);

  const total = totalCountResult[0].total;

  return {
    meta: {
      limit: pageSize,
      page: pageNo,
      pages: Math.ceil(total / pageSize),
      total,
    },
    products: enrichedResults,
  };
};

const queryProductWithoutSearchString = async ({
  communityObjectId,
  pageNo,
  pageSize,
  sortBy,
  sortOrder,
  productType,
  status,
  priceTypes,
  timingStatus,
  isVisible = false,
}) => {
  const now = new Date();
  const skip = (pageNo - 1) * pageSize;
  const queriesByGroup = await getQueriesByGroup({
    communityObjectId,
    productType,
    status,
    priceTypes,
    timingStatus,
    now,
    sortBy,
    sortOrder,
    isVisible,
  });

  // Step 1: Count items in each group
  const ranges = [];

  // Must do it in order, cus this will affect soring logic
  for await (const [group, { query }] of Object.entries(queriesByGroup)) {
    const count = await CommunityProductModel.countDocuments(query);
    ranges.push({ group, count });
  }

  let cumulative = 0;
  ranges.forEach((r) => {
    r.from = cumulative;
    r.to = cumulative + r.count - 1;
    cumulative += r.count;
  });
  if (cumulative === 0) {
    return {
      meta: {
        limit: pageSize,
        page: pageNo,
        pages: 0,
        total: 0,
      },
      products: [],
    };
  }

  // startIndex = 0
  // endIndex = 4
  const startIndex = skip;
  const endIndex = skip + pageSize - 1;

  /*
     groupsToQuery = [
      {
        "group": "upcoming",
        "count": 4,
        "from": 0,
        "to": 3
      },
      {
        "group": "alwaysOn",
        "count": 0,
        "from": 4,
        "to": 3
      },
      {
        "group": "past",
        "count": 20,
        "from": 4,
        "to": 23
      }
    ]
   */
  const groupsToQuery = ranges.filter(
    (r) => !(r.to < startIndex || r.from > endIndex)
  );

  // - First group
  //    groupSkip = 0, groupLimit = 3 - 0 + 1 = 4
  // - Second group: dont have item, skip
  // - Third group
  //    groupSkip = 0, groupLimit = 4 - 4 + 1 = 1
  const queries = await Promise.all(
    groupsToQuery.map(async (group) => {
      const groupSkip = Math.max(startIndex - group.from, 0);
      const groupLimit =
        Math.min(group.to, endIndex) - (group.from + groupSkip) + 1;

      const { query, sort } = queriesByGroup[group.group];
      const results = await CommunityProductModel.find(query)
        .sort(sort)
        .skip(groupSkip)
        .limit(groupLimit)
        .lean();

      // For each result, enrich with memberCount, folderItemCounts
      const enrichedResults = await enrichResults(results);

      return enrichedResults;
    })
  );

  const paginatedResults = queries.flat();

  return {
    meta: {
      limit: pageSize,
      page: pageNo,
      pages: Math.ceil(cumulative / pageSize),
      total: cumulative,
    },
    products: paginatedResults,
  };
};

exports.getTopSellingProducts = async ({ communityObjectId }) => {
  const cacheKey = `community:${communityObjectId}:top_products`;
  const cacheField = 'data';
  const TTL_SECONDS = 1 * 60 * 60; // 1 hour
  const TOP_LIMIT = 3;

  // Check if the result is cached
  const cachedResult = await hgetAsync(cacheKey, cacheField);
  if (cachedResult) {
    const top3Products = JSON.parse(cachedResult);
    // If the cache has at least 3 products, return them
    if (Array.isArray(top3Products) && top3Products.length === TOP_LIMIT) {
      return top3Products;
    }
    // If there are fewer than 3 products, we need to fetch from the database
  }

  const activeProductsFilter = {
    communityObjectId: new ObjectId(communityObjectId),
    status: PRODUCT_STATUS.PUBLISHED,
    isVisible: { $ne: false },
    $or: [
      {
        productType: PRODUCT_TYPE.EVENT,
        'metadata.endTime': { $gte: new Date() },
      },
      {
        productType: PRODUCT_TYPE.CHALLENGE,
        'metadata.challengeType': PROGRAM_CHALLENGE_TYPE.FIXED,
        'metadata.endTime': { $gte: new Date() },
      },
      {
        productType: {
          $in: [
            PRODUCT_TYPE.COURSE,
            PRODUCT_TYPE.DIGITAL_FILES,
            PRODUCT_TYPE.SESSION,
          ],
        },
      },
      {
        productType: PRODUCT_TYPE.CHALLENGE,
        'metadata.challengeType': PROGRAM_CHALLENGE_TYPE.ALWAYS_ON,
      },
    ],
  };

  const allProducts = await CommunityProductModel.find(
    activeProductsFilter
  ).lean();

  if (allProducts.length === 0) {
    return [];
  }
  const idsByType = allProducts.reduce((acc, p) => {
    const t = p.productType; // e.g. 'course' | 'event' | 'session' | 'digital_files' etc
    if (!acc[t]) acc[t] = [];
    acc[t].push(p.entityObjectId);
    return acc;
  }, {});

  // Helper to aggregate totals for a given batch model
  const sumTotals = async (Model, ids) => {
    if (!ids || ids.length === 0) return Promise.resolve([]);

    const pipeline = [
      {
        $match: {
          communityObjectId: new ObjectId(communityObjectId),
          entityObjectId: { $in: ids },
        },
      },
      {
        $group: {
          _id: '$entityObjectId',
          totalMembers: { $sum: '$currentSize' },
        },
      },
      // sort and limit to top 3
      {
        $sort: { totalMembers: -1 },
      },
      {
        $limit: TOP_LIMIT,
      },
    ];

    const data = await Model.aggregate(pipeline);
    return data;
  };

  // Hit all batch collections only for needed IDs
  const [
    courseTotal,
    digitalFilesTotal,
    eventTotals,
    programTotals,
    sessionTotals,
  ] = await Promise.all([
    sumTotals(folderViewerBatchMetadataModel, idsByType.COURSE),
    sumTotals(folderViewerBatchMetadataModel, idsByType.DIGITAL_FILES),
    sumTotals(eventAttendeeBatchMetadataModel, idsByType.EVENT),
    sumTotals(programParticipantBatchMetadataModel, idsByType.PROGRAM),
    sumTotals(sessionAttendeeBatchMetadataModel, idsByType.SESSION),
  ]);

  const totalsCombined = [
    ...courseTotal.map((r) => ({
      ...r,
      productType: PRODUCT_TYPE.COURSE,
    })),
    ...digitalFilesTotal.map((r) => ({
      ...r,
      productType: PRODUCT_TYPE.DIGITAL_FILES,
    })),
    ...eventTotals.map((r) => ({ ...r, productType: PRODUCT_TYPE.EVENT })),
    ...programTotals.map((r) => ({
      ...r,
      productType: PRODUCT_TYPE.CHALLENGE,
    })),
    ...sessionTotals.map((r) => ({
      ...r,
      productType: PRODUCT_TYPE.SESSION,
    })),
  ];
  const top3Totals = totalsCombined
    .sort((a, b) => b.totalMembers - a.totalMembers)
    .slice(0, TOP_LIMIT);

  const prodMap = new Map(
    allProducts.map((p) => [p.entityObjectId.toString(), p])
  );

  let top3 = top3Totals
    .map(({ _id, totalMembers, productType }) => {
      const prod = prodMap.get(_id.toString());
      if (!prod) return null;
      return { ...prod, totalMembers, productType };
    })
    .filter(Boolean);

  if (top3.length < TOP_LIMIT) {
    const missing = TOP_LIMIT - top3.length;
    const latest = await CommunityProductModel.find({
      ...activeProductsFilter,
      entityObjectId: { $nin: top3.map((p) => p.entityObjectId) },
    })
      .sort({ updatedAt: -1 })
      .limit(missing)
      .lean();

    top3 = [...top3, ...latest];
  }

  const enriched = await enrichResults(top3);

  // Cache the result
  await hsetAsync(cacheKey, cacheField, JSON.stringify(enriched));

  await expireAsync(cacheKey, TTL_SECONDS);

  return enriched;
};

exports.getAllProductsForReordering = async ({ communityObjectId }) => {
  const { config } = await CommunityModel.findById(communityObjectId, {
    _id: 1,
    config: 1,
  }).lean();

  if (!config?.customProductOrdering) {
    const queriesByGroup = await getQueriesByGroup({
      communityObjectId,
      productType: null,
      status: PRODUCT_STATUS.PUBLISHED,
      priceTypes: null,
      now: new Date(),
    });

    const queryOrder = [queriesByGroup.UPCOMING, queriesByGroup.ALWAYS_ON];

    const products = [];
    for await (const group of queryOrder) {
      const groupProducts = await CommunityProductModel.find(group.query)
        .sort(group.sort)
        .lean();

      products.push(...groupProducts);
    }

    return products;
  }

  const match = {
    communityObjectId: new ObjectId(communityObjectId),
    status: PRODUCT_STATUS.PUBLISHED,
  };
  const query = {
    ...match,
    $or: [
      // Any product with endTime in the future
      { 'metadata.endTime': { $gte: new Date() } },
      // Any product without an endTime at all
      { 'metadata.endTime': null },
      { 'metadata.endTime': { $exists: false } },
    ],
  };
  const products = await CommunityProductModel.find(query)
    .sort({ index: 1 })
    .lean();

  return products;
};

exports.changeDisplayProducts = async ({
  communityObjectId,
  products,
}) => {
  const communityId = new ObjectId(communityObjectId);

  let hasIndexUpdate = false;

  const bulkUpdateOps = products
    .map((product) => {
      const set = {};
      if (product.index != null) {
        set.index = product.index;
        hasIndexUpdate = true;
      }
      if (product.isVisible != null) set.isVisible = product.isVisible;
      if (Object.keys(set).length === 0) return null; // nothing to update for this item

      return {
        updateOne: {
          filter: {
            communityObjectId: communityId,
            _id: new ObjectId(product.productId),
          },
          update: { $set: set },
        },
      };
    })
    .filter(Boolean);

  const result = await CommunityProductModel.bulkWrite(bulkUpdateOps);
  if (result.matchedCount !== bulkUpdateOps.length) {
    throw new Error('Some products were not found for this community');
  }

  // only flip the flag if an index update was actually requested
  if (hasIndexUpdate) {
    await CommunityModel.updateOne(
      { _id: communityId },
      { $set: { 'config.customProductOrdering': true } }
    );
  }

  return {
    isReordered: true,
    modifiedCount: result.modifiedCount,
  };
};

exports.isUserEnrolledInProduct = async ({
  learnerId,
  productId,
  productType,
  isProductFree,
}) => {
  if (!learnerId || !productId) {
    return {
      isEnrolled: false,
    };
  }

  const handler = handlers[productType];
  const { isEnrolled, entityEnrolmentInfo } =
    await handler.checkMemberAccess(productId, learnerId, isProductFree);

  return {
    isEnrolled,
    entityEnrolmentInfo,
  };
};

exports.getExtraProductInfo = async ({
  productType,
  productId,
  enrolmentInfo,
}) => {
  const handler = handlers[productType];
  // if it doesn't have a handler, return empty object then there is no extra info needed
  if (!handler.getExtraProductInfo) {
    return {};
  }
  const extraInfo = await handler.getExtraProductInfo(
    productId,
    enrolmentInfo
  );

  return extraInfo;
};

exports.getProducts = async ({
  communityObjectId,
  pageNo,
  pageSize,
  sortBy,
  sortOrder,
  searchString,
  productType,
  status,
  priceTypes,
  timingStatus,
  isVisible = false,
}) => {
  const priceTypeArr = priceTypes ? priceTypes.split(',') : null;
  let results = [];
  if (searchString) {
    results = await queryProductWithSearchString({
      communityObjectId,
      pageNo,
      pageSize,
      sortBy,
      sortOrder,
      searchString,
      productType,
      status,
      priceTypes: priceTypeArr,
      timingStatus,
    });
  } else {
    results = await queryProductWithoutSearchString({
      communityObjectId,
      pageNo,
      pageSize,
      sortBy,
      sortOrder,
      productType,
      status,
      priceTypes: priceTypeArr,
      timingStatus,
      isVisible,
    });
  }

  const community = await CommunityModel.findById(communityObjectId, {
    _id: 1,
    isPaidCommunity: 1,
    baseCurrency: 1,
    config: 1,
    featurePermissions: 1,
  }).lean();

  const finalProducts = await Promise.all(
    results.products.map(async (product) => {
      const publishSelectedProductAllowed =
        await PublishProductUsageService.publishSelectedProductAllowed(
          communityObjectId,
          product.createdAt,
          community
        );
      return {
        ...product,
        publishSelectedProductAllowed,
      };
    })
  );

  results.products = finalProducts;
  return results;
};

exports.getMemberProducts = async ({
  communityObjectId,
  pageNo,
  pageSize,
  sortBy,
  sortOrder,
  searchString,
  productType,
  status,
  priceTypes,
  timingStatus,
  isVisible,
}) => {
  const communityId = new ObjectId(communityObjectId);
  const community = await CommunityModel.findById(communityId, {
    _id: 1,
    isPaidCommunity: 1,
    baseCurrency: 1,
    'config.customProductOrdering': 1,
    featurePermissions: 1,
  }).lean();

  if (community?.config?.customProductOrdering) {
    // Base filters; keep it equivalent between find & count
    const match = {
      communityObjectId: communityId,
      isVisible: { $ne: false },
      status: status || PRODUCT_STATUS.PUBLISHED,
      ...(productType && { productType }),
    };

    const query = {
      ...match,
    };

    if (timingStatus === PRODUCT_DATA_QUERY_GROUP.PAST) {
      query['metadata.endTime'] = { $lt: new Date() };
    } else {
      query['$or'] = [
        { 'metadata.endTime': { $gte: new Date() } },
        { 'metadata.endTime': { $exists: false } },
        { 'metadata.endTime': null },
      ];
    }

    const [products, totalResults] = await Promise.all([
      CommunityProductModel.find(query)
        .sort({ index: 1 }) // stable ordering
        .skip((pageNo - 1) * pageSize)
        .limit(pageSize)
        .lean(),
      CommunityProductModel.countDocuments(query),
    ]);

    const finalProducts = await enrichResults(products);

    return {
      meta: {
        limit: pageSize,
        page: pageNo,
        pages: Math.ceil(totalResults / pageSize) || 1,
        total: totalResults,
      },
      products: finalProducts,
    };
  }

  return this.getProducts({
    communityObjectId,
    pageNo,
    pageSize,
    sortBy,
    sortOrder,
    searchString,
    productType,
    status,
    priceTypes,
    timingStatus,
    isVisible,
  });
};

exports.addMetadataForCommunity = async ({ communityObjectId }) => {
  // compute the following metadata for the community
  // asActiveChallenges, hasPastChallenges. hasUpcomingEvents + hasPastEvents.

  const commonQuery = {
    status: PRODUCT_STATUS.PUBLISHED,
    communityObjectId: new ObjectId(communityObjectId),
    isVisible: { $ne: false },
  };
  const [upcomingChallenges, pastChallenges, upcomingEvents, pastEvents] =
    await Promise.all([
      CommunityProductModel.findOne({
        ...commonQuery,
        productType: PRODUCT_TYPE.CHALLENGE,
        $or: [
          {
            'metadata.challengeType': PROGRAM_CHALLENGE_TYPE.FIXED,
            'metadata.endTime': { $gte: new Date() },
          },
          {
            'metadata.challengeType': PROGRAM_CHALLENGE_TYPE.ALWAYS_ON,
          },
        ],
      })
        .select('_id')
        .lean(),
      CommunityProductModel.findOne({
        ...commonQuery,
        productType: PRODUCT_TYPE.CHALLENGE,
        'metadata.challengeType': PROGRAM_CHALLENGE_TYPE.FIXED,
        'metadata.endTime': { $lt: new Date() },
      })
        .select('_id')
        .lean(),
      CommunityProductModel.findOne({
        ...commonQuery,
        productType: PRODUCT_TYPE.EVENT,
        'metadata.endTime': { $gte: new Date() },
      })
        .select('_id')
        .lean(),
      CommunityProductModel.findOne({
        ...commonQuery,
        productType: PRODUCT_TYPE.EVENT,
        'metadata.endTime': { $lt: new Date() },
      })
        .select('_id')
        .lean(),
    ]);

  return {
    hasActiveChallenges: !!upcomingChallenges,
    hasPastChallenges: !!pastChallenges,
    hasUpcomingEvents: !!upcomingEvents,
    hasPastEvents: !!pastEvents,
  };
};
