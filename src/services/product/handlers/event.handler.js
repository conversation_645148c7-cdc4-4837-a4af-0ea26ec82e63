const {
  PRODUCT_TYPE,
  PRODUCT_STATUS,
  PRODUCT_PRICE_TYPE,
} = require('../constants');
const {
  EVENT_STATUS,
  COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES,
  COMMUNITY_EVENT_ACCESS_TYPES,
} = require('../../../communitiesAPI/constants');
const { ACCESS_TYPE } = require('../../pricing/constants');
const { PRICE_TYPE } = require('../../../constants/common');
const EventAttendeesModel = require('../../../communitiesAPI/models/eventAttendees.model');
const {
  generateCoverMediaItems,
} = require('../../../utils/multipleCoverMediaItems.util');
const {
  COVER_MEDIA_ENTITY_TYPES,
} = require('../../../constants/coverMediaItems.constant');
const CommunityEventsModel = require('@/src/communitiesAPI/models/communityEvents.model');

exports.processEntity = async (entity) => {
  let status;
  switch (entity.status) {
    case EVENT_STATUS.DRAFT:
      status = PRODUCT_STATUS.UNPUBLISHED;
      break;
    case EVENT_STATUS.PUBLISHED:
      status = PRODUCT_STATUS.PUBLISHED;
      break;
    case EVENT_STATUS.DELETED:
      status = PRODUCT_STATUS.DELETED;
      break;
    default:
      throw new Error(`Invalid event status: ${entity.status}`);
  }
  let priceType;
  if (entity.access === ACCESS_TYPE.FREE) {
    priceType = PRODUCT_PRICE_TYPE.FREE;
  } else if (entity.pricingConfig?.priceType === PRICE_TYPE.FLEXIBLE) {
    priceType = PRODUCT_PRICE_TYPE.FLEXIBLE;
  } else {
    priceType = PRODUCT_PRICE_TYPE.PAID;
  }

  const product = {
    communityObjectId: entity.communities[0],
    entityObjectId: entity._id,
    productType: PRODUCT_TYPE.EVENT,
    status,
    title: entity.title,
    coverImg: entity.bannerImg,
    coverMediaItems: entity.coverMediaItems,
    slug: entity.slug,
    priceType,
    pricingConfig: entity.pricingConfig,
    amount: entity.amount,
    currency: entity.currency,
    earningAnalytics: entity.earningAnalytics,
    metadata: {
      timezoneId: entity.timezoneId,
      startTime: entity.startTime,
      endTime: entity.endTime,
      location: entity.type,
    },
    createdAt:
      entity.createdAt ??
      entity.lastModifiedTimeStamp ??
      entity.updatedAt ??
      entity.startTime ??
      new Date(),
    updatedAt:
      entity.lastModifiedTimeStamp ??
      entity.updatedAt ??
      entity.startTime ??
      new Date(),
  };

  return product;
};

exports.getMemberCount = async (eventId) => {
  const eventAttendeesList = await EventAttendeesModel.find({
    eventObjectId: eventId,
    status: {
      $in: [
        COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING,
        COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.NOT_GOING,
      ],
    },
  }).lean();

  const attendeeQuantity = eventAttendeesList.reduce(
    (acc, attendee) => acc + (attendee.quantity ?? 1),
    0
  );

  return attendeeQuantity;
};

exports.getMetadata = async (entity) => {
  const coverMediaItems = await generateCoverMediaItems({
    entity,
    entityType: COVER_MEDIA_ENTITY_TYPES.EVENT,
    isCommunityManager: true,
  });
  return { coverMediaItems };
};

exports.checkMemberAccess = async (eventId, learnerObjectId) => {
  const eventAttendee = await EventAttendeesModel.findOne({
    eventObjectId: eventId,
    learnerObjectId,
    status: {
      $in: [
        COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING,
        COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING,
      ],
    },
  }).lean();

  if (!eventAttendee) {
    return { isEnrolled: false, entityEnrolmentInfo: null };
  }

  return {
    isEnrolled: !!eventAttendee,
    entityEnrolmentInfo: eventAttendee,
  };
};

exports.getExtraProductInfo = async (eventId, enrolmentInfo) => {
  const eventInfo = await CommunityEventsModel.findById(eventId)
    .select(
      'access isSoldOut hideAttendeesCount attendeeLimit isCapacitySet requiresApproval host inPersonLocation inPersonLocationMetadata recordingLink liveLink icsFileLink'
    )
    .lean();

  if (
    eventInfo?.access === COMMUNITY_EVENT_ACCESS_TYPES.PAID &&
    (!enrolmentInfo?.isEnrolled ||
      enrolmentInfo?.entityEnrolmentInfo?.status ===
        COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING)
  ) {
    delete eventInfo.liveLink;
    delete eventInfo.recordingLink;
    delete eventInfo.icsFileLink;
    delete eventInfo.chatGroupLink;
  }

  return eventInfo;
};
