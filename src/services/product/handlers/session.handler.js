const { ObjectId } = require('mongoose').Types;
const {
  PRODUCT_TYPE,
  PRODUCT_STATUS,
  PRODUCT_PRICE_TYPE,
} = require('../constants');
const {
  COMMUNITY_FOLDER_STATUS,
  COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES,
} = require('../../../communitiesAPI/constants');
const { ACCESS_TYPE } = require('../../pricing/constants');
const { PRICE_TYPE } = require('../../../constants/common');
const sessionAttendeesModel = require('../../../models/oneOnOneSessions/sessionAttendees.model');
const {
  generateCoverMediaItems,
} = require('../../../utils/multipleCoverMediaItems.util');
const {
  COVER_MEDIA_ENTITY_TYPES,
} = require('../../../constants/coverMediaItems.constant');
const communityFoldersModel = require('@/src/communitiesAPI/models/communityFolders.model');
const NameUtils = require('@/src/utils/name.util');

exports.processEntity = async (entity) => {
  let status;
  switch (entity.status) {
    case COMMUNITY_FOLDER_STATUS.UNPUBLISHED:
      status = PRODUCT_STATUS.UNPUBLISHED;
      break;
    case COMMUNITY_FOLDER_STATUS.PUBLISHED:
      status = PRODUCT_STATUS.PUBLISHED;
      break;
    case COMMUNITY_FOLDER_STATUS.DELETED:
      status = PRODUCT_STATUS.DELETED;
      break;
    default:
      throw new Error(`Invalid session status: ${entity.status}`);
  }
  let priceType;
  if (entity.access === ACCESS_TYPE.FREE) {
    priceType = PRODUCT_PRICE_TYPE.FREE;
  } else if (entity.pricingConfig?.priceType === PRICE_TYPE.FLEXIBLE) {
    priceType = PRODUCT_PRICE_TYPE.FLEXIBLE;
  } else {
    priceType = PRODUCT_PRICE_TYPE.PAID;
  }

  const product = {
    communityObjectId: entity.communityObjectId,
    entityObjectId: entity._id,
    productType: PRODUCT_TYPE.SESSION,
    status,
    title: entity.title,
    coverImg: entity.thumbnail,
    coverMediaItems: entity.coverMediaItems,
    slug: entity.resourceSlug,
    priceType,
    pricingConfig: entity.pricingConfig,
    amount: entity.amount,
    currency: entity.currency,
    earningAnalytics: entity.earningAnalytics,
    metadata: {
      durationIntervalInMinutes: entity.durationIntervalInMinutes,
    },
    createdAt: entity.createdAt,
    updatedAt: entity.updatedAt,
  };

  return product;
};

exports.getMemberCount = async (entityObjectId) => {
  const accessCount = await sessionAttendeesModel.countDocuments({
    sessionObjectId: entityObjectId,
    status: COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.BOOKED,
  });
  return accessCount;
};

exports.getMetadata = async (entity) => {
  const coverMediaItems = await generateCoverMediaItems({
    entity,
    entityType: COVER_MEDIA_ENTITY_TYPES.SESSION,
    isCommunityManager: true,
  });
  return { coverMediaItems };
};

exports.checkMemberAccess = async ({ learnerId, sessionId }) => {
  const sessionAttendee = await sessionAttendeesModel
    .findOne({
      sessionObjectId: new ObjectId(sessionId),
      attendeeLearnerObjectId: new ObjectId(learnerId),
      status: {
        $in: [
          COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.BOOKED,
          COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.PENDING,
        ],
      },
      sessionEndTime: { $gt: new Date() },
    })
    .lean();

  return {
    isEnrolled: !!sessionAttendee,
    entityEnrolmentInfo: sessionAttendee,
  };
};

exports.getExtraProductInfo = async (productId) => {
  const sessionInfo = await communityFoldersModel
    .findById(productId)
    .select('hostInfo')
    .populate(
      'hostInfo.hostLearnerObjectId',
      'firstName lastName email profileImage'
    )
    .lean();
  const { hostInfo: { hostLearnerObjectId } = {} } = sessionInfo || {};

  const fullName = NameUtils.getName(
    hostLearnerObjectId.firstName,
    hostLearnerObjectId.lastName,
    hostLearnerObjectId.email
  );

  return {
    hostInfo: {
      hostName: fullName,
      hostProfileImage: hostLearnerObjectId.profileImage,
      hostLearnerObjectId,
    },
  };
};
