const { ObjectId } = require('mongoose').Types;
const {
  PRODUCT_TYPE,
  PRODUCT_STATUS,
  PRODUCT_PRICE_TYPE,
} = require('../constants');
const {
  COMMUNITY_FOLDER_STATUS,
  communityFolderItemTypesMap,
  FOLDER_ITEM_STATUS,
} = require('../../../communitiesAPI/constants');
const { ACCESS_TYPE } = require('../../pricing/constants');
const { PRICE_TYPE } = require('../../../constants/common');
const { FOLDER_VIEWER_STATUS } = require('../../folder/constants');
const folderViewersModel = require('../../../models/product/folderViewers.model');
const communityFolderItemsModel = require('../../../communitiesAPI/models/communityFolderItems.model');
const {
  generateCoverMediaItems,
} = require('../../../utils/multipleCoverMediaItems.util');
const {
  COVER_MEDIA_ENTITY_TYPES,
} = require('../../../constants/coverMediaItems.constant');
const communityFolderPurchasesModel = require('@/src/communitiesAPI/models/communityFolderPurchases.model');

exports.processEntity = async (entity) => {
  let status;
  switch (entity.status) {
    case COMMUNITY_FOLDER_STATUS.UNPUBLISHED:
      status = PRODUCT_STATUS.UNPUBLISHED;
      break;
    case COMMUNITY_FOLDER_STATUS.PUBLISHED:
      status = PRODUCT_STATUS.PUBLISHED;
      break;
    case COMMUNITY_FOLDER_STATUS.DELETED:
      status = PRODUCT_STATUS.DELETED;
      break;
    default:
      throw new Error(`Invalid digital files status: ${entity.status}`);
  }
  let priceType;
  if (entity.access === ACCESS_TYPE.FREE) {
    priceType = PRODUCT_PRICE_TYPE.FREE;
  } else if (entity.pricingConfig?.priceType === PRICE_TYPE.FLEXIBLE) {
    priceType = PRODUCT_PRICE_TYPE.FLEXIBLE;
  } else {
    priceType = PRODUCT_PRICE_TYPE.PAID;
  }

  const product = {
    communityObjectId: entity.communityObjectId,
    entityObjectId: entity._id,
    productType: PRODUCT_TYPE.COURSE,
    status,
    title: entity.title,
    coverImg: entity.thumbnail,
    coverMediaItems: entity.coverMediaItems,
    slug: entity.resourceSlug,
    priceType,
    pricingConfig: entity.pricingConfig,
    amount: entity.amount,
    currency: entity.currency,
    earningAnalytics: entity.earningAnalytics,
    metadata: {},
    createdAt: entity.createdAt,
    updatedAt: entity.updatedAt,
  };

  return product;
};

exports.getMemberCount = async (entityObjectId) => {
  const accessCount = await folderViewersModel.countDocuments({
    folderObjectId: entityObjectId,
    status: {
      $in: [FOLDER_VIEWER_STATUS.FREE, FOLDER_VIEWER_STATUS.PAID],
    },
  });
  return accessCount;
};

exports.getMetadata = async (entity) => {
  const folderItemCount = await communityFolderItemsModel.count({
    communityFolderObjectId: entity.entityObjectId,
    type: { $ne: communityFolderItemTypesMap.SECTION },
    status: FOLDER_ITEM_STATUS.PUBLISHED,
  });

  const coverMediaItems = await generateCoverMediaItems({
    entity,
    entityType: COVER_MEDIA_ENTITY_TYPES.FOLDER,
    isCommunityManager: true,
  });

  return { folderItemCount, coverMediaItems };
};

exports.checkMemberAccess = async (folderId, learnerId, isProductFree) => {
  const folderObjectId = new ObjectId(folderId);
  const learnerObjectId = new ObjectId(learnerId);

  const Model = isProductFree
    ? folderViewersModel
    : communityFolderPurchasesModel;

  const isEnrolled = Boolean(
    await Model.findOne({ folderObjectId, learnerObjectId })
      .select('_id') // grab only the id
      .lean()
  );

  return { isEnrolled };
};
