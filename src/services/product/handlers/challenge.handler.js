const {
  PRODUCT_TYPE,
  PRODUCT_STATUS,
  PRODUCT_PRICE_TYPE,
} = require('../constants');
const { ACCESS_TYPE } = require('../../pricing/constants');
const { PRICE_TYPE } = require('../../../constants/common');
const {
  PROGRAM_STATUS,
  PARTICIPANT_PROGRAM_STATUS,
} = require('../../program/constants');
const ProgramParticipant = require('../../../models/program/programParticipant.model');
const {
  generateCoverMediaItems,
} = require('../../../utils/multipleCoverMediaItems.util');
const {
  COVER_MEDIA_ENTITY_TYPES,
} = require('../../../constants/coverMediaItems.constant');
const ProgramModel = require('@/src/models/program/program.model');

exports.processEntity = async (entity) => {
  let status;
  switch (entity.status) {
    case PROGRAM_STATUS.DRAFT:
      status = PRODUCT_STATUS.UNPUBLISHED;
      break;
    case PROGRAM_STATUS.PUBLISHED:
      status = PRODUCT_STATUS.PUBLISHED;
      break;
    case PROGRAM_STATUS.DELETED:
      status = PRODUCT_STATUS.DELETED;
      break;
    default:
      throw new Error(`Invalid session status: ${entity.status}`);
  }
  let priceType;
  if (entity.access === ACCESS_TYPE.FREE) {
    priceType = PRODUCT_PRICE_TYPE.FREE;
  } else if (entity.pricingConfig?.priceType === PRICE_TYPE.FLEXIBLE) {
    priceType = PRODUCT_PRICE_TYPE.FLEXIBLE;
  } else {
    priceType = PRODUCT_PRICE_TYPE.PAID;
  }

  const product = {
    communityObjectId: entity.communityObjectId,
    entityObjectId: entity._id,
    productType: PRODUCT_TYPE.CHALLENGE,
    status,
    title: entity.title,
    coverImg: entity.cover,
    coverMediaItems: entity.coverMediaItems,
    slug: entity.slug,
    priceType,
    pricingConfig: entity.pricingConfig,
    amount: entity.pricingConfig?.amount,
    currency: entity.pricingConfig?.currency,
    earningAnalytics: entity.earningAnalytics,
    metadata: {
      challengeType: entity.challengeType,
      startTime: entity.startTime,
      endTime: entity.endTime,
    },
    createdAt: entity.createdAt,
    updatedAt: entity.updatedAt,
  };

  return product;
};

exports.getMemberCount = async (entityObjectId) => {
  const participantCount = await ProgramParticipant.countDocuments({
    programObjectId: entityObjectId,
    status: {
      $in: [
        PARTICIPANT_PROGRAM_STATUS.COMPLETED,
        PARTICIPANT_PROGRAM_STATUS.PARTICIPATED,
        PARTICIPANT_PROGRAM_STATUS.WINNER,
      ],
    },
  });

  return participantCount;
};

exports.getMetadata = async (entity) => {
  const coverMediaItems = await generateCoverMediaItems({
    entity,
    entityType: COVER_MEDIA_ENTITY_TYPES.CHALLENGE,
    isCommunityManager: true,
  });
  return { coverMediaItems };
};

exports.checkMemberAccess = async (challengeId, learnerId) => {
  const participant = await ProgramParticipant.findOne({
    programObjectId: challengeId,
    learnerObjectId: learnerId,
    status: {
      $in: [
        PARTICIPANT_PROGRAM_STATUS.COMPLETED,
        PARTICIPANT_PROGRAM_STATUS.PARTICIPATED,
        PARTICIPANT_PROGRAM_STATUS.WINNER,
      ],
    },
  }).lean();

  return {
    isEnrolled: !!participant,
    entityEnrolmentInfo: participant,
  };
};

exports.getExtraProductInfo = async (productId) => {
  const challengeInfo = await ProgramModel.findById(productId)
    .select('canJoinAfterStart')
    .lean();
  return challengeInfo;
};
