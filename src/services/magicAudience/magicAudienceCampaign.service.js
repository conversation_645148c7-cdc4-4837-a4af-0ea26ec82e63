const { DateTime } = require('luxon');
const ObjectId = require('mongoose').Types.ObjectId;
const CommunityModel = require('../../communitiesAPI/models/community.model');
const {
  META_APP_ACCESS_TOKEN,
  NAS_IO_FRONTEND_URL,
} = require('../../config');
const MetaAdsCampaigns = require('../../models/magicAudience/metaAdsCampaigns.model');
const MetaAdsIntegration = require('../../models/magicAudience/metaAdsIntegration.model');
const MetaAdsObjects = require('../../models/magicAudience/metaAdsObjects.model');
const MetaAdsSets = require('../../models/magicAudience/metaAdsSets.model');
const PrimaryMongooseConnection = require('../../rpc/primaryMongooseConnection');
const {
  getCountryCodeByCountryName,
} = require('../countryInfoMapping.service');
const adsWalletService = require('../wallet/adsCampaign/adCampaignRevert.service');
const {
  getDeductibleWalletBalance,
} = require('../wallet/adsCampaign/getAdsBalance.service');

const {
  META_OPTIMIZATION_GOAL,
  META_ADS_STATUS,
  DEFAULT_AD_DAILY_BUDGET,
  META_ADS_OBJECTIVE,
  META_BILLING_EVENTS,
  META_AD_ENTITIES_STATUS,
  LINK_CLICK_EVENT,
  META_AD_FORMATS,
  REGULATED_COUNTRY_CODES,
  getRegionalRegulatedCategories,
} = require('./constants');
const {
  createMetaAdsCamapaign,
  createMetaAdSet,
  createMetaAdsAdObject,
  updateAdObject,
  createVideoOnMeta,
  getEstimatedDeliveryOfAdSet,
  updateAdSet,
  updateMetaAdsCampaign,
  deleteMetaAdsAdObject,
  deleteMetaAdsAdSet,
  deleteMetaAdsCampaign,
  getCampaignInsights,
  getMetaInsights,
  getUserAccessToken,
  getUsersMetaAccountPages,
  addUsersPageToBusiness,
  approveUserPageToBusiness,
  getPageInformation,
  addNasIOCommunityLinkToPage,
  getVideoUploadStatusFromMeta,
  addAgencyToTheUsersPage,
  createMetaAdsPixel,
} = require('../../rpc/metaAds.service');
const {
  getEntityInfo,
  getAdLinkUrl,
  buildAssetFeedSpec,
  computeStartAndEndTime,
  aggregateTotals,
  calcPct,
  getActionValue,
  getMetaAdsConversionEvent,
  getConversionEventName,
  getValidCountryCode,
} = require('./utils/adsData.util');
const productService = require('../common/communityProducts.service');
const logger = require('../logger.service');
const { generateAdsText } = require('./utils/adsTextGeneration.util');
const NasioMetaAdsAccounts = require('../../models/magicAudience/nasioMetaAdsAccounts.model');
const {
  checkIfEntityIsEligibleToRunACampaign,
} = require('./utils/campaignValidator.util');
const CommunityEventsModel = require('../../communitiesAPI/models/communityEvents.model');
const {
  EVENT_STATUS,
  communityLibraryStatusMap,
} = require('../../communitiesAPI/constants');
const ProgramModel = require('../../models/program/program.model');
const {
  PROGRAM_STATUS,
  PROGRAM_CHALLENGE_TYPE,
} = require('../program/constants');
const communityFoldersModel = require('../../communitiesAPI/models/communityFolders.model');
const {
  sendEmailToUser,
  sendApplicationEmail,
  sendCampaignPendingApprovalEmail,
} = require('./magicAudienceNotifications.service');
const MetaAdsCommunitiesApplications = require('../../models/magicAudience/metaAdsCommunitiesApplications.model');

const createCampaignAdSetAndAd = async ({
  campaignInfo,
  adSetInfo,
  adObjects,
  integration,
  communityInfo,
  createAdObjects = false, // if true, we will create ad objects
}) => {
  let { fbCampaignId } = campaignInfo;
  let { fbAdSetId } = adSetInfo;
  const { countryCreatedIn } = communityInfo;
  if (!fbCampaignId) {
    fbCampaignId = await createMetaAdsCamapaign({
      ...campaignInfo,
      status: META_ADS_STATUS.PAUSED, // facebook requires a PAUSED status for the campaign creation
      metaAdsIntegration: integration,
      objective: META_ADS_OBJECTIVE.OUTCOME_SALES,
    });
    await MetaAdsCampaigns.updateOne(
      { _id: campaignInfo._id },
      { $set: { fbCampaignId } }
    );
  }

  if (!fbAdSetId) {
    fbAdSetId = await createMetaAdSet({
      ...adSetInfo,
      campaignName: `${campaignInfo.campaignName} adset`,
      metaAdsCampaignId: fbCampaignId,
      metaAdsIntegration: integration,
      promotedObject: {
        pixel_id: communityInfo?.trackingPixels?.facebookPixel,
        custom_event_type: adSetInfo.conversionEvent,
      },
      status: META_ADS_STATUS.PAUSED, // facebook requires a PAUSED status for the ad set creation
      billingEvent: META_BILLING_EVENTS.IMPRESSIONS,
      countryCode: getValidCountryCode(
        await getCountryCodeByCountryName(countryCreatedIn)
      ),
    });
    await MetaAdsSets.updateOne(
      { _id: adSetInfo._id },
      { $set: { fbAdSetId } }
    );
  }

  if (!createAdObjects) {
    // if we don't want to create ad objects, we return here
    return {
      metaAdsCampaign: { ...campaignInfo, fbCampaignId },
      metaAdsAdSet: { ...adSetInfo, fbAdSetId },
      metaAdsAdObjects: adObjects,
      integration,
    };
  }
  const pageId = integration.pageId;
  const adsAccountId = integration.adsAccountId;

  const createPromises = adObjects.map(async (obj) => {
    if (obj.fbAdId) {
      return {
        localId: obj._id,
        fbAdId: obj.fbAdId, // already created
      };
    }
    const assetFeedSpec = buildAssetFeedSpec({
      adFormat: obj.adFormat,
      pageId,
      primaryTexts: obj.primaryTexts,
      headlines: obj.headlines,
      descriptions: obj.descriptions,
      linkUrls: obj.linkUrls,
      mediaUrls: obj.mediaUrls,
      videoInfo: obj.videoInfo,
    });

    const fbAdId = await createMetaAdsAdObject({
      name: `${campaignInfo.campaignName} • ${obj.adFormat}`,
      adset_id: fbAdSetId,
      creative: {
        object_story_spec: { page_id: pageId },
        asset_feed_spec: assetFeedSpec,
      },
      status: META_ADS_STATUS.PAUSED,
      access_token: META_APP_ACCESS_TOKEN,
      adsAccountId, // only once
    });

    return { localId: obj._id, fbAdId };
  });

  const createdAds = await Promise.all(createPromises);

  if (createdAds.length) {
    await MetaAdsObjects.bulkWrite(
      createdAds.map(({ localId, fbAdId }) => ({
        updateOne: {
          filter: { _id: localId },
          update: { $set: { fbAdId } },
        },
      }))
    );
  }

  const metaAdsAdObjects = adObjects.map((obj) => {
    const { fbAdId } =
      createdAds.find((c) => c.localId.equals(obj._id)) ?? {};
    return { ...obj, fbAdId };
  });

  return {
    metaAdsCampaign: { ...campaignInfo, fbCampaignId },
    metaAdsAdSet: { ...adSetInfo, fbAdSetId },
    metaAdsAdObjects,
    integration,
  };
};

const disconnectUsersPageFromBusiness = async ({ communityId }) => {
  const integration = await MetaAdsIntegration.findOne({
    communityObjectId: communityId,
  }).lean();
  if (!integration) {
    throw new Error('Meta Ads integration not found for this community');
  }
  // check if there are any campaigns in 'pending review' or 'active' state
  const campaign = await MetaAdsCampaigns.findOne({
    communityObjectId: communityId,
    status: {
      $in: [META_ADS_STATUS.PENDING, META_ADS_STATUS.ACTIVE],
    },
  }).lean();

  if (campaign) {
    throw new Error(
      'Cannot disconnect page from business when there are active campaigns'
    );
  }
  // deregister the campaigns in all the existing campaigns that are in paused or draft state
  await Promise.all([
    await MetaAdsCampaigns.updateMany(
      {
        communityObjectId: communityId,
        status: {
          $in: [META_ADS_STATUS.PAUSED, META_ADS_STATUS.DRAFT],
        },
      },
      {
        $unset: {
          fbCampaignId: 1,
        },
      }
    ),
    await MetaAdsSets.updateMany(
      {
        communityObjectId: communityId,
        status: {
          $in: [META_ADS_STATUS.PAUSED, META_ADS_STATUS.DRAFT],
        },
      },
      { $unset: { fbAdSetId: 1 } }
    ),

    await MetaAdsObjects.updateMany(
      {
        communityObjectId: communityId,
        status: {
          $in: [META_ADS_STATUS.PAUSED, META_ADS_STATUS.DRAFT],
        },
      },
      { $unset: { fbAdId: 1 } }
    ),
  ]);
  // now delete the integration
  await MetaAdsIntegration.deleteOne({ communityObjectId: communityId });
  await NasioMetaAdsAccounts.updateOne(
    {
      communityObjectId: communityId,
    },
    {
      $unset: { communityObjectId: 1 },
    }
  );
};
/**
 * Paginated list of campaigns (+ first ad-set and spend)
 * @param {ObjectId} communityId
 * @param {number} page      (1-based)
 * @param {number} limit
 */
const getMagicAudienceCampaigns = async ({ communityId, page, limit }) => {
  const filter = {
    communityObjectId: communityId,
    status: { $nin: META_ADS_STATUS.DELETED },
  };

  const [campaigns, totalCount, wallet] = await Promise.all([
    MetaAdsCampaigns.find(filter)
      .select(
        'fbCampaignId createdByLearnerObjectId status createdAt entityObjectId entityType campaignName totalSpend totalConvertedEvents'
      )
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean(),
    MetaAdsCampaigns.countDocuments(filter),
    getDeductibleWalletBalance({
      communityId,
    }),
  ]);

  if (!campaigns.length) {
    return {
      metaAdsCampaigns: [],
      totalCount,
      totalPages: 0,
      adsWallet: wallet,
    };
  }

  const totalPages = Math.ceil(totalCount / limit);
  const campaignIds = campaigns.map(({ _id }) => _id);

  const [
    entityPairs, // [[entityId, info], …]
    adSets, // all ad-sets for this page
  ] = await Promise.all([
    /* entity info for each campaign ------------------------------------- */
    Promise.all(
      campaigns.map(async ({ entityType, entityObjectId }) => {
        const info = await getEntityInfo({ entityType, entityObjectId });
        return [entityObjectId.toString(), info];
      })
    ),

    /* ad-sets ------------------------------------------------------------ */
    MetaAdsSets.find({
      campaignId: { $in: campaignIds },
      communityObjectId: communityId,
      status: { $ne: META_ADS_STATUS.DELETED },
    })
      .select(
        'campaignId fbAdSetId dailyBudgetInUSD startTime endTime totalBudgetInUSD duration conversionEvent'
      )
      .lean(),
  ]);

  const entityMap = Object.fromEntries(entityPairs); // entityObjectId -> info
  const adSetMap = adSets.reduce((acc, as) => {
    acc[as.campaignId.toString()] = as;
    return acc;
  }, {});
  /* spend insights ----------------------------------------------------- */
  const insightPairs = await Promise.all(
    campaigns
      .filter(
        ({ fbCampaignId, status }) =>
          fbCampaignId && status !== META_ADS_STATUS.DRAFT
      )
      .map(
        async ({
          fbCampaignId,
          status,
          totalSpend,
          totalConvertedEvents,
          _id,
        }) => {
          if (status === META_ADS_STATUS.COMPLETED) {
            return [
              fbCampaignId,
              {
                spend: totalSpend,
                summary: {
                  convertedEvents: {
                    value: totalConvertedEvents,
                  },
                },
              },
            ];
          }

          const adSet = adSetMap[_id.toString()];
          const [insight] = await getCampaignInsights(fbCampaignId, {
            fields: 'spend',
          });

          const campaignInsights = await getMetaInsights({
            objectId: fbCampaignId,
            level: 'campaign',
            fields: 'impressions,actions,spend',
            allTime: true,
          });

          const convertedEventsValue = getActionValue(
            campaignInsights?.[0]?.actions ?? [],
            getConversionEventName(adSet.conversionEvent)
          );
          return [
            fbCampaignId,
            {
              spend: insight?.spend ?? 0,
              summary: {
                convertedEvents: {
                  value: convertedEventsValue,
                },
              },
            },
          ];
        }
      )
  );
  const insightMap = Object.fromEntries(insightPairs); // fbCampaignId   -> spend insight

  const adSetIds = adSets.map(({ _id }) => _id);
  // we need to full ad objects
  const adObjectsRaw = await MetaAdsObjects.find({
    adSetId: { $in: adSetIds },
    status: { $ne: META_ADS_STATUS.DELETED },
  }).lean();

  const adObjectMapViaAdSetId = adObjectsRaw.reduce((acc, obj) => {
    const key = String(obj.adSetId);
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(obj);
    return acc;
  }, {});

  const metaAdsCampaigns = campaigns.map((campaign) => {
    const set = adSetMap[campaign._id.toString()];
    const ads = set ? adObjectMapViaAdSetId[set._id.toString()] ?? [] : [];

    return {
      ...campaign,
      entityInfo: entityMap[campaign.entityObjectId.toString()] ?? null,
      adSet: set ?? null,
      adObjects: ads,
      campaignInsights: insightMap[campaign.fbCampaignId] ?? null,
    };
  });

  return {
    metaAdsCampaigns,
    totalCount,
    totalPages,
    adsWallet: wallet,
  };
};

const getMagicAudienceProducts = async ({
  communityId,
  pageSize,
  search,
}) => {
  const products = await productService.retrieveAllProducts({
    search,
    communityObjectId: communityId,
    onlyPublished: true,
    onlyPaid: false,
    pageSize,
    withSubscriptionType: false,
  });

  if (!products?.products?.length) {
    return products; // no products, nothing to reorder
  }
  /* Running + pending campaigns */
  // we are ok to find all the campaigns as there will be very few of them running
  const campaigns = await MetaAdsCampaigns.find({
    communityObjectId: communityId,
    status: { $in: [META_ADS_STATUS.ACTIVE, META_ADS_STATUS.PENDING] },
  })
    .select('entityObjectId entityType status')
    .lean();

  if (campaigns.length === 0) return products; // nothing to reorder

  const keyOf = (x) => `${x.entityType}:${x.entityObjectId}`;

  const uniqueCampaigns = Array.from(
    new Map(campaigns.map((c) => [keyOf(c), c])).values()
  );

  /* Fetch entityInfo once per unique campaign entity */
  const entityInfos = await Promise.all(
    uniqueCampaigns.map(async ({ entityObjectId, entityType }) =>
      getEntityInfo({ entityObjectId, entityType })
    )
  );

  const entityInfoMap = new Map(
    uniqueCampaigns.map((c, i) => [keyOf(c), entityInfos[i]])
  );

  const campaignMap = new Map(campaigns.map((c) => [keyOf(c), c]));

  const withCampaign = [];
  const withoutCampaign = [];

  const seenKeys = new Set();

  products.products.forEach((product) => {
    const k = keyOf({
      entityType: product.type.toLowerCase(),
      entityObjectId: product.entityObjectId,
    });

    // attach campaign + entity details
    // eslint-disable-next-line no-param-reassign
    product.campaignInfo = campaignMap.get(k) ?? null;
    // eslint-disable-next-line no-param-reassign
    product.entityInfo = entityInfoMap.get(k) ?? null;

    seenKeys.add(k);

    if (product.campaignInfo) {
      withCampaign.push(product); // goes to the TOP block
    } else {
      withoutCampaign.push(product); // stays at the bottom block
    }
  });

  const extras = [];

  uniqueCampaigns.forEach((c) => {
    const k = keyOf(c);
    if (seenKeys.has(k)) return; // already represented and added

    extras.push({
      campaignInfo: campaignMap.get(k),
      ...entityInfoMap.get(k), // add entity info
      entityObjectId: c.entityObjectId,
      entityType: c.entityType.toUpperCase(),
    });
  });

  /* Return in requested priority order */
  return { products: [...withCampaign, ...extras, ...withoutCampaign] };
};

const getMagicAudienceRecommendedProducts = async ({ communityId }) => {
  const now = DateTime.now();
  const FIVE_DAYS_AGO = now.minus({ days: 5 }).toJSDate();
  const TWO_DAYS_FROM_NOW = now.plus({ days: 2 }).toJSDate();

  const [events, challenges, folders] = await Promise.all([
    /* Events: created ≤ 5 days ago & start ≥ 2 days in future */
    CommunityEventsModel.find({
      communities: communityId,
      status: EVENT_STATUS.PUBLISHED, // “published”
      createdAt: { $gte: FIVE_DAYS_AGO },
      startTime: { $gte: TWO_DAYS_FROM_NOW },
    })
      .sort({ createdAt: -1 })
      .limit(3)
      .lean(),
    /* Challenges: created ≤ 5 days ago */
    ProgramModel.find({
      communityObjectId: communityId,
      status: PROGRAM_STATUS.PUBLISHED,
      createdAt: { $gte: FIVE_DAYS_AGO },
      $or: [
        /* Case 1: always-on challenges */
        { challengeType: PROGRAM_CHALLENGE_TYPE.ALWAYS_ON },

        /* Case 2: fixed / dated challenges */
        {
          challengeType: { $ne: PROGRAM_CHALLENGE_TYPE.ALWAYS_ON }, // anything NOT always-on
          startTime: { $gte: TWO_DAYS_FROM_NOW }, // must start ≥ 48 h ahead
        },
      ],
    })
      .sort({ createdAt: -1 })
      .limit(3)
      .lean(),

    /* Folders: created ≤ 5 days ago */
    communityFoldersModel
      .find({
        communityObjectId: communityId,
        status: communityLibraryStatusMap.PUBLISHED,
        createdAt: { $gte: FIVE_DAYS_AGO },
      })
      .sort({ createdAt: -1 })
      .limit(3)
      .lean(),
  ]);

  // front end will display according to whatever logic they want to
  return {
    events, // top-3 future events
    folders, // top-3 recent folders
    challenges, // top-3 recent challenges
  };
};

const getMagicAudienceAllAdEntities = async ({
  communityId,
  campaignId,
}) => {
  const [campaign, adSet, integration] = await Promise.all([
    MetaAdsCampaigns.findOne({
      _id: campaignId,
      communityObjectId: communityId,
    }).lean(),
    MetaAdsSets.findOne({
      campaignId,
      communityObjectId: communityId,
    }).lean(),
    MetaAdsIntegration.findOne({
      communityObjectId: communityId,
    }).lean(),
  ]);

  if (!campaign) {
    throw new Error('Meta Ads campaign not found');
  }
  if (!adSet) {
    throw new Error('Meta Ads ad set not found');
  }
  const adObjects = await MetaAdsObjects.find({
    adSetId: adSet._id,
  }).lean();

  const entityInfo = await getEntityInfo({
    entityObjectId: campaign.entityObjectId,
    entityType: campaign.entityType,
  });

  return {
    adObjects,
    adSet,
    entityInfo,
    integration,
    campaign,
  };
};
const getMagicAudienceCampaignById = async ({
  communityId,
  campaignId,
  dateStart,
  dateEnd, // today,
}) => {
  /* ---------- fetch DB documents (unchanged) ---------- */
  const [campaign, adSet, integration, communityInfo, hasActiveCampaigns] =
    await Promise.all([
      MetaAdsCampaigns.findOne({
        _id: campaignId,
        communityObjectId: communityId,
      })
        .populate('createdByLearnerObjectId', 'name email')
        .lean(),
      MetaAdsSets.findOne({
        campaignId,
        communityObjectId: communityId,
      }).lean(),
      MetaAdsIntegration.findOne({
        communityObjectId: communityId,
      }).lean(),
      CommunityModel.findById(communityId).lean(),
      MetaAdsCampaigns.exists({
        communityObjectId: communityId,
        status: { $in: [META_ADS_STATUS.ACTIVE, META_ADS_STATUS.PENDING] },
      }),
    ]);

  if (!campaign) throw new Error('Meta Ads campaign not found');
  if (!adSet) throw new Error('Meta Ads ad set not found');

  const isCampaignCompleted =
    campaign.status === META_ADS_STATUS.COMPLETED;

  if (isCampaignCompleted && !dateStart && !dateEnd) {
    // if the campaign is completed then we will set the dateStart and dateEnd to the campaign's start and end time
    // eslint-disable-next-line no-param-reassign
    dateStart = DateTime.fromJSDate(adSet.startTime).toFormat(
      'yyyy-MM-dd'
    );
    // eslint-disable-next-line no-param-reassign
    dateEnd = DateTime.fromJSDate(adSet.endTime).toFormat('yyyy-MM-dd');
  } else if (!dateStart && !dateEnd) {
    // default to last 7 days (if front end didn't specify dates)
    // eslint-disable-next-line no-param-reassign
    dateStart = DateTime.now().minus({ days: 7 }).toFormat('yyyy-MM-dd');
    // eslint-disable-next-line no-param-reassign
    dateEnd = DateTime.now().toFormat('yyyy-MM-dd');
  }
  const entityInfo = await getEntityInfo({
    entityObjectId: campaign.entityObjectId,
    entityType: campaign.entityType,
  });
  let adObjects = await MetaAdsObjects.find({
    adSetId: adSet._id,
    status: { $ne: META_ADS_STATUS.DELETED },
  }).lean();

  // if integration is there AND campaign OR fbAdSet are not created yet then we will create them
  if (integration && (!campaign?.fbCampaignId || !adSet?.fbAdSetId)) {
    // we will create the magic Audience campaign,adset and adobject
    return createCampaignAdSetAndAd({
      campaignInfo: campaign,
      adSetInfo: adSet,
      adObjects,
      integration,
      communityInfo,
      createAdObjects: false, // we don't want to create ad objects here (we'll do it in the approve ad object flow)
    });
  }
  if (
    !integration ||
    (!campaign.fbCampaignId && campaign.status === META_ADS_STATUS.DRAFT)
  ) {
    return {
      campaign,
      adSet,
      adObjects,
      insights: {},
      integration,
      hasActiveCampaigns,
    };
  }

  const startDT = DateTime.fromISO(dateStart);
  const endDT = DateTime.fromISO(dateEnd);
  const daysSpan = endDT.diff(startDT, 'days').days + 1; // inclusive
  const prevEnd = startDT.minus({ days: 1 });
  const prevStart = prevEnd.minus({ days: daysSpan - 1 });

  const [
    [campaignSpendData], // total spend for ALL time
    campaignDaily, // daily rows this period
    adInsightsRaw, // ad-level rows this period
    previousPeriodRows, // daily rows previous period
  ] = await Promise.all([
    getCampaignInsights(campaign.fbCampaignId, { fields: 'spend' }),
    getMetaInsights({
      objectId: campaign.fbCampaignId,
      level: 'campaign',
      fields: 'impressions,actions,spend',
      dateStart,
      dateEnd,
      timeIncrement: 1,
    }),
    getMetaInsights({
      objectId: campaign.fbCampaignId,
      level: 'ad',
      fields: 'ad_id,ad_name,impressions,actions,spend',
      dateStart,
      dateEnd,
    }),
    getMetaInsights({
      objectId: campaign.fbCampaignId,
      level: 'campaign',
      fields: 'impressions,actions,spend',
      dateStart: prevStart.toISODate(),
      dateEnd: prevEnd.toISODate(),
      timeIncrement: 1,
    }),
  ]);

  const adInsightMap = Object.fromEntries(
    adInsightsRaw.map((r) => [r.ad_id, r])
  );

  let bestIdx = -1; // index of lowest finite CPSU
  let worstIdx = -1; // index of highest finite CPSU
  let bestCPSU = Infinity;
  let worstCPSU = -Infinity;
  let finiteCount = 0; // ads that have ≥1 sign-up

  adObjects = adObjects.map((ad, i) => {
    const insight = adInsightMap[ad.fbAdId] ?? null;
    const convertedEvents = getActionValue(
      insight?.actions,
      getConversionEventName(adSet.conversionEvent)
    );
    const spend = Number(insight?.spend ?? 0);
    const cpsu = convertedEvents ? spend / convertedEvents : null; // null = 0 sign-ups

    if (cpsu != null) {
      finiteCount += 1;

      if (cpsu < bestCPSU) {
        bestCPSU = cpsu;
        bestIdx = i;
      }
      if (cpsu > worstCPSU) {
        worstCPSU = cpsu;
        worstIdx = i;
      }
    }

    return {
      ...ad,
      adInsight: insight,
      costPerResults: cpsu,
      convertedEvents,
    };
  });

  if (adObjects.length) {
    /* always tag one best performer  */
    const winner = bestIdx;

    if (winner !== -1) {
      adObjects[winner] = { ...adObjects[winner], bestPerformer: true };
    }

    /* tag needsNewCreative only when we have ≥2 finite CPSU ads */
    if (finiteCount > 1 && worstIdx !== -1 && worstIdx !== winner) {
      adObjects[worstIdx] = {
        ...adObjects[worstIdx],
        needsNewCreative: true,
      };
    }
  }

  campaignDaily.forEach((row) => {
    const dailyConvertedEvents = getActionValue(
      row.actions,
      getConversionEventName(adSet.conversionEvent)
    );

    // eslint-disable-next-line no-param-reassign
    row.convertedEvents = dailyConvertedEvents;
    // eslint-disable-next-line no-param-reassign
    row.pageVisits = getActionValue(row.actions, LINK_CLICK_EVENT);

    const dailySpend = Number(row.spend ?? 0);

    // eslint-disable-next-line no-param-reassign
    row.costPerResults =
      dailyConvertedEvents === 0
        ? 0
        : (dailySpend / dailyConvertedEvents).toFixed(2);
  });

  const currentTotals = aggregateTotals(
    campaignDaily,
    getConversionEventName(adSet.conversionEvent)
  );
  const previousTotals = aggregateTotals(
    previousPeriodRows,
    getConversionEventName(adSet.conversionEvent)
  );

  const costPerConvertedEventCurrent =
    currentTotals.convertedEvents === 0
      ? 0
      : currentTotals.spend / currentTotals.convertedEvents;
  const costPerConvertedEventPrevious =
    previousTotals.convertedEvents === 0
      ? 0
      : previousTotals.spend / previousTotals.convertedEvents;

  const insights = {
    daily: campaignDaily, // keep graph data
    summary: {
      impressions: {
        value: currentTotals.impressions,
        percentage: calcPct(
          currentTotals.impressions,
          previousTotals.impressions
        ),
      },
      pageVisits: {
        value: currentTotals.pageVisits,
        percentage: calcPct(
          currentTotals.pageVisits,
          previousTotals.pageVisits
        ),
      },
      convertedEvents: {
        value: currentTotals.convertedEvents,
        percentage: calcPct(
          currentTotals.convertedEvents,
          previousTotals.convertedEvents
        ),
      },
      costPerResults: {
        value: costPerConvertedEventCurrent.toFixed(2),
        percentage: calcPct(
          costPerConvertedEventCurrent,
          costPerConvertedEventPrevious
        ),
      },
    },
  };
  campaign.entityInfo = entityInfo;
  campaign.totalSpend = Number(campaignSpendData?.spend ?? 0);

  return {
    campaign,
    adSet,
    adObjects,
    insights,
    integration,
    hasActiveCampaigns,
  };
};

const getMetaPages = async ({ code, redirectUri }) => {
  const accessToken = await getUserAccessToken(code, redirectUri);
  if (!accessToken) {
    throw new Error('Failed to get access token for Meta Pages');
  }

  const pages = await getUsersMetaAccountPages({
    accessToken,
  });

  return {
    pages,
    accessToken,
  };
};

const updateAllTimeDailyAdAndCampaignInsights = async ({
  communityId,
  fbCampaignId,
}) => {
  const [dailyAdAnalytics, dailyCampaignAnalytics] = await Promise.all([
    getMetaInsights({
      objectId: fbCampaignId,
      level: 'ad',
      fields: 'ad_id,ad_name,impressions,actions,spend',
      timeIncrement: 1,
      allTime: true,
    }),
    getMetaInsights({
      objectId: fbCampaignId,
      level: 'campaign',
      fields: 'impressions,actions,spend',
      timeIncrement: 1,
      allTime: true,
    }),
  ]);

  const adInsightsMapDaily = new Map();

  for (const insight of dailyAdAnalytics) {
    const id = insight.ad_id;
    if (!adInsightsMapDaily.has(id)) adInsightsMapDaily.set(id, []);
    adInsightsMapDaily.get(id).push(insight);
  }

  const adInsightIds = Array.from(adInsightsMapDaily.keys());
  // update daily insights with ad-level data
  await Promise.all(
    adInsightIds.map(async (adId) => {
      const adInsight = adInsightsMapDaily.get(adId);
      // update adObject with adInsight
      await MetaAdsObjects.updateOne(
        {
          fbAdId: adId,
          communityObjectId: communityId,
        },
        {
          $set: {
            allTimeDailyAdInsights: adInsight,
          },
        }
      );
    }),
    MetaAdsCampaigns.updateOne(
      { fbCampaignId, communityObjectId: communityId },
      {
        $set: {
          allTimeDailyCampaignInsights: dailyCampaignAnalytics,
        },
      }
    )
  );
};
const connectUsersPageToBusiness = async ({
  pageId,
  accessToken,
  communityId,
}) => {
  if (!pageId || !accessToken || !communityId) {
    throw new Error(
      'pageId, accessToken and communityId are all required.'
    );
  }

  const communityInfo = await CommunityModel.findById(communityId).lean();
  // attach + approve the page (must be sequential)
  // approval of agency

  await addUsersPageToBusiness({ pageId, accessToken });
  await addAgencyToTheUsersPage({
    pageId,
    accessToken,
  });

  await approveUserPageToBusiness({ pageId, accessToken });
  await addNasIOCommunityLinkToPage({
    communityLink: `${NAS_IO_FRONTEND_URL}/${communityInfo.link}`,
    pageId,
    accessToken,
  });

  const {
    name: pageTitle = '',
    about: pageDescription = '',
    picture,
  } = (await getPageInformation(pageId, ['name', 'about', 'picture'])) ??
  {};

  const pageThumbnail = picture?.data?.url ?? '';

  const nasAccount = await NasioMetaAdsAccounts.findOne({
    $or: [
      { communityObjectId: new ObjectId(communityId) },
      { communityObjectId: { $exists: false } },
    ],
  })
    .select('adsAccountId pixelId')
    .lean();

  if (!nasAccount?.adsAccountId || !nasAccount?.pixelId) {
    throw new Error(
      'Nasio Ads Account or Pixel ID not found. Please contact support.'
    );
  }

  const [metaAdsIntegration] = await Promise.all([
    MetaAdsIntegration.updateOne(
      {
        adsAccountId: nasAccount.adsAccountId,
        communityObjectId: communityId,
      },
      {
        $set: {
          adsAccountId: nasAccount.adsAccountId,
          pixelId: nasAccount.pixelId,
          pageId,
          pageTitle,
          pageDescription,
          pageThumbnail,
        },
      },
      {
        upsert: true, // create if not exists
      }
    ),
    CommunityModel.updateOne(
      { _id: communityId },
      { $set: { 'trackingPixels.facebookPixel': nasAccount.pixelId } }
    ),
    NasioMetaAdsAccounts.updateOne(
      { pixelId: nasAccount.pixelId }, // update the communityObjectId
      { $set: { communityObjectId: communityId } }
    ),
  ]);

  return metaAdsIntegration; // unchanged return shape
};
/**
 * This function will get the estimated delivery of the ad set, like how many impressions and results info
 * @param {ObjectId} communityId
 * @param {ObjectId} campaignId
 * @param {Object} targeting
 * @returns {Promise<estimationArray>}
 */
const getMagicAudienceCampaignEstimate = async ({
  communityId,
  campaignId,
  targeting,
}) => {
  const adSet = await MetaAdsSets.findOne({
    campaignId,
    communityObjectId: communityId,
  })
    .populate('campaignId')
    .lean();

  if (!adSet) throw new Error('Meta Ads campaign not found');

  if (!adSet.fbAdSetId) {
    throw new Error('Meta Ads ad set not found');
  }
  const { optimizationGoal } = adSet;

  const estimate = await getEstimatedDeliveryOfAdSet({
    adSetId: adSet.fbAdSetId,
    optimizationGoal,
    targeting,
  });

  return estimate;
};

/**
 * this function is used to pause all the ads in a campaign
 * it will update the status of the campaign, adset and adobjects to PAUSED state in both fb and db
 * @param {ObjectId} campaignId
 * @param {ObjectId} communityId}
 * @returns {Promise<MetaAdsCampaigns>}
 */
const pauseAllAds = async ({ campaignId, communityId }) => {
  const [adSet] = await Promise.all([
    MetaAdsSets.findOne({
      campaignId,
      communityObjectId: communityId,
    })
      .populate('campaignId')
      .lean(),
  ]);

  const { fbAdSetId } = adSet; // FB’s numeric ad‑set id
  const { fbCampaignId, status } = adSet.campaignId;

  if (!fbAdSetId || !fbCampaignId) {
    throw new Error(
      'Cannot pause ads in campaign without a valid Facebook ad set or campaign ID.'
    );
  }

  if (status !== META_ADS_STATUS.ACTIVE) {
    throw new Error(
      `Cannot pause ads in campaign with status ${status}. Only ACTIVE campaigns can be paused.`
    );
  }
  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    const basePayload = {
      status: META_ADS_STATUS.PAUSED,
      access_token: META_APP_ACCESS_TOKEN,
    };

    const adObjects = await MetaAdsObjects.find({
      adSetId: adSet._id,
      status: META_ADS_STATUS.ACTIVE,
    })
      .select('fbAdId')
      .lean();

    await Promise.all([
      updateMetaAdsCampaign({
        campaignId: fbCampaignId,
        graphPayload: basePayload,
      }),
      updateAdSet({
        adSetId: fbAdSetId,
        graphPayload: basePayload,
      }),
      ...adObjects.map(({ fbAdId }) =>
        updateAdObject({
          fbAdId,
          graphPayload: basePayload,
        })
      ),
    ]);

    // update the status in the DB
    await MetaAdsCampaigns.updateOne(
      { _id: campaignId },
      { $set: { status: META_ADS_STATUS.PAUSED } },
      { session }
    );
    await MetaAdsSets.updateOne(
      { _id: adSet._id },
      { $set: { status: META_ADS_STATUS.PAUSED } },
      { session }
    );
    await MetaAdsObjects.updateMany(
      { adSetId: adSet._id, status: META_ADS_STATUS.ACTIVE },
      { $set: { status: META_ADS_STATUS.PAUSED } },
      { session }
    );
    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }

  return MetaAdsCampaigns.findById(campaignId).lean();
};

const isCampaignAlreadyActiveForEntity = async ({
  entityObjectId,
  entityType,
  isDraft = false,
}) => {
  const statuses = [
    META_ADS_STATUS.ACTIVE,
    META_ADS_STATUS.PENDING,
    META_ADS_STATUS.PAUSED,
  ];

  if (isDraft) {
    statuses.push(META_ADS_STATUS.DRAFT);
  }
  const campaign = await MetaAdsCampaigns.findOne({
    entityObjectId,
    entityType,
    status: {
      $in: statuses,
    },
  }).lean();

  return Boolean(campaign);
};

/**
 * This function creates the campaign object + ad set object both on facebook (if already connected) and in the DB
 * @param {Community} communityObjectId:communityId
 * @param {ObjectId} entityObjectId  _id of the entity
 * @param {String} entityType can be community or any other product entity
 * @param {String} campaignName
 * @param {META_ADS_STATUS} status:
 * @param {Learner} createdByLearnerObjectId:learnerObjectId
 * @returns {Promise<metaAdsCampaignObjects>}
 */
const createMagicAudienceCampaign = async ({
  communityObjectId,
  entityObjectId,
  entityType,
  campaignName,
  createdByLearnerObjectId,
}) => {
  // eslint-disable-next-line no-param-reassign
  entityType = entityType.toLowerCase();
  /*
  NOTE: ad set needs a budget so we are creating the adset with a default budget because without an adset we cannot have ad objects
    step 1: check if the facebook ad connection is present
    step 2: if the connection is present, create campaign, adset on facebook and db
    step 3: if the connection is not present, create campaign, adset in db
    step 4: return the created campaign object + ad set object
  */
  // TODO: to use entityInfo in the future
  // eslint-disable-next-line no-unused-vars
  const [entityInfo, metaAdsIntegration, communityInfo] =
    await Promise.all([
      getEntityInfo({
        entityType,
        entityObjectId,
      }),
      MetaAdsIntegration.findOne({ communityObjectId }).lean(),
      CommunityModel.findById(communityObjectId).lean(),
    ]);

  const isThereExistingCampaign = await isCampaignAlreadyActiveForEntity({
    entityObjectId,
    entityType,
    isDraft: true, // we want to check for draft campaigns as well
  });

  if (isThereExistingCampaign) {
    throw new Error(`There is already a campaign for this ${entityType}.`);
  }
  const { isEntityPaid } = entityInfo; // THIS WILL BE USED LATER
  const { countryCreatedIn } = communityInfo;

  const countryCode = getValidCountryCode(
    await getCountryCodeByCountryName(countryCreatedIn)
  );

  const responseData = {};

  const facebookConnected = Boolean(metaAdsIntegration);

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    const campaignDoc = {
      communityObjectId,
      entityObjectId,
      entityType,
      campaignName,
      status: META_ADS_STATUS.DRAFT, // start with draft state
      createdByLearnerObjectId,
      objective: META_ADS_OBJECTIVE.OUTCOME_SALES,
    };

    if (facebookConnected) {
      campaignDoc.fbCampaignId = await createMetaAdsCamapaign({
        ...campaignDoc,
        metaAdsIntegration,
        status: META_ADS_STATUS.PAUSED, // facebook requires a PAUSED status for the campaign creation
      });
    }

    const [metaAdsCampaign] = await MetaAdsCampaigns.create(
      [campaignDoc],
      {
        session,
      }
    );

    const conversionEvent = getMetaAdsConversionEvent(
      isEntityPaid,
      communityInfo?.isPaidCommunity
    );

    const adSetDoc = {
      campaignName,
      campaignId: metaAdsCampaign._id,
      optimizationGoal: META_OPTIMIZATION_GOAL.OFFSITE_CONVERSIONS,
      status: META_ADS_STATUS.DRAFT,
      communityObjectId,
      startTime: new Date(),
      endTime: new Date().setDate(new Date().getDate() + 1), // default end date, end date doesn't matter in terms of campaign creation in draft state
      dailyBudgetInUSD: DEFAULT_AD_DAILY_BUDGET,
      totalBudgetInUSD: DEFAULT_AD_DAILY_BUDGET,
      billingEvent: META_BILLING_EVENTS.IMPRESSIONS,
      targeting: {
        geo_locations: {
          countries: [countryCode],
        },
      },
      conversionEvent, // to be decided (to be sent by front end)
    };

    if (facebookConnected) {
      adSetDoc.fbAdSetId = await createMetaAdSet({
        ...adSetDoc,
        metaAdsCampaignId: campaignDoc.fbCampaignId,
        metaAdsIntegration,
        promotedObject: {
          pixel_id: communityInfo?.trackingPixels?.facebookPixel,
          custom_event_type: conversionEvent, // to be decided
        },
        status: META_ADS_STATUS.PAUSED, // facebook requires a PAUSED status for the ad set creation
        countryCode,
      });
    }

    const [metaAdsAdSet] = await MetaAdsSets.create([adSetDoc], {
      session,
    });

    responseData.metaAdsCampaign = metaAdsCampaign;
    responseData.metaAdsAdSet = metaAdsAdSet;

    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }

  return responseData;
};

/**
 * This function created the ad object on facebook(if the connection is present) and in the DB
 * @param {ObjectId} communityObjectId
 * @param {ObjectId} campaignObjectId
 * @param {ObjectId} createdByLearnerObjectId
 * @param {Array<String>} primaryTexts
 * @param {Array<String>} headlines
 * @param {Array<String>} descriptions
 * @param {enum} adFormat 'SINGLE_IMAGE'|'SINGLE_VIDEO'mediaUrls
 * @returns {Promise<MetaAdsObjects>}
 */
const createMagicAudienceAdObject = async ({
  communityObjectId,
  campaignObjectId,
  createdByLearnerObjectId,
  primaryTexts,
  headlines,
  descriptions,
  adFormat, // 'SINGLE_IMAGE' | 'SINGLE_VIDEO'
  mediaUrls, // array for images
  videoInfo,
  adName,
  isAiGenerated = false, // whether the ad is AI generated or not
}) => {
  const [adSet, community, latestAdObject, campaignInfo] =
    await Promise.all([
      MetaAdsSets.findOne({
        campaignId: campaignObjectId,
        communityObjectId,
      })
        .populate('campaignId')
        .lean(),
      CommunityModel.findById(communityObjectId).lean(),
      MetaAdsObjects.findOne({
        communityObjectId,
        adSetId: campaignObjectId,
        status: { $ne: META_ADS_STATUS.DELETED },
      })
        .select('adSetId variantNumber')
        .sort({ variantNumber: -1 })
        .limit(1)
        .lean(),
      MetaAdsCampaigns.findOne({
        _id: campaignObjectId,
        communityObjectId,
        status: { $ne: META_ADS_STATUS.DELETED },
      }).lean(),
    ]);

  const variantNumber = latestAdObject?.variantNumber
    ? latestAdObject.variantNumber + 1
    : 1;

  if (!adSet) throw new Error('Meta Ads ad-set not found');
  const linkUrls = [await getAdLinkUrl(adSet.campaignId, community)];

  const entityInfo = await getEntityInfo({
    entityType: adSet.campaignId.entityType,
    entityObjectId: adSet.campaignId.entityObjectId,
  });

  await checkIfEntityIsEligibleToRunACampaign({
    entity: entityInfo,
    entityType: adSet.campaignId.entityType,
  });
  // need to generate the text generation for the ad
  if (isAiGenerated) {
    const generatedAdsData = await generateAdsText(entityInfo);
    // eslint-disable-next-line no-param-reassign
    primaryTexts = [generatedAdsData.primaryText];
    // eslint-disable-next-line no-param-reassign
    headlines = [generatedAdsData.headlines];
    // eslint-disable-next-line no-param-reassign
    descriptions = [generatedAdsData.description];
  }

  let dbAdStatus = META_ADS_STATUS.DRAFT; // default status

  if (
    [
      META_ADS_STATUS.PAUSED,
      META_ADS_STATUS.ACTIVE,
      META_ADS_STATUS.PENDING,
    ].includes(campaignInfo.status)
  ) {
    dbAdStatus = META_ADS_STATUS.PENDING; // if the ad-set is in PAUSED, ACTIVE or PENDING state, we will create the ad in PENDING state (someone from the team will review it)
  }

  const dbPayload = {
    campaignId: campaignObjectId,
    adName,
    adSetId: adSet._id,
    variantNumber,
    adFormat,
    status: dbAdStatus,
    createdByLearnerObjectId,
    primaryTexts,
    headlines,
    descriptions,
    linkUrls,
    mediaUrls,
    communityObjectId,
    ...(videoInfo && {
      videoInfo: videoInfo.map(
        ({ videoId, videoUrl, videoThumbnailUrl }) => ({
          ...(videoId && { videoId }),
          videoUrl,
          ...(videoThumbnailUrl && { videoThumbnailUrl }),
        })
      ),
    }),
  };

  // if (integration) {
  //   const { adsAccountId, pageId } = integration;
  //   const { fbAdSetId: adsetId } = adSet; // FB’s numeric ad‑set id
  //   const assetFeedSpec = buildAssetFeedSpec({
  //     adFormat,
  //     pageId,
  //     primaryTexts,
  //     headlines,
  //     descriptions,
  //     linkUrls,
  //     mediaUrls,
  //     videoInfo,
  //   });

  //   const objectStorySpec = {
  //     page_id: pageId,
  //   };

  //   const graphPayload = {
  //     name: `${adSet?.campaignId?.campaignName ?? ''} • ${adFormat}`,
  //     adset_id: adsetId,
  //     creative: {
  //       object_story_spec: objectStorySpec,
  //       asset_feed_spec: assetFeedSpec,
  //     },
  //     status: META_ADS_STATUS.PAUSED,
  //     access_token: META_APP_ACCESS_TOKEN,
  //     adsAccountId,
  //   };

  //   dbPayload.fbAdId = await createMetaAdsAdObject({
  //     ...graphPayload,
  //     adsAccountId,
  //   });
  // }
  const metaAdsObject = await MetaAdsObjects.create(dbPayload);
  return metaAdsObject;
};

const resettleFundsAndUpdateCampaignStatus = async ({
  status,
  communityId,
  campaignInfo,
  remainingFunds,
  rejectedReason = null, // reason for rejection if any
}) => {
  const {
    _id: campaignId,
    fbCampaignId,
    status: currentStatus,
  } = campaignInfo;

  const adSet = await MetaAdsSets.findOne(
    { campaignId, communityObjectId: communityId },
    { fbAdSetId: 1 } // project only what we use
  ).lean();

  const adObjects = await MetaAdsObjects.find(
    { adSetId: adSet._id, status: { $ne: META_ADS_STATUS.DELETED } },
    { fbAdId: 1 } // only fbAdId ⇒ less payload
  ).lean();

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    // if it is a draft campaign, we will delete the campaign, ad set and ad objects
    if (adSet === META_ADS_STATUS.DRAFT) {
      await Promise.all([
        deleteMetaAdsCampaign(fbCampaignId),
        deleteMetaAdsAdSet(adSet.fbAdSetId),
        ...adObjects.map(({ fbAdId }) => deleteMetaAdsAdObject(fbAdId)),
      ]);
    } else {
      // we will never delete them to retain analytics (from facebook)
      await Promise.all([
        updateMetaAdsCampaign({
          campaignId: fbCampaignId,
          graphPayload: {
            status: META_ADS_STATUS.PAUSED,
            access_token: META_APP_ACCESS_TOKEN,
          },
        }),
        updateAdSet({
          adSetId: adSet.fbAdSetId,
          graphPayload: {
            status: META_ADS_STATUS.PAUSED,
            access_token: META_APP_ACCESS_TOKEN,
          },
        }),
        // deleteMetaAdsAdSet(adSet.fbAdSetId),
        ...adObjects.map(({ fbAdId }) =>
          updateAdObject({
            fbAdId,
            graphPayload: {
              status: META_ADS_STATUS.PAUSED,
              access_token: META_APP_ACCESS_TOKEN,
            },
          })
        ),
      ]);
    }

    const deleted = { status, facebookStatus: META_ADS_STATUS.PAUSED };

    await MetaAdsObjects.updateMany(
      { campaignId },
      { $set: deleted },
      { session }
    );

    await MetaAdsSets.updateOne(
      { _id: adSet._id },
      { $set: deleted },
      { session }
    );
    await MetaAdsCampaigns.updateOne(
      { _id: campaignId },
      {
        $set: {
          ...deleted,
          ...(rejectedReason && { errorReason: rejectedReason }),
        },
      },
      { session }
    );

    // if the campaign was in PAUSED or ACTIVE or COMPLETED state,
    if (
      [
        META_ADS_STATUS.PAUSED,
        META_ADS_STATUS.ACTIVE,
        META_ADS_STATUS.COMPLETED,
      ].includes(currentStatus)
    ) {
      await adsWalletService.returnAdsCampaignFunds({
        campaignId,
        remainingFunds,
        session,
      });
    } else if (currentStatus === META_ADS_STATUS.PENDING) {
      await adsWalletService.revertAdsCampaignFunds({
        campaignId,
        session,
      });
    }

    await session.commitTransaction();
  } catch (err) {
    await session.abortTransaction();
    throw err;
  } finally {
    await session.endSession();
  }
};

const settleFundsForCompletedMagicAudienceCampaign = async ({
  communityId,
  campaignId,
  totalSpend,
}) => {
  let remainingFunds = 0;
  const adsSet = await MetaAdsSets.findOne({
    campaignId,
    communityObjectId: communityId,
  })
    .populate('campaignId')
    .lean();
  if (!adsSet) {
    throw new Error('Meta Ads ad set not found');
  }
  const { totalBudgetInUSD, campaignId: campaignInfo } = adsSet;

  if (!campaignInfo) {
    throw new Error('Meta Ads campaign not found');
  }
  remainingFunds = totalBudgetInUSD - totalSpend * 100;
  // ASK JH about this case when totalSpend is more than totalBudgetInUSD
  // update we'll mark them as 0 but we will store the totalSpend in the campaign
  if (remainingFunds < 0) {
    remainingFunds = 0; // set remaining funds to 0 if total spend is more than total budget
    logger.warn(
      `Total spend ${totalSpend} is more than total budget ${totalBudgetInUSD}. Setting remaining funds to 0.`
    );
  }

  const [{ actions, impressions } = {}] = await getCampaignInsights(
    campaignInfo.fbCampaignId,
    {
      fields: 'actions,spend,impressions',
    }
  );
  await MetaAdsCampaigns.updateOne(
    { _id: campaignId },
    {
      $set: {
        totalSpend,
      },
    }
  );
  await resettleFundsAndUpdateCampaignStatus({
    campaignInfo,
    status: META_ADS_STATUS.COMPLETED,
    communityId,
    remainingFunds,
  });

  try {
    await sendEmailToUser({
      campaignId,
      analyticData: {
        impressions: impressions || 0,
        actions: actions || [],
        spend: totalSpend,
      },
    });
  } catch (error) {
    logger.error(
      `Error while settling funds for completed magic audience campaign: ${error.message}`
    );
  }

  await updateAllTimeDailyAdAndCampaignInsights({
    fbCampaignId: campaignInfo.fbCampaignId,
    communityId,
  });
  return true;
};

const pauseMagicAudienceAdObject = async ({
  adObjectId: adId,
  communityId,
  campaignId,
}) => {
  const [adObject, integration, campaignInfo] = await Promise.all([
    MetaAdsObjects.findById(adId).lean(),
    MetaAdsIntegration.findOne({ communityObjectId: communityId })
      .select('pageId') // lighter cursor
      .lean(),
    MetaAdsCampaigns.findById(campaignId).lean(),
  ]);
  if (!adObject) throw new Error('Meta Ads ad object not found');
  if (!integration) {
    throw new Error('Meta Ads integration not found');
  }
  if (!adObject.fbAdId) {
    throw new Error('Meta Ads ad object does not have a Facebook ad ID');
  }

  // check if the existing ad object is in pending state if yes then we will delete it
  if (adObject.status === META_ADS_STATUS.PENDING) {
    throw new Error(
      'Cannot pause ad object in PENDING state. Please approve the ad object first.'
    );
  }

  if (
    [META_ADS_STATUS.ACTIVE, META_ADS_STATUS.PAUSED].includes(
      campaignInfo.status
    )
  ) {
    throw new Error(
      'Cannot pause ad object in DRAFT state. Please publish the campaign first.'
    );
  }

  const graphPayload = {
    status: META_ADS_STATUS.PAUSED,
    access_token: META_APP_ACCESS_TOKEN,
  };

  await updateAdObject({
    graphPayload,
    fbAdId: adObject.fbAdId,
  });

  const updatedAdObject = await MetaAdsObjects.findOneAndUpdate(
    { _id: adId },
    {
      $set: {
        status: META_ADS_STATUS.PAUSED,
      },
    },
    {
      new: true,
    }
  );

  return updatedAdObject;
};
/**
 * This function updates the ad object on facebook(if the connection is present) and in the DB
 * @param {ObjectId} adObjectId
 * @param {Object} updateAdBody
 * @param {ObjectId} campaignId
 * @param {ObjectId} communityId
 * @returns {Promise<MetaAdsObjects>}
 */
const updateMagicAudienceAdObject = async ({
  adObjectId,
  updateAdBody,
  campaignId,
  communityId,
}) => {
  const [adObj, integration, campaignInfo] = await Promise.all([
    MetaAdsObjects.findById(adObjectId).lean(),
    MetaAdsIntegration.findOne({ communityObjectId: communityId })
      .select('pageId') // lighter cursor
      .lean(),
    MetaAdsCampaigns.findById(campaignId).lean(),
  ]);

  if (!adObj) throw new Error('Meta Ads ad object not found');

  // if updateAdBody.mediaUrls has any empty string the throw an error
  if (updateAdBody?.mediaUrls?.some((url) => !url || url.trim() === '')) {
    throw new Error('Media URLs cannot be empty or blank strings.');
  }

  const nextAd = { ...adObj, ...updateAdBody };

  if (integration && adObj.fbAdId) {
    if (updateAdBody?.status === META_ADS_STATUS.DELETED) {
      await deleteMetaAdsAdObject(adObj.fbAdId);
    } // if the ad object is in ACTIVE or PAUSED state, we will update the ad object on facebook to PAUSED state and make it approval pending in the DB
    else if (
      [META_ADS_STATUS.ACTIVE, META_ADS_STATUS.PAUSED].includes(
        campaignInfo.status
      )
    ) {
      // update the ad object on facebook to PAUSED state
      const graphPayload = {
        status: META_ADS_STATUS.PAUSED,
        access_token: META_APP_ACCESS_TOKEN,
      };
      await updateAdObject({
        fbAdId: adObj.fbAdId,
        graphPayload,
      });
      // set the status to PENDING in the DB
      nextAd.status = META_ADS_STATUS.PENDING;
    }
  }
  const updatedAdObj = await MetaAdsObjects.findOneAndUpdate(
    { _id: adObjectId },
    {
      $set: {
        ...nextAd,
      },
    },
    {
      new: true,
    }
  );
  return updatedAdObj;
};

/**
 * This function updates the ad set on facebook(if the connection is present) and in the DB
 * @param {ObjectId} communityId
 * @param {ObjectId} campaignId
 * @param {Object} targeting
 * @param {Number} duration
 * @param {String} dailyBudgetInUSD
 * @returns {Promise<MetaAdsSets>}
 */
const updateMagicAudienceCampaign = async ({
  communityId,
  campaignId,
  targeting,
  duration,
  dailyBudgetInUSD,
}) => {
  const [integration, adSet] = await Promise.all([
    MetaAdsIntegration.findOne({ communityObjectId: communityId }).lean(),
    MetaAdsSets.findOne({
      campaignId,
      communityObjectId: communityId,
    })
      .populate('campaignId')
      .lean(),
  ]);
  const adObjects = await MetaAdsObjects.find({
    adSetId: adSet?._id,
    status: { $nin: [META_ADS_STATUS.DELETED, META_ADS_STATUS.REJECTED] },
  }).lean();

  if (!integration) {
    throw new Error('Meta Ads integration not found');
  }
  if (!adSet) throw new Error('Meta Ads campaign not found');

  const isThereExistingCampaign = await isCampaignAlreadyActiveForEntity({
    entityObjectId: adSet.campaignId.entityObjectId,
    entityType: adSet.campaignId.entityType,
    isDraft: false, // we only want to check for active campaigns
  });

  if (isThereExistingCampaign) {
    throw new Error(
      `There is already an active campaign for this ${adSet.campaignId.entityType} you cannot edit the ad set.`
    );
  }
  // create the ad objects if not present
  const { metaAdsAdSet } = await createCampaignAdSetAndAd({
    campaignInfo: adSet.campaignId,
    adSetInfo: adSet,
    adObjects,
    integration,
    communityInfo: {
      _id: communityId,
      trackingPixels: {
        facebookPixel: integration.pixelId,
      },
    },
    createAdObjects: false, // we don't want to create ad objects here (we'll do it in the approve ad object flow)
  });

  const adSetId = metaAdsAdSet.fbAdSetId; // FB’s numeric ad‑set id

  const graphPayload = {
    adset_id: adSetId,
    access_token: META_APP_ACCESS_TOKEN,
  };

  if (targeting) {
    graphPayload.targeting = targeting;
  }
  if (dailyBudgetInUSD) {
    graphPayload.lifetime_budget = dailyBudgetInUSD * duration; // set lifetime budget
  }
  await updateAdSet({
    graphPayload,
    adSetId,
  });

  const { startTime, endTime } = computeStartAndEndTime({
    duration,
  });
  const mergedPayloadForAdSet = {
    ...adSet,
    ...{
      duration,
      targeting,
      dailyBudgetInUSD,
      totalBudgetInUSD: dailyBudgetInUSD * duration,
      startTime,
      endTime,
    },
  };

  const updatedAdSet = await MetaAdsSets.findOneAndUpdate(
    { _id: adSet._id },
    {
      $set: {
        ...mergedPayloadForAdSet,
      },
    },
    {
      new: true,
    }
  );

  return updatedAdSet;
};

/**
 * This function uploads the video to meta and returns the videoId
 * @param {String} videoLink
 * @param {ObjectId} communityId
 * @returns {Promise<String>}
 */
const uploadVideoToMeta = async ({ videoLink, communityId }) => {
  const integration = await MetaAdsIntegration.findOne({
    communityObjectId: communityId,
  });
  if (!integration) {
    throw new Error('Meta Ads integration not found');
  }

  const { adsAccountId } = integration;

  const videoId = await createVideoOnMeta({
    videoLink,
    adsAccountId,
  });
  return videoId;
};

const getMagicAudienceVideoUploadStatus = async ({ videoId }) => {
  const videoStatus = await getVideoUploadStatusFromMeta({
    videoId,
  });

  if (!videoStatus) {
    throw new Error('Failed to get video upload status from Meta');
  }

  return videoStatus;
};
/**
 * This function submits the campaign for review and updates the status in the DB
 * @param {ObjectId} campaignId
 * @returns {Promise<Object>}
 */
const submitCampaignForReview = async ({
  campaignId,
  session,
  topupOrderId,
}) => {
  await MetaAdsCampaigns.updateOne(
    {
      _id: campaignId,
      status: META_ADS_STATUS.DRAFT, // only submit draft campaigns for review
    },
    {
      $set: {
        status: META_ADS_STATUS.PENDING,
        submissionDate: new Date(), // set the submission date to now
      },
    },
    { session }
  );

  await MetaAdsSets.updateOne(
    {
      campaignId,
      status: META_ADS_STATUS.DRAFT, // only submit draft campaigns for review
    },
    {
      $set: {
        status: META_ADS_STATUS.PENDING,
      },
    },
    { session }
  );

  await MetaAdsObjects.updateMany(
    {
      campaignId,
      status: META_ADS_STATUS.DRAFT, // only submit draft campaigns for review
    },
    {
      $set: {
        status: META_ADS_STATUS.PENDING,
      },
    },
    { session }
  );

  try {
    await sendCampaignPendingApprovalEmail({
      campaignId,
      topupOrderId,
    });
  } catch (error) {
    logger.error(
      `Error while sending email for ad campaign ${error.message}`
    );
  }

  return {
    campaignId,
    status: META_ADS_STATUS.PENDING,
  };
};

/**
 * This function makes the campaign live on facebook and updates the status in the DB
 * @param {ObjectId} campaignId
 * @param {ObjectId} communityId
 * @returns {Promise<Object>}
 */
const makeCampaignLive = async ({
  campaignInfo,
  communityId,
  isResume = false,
}) => {
  const [campaign, adSet, integration] = await Promise.all([
    MetaAdsCampaigns.findById(campaignInfo._id).lean(),
    MetaAdsSets.findOne({
      campaignId: campaignInfo._id,
      communityObjectId: communityId,
    }).lean(),
    MetaAdsIntegration.findOne({ communityObjectId: communityId }).lean(),
  ]);

  const adObjects = await MetaAdsObjects.find({
    adSetId: adSet?._id,
    status: {
      $nin: [
        META_ADS_STATUS.DELETED,
        META_ADS_STATUS.REJECTED,
        META_ADS_STATUS.PENDING,
      ],
    },
  }).lean();

  if (!campaign) throw new Error('Meta Ads campaign not found');
  if (!adSet) throw new Error('Meta Ads ad set not found');
  if (!adObjects) throw new Error('Meta Ads ad objects not found');
  if (!integration) throw new Error('Meta Ads integration not found');

  const { fbAdSetId } = adSet; // FB’s numeric ad‑set id
  const { fbCampaignId } = campaign; // FB’s numeric campaign id
  const { adsAccountId } = integration;

  const { endTime, startTime } = computeStartAndEndTime({
    duration: adSet.duration,
  });

  const graphPayloadForCampaign = {
    campaign_id: fbCampaignId,
    adset_id: fbAdSetId,
    status: META_ADS_STATUS.ACTIVE,
    access_token: META_APP_ACCESS_TOKEN,
    adsAccountId,
  };

  const graphPayloadForAdSet = {
    adset_id: fbAdSetId,
    status: META_ADS_STATUS.ACTIVE,
    access_token: META_APP_ACCESS_TOKEN,
    adsAccountId,
  };

  if (!isResume) {
    graphPayloadForAdSet.time_stop = endTime; // set end time only when we are making the campaign live for the first time
    graphPayloadForAdSet.start_time = startTime; // set start time only when we are making the campaign live for the first time
    graphPayloadForAdSet.lifetime_budget = adSet.totalBudgetInUSD; // set start time only when we are making the campaign live for the first time

    // check if target contains REGULATED_COUNTRY_CODES
    const { targeting } = adSet;

    const countries = targeting?.geo_locations?.countries ?? [];

    // Build a unique list of regulated categories for any regulated country
    const regulatedCategories = [
      ...new Set(
        countries
          .filter((c) => REGULATED_COUNTRY_CODES.includes(c))
          .flatMap(getRegionalRegulatedCategories)
      ),
    ];

    // Add the field only when needed
    if (regulatedCategories.length) {
      graphPayloadForAdSet.regional_regulated_categories =
        regulatedCategories;
    }
  }

  const graphPayloadForAdObject = {
    // adset_id: fbAdSetId,
    status: META_ADS_STATUS.ACTIVE,
    access_token: META_APP_ACCESS_TOKEN,
    // adsAccountId,
  };
  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    // update the campaign
    await Promise.all([
      await updateMetaAdsCampaign({
        graphPayload: graphPayloadForCampaign,
        campaignId: fbCampaignId,
      }),
      await updateAdSet({
        graphPayload: graphPayloadForAdSet,
        adSetId: fbAdSetId,
      }),
      ...adObjects.map(({ fbAdId }) =>
        updateAdObject({
          graphPayload: graphPayloadForAdObject,
          fbAdId,
        })
      ),
    ]);

    await MetaAdsCampaigns.updateOne(
      { _id: campaign._id },
      {
        $set: {
          status: META_ADS_STATUS.ACTIVE,
        },
      },
      { session }
    );
    await MetaAdsSets.updateOne(
      { _id: adSet._id },
      {
        $set: {
          status: META_ADS_STATUS.ACTIVE,
          ...(!isResume && { startTime, endTime }),
        },
      },
      { session }
    );
    await MetaAdsObjects.updateMany(
      {
        adSetId: adSet._id,
        status: {
          $nin: [
            META_ADS_STATUS.DELETED,
            META_ADS_STATUS.REJECTED,
            META_ADS_STATUS.PENDING,
          ],
        },
      },
      {
        $set: {
          status: META_ADS_STATUS.ACTIVE,
        },
      },
      { session }
    );

    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }

  return {
    campaignId: fbCampaignId,
    adSetId: fbAdSetId,
    status: META_ADS_STATUS.ACTIVE,
  };
};

const createorUpdateAdObjectAlongWithMediaFiles = async ({
  adObject,
  adSetInfo,
  integration,
}) => {
  const { adFormat } = adObject;
  const { fbAdSetId } = adSetInfo;

  const updateBody = {};

  if (adFormat === META_AD_FORMATS.SINGLE_VIDEO) {
    // if the ad format is single video, we need to upload the video first
    const { videoInfo } = adObject;
    const videoId = await createVideoOnMeta({
      videoLink: videoInfo[0].videoUrl,
      adsAccountId: integration.adsAccountId,
    });
    // check the status of the video upload
    let isUploaded = false;
    let maxRetries = 20; //  20 * 6 seconds = 120 seconds
    while (!isUploaded && maxRetries > 0) {
      // eslint-disable-next-line no-await-in-loop
      const videoUploadStatus = await getMagicAudienceVideoUploadStatus({
        videoId,
      });

      const { video_status: status } = videoUploadStatus;

      if (status === 'ready') {
        isUploaded = true; // video is ready
      }

      if (status === 'error') {
        throw new Error(
          `Video upload failed for videoId: ${videoId}. Please try again or reject the campaign.`
        );
      }

      maxRetries--; // decrement after status check
      // wait for 3-4 seconds before checking the status again
      // eslint-disable-next-line no-await-in-loop
      await new Promise((resolve) => setTimeout(resolve, 6000));
    }
    // eslint-disable-next-line no-param-reassign
    adObject.videoInfo = [{ videoId, videoUrl: videoInfo[0].videoUrl }];

    updateBody.videoInfo = [
      {
        videoId,
        videoUrl: videoInfo[0].videoUrl,
        videoThumbnailUrl: videoInfo?.[0]?.videoThumbnailUrl ?? '',
      },
    ];
  } else if (adFormat === META_AD_FORMATS.SINGLE_IMAGE) {
    // if the ad format is single image, we need to upload the image first
    const { mediaUrls } = adObject;
    if (!mediaUrls || mediaUrls.length === 0) {
      throw new Error(
        'Media URLs are required for single image ad format'
      );
    }
  }

  const { adsAccountId, pageId } = integration;

  const assetFeedSpec = buildAssetFeedSpec({
    adFormat,
    pageId,
    primaryTexts: adObject.primaryTexts,
    headlines: adObject.headlines,
    descriptions: adObject.descriptions,
    linkUrls: adObject.linkUrls,
    mediaUrls: adObject.mediaUrls,
    videoInfo: adObject.videoInfo,
  });
  const objectStorySpec = {
    page_id: pageId,
  };

  const graphPayload = {
    name: `${adSetInfo?.campaignId?.campaignName ?? ''} • ${adFormat}`,
    adset_id: fbAdSetId,
    creative: {
      object_story_spec: objectStorySpec,
      asset_feed_spec: assetFeedSpec,
    },
    status: META_ADS_STATUS.PAUSED, // we will set the ad to PAUSED state after creating the ad object
    access_token: META_APP_ACCESS_TOKEN,
    adsAccountId,
  };

  let fbAdId;

  if (!adObject.fbAdId) {
    fbAdId = await createMetaAdsAdObject({
      ...graphPayload,
      adsAccountId,
    });
    updateBody.fbAdId = fbAdId; // set the fbAdId in the update body
  } else {
    // update the ad object if it already exists
    fbAdId = await updateAdObject({
      graphPayload: {
        creative: {
          object_story_spec: objectStorySpec,
          asset_feed_spec: assetFeedSpec,
        },
        access_token: META_APP_ACCESS_TOKEN,
        status: META_ADS_STATUS.PAUSED, // we will set the ad to PAUSED state after updating the ad object
      },
      fbAdId: adObject.fbAdId,
    });
  }

  await MetaAdsObjects.updateOne(
    {
      _id: adObject._id,
    },
    {
      $set: {
        ...updateBody,
      },
    }
  );

  return fbAdId; // return the facebook ad id
};

const makeAdObjectLive = async ({ fbAdId, adObject, adSetInfo }) => {
  const adObjectId = adObject._id;

  const integration = await MetaAdsIntegration.findOne({
    communityObjectId: adObject.communityObjectId,
  }).lean();

  if (!integration) {
    throw new Error('Meta Ads integration not found');
  }
  await createorUpdateAdObjectAlongWithMediaFiles({
    adObject,
    adSetInfo,
    integration,
  });

  // eslint-disable-next-line no-param-reassign
  const adObjectInfo = await MetaAdsObjects.findOne({
    _id: adObjectId,
  }).lean();
  const graphPayload = {
    status: META_ADS_STATUS.ACTIVE,
    access_token: META_APP_ACCESS_TOKEN,
  };

  await updateAdObject({
    graphPayload,
    fbAdId: fbAdId || adObjectInfo.fbAdId,
  });
  await MetaAdsObjects.updateOne(
    { _id: adObjectId },
    {
      $set: {
        status: META_ADS_STATUS.ACTIVE,
      },
    }
  );
};

/**
 * This function approves or rejects the campaign based on the status
 * @param {ObjectId} communityId
 * @param {ObjectId} campaignId
 * @param {String} status
 * @param {String} rejectedReason
 * @returns {Promise<Object>}
 */
const approveOrRejectMagicAudienceCampaign = async ({
  communityId,
  campaignId,
  status,
  rejectedReason,
}) => {
  const campaignInfo = await MetaAdsCampaigns.findById(campaignId).lean();
  const adSet = await MetaAdsSets.findOne({
    campaignId,
    communityObjectId: communityId,
  })
    .populate('campaignId')
    .lean();

  if (status === META_ADS_STATUS.ACTIVE) {
    await makeCampaignLive({
      campaignInfo,
      communityId,
    });
  } else if (status === META_ADS_STATUS.REJECTED) {
    let remainingFunds = 0; // default value
    if (
      [META_ADS_STATUS.PAUSED, META_ADS_STATUS.ACTIVE].includes(
        campaignInfo.status
      )
    ) {
      const [
        { spend: totalSpend = 0 } = {}, // default to 0 if no spend data is available
      ] = await getCampaignInsights(campaignInfo.fbCampaignId, {
        fields: 'spend',
      });

      remainingFunds = adSet.totalBudgetInUSD - Number(totalSpend) * 100; // convert to cents

      await MetaAdsCampaigns.updateOne(
        { _id: campaignId },
        {
          $set: {
            totalSpend,
          },
        }
      );
    }
    await resettleFundsAndUpdateCampaignStatus({
      status: META_ADS_STATUS.REJECTED,
      communityId,
      campaignInfo,
      remainingFunds, // no funds to return in case of rejection ( because the campaign is not live yet and it was rejected before going live)
      rejectedReason,
    });
  }
  // because we don't care if the email delivery fails or not, we will just log the error
  try {
    await sendEmailToUser({
      campaignId,
    });
  } catch (error) {
    logger.error(
      `Error while sending email for campaign ${status}: ${error.message}`
    );
  }
  return {
    campaignId,
    status,
  };
};

/**
 * Approve (→ live or paused) / Reject an ad-object.
 *
 *  ACTIVE   + campaign live   → makeAdObjectLive (FB)  + make ACTIVE in MONGO¯
 *  ACTIVE   + campaign not live → mark PAUSED in Mongo
 *  REJECTED → set REJECTED in Mongo; if campaign live also pause on FB
 */
async function approveOrRejectMagicAudienceAdObject({
  adObjectId,
  status,
  rejectedReason,
}) {
  /* ---------- 1. fetch ad-object (+ campaign id) & check campaign state ---------- */
  const adObject = await MetaAdsObjects.findById(adObjectId)
    .populate({ path: 'adSetId', select: 'campaignId' }) // only what we nee
    .lean();

  if (!adObject) throw new Error('Ad object not found');
  const adSetInfo = await MetaAdsSets.findById(adObject.adSetId);
  const integration = await MetaAdsIntegration.findOne({
    communityObjectId: adObject.communityObjectId,
  }).lean();
  if (!integration) {
    throw new Error('Meta Ads integration not found');
  }
  const { campaignId } = adObject.adSetId;
  const isCampaignLive = await MetaAdsCampaigns.exists({
    _id: campaignId,
    status: META_ADS_STATUS.ACTIVE,
  });

  if (status === META_ADS_STATUS.REJECTED) {
    await MetaAdsObjects.updateOne(
      { _id: adObjectId },
      {
        $set: {
          status: META_ADS_STATUS.REJECTED,
          errorReason: rejectedReason,
        },
      }
    );

    if (adObject.fbAdId) {
      await updateAdObject({
        fbAdId: adObject.fbAdId,
        graphPayload: {
          status: META_ADS_STATUS.PAUSED,
          access_token: META_APP_ACCESS_TOKEN,
        },
      });
    }
  } else if (status === META_ADS_STATUS.ACTIVE || status === 'APPROVED') {
    if (isCampaignLive) {
      /* — campaign live: make the ad live on Facebook — */
      await makeAdObjectLive({
        fbAdId: adObject.fbAdId,
        adObject,
        adSetInfo,
      });
      await MetaAdsObjects.updateOne(
        { _id: adObjectId },
        { $set: { status: META_ADS_STATUS.ACTIVE } }
      );
    } else {
      await createorUpdateAdObjectAlongWithMediaFiles({
        adObject,
        adSetInfo,
        integration,
      });

      /* — campaign not live yet: keep it paused in Mongo — */
      await MetaAdsObjects.updateOne(
        { _id: adObjectId },
        { $set: { status: META_ADS_STATUS.PAUSED } }
      );
    }
  }

  // send email to user about the ad object approval or rejection
  try {
    await sendEmailToUser({
      campaignId,
      singleAdObjectId: adObjectId,
    });
  } catch (error) {
    logger.error(
      `Error while sending email for ad object ${status}: ${error.message}`
    );
  }

  return { adObjectId, status };
}

const activateAllAds = async ({ campaignId, communityId }) => {
  const campaignInfo = await MetaAdsCampaigns.findOne({
    _id: campaignId,
    communityObjectId: communityId,
  }).lean();

  const { status } = campaignInfo;

  if (status !== META_ADS_STATUS.PAUSED) {
    throw new Error(
      `Cannot activate ads in campaign with status ${status}. Only PAUSED campaigns can be activated.`
    );
  }

  await makeCampaignLive({
    campaignInfo,
    communityId,
    isResume: true,
  });

  return {
    campaignId: campaignInfo.fbCampaignId,
    status: META_ADS_STATUS.ACTIVE,
  };
};

const completeAllAds = async ({ campaignId, communityId }) => {
  const campaignInfo = await MetaAdsCampaigns.findOne({
    _id: campaignId,
    communityObjectId: communityId,
  })
    .select('fbCampaignId status')
    .lean();

  if (
    ![META_ADS_STATUS.PAUSED, META_ADS_STATUS.ACTIVE].includes(
      campaignInfo.status
    )
  ) {
    throw new Error(
      `Cannot complete ads in campaign with status ${campaignInfo.status}. Only PAUSED or ACTIVE campaigns can be completed.`
    );
  }

  if (!campaignInfo.fbCampaignId) {
    throw new Error('Meta Ads campaign not found ');
  }

  const [
    { spend: totalSpend = 0, actions, impressions } = {}, // default to 0 if no spend data is available
  ] = await getCampaignInsights(campaignInfo.fbCampaignId, {
    fields: 'spend,actions,impressions',
  });

  await settleFundsForCompletedMagicAudienceCampaign({
    communityId,
    campaignId,
    totalSpend,
  });

  try {
    await sendEmailToUser({
      campaignId,
      analyticData: {
        actions: actions || [],
        impressions: impressions || 0,
        spend: totalSpend,
      },
    });
  } catch (error) {
    logger.error(
      `Error while sending email for campaign completion: ${error.message}`
    );
  }

  await updateAllTimeDailyAdAndCampaignInsights({
    fbCampaignId: campaignInfo.fbCampaignId,
    communityId,
  });
  return {
    campaignId: campaignInfo.fbCampaignId,
    status: META_ADS_STATUS.COMPLETED,
  };
};

/**
 * This function deletes the campaign, ad set and ad objects from facebook and updates the status in the DB
 * @param {ObjectId} campaignId
 * @param {ObjectId} communityId
 * @returns {Promise<Boolean>}
 */
const deleteMagicAudienceCampaign = async ({
  campaignId,
  communityId,
}) => {
  const [integration, adSet, campaignInfo] = await Promise.all([
    MetaAdsIntegration.findOne({ communityObjectId: communityId }).lean(),
    MetaAdsSets.findOne({
      campaignId,
      communityObjectId: communityId,
    }).lean(),
    MetaAdsCampaigns.findById(campaignId).lean(),
  ]);

  const { fbAdSetId: adSetId } = adSet; // FB’s numeric ad‑set id
  if (integration && adSetId) {
    const currentStatus = campaignInfo.status;

    let remainingFunds = 0; // default value
    // this means the campaign was active once and we need to calculate the remaining funds
    if (
      currentStatus === META_ADS_STATUS.ACTIVE ||
      currentStatus === META_ADS_STATUS.PAUSED
    ) {
      const [
        { spend: totalSpend = 0 } = {}, // default to 0 if no spend data is available
      ] = await getCampaignInsights(campaignInfo.fbCampaignId, {
        fields: 'spend',
      });

      remainingFunds = adSet.totalBudgetInUSD - Number(totalSpend) * 100; // convert to cents

      await MetaAdsCampaigns.updateOne(
        { _id: campaignId },
        {
          $set: {
            totalSpend,
          },
        }
      );
    }

    await resettleFundsAndUpdateCampaignStatus({
      campaignInfo,
      status: META_ADS_STATUS.DELETED,
      communityId,
      remainingFunds,
    });
  }

  await Promise.all([
    MetaAdsCampaigns.updateOne(
      { _id: campaignId },
      {
        $set: {
          status: META_ADS_STATUS.DELETED,
        },
      }
    ),
    MetaAdsSets.updateOne(
      { _id: adSet._id },
      {
        $set: {
          status: META_ADS_STATUS.DELETED,
        },
      }
    ),
    MetaAdsObjects.updateMany(
      { adSetId: adSet._id },
      {
        $set: {
          status: META_ADS_STATUS.DELETED,
        },
      }
    ),
  ]);

  return true;
};

const assignAdAccountToCommunity = async ({
  communityId,
  pageId,
  pageTitle,
  pageDescription,
}) => {
  const integration = await MetaAdsIntegration.findOne({
    communityObjectId: communityId,
  }).lean();
  if (integration) {
    throw new Error(
      'Meta Ads integration already exists for this community'
    );
  }

  const communityInfo = await CommunityModel.findById(communityId).lean();
  if (!communityInfo) {
    throw new Error('Community not found');
  }

  const nasAccount = await NasioMetaAdsAccounts.findOne({
    communityObjectId: { $exists: false },
  })
    .select('adsAccountId pixelId')
    .lean();

  if (!nasAccount?.adsAccountId || !nasAccount?.pixelId) {
    throw new Error(
      'Nasio Ads Account or Pixel ID not found. Please contact support.'
    );
  }

  const [metaAdsIntegration] = await Promise.all([
    MetaAdsIntegration.updateOne(
      {
        adsAccountId: nasAccount.adsAccountId,
        communityObjectId: communityId,
      },
      {
        $set: {
          adsAccountId: nasAccount.adsAccountId,
          pixelId: nasAccount.pixelId,
          pageId,
          pageTitle,
          pageDescription,
          pageThumbnail: '',
        },
      },
      {
        upsert: true, // create if not exists
      }
    ),
    CommunityModel.updateOne(
      { _id: communityId },
      { $set: { 'trackingPixels.facebookPixel': nasAccount.pixelId } }
    ),
    NasioMetaAdsAccounts.updateOne(
      { pixelId: nasAccount.pixelId }, // update the communityObjectId
      { $set: { communityObjectId: communityId } }
    ),
  ]);

  return metaAdsIntegration;
};
const getEntityCampaignInfo = async ({ entityObjectId, entityType }) => {
  const campaign = await MetaAdsCampaigns.find({
    entityObjectId,
    status: { $ne: META_ADS_STATUS.DELETED },
    entityType,
  }).lean();

  return {
    activeCampaigns: campaign?.filter(
      (c) => c?.status === META_ADS_STATUS.ACTIVE
    ),
    pausedCampaigns: campaign?.filter(
      (c) => c?.status === META_ADS_STATUS.PAUSED
    ),
    pendingCampaigns: campaign?.filter(
      (c) => c?.status === META_ADS_STATUS.PENDING
    ),
    rejectedCampaigns: campaign?.filter(
      (c) => c?.status === META_ADS_STATUS.REJECTED
    ),
    draftCampaigns: campaign?.filter(
      (c) => c?.status === META_ADS_STATUS.DRAFT
    ),
  };
};

// this is only for time being, we will remove this in future once we get an app approval
const applyForMagicAudience = async ({ communityId, learner }) => {
  const applicationExists = await MetaAdsCommunitiesApplications.findOne({
    communityObjectId: communityId,
  }).lean();

  if (applicationExists) {
    return {
      success: false,
      message: 'Application already exists for this community.',
    };
  }

  await MetaAdsCommunitiesApplications.create({
    communityObjectId: communityId,
  });
  await sendApplicationEmail({
    learner,
  });
  // send an email
};
const reportedIssuesOnAdEntities = async ({
  level,
  entityId,
  errorCode,
  errorMessage,
  errorSummary,
}) => {
  const REASON = `${errorCode} - ${errorMessage} - ${errorSummary}`.trim();
  const ISSUE = {
    status: META_ADS_STATUS.REJECTED,
    facebookStatus: META_AD_ENTITIES_STATUS.WITH_ISSUES,
  };

  let campaignInfo; // populated in every branch
  let adSetInfo; // populated for fund-settlement
  let singleAd = false;

  /* ---------- branch by level ---------------------------------------------- */
  switch (level) {
    /* ───────────── AD ───────────── */
    case 'AD': {
      const ad = await MetaAdsObjects.findOne(
        { fbAdId: entityId },
        { adSetId: 1, campaignId: 1 }
      ).lean();

      if (!ad)
        return logger.error(`Ad|${entityId}|not-found (reportIssues)`);

      /* fetch campaign + ad-set & update the ad in parallel */
      const [campaign, adSet] = await Promise.all([
        MetaAdsCampaigns.findById(ad.campaignId, {
          fbCampaignId: 1,
          communityObjectId: 1,
        }).lean(),
        MetaAdsSets.findById(ad.adSetId, { totalBudgetInUSD: 1 }).lean(),
        MetaAdsObjects.updateOne(
          { fbAdId: entityId },
          { $set: { ...ISSUE, errorFacebookReason: REASON } }
        ),
      ]);

      campaignInfo = campaign;
      adSetInfo = adSet;

      /* quick check: any *other* active ad in this ad-set? */
      singleAd = !(await MetaAdsObjects.exists({
        adSetId: ad.adSetId,
        fbAdId: { $ne: entityId },
        status: { $ne: META_ADS_STATUS.DELETED },
      }));
      break;
    }

    /* ───────────── AD-SET ───────────── */
    case 'ADSET': {
      adSetInfo = await MetaAdsSets.findOne(
        { fbAdSetId: entityId },
        { _id: 1, campaignId: 1, totalBudgetInUSD: 1 }
      ).lean();

      if (!adSetInfo)
        return logger.error(`AdSet|${entityId}|not-found (reportIssues)`);

      campaignInfo = await MetaAdsCampaigns.findById(
        adSetInfo.campaignId,
        {
          fbCampaignId: 1,
          communityObjectId: 1,
        }
      ).lean();

      /* mark ad-set, its ads, and the campaign in parallel */
      await Promise.all([
        MetaAdsSets.updateOne(
          { _id: adSetInfo._id },
          { $set: { ...ISSUE, errorFacebookReason: REASON } }
        ),
        MetaAdsObjects.updateMany(
          { adSetId: adSetInfo._id },
          { $set: ISSUE }
        ),
        MetaAdsCampaigns.updateOne(
          { _id: campaignInfo._id },
          { $set: ISSUE }
        ),
      ]);
      break;
    }

    /* ───────────── CAMPAIGN ───────────── */
    case 'CAMPAIGN': {
      campaignInfo = await MetaAdsCampaigns.findOne(
        { fbCampaignId: entityId },
        { _id: 1, fbCampaignId: 1, communityObjectId: 1, adSetIds: 1 }
      ).lean();

      if (!campaignInfo)
        return logger.error(
          `Campaign|${entityId}|not-found (reportIssues)`
        );

      /* mark campaign, its ad-sets, and ads in parallel */
      await Promise.all([
        MetaAdsCampaigns.updateOne(
          { _id: campaignInfo._id },
          { $set: { ...ISSUE, errorFacebookReason: REASON } }
        ),
        MetaAdsSets.updateMany(
          { campaignId: campaignInfo._id },
          { $set: ISSUE }
        ),
        MetaAdsObjects.updateMany(
          { adSetId: { $in: campaignInfo.adSetIds } },
          { $set: ISSUE }
        ),
      ]);

      /* any ad-set will do for budget lookup */
      adSetInfo = await MetaAdsSets.findOne(
        { campaignId: campaignInfo._id },
        { totalBudgetInUSD: 1 }
      ).lean();
      break;
    }

    /* ───────────── unsupported ───────────── */
    default:
      logger.warn(`Unsupported level "${level}" passed to reportIssues`);
      return;
  }

  /* ---------- fund settlement (when required) ------------------------------ */
  if (!singleAd || level !== 'AD') {
    const [{ spend = 0 } = {}] = await getCampaignInsights(
      campaignInfo.fbCampaignId,
      { fields: 'spend' }
    );

    const remainingFunds =
      (adSetInfo?.totalBudgetInUSD ?? 0) - spend * 100; // convert to cents

    await MetaAdsCampaigns.updateOne(
      { _id: campaignInfo._id },
      {
        $set: {
          totalSpend: spend,
        },
      }
    );

    await resettleFundsAndUpdateCampaignStatus({
      status: META_ADS_STATUS.REJECTED,
      communityId: campaignInfo.communityObjectId,
      campaignInfo,
      remainingFunds,
      rejectedReason: REASON,
    });
  }

  return { level, entityId };
};

const handleAdsWebhookInProcess = async ({
  level,
  entityId,
  statusName,
}) => {
  const LEVEL_CONFIG = {
    AD: { model: MetaAdsObjects, key: 'fbAdId' },
    ADSET: { model: MetaAdsSets, key: 'fbAdSetId' },
    CAMPAIGN: { model: MetaAdsCampaigns, key: 'fbCampaignId' },
  };

  const config = LEVEL_CONFIG[level];
  if (!config) {
    return logger.warn(
      `handleAdsWebhookInProcess ➜ unknown level "${level}"`
    );
  }

  await config.model.updateOne(
    { [config.key]: entityId },
    { $set: { facebookStatus: statusName } }
  );
};

const terminateMagicAudienceCampaign = async ({
  communityId,
  campaignId,
}) => {
  const [integration, adSet, campaignInfo] = await Promise.all([
    MetaAdsIntegration.findOne({ communityObjectId: communityId }).lean(),
    MetaAdsSets.findOne({
      campaignId,
      communityObjectId: communityId,
    }).lean(),
    MetaAdsCampaigns.findById(campaignId).lean(),
  ]);

  const adObjects = await MetaAdsObjects.find({
    adSetId: adSet?._id,
  }).lean();

  const { fbAdSetId: adSetId } = adSet; // FB’s numeric ad‑set id
  if (integration && adSetId) {
    await Promise.all([
      await deleteMetaAdsCampaign(campaignInfo.fbCampaignId),
      await deleteMetaAdsAdSet(adSetId),
      ...adObjects.map((adObject) => {
        const { fbAdId } = adObject;
        if (!fbAdId) {
          return true; // if fbAdId is not present, we can skip deleting it
        }
        return deleteMetaAdsAdObject(fbAdId);
      }),
    ]);
  }

  await Promise.all([
    MetaAdsCampaigns.updateOne(
      { _id: campaignId },
      {
        $set: {
          status: META_ADS_STATUS.DELETED,
        },
      }
    ),
    MetaAdsSets.updateOne(
      { _id: adSet._id },
      {
        $set: {
          status: META_ADS_STATUS.DELETED,
        },
      }
    ),
    MetaAdsObjects.updateMany(
      { adSetId: adSet._id },
      {
        $set: {
          status: META_ADS_STATUS.DELETED,
        },
      }
    ),
  ]);

  return true;
};

const createMagicAudiencePixel = async (adsAccountId) => {
  const nasioMetaAdsAccounts = await NasioMetaAdsAccounts.countDocuments();

  const newPixelName = `prod_pixel_${nasioMetaAdsAccounts + 1}`;
  const pixel = await createMetaAdsPixel({
    pixelName: newPixelName,
    adsAccountId,
  });

  if (!pixel) {
    throw new Error('Failed to create Magic Audience Pixel');
  }

  await NasioMetaAdsAccounts.create({
    pixelId: pixel,
    adsAccountId,
  });

  return {
    pixelId: pixel,
  };
};

const updateAdObjectLink = async ({
  adObjectId,
  campaignId,
  communityId,
}) => {
  const [community, campaignInfo] = await Promise.all([
    CommunityModel.findById(communityId).lean(),
    MetaAdsCampaigns.findById(campaignId).lean(),
  ]);

  const linkUrls = [await getAdLinkUrl(campaignInfo, community)];

  await MetaAdsObjects.updateOne(
    { _id: adObjectId },
    {
      $set: {
        linkUrls,
      },
    }
  );
};

module.exports = {
  getMagicAudienceRecommendedProducts,
  getMagicAudienceVideoUploadStatus,
  getMagicAudienceCampaignEstimate,
  getMagicAudienceCampaigns,
  getMagicAudienceCampaignById,
  getMagicAudienceProducts,
  getEntityCampaignInfo,
  getMagicAudienceAllAdEntities,
  getMetaPages,
  createMagicAudienceCampaign,
  createMagicAudienceAdObject,
  updateMagicAudienceAdObject,
  updateMagicAudienceCampaign,
  uploadVideoToMeta,
  submitCampaignForReview,
  makeCampaignLive,
  deleteMagicAudienceCampaign,
  approveOrRejectMagicAudienceCampaign,
  approveOrRejectMagicAudienceAdObject,
  reportedIssuesOnAdEntities,
  handleAdsWebhookInProcess,
  connectUsersPageToBusiness,
  pauseAllAds,
  pauseMagicAudienceAdObject,
  activateAllAds,
  completeAllAds,
  disconnectUsersPageFromBusiness,
  terminateMagicAudienceCampaign,
  settleFundsForCompletedMagicAudienceCampaign,
  assignAdAccountToCommunity,
  applyForMagicAudience,
  createMagicAudiencePixel,
  updateAdObjectLink,
};
