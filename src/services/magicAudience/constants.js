const CommunityModel = require('../../communitiesAPI/models/community.model');
const CommunityEventsModel = require('../../communitiesAPI/models/communityEvents.model');
const communityFoldersModel = require('../../communitiesAPI/models/communityFolders.model');
const ProgramModel = require('../../models/program/program.model');

const META_ADS_GRAPH_API = 'https://graph.facebook.com/v22.0/';
const DEFAULT_AD_ACCOUNT_ID_DEV = '****************'; // TODO remove this later
const DEFAULT_CURRENCY_FOR_AD_ACCOUNT = 'USD';
const META_ADS_STATUS = {
  ACTIVE: 'ACTIVE',
  PAUSED: 'PAUSED',
  DELETED: 'DELETED',
  PENDING: 'APPROVAL_PENDING',
  REJECTED: 'REJECTED',
  DRAFT: 'DRAFT',
  COMPLETED: 'COMPLETED',
  ARCHIVED: 'ARCHIVED',
};

const LINK_CLICK_EVENT = 'link_click'; // page-visit proxy
const CONVERTED_EVENT = 'purchase'; // “sign-up” proxy - this may change later (have to check the product team) // TODO: check with product team and change this later

const META_ADS_CONVERSION_EVENTS = {
  SUBSCRIBE: 'SUBSCRIBE',
  COMPLETE_REGISTRATION: 'COMPLETE_REGISTRATION',
};
const META_AD_ENTITIES_STATUS = {
  WITH_ISSUES: 'WITH_ISSUES',
};

const MAGIC_ADS_MAIL_TYPES = {
  MAGIC_ADS_CAMPAIGN_LIVE_WITH_ADS: 'MAGIC_ADS_CAMPAIGN_LIVE_WITH_ADS',
  MAGIC_ADS_CAMPAIGN_LIVE_WITH_ISSUES_IN_ADS:
    'MAGIC_ADS_CAMPAIGN_LIVE_WITH_ISSUES_IN_ADS',
  MAGIC_ADS_CAMPAIGN_REJECTED: 'MAGIC_ADS_CAMPAIGN_REJECTED',
  MAGIC_ADS_AD_OBJECT_APPROVED: 'MAGIC_ADS_AD_OBJECT_APPROVED',
  MAGIC_ADS_AD_OBJECT_REJECTED: 'MAGIC_ADS_AD_OBJECT_REJECTED',
  MAGIC_ADS_CAMPAIGN_ENDED: 'MAGIC_ADS_CAMPAIGN_ENDED',
  MAGIC_ADS_CAMPAIGN_SUBMITTED: 'MAGIC_ADS_CAMPAIGN_SUBMITTED',
  MAGIC_ADS_FEATURE_APPLY: 'MAGIC_ADS_FEATURE_APPLY',
};
const META_ADS_STATUS_LIST = [
  META_ADS_STATUS.ACTIVE,
  META_ADS_STATUS.PAUSED,
  META_ADS_STATUS.DELETED,
  META_ADS_STATUS.PENDING,
  META_ADS_STATUS.REJECTED,
  META_ADS_STATUS.DRAFT,
];
const DEFAULT_AD_DAILY_BUDGET = 200; // 2 USD
const META_ADS_ENTITY_TYPE = {
  COMMUNITY: 'community',
  EVENT: 'event',
  SESSION: 'session',
  FOLDER: 'folder',
  CHALLENGE: 'challenge',
  DIGITAL_FILES: 'digital_files',
  COURSE: 'course',
};
const META_ADS_OBJECTIVE = {
  OUTCOME_TRAFFIC: 'OUTCOME_TRAFFIC',
  OUTCOME_CONVERSIONS: 'OUTCOME_CONVERSIONS',
  OUTCOME_SALES: 'OUTCOME_SALES',
};
const META_OPTIMIZATION_GOAL = {
  LINK_CLICKS: 'LINK_CLICKS',
  CONVERSIONS: 'CONVERSIONS',
  OFFSITE_CONVERSIONS: 'OFFSITE_CONVERSIONS',
  LEAD_GENERATION: 'LEAD_GENERATION',
  ENGAGEMENT: 'ENGAGEMENT',
  BRAND_AWARENESS: 'BRAND_AWARENESS',
  REACH: 'REACH',
};

const META_AD_FORMATS = {
  SINGLE_IMAGE: 'SINGLE_IMAGE',
  SINGLE_VIDEO: 'SINGLE_VIDEO',
};

const META_BILLING_EVENTS = {
  IMPRESSIONS: 'IMPRESSIONS',
  LINK_CLICKS: 'LINK_CLICKS',
  POST_ENGAGEMENT: 'POST_ENGAGEMENT',
  PAGE_ENGAGEMENT: 'PAGE_ENGAGEMENT',
  APP_INSTALLS: 'APP_INSTALLS',
  LEAD_GENERATION: 'LEAD_GENERATION',
  VIDEO_VIEWS: 'VIDEO_VIEWS',
};

const META_BID_STRATEGY = {
  LOWEST_COST_WITHOUT_CAP: 'LOWEST_COST_WITHOUT_CAP',
};

const META_AD_CALL_TO_ACTION_TYPES = {
  LEARN_MORE: 'LEARN_MORE',
  SIGN_UP: 'SIGN_UP',
  APPLY_NOW: 'APPLY_NOW',
  GET_OFFER: 'GET_OFFER',
  GET_QUOTE: 'GET_QUOTE',
  BOOK_NOW: 'BOOK_NOW',
  DOWNLOAD: 'DOWNLOAD',
  SHOP_NOW: 'SHOP_NOW',
  WATCH_MORE: 'WATCH_MORE',
  INSTALL_APP: 'INSTALL_APP',
};

const PLATFORM_CONFIG = {
  facebook: {
    facebook_positions: [
      'feed',
      'right_hand_column',
      'instant_article',
      'marketplace',
      'search',
      'story',
      'video_feeds',
    ],
  },
  messenger: {
    messenger_positions: ['inbox', 'story_home', 'sponsored_message'],
  },
  audience_network: {
    audience_network_positions: [
      'classic',
      'instream_video',
      'rewarded_video',
    ],
  },
  instagram: {
    instagram_positions: [
      'stream',
      'story',
      'explore',
      'reels',
      'shop',
      'profile_feed',
      'profile_reels',
    ],
  },
};

const MODEL_BY_ENTITY = {
  [META_ADS_ENTITY_TYPE.COMMUNITY]: CommunityModel,
  [META_ADS_ENTITY_TYPE.EVENT]: CommunityEventsModel,
  [META_ADS_ENTITY_TYPE.SESSION]: communityFoldersModel,
  [META_ADS_ENTITY_TYPE.FOLDER]: communityFoldersModel,
  [META_ADS_ENTITY_TYPE.CHALLENGE]: ProgramModel,
  [META_ADS_ENTITY_TYPE.DIGITAL_FILES]: communityFoldersModel,
  [META_ADS_ENTITY_TYPE.COURSE]: communityFoldersModel,
};
const GET_SLUG_OF_PRODUCT = {
  [META_ADS_ENTITY_TYPE.COMMUNITY]: () => '',
  [META_ADS_ENTITY_TYPE.EVENT]: ({ slug }) => slug,
  [META_ADS_ENTITY_TYPE.SESSION]: ({ resourceSlug }) => resourceSlug,
  [META_ADS_ENTITY_TYPE.FOLDER]: ({ resourceSlug }) => resourceSlug,
  [META_ADS_ENTITY_TYPE.DIGITAL_FILES]: ({ resourceSlug }) => resourceSlug,
  [META_ADS_ENTITY_TYPE.COURSE]: ({ resourceSlug }) => resourceSlug,
  [META_ADS_ENTITY_TYPE.CHALLENGE]: ({ slug }) => slug,
};

const META_ADS_DATE_PRESET = {
  TODAY: 'today',
  LAST_7_DAYS: 'last_7d',
  LAST_30_DAYS: 'last_30d',
  THIS_MONTH: 'this_month',
  LAST_MONTH: 'last_month',
};
// russia, china, iran, north korea
const ILLEGAL_COUNTRY_CODES = ['RU', 'CN', 'IR', 'KP'];

const getRegionalRegulatedCategories = (code) => {
  switch (code.toUpperCase()) {
    case 'SG': // Singapore
      return ['SINGAPORE_UNIVERSAL'];
    case 'TW': // Taiwan
      return ['TAIWAN_UNIVERSAL'];
    case 'AU': // Australia (Financial Service ads)
      return ['AUSTRALIA_FINSERV'];
    default:
      return null; // Not required
  }
};
const REGULATED_COUNTRY_CODES = [
  'SG',
  'TW', // Taiwan
  'AU', // Australia
];
module.exports = {
  ILLEGAL_COUNTRY_CODES,
  META_ADS_CONVERSION_EVENTS,
  META_ADS_STATUS,
  META_OPTIMIZATION_GOAL,
  META_ADS_ENTITY_TYPE,
  META_ADS_GRAPH_API,
  META_ADS_OBJECTIVE,
  DEFAULT_AD_ACCOUNT_ID_DEV,
  META_BILLING_EVENTS,
  META_BID_STRATEGY,
  META_AD_ENTITIES_STATUS,
  DEFAULT_AD_DAILY_BUDGET,
  DEFAULT_CURRENCY_FOR_AD_ACCOUNT,
  META_AD_CALL_TO_ACTION_TYPES,
  META_AD_FORMATS,
  PLATFORM_CONFIG,
  META_ADS_STATUS_LIST,
  MODEL_BY_ENTITY,
  GET_SLUG_OF_PRODUCT,
  META_ADS_DATE_PRESET,
  LINK_CLICK_EVENT,
  CONVERTED_EVENT,
  MAGIC_ADS_MAIL_TYPES,
  REGULATED_COUNTRY_CODES,
  getRegionalRegulatedCategories,
};
