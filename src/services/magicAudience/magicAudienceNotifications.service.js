const { DateTime } = require('luxon');
const {
  NAS_IO_FRONTEND_URL,
  PAYMENT_MAILER_URL,
} = require('../../config');
const learnersModel = require('../../models/learners.model');
const MetaAdsCampaigns = require('../../models/magicAudience/metaAdsCampaigns.model');
const MetaAdsObjects = require('../../models/magicAudience/metaAdsObjects.model');
const MetaAdsSets = require('../../models/magicAudience/metaAdsSets.model');
const CommunityTopupOrderModel = require('../../models/order/communityTopupOrder.model');
const { getName } = require('../../utils/name.util');
const logger = require('../logger.service');
const { sendEmail } = require('../notification');
const { MAGIC_ADS_MAIL_TYPES, META_ADS_STATUS } = require('./constants');
const {
  getEntityInfo,
  getConversionEventName,
  getActionValue,
} = require('./utils/adsData.util');
const { getCommonVariables } = require('./utils/notifications.util');
const { PURCHASE_TYPE } = require('../../constants/common');
const { sendMessageToSQSQueue } = require('../../handlers/sqs.handler');

const getMagicAudienceMailVariables = async ({
  campaignInfo,
  topupOrder,
  entityInfo,
  additionalData = {},
}) => {
  const learner = await learnersModel
    .findById(topupOrder.learnerObjectId)
    .select({ firstName: 1, lastName: 1, email: 1 })
    .lean();

  if (!learner) {
    throw new Error('Learner not found');
  }

  const name = getName(learner.firstName, learner.lastName, learner.email);

  const commonVariables = getCommonVariables({
    firstName: name,
    productName: entityInfo.title,
    campaignName: campaignInfo.campaignName,
    magicAdsDashboard: `${NAS_IO_FRONTEND_URL}/portal/magic-ads/manage?id=${campaignInfo._id}`,
  });

  return {
    ...commonVariables,
    ...additionalData,
    toMail: [learner.email],
    toMailName: [name],
  };
};

const sendEmailToUser = async ({
  campaignId,
  singleAdObjectId = null,
  analyticData = {},
}) => {
  let singleAdObject = null;

  if (singleAdObjectId) {
    singleAdObject = await MetaAdsObjects.findById(
      singleAdObjectId
    ).lean();
  }

  const { actions, spend: totalSpend, impressions } = analyticData || {};
  const campaignInfo = await MetaAdsCampaigns.findById(campaignId).read(
    'primary'
  );
  const adSetInfo = await MetaAdsSets.findOne({
    campaignId: campaignInfo._id,
  })
    .read('primary')
    .lean();
  const adObjects = await MetaAdsObjects.find({
    campaignId: campaignInfo._id,
    adSetId: adSetInfo._id,
    status: {
      $in: [
        META_ADS_STATUS.ACTIVE,
        META_ADS_STATUS.REJECTED,
        META_ADS_STATUS.PAUSED,
      ],
    },
  })
    .read('primary')
    .lean();

  const entityInfo = await getEntityInfo({
    entityType: campaignInfo.entityType,
    entityObjectId: campaignInfo.entityObjectId,
  });
  const campaignStatus = campaignInfo.status;
  let mailType;
  let additionalData = {};

  // 🔹 1. First priority: Ad-level updates
  if (
    singleAdObject?.status === META_ADS_STATUS.ACTIVE ||
    singleAdObject?.status === META_ADS_STATUS.PAUSED ||
    singleAdObject?.status === META_ADS_STATUS.PENDING
  ) {
    mailType = MAGIC_ADS_MAIL_TYPES.MAGIC_ADS_AD_OBJECT_APPROVED;
    additionalData.adName = singleAdObject.adName;
  } else if (singleAdObject?.status === META_ADS_STATUS.REJECTED) {
    mailType = MAGIC_ADS_MAIL_TYPES.MAGIC_ADS_AD_OBJECT_REJECTED;
    additionalData = {
      adName: singleAdObject.adName,
      rejectionReason:
        singleAdObject.errorFacebookReason ||
        singleAdObject.errorReason ||
        'No specific reason provided',
    };

    // 🔹 2. Next: Campaign-level updates
  } else if (campaignStatus === META_ADS_STATUS.REJECTED) {
    mailType = MAGIC_ADS_MAIL_TYPES.MAGIC_ADS_CAMPAIGN_REJECTED;
    additionalData.rejectionReason =
      campaignInfo.errorReason ||
      campaignInfo.errorFacebookReason ||
      'No specific reason provided';
  } else if (campaignStatus === META_ADS_STATUS.COMPLETED) {
    const convertedEvents = getActionValue(
      actions,
      getConversionEventName(adSetInfo.conversionEvent)
    );

    const costPerSignup = totalSpend ? totalSpend / convertedEvents : 0;

    // Update campaign with total impressions and converted events
    await MetaAdsCampaigns.updateOne(
      {
        _id: campaignId,
      },
      {
        totalImpressions: impressions || 0,
        totalConvertedEvents: convertedEvents || 0,
      }
    );
    mailType = MAGIC_ADS_MAIL_TYPES.MAGIC_ADS_CAMPAIGN_ENDED;
    additionalData = {
      startDate: DateTime.fromJSDate(adSetInfo.startTime).toFormat(
        'yyyy-MM-dd'
      ),
      endDate: DateTime.fromJSDate(adSetInfo.endTime).toFormat(
        'yyyy-MM-dd'
      ),
      signups: convertedEvents,
      costPerSignup: `USD ${
        convertedEvents > 0 ? costPerSignup.toFixed(2) : 0
      }`,
      budgetUsed: `USD ${totalSpend}`,
      eventName: adSetInfo.conversionEvent,
    };
  } else if (campaignStatus === META_ADS_STATUS.ACTIVE) {
    const approvedAds = adObjects.filter(
      (ad) => ad.status === META_ADS_STATUS.ACTIVE
    );
    const rejectedAds = adObjects.filter(
      (ad) => ad.status === META_ADS_STATUS.REJECTED
    );
    const allAdsApproved = approvedAds.length === adObjects.length;

    if (allAdsApproved) {
      mailType = MAGIC_ADS_MAIL_TYPES.MAGIC_ADS_CAMPAIGN_LIVE_WITH_ADS;
    } else {
      mailType =
        MAGIC_ADS_MAIL_TYPES.MAGIC_ADS_CAMPAIGN_LIVE_WITH_ISSUES_IN_ADS;
      additionalData = {
        approvedCount: approvedAds.length,
        totalCount: adObjects.length,
        rejectedAdsList: rejectedAds.map((ad) => ({
          adName: ad.adName,
          reason:
            ad.errorFacebookReason ||
            ad.errorReason ||
            'No specific reason provided',
        })),
      };
    }
  } else if (campaignStatus === META_ADS_STATUS.PENDING) {
    mailType = MAGIC_ADS_MAIL_TYPES.MAGIC_ADS_CAMPAIGN_SUBMITTED;
  } else {
    // 🔹 3. Unknown state
    throw new Error(
      `Unknown campaign or ad object status: ${campaignStatus}`
    );
  }

  const emailVariables = await getMagicAudienceMailVariables({
    campaignInfo,
    mailType,
    topupOrder: {
      learnerObjectId: campaignInfo.createdByLearnerObjectId,
    },
    entityInfo,
    additionalData,
  });

  const mailReqBody = {
    mailType,
    mailCourse: 'All',
    mailCourseOffer: 'All',
    toMail: emailVariables.toMail,
    toMailName: emailVariables.toMailName,
    data: emailVariables,
    requesterServiceName: 'Learn Portal Backend',
  };

  await sendEmail(mailReqBody);
  logger.info(`Email sent successfully for mail type: ${mailType}`);
};

const sendApplicationEmail = async ({ learner }) => {
  const learnerInfo = await learnersModel
    .findById(learner._id)
    .select({ firstName: 1, lastName: 1, email: 1 })
    .lean();
  const name = getName(
    learnerInfo.firstName,
    learnerInfo.lastName,
    learnerInfo.email
  );

  const mailVariables = {
    first_name: name,
  };

  const mailReqBody = {
    mailType: MAGIC_ADS_MAIL_TYPES.MAGIC_ADS_FEATURE_APPLY,
    mailCourse: 'All',
    mailCourseOffer: 'All',
    toMail: [learnerInfo.email],
    toMailName: [name],
    data: mailVariables,
    requesterServiceName: 'Learn Portal Backend',
  };

  await sendEmail(mailReqBody);
};

const sendCampaignPendingApprovalEmail = async ({
  campaignId,
  topupOrderId,
}) => {
  logger.info(
    `Preparing to send campaign pending approval email for campaign ID: ${campaignId}`
  );

  const mailType = MAGIC_ADS_MAIL_TYPES.MAGIC_ADS_CAMPAIGN_SUBMITTED;

  const [topupOrder, campaignInfo] = await Promise.all([
    CommunityTopupOrderModel.findById(topupOrderId).lean(),
    MetaAdsCampaigns.findById(campaignId).lean(),
  ]);

  if (!topupOrder || !campaignInfo) {
    throw new Error('Topup order or campaign info not found');
  }

  logger.info(`Fetched campaign info for campaign ID: ${campaignId}`);

  const entityInfo = await getEntityInfo({
    entityType: campaignInfo.entityType,
    entityObjectId: campaignInfo.entityObjectId,
  });

  logger.info(
    `Sending campaign pending approval email for campaign ID: ${campaignId}`
  );
  // get topup order info

  logger.info(
    `Sending campaign pending approval email for campaign ID: ${campaignId} and topup order ID: ${
      topupOrderId || 'N/A'
    } and for community ID: ${campaignInfo.communityObjectId}`
  );

  const emailVariables = await getMagicAudienceMailVariables({
    campaignInfo,
    topupOrder,
    entityInfo,
    additionalData: {},
  });

  const mailReqBody = {
    mailType,
    mailCourse: 'All',
    mailCourseOffer: 'All',
    toMail: emailVariables.toMail,
    toMailName: emailVariables.toMailName,
    data: emailVariables,
    requesterServiceName: 'Learn Portal Backend',
  };

  logger.info(
    `Preparing to send email for campaign pending approval with variables: ${JSON.stringify(
      emailVariables
    )}`
  );

  const config = {
    purchasedId: String(topupOrderId),
    purchaseType: PURCHASE_TYPE.ADS_CAMPAIGN_TOPUP,
    entityObjectId: String(campaignInfo._id),
    communityObjectId: String(campaignInfo.communityObjectId),
    learnerObjectId: String(topupOrder.learnerObjectId),
    generateReceipt: true,
  };

  if (topupOrder?.amountInUsd === 0) {
    config.generateReceipt = false;
  }

  mailReqBody.config = config;

  const messageGroupId = `${campaignInfo._id}`;

  logger.info(
    `Sending message to SQS queue for campaign pending approval with group ID: ${messageGroupId}`
  );
  await sendMessageToSQSQueue({
    queueUrl: PAYMENT_MAILER_URL,
    messageBody: { data: mailReqBody },
    messageGroupId,
  });
};

module.exports = {
  sendCampaignPendingApprovalEmail,
  sendApplicationEmail,
  getMagicAudienceMailVariables,
  sendEmailToUser,
};
