const { DateTime } = require('luxon');
const AnnouncementModel = require('../../models/announcements.model');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const { ResourceNotFoundError } = require('../../utils/error.util');
const {
  ANNOUNCEMENT_VISIBILITY_SELECTIONS,
  ANNOUNCEMENT_MARKET,
  EBANX_SUPPORTED_CURRENCY,
} = require('../../constants/common');
const { ENTITY_TYPE: PLAN_ENTITY_TYPE } = require('../plan/constants');
const {
  latamCountriesArray,
  COUNTRY_CREATED,
} = require('../../communitiesAPI/constants');

function filterAnnouncement({
  announcement,
  communityCode,
  planType,
  communityMarket,
  currentUtcDateTime,
}) {
  const visibilityAllowed = announcement.visibilities || [];

  let visibilityCategory = ANNOUNCEMENT_VISIBILITY_SELECTIONS.TRIAL;
  if (planType === PLAN_ENTITY_TYPE.PRO) {
    visibilityCategory = ANNOUNCEMENT_VISIBILITY_SELECTIONS.PRO;
  } else if (planType === PLAN_ENTITY_TYPE.PLATINUM) {
    visibilityCategory = ANNOUNCEMENT_VISIBILITY_SELECTIONS.PLATINUM;
  }

  if (!visibilityAllowed.includes(visibilityCategory)) {
    return false;
  }

  if (!(announcement.marketsAllowed ?? []).includes(communityMarket)) {
    return false;
  }

  if (
    announcement.effectiveStartTime &&
    announcement.effectiveStartTime > currentUtcDateTime
  ) {
    return false;
  }

  if (
    announcement.effectiveEndTime &&
    announcement.effectiveEndTime < currentUtcDateTime
  ) {
    return false;
  }

  if (
    !announcement.liveMode &&
    announcement.targetTestCommunitiesCode.includes(communityCode)
  ) {
    return true;
  }

  const targetCommunitiesCode = announcement.targetCommunitiesCode ?? [];

  if (
    announcement.liveMode &&
    (targetCommunitiesCode.length === 0 ||
      targetCommunitiesCode.includes(communityCode))
  ) {
    return true;
  }

  return false;
}

function transformAnnouncementByLanguage(
  announcement,
  languagePreference
) {
  const selectedAnnouncementLanguage = announcement;

  if (announcement.title) {
    selectedAnnouncementLanguage.title =
      announcement.title[languagePreference] ||
      announcement.title.en ||
      '';
  }

  if (announcement.description) {
    selectedAnnouncementLanguage.description =
      announcement.description[languagePreference] ||
      announcement.description.en ||
      '';
  }

  if (announcement.cta) {
    selectedAnnouncementLanguage.cta.name =
      announcement.cta.name?.[languagePreference] ||
      announcement.cta.name?.en ||
      '';
    selectedAnnouncementLanguage.cta.link =
      announcement.cta.redirectLink?.[languagePreference] ||
      announcement.cta.redirectLink?.en ||
      announcement.cta.link ||
      '';
    delete selectedAnnouncementLanguage.cta.redirectLink;
  }

  if (announcement.secondaryCta) {
    selectedAnnouncementLanguage.secondaryCta.name =
      announcement.secondaryCta.name?.[languagePreference] ||
      announcement.secondaryCta.name?.en ||
      '';
    selectedAnnouncementLanguage.secondaryCta.link =
      announcement.secondaryCta.redirectLink?.[languagePreference] ||
      announcement.secondaryCta.redirectLink?.en ||
      announcement.secondaryCta.link ||
      '';
    delete selectedAnnouncementLanguage.secondaryCta.redirectLink;
  }

  return selectedAnnouncementLanguage;
}

exports.retrieveAnnouncements = async ({
  languagePreference,
  communityId,
}) => {
  const currentUtcDateTime = DateTime.utc();

  const [community, announcements] = await Promise.all([
    CommunityModel.findById(communityId, {
      code: 1,
      config: 1,
      baseCurrency: 1,
      countryCreatedIn: 1,
    }).lean(),
    AnnouncementModel.find({
      disable: false,
    })
      .sort({ _id: -1 })
      .limit(20)
      .lean(),
  ]);

  if (!community) {
    throw new ResourceNotFoundError('Community not found');
  }

  const { countryCreatedIn, baseCurrency } = community;
  let communityMarket = ANNOUNCEMENT_MARKET.ROW;
  if (baseCurrency === 'INR') {
    communityMarket = ANNOUNCEMENT_MARKET.INR;
  } else if (
    countryCreatedIn === COUNTRY_CREATED.UNITED_STATES &&
    ['USD'].includes(baseCurrency)
  ) {
    communityMarket = ANNOUNCEMENT_MARKET.US;
  } else if (
    ['USD', 'EUR'].includes(baseCurrency) &&
    latamCountriesArray.includes(countryCreatedIn)
  ) {
    communityMarket = ANNOUNCEMENT_MARKET.LATAM;
  } else if (
    Object.values(EBANX_SUPPORTED_CURRENCY).includes(baseCurrency)
  ) {
    communityMarket = ANNOUNCEMENT_MARKET.LATAM;
  }

  const filteredAnnouncements = announcements
    .filter((announcement) =>
      filterAnnouncement({
        announcement,
        communityCode: community.code,
        planType: community.config?.planType,
        communityMarket,
        currentUtcDateTime,
      })
    )
    .map((announcement) =>
      transformAnnouncementByLanguage(announcement, languagePreference)
    );

  return filteredAnnouncements;
};
