/* eslint-disable no-param-reassign */
const { ObjectId } = require('mongoose').Types;
const { DateTime, Info } = require('luxon');
const { Readable } = require('stream');

const PublishProductUsageService = require('@services/featurePermissions/publishProductUsage.service');

const {
  sessionAttendeesSchema,
} = require('../../api/v1/oneOnOneSessions/schema');
const {
  COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES,
  COMMUNITY_FOLDER_STATUS,
  COMMUNITY_ONE_TIME_PAYMENT_ENTITIES,
} = require('../../communitiesAPI/constants');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const CommunityFoldersModel = require('../../communitiesAPI/models/communityFolders.model');
const {
  discountValidationForEntities,
  discountCreationForEntities,
} = require('../../communitiesAPI/services/common/communityDiscounts.service');
const RegexUtils = require('../../utils/regex.util');
const {
  convertToUTC,
  convertToUTCForAnyTimezone,
} = require('../../communitiesAPI/services/common/dateUtils');

const {
  checkIfResourcesPostsOrEventsWithSlugExists,
} = require('../../communitiesAPI/services/common/utils');
const {
  PURCHASE_TYPE,
  TRANSACTION_TYPE,
  MILESTONE_ACTIVITY_TYPES,
  SESSION_TYPES,
  ADDON_ACTION_EVENT_TYPES,
  BATCH_METADATA_MODEL_TYPE,
  COMMUNITY_SHORT_CODE_LEN,
} = require('../../constants/common');
const { PRODUCT_ERROR } = require('../../constants/errorCode');
const UserModel = require('../../models/users.model');
const learnersModel = require('../../models/learners.model');
const MongoDbUtils = require('../../utils/mongodb.util');
const SessionAttendeesModel = require('../../models/oneOnOneSessions/sessionAttendees.model');
const ActionEventService = require('../actionEvent');
const {
  ToUserError,
  ParamError,
  ResourceNotFoundError,
  InternalError,
} = require('../../utils/error.util');
const logger = require('../loggerCreation.service');

const RefundService = require('../transaction/transactionRefund.service');
const {
  SESSION_MAIL_TYPES,
  DEFAULT_IMAGES,
} = require('../mail/constants');
const { sendEmail } = require('../notification');
const RawTransactionModel = require('../../models/rawTransaction.model');
const {
  getEventTimeInfo,
  formatICSDateTimeString,
} = require('../mail/formatVariableData.service');
const {
  REROUTE_MEMBER_LINK,
  NAS_IO_FRONTEND_URL,
} = require('../../config');
const communityAddonTransactionsModel = require('../../communitiesAPI/models/communityAddonTransactions.model');
const entityCurrencyUtil = require('../../utils/entityCurrency.util');
const {
  getHostOccupiedCalendarSlotMap,
  addEventsInCalendar,
  deleteEventsInCalendar,
} = require('../calendar/manage.service');
const discountService = require('../../communitiesAPI/services/common/communityDiscounts.service');
const pricingService = require('../pricing');
const {
  retrieveLearnerObjectIdsViaSearch,
} = require('../common/membershipSearch.service');
const { affiliateProductService } = require('../affiliate');
const batchMetadataService = require('../batchMetadata');
const { makeUrlShortCode } = require('../../utils/makeShortCode');
const notificationCommonService = require('../communityNotification/email/common.service');
const mailUtils = require('../../utils/mail.util');
const receiptService = require('../receipts/receipts.service');
const {
  getUpdateDataForMultipleCoverMediaItems,
  hasVideoCoverMediaItems,
} = require('../../utils/multipleCoverMediaItems.util');
const {
  COVER_MEDIA_ENTITY_TYPES,
} = require('../../constants/coverMediaItems.constant');
const {
  verifyVideoCoverMediaItems,
  deleteRemovedVideoCoverMediaItems,
} = require('../coverMediaItems/common.service');
const { googleCalendarAlert } = require('../communityNotification/lark');
const SyncProductDataService = require('../product/syncProductData.service');
const {
  PRODUCT_TYPE,
  PRODUCT_CHANGE_LOG_TYPE,
} = require('../product/constants');
const ProductChangeLogService = require('../product/productChangeLog.service');
const {
  purgeEntityLandingPageCache,
  ENTITY_LANDING_PAGE,
} = require('../../utils/memberPortalLinks.utils');
const priceUtil = require('../../utils/price.util');
const {
  checkActiveCampaignForSlugChange,
} = require('../../utils/metaAds.util');

const dayToNumberMap = {
  Sunday: 0,
  Monday: 1,
  Tuesday: 2,
  Wednesday: 3,
  Thursday: 4,
  Friday: 5,
  Saturday: 6,
};

function getTimezoneText(timezone) {
  const dt = DateTime.now().setZone(timezone);
  const offset = dt.offset / 60;
  const sign = offset >= 0 ? '+' : '-';
  const hours = Math.abs(Math.floor(offset));
  const minutes = Math.abs(Math.floor((offset % 1) * 60));

  return `${timezone.split('/')[1]} GMT${sign}${hours}:${minutes
    .toString()
    .padStart(2, '0')}`;
}

const getTheLatestInterval = (intervals = [], timezone) => {
  let latestInterval = intervals[0];
  intervals.forEach((interval) => {
    const latestIntervalTime = DateTime.fromFormat(
      latestInterval.from,
      'HH:mm',
      {
        zone: timezone,
      }
    );
    const intervalTime = DateTime.fromFormat(interval.from, 'HH:mm', {
      zone: timezone,
    });
    if (intervalTime > latestIntervalTime) {
      latestInterval = interval;
    }
  });
  return latestInterval;
};

// Function to check if a time slot is booked
const isTimeSlotOverlapping = (
  startTime,
  endTime,
  bookedSlots,
  timezone
) => {
  if (Object.keys(bookedSlots).length === 0) {
    return false; // Slot is available
  }

  const startTimeInLuxon = DateTime.fromISO(startTime, {
    zone: timezone,
  });

  const endTimeInLuxon = DateTime.fromISO(endTime, {
    zone: timezone,
  });
  for (const bookedStartTime in bookedSlots) {
    if (bookedStartTime) {
      const bookedEndTime = bookedSlots[bookedStartTime];

      const bookedStart = DateTime.fromISO(bookedStartTime, {
        zone: timezone,
      });
      const bookedEnd = DateTime.fromISO(bookedEndTime, {
        zone: timezone,
      });

      // StartOfA < EndOfB && EndOfA > StartOfB
      // A-----B
      //       C-----D
      // we have to ignore the case when the start time (of B i.e C) is equal to the end time of (A i.e B) the booked slot
      // which is why we are < instead of <=
      if (startTimeInLuxon < bookedEnd && bookedStart < endTimeInLuxon) {
        return true; // Slot is not available
      }
    }
  }

  return false; // Slot is available
};

const convertAnswerToProperType = (dataField, answer) => {
  if (answer == null) {
    return;
  }

  let convertedAnswer;
  if (dataField.fieldDataType === 'number') {
    convertedAnswer = Number(answer);
  }

  if (dataField.fieldDataType === 'checkbox') {
    convertedAnswer = [];
    // eslint-disable-next-line no-unused-expressions
    dataField?.options?.forEach((option) => {
      if (answer[option.value]) {
        convertedAnswer.push(option.label);
      }
    });
  }

  if (dataField.fieldDataType === 'radio') {
    // eslint-disable-next-line no-unused-expressions
    dataField?.options?.forEach((option) => {
      if (answer === option.value) {
        convertedAnswer = {
          value: answer,
          label: option.label,
        };
      }
    });
  }

  if (
    dataField.fieldDataType === 'text' ||
    dataField.fieldDataType === 'url'
  ) {
    convertedAnswer = answer;
  }

  return convertedAnswer;
};
const convertUTCIntervalsToAnotherTimezoneUsingDay = (
  day,
  from,
  to,
  timezone
) => {
  const fromDateTime = DateTime.utc().set({
    weekday: dayToNumberMap[day],
    hour: from.split(':')[0],
    minute: from.split(':')[1],
    second: 0,
  });

  // if to == 00:00 the set the day to the next day

  let weekday = dayToNumberMap[day];
  if (to === '00:00') {
    // weekday is not more than 6
    weekday = weekday < 6 ? weekday + 1 : 0;
  }

  const toDateTime = DateTime.utc().set({
    hour: to.split(':')[0],
    minute: to.split(':')[1],
    second: 0,
    weekday,
  });

  const fromTimeInGivenTimezone = fromDateTime.setZone(timezone);
  const toTimeInGivenTimezone = toDateTime.setZone(timezone);

  const fromTimeInGivenTimezoneString =
    fromTimeInGivenTimezone.toFormat('HH:mm');

  const toTimeInGivenTimezoneString =
    toTimeInGivenTimezone.toFormat('HH:mm');

  let isToTime24Hour = false;

  if (
    toTimeInGivenTimezone.hour === 0 &&
    toTimeInGivenTimezone.minute === 0
  ) {
    isToTime24Hour = true;
  }

  const intervalsInGivenTimezone = [];

  if (
    fromTimeInGivenTimezone.day < toTimeInGivenTimezone.day &&
    !isToTime24Hour
  ) {
    intervalsInGivenTimezone.push({
      day: fromTimeInGivenTimezone.weekdayLong,
      intervals: [
        {
          from: fromTimeInGivenTimezoneString,
          to: '00:00',
        },
      ],
      date: fromTimeInGivenTimezone.toFormat('dd/MM/yyyy'),
    });

    const nextDay = toTimeInGivenTimezone.weekdayLong;

    intervalsInGivenTimezone.push({
      day: nextDay,
      intervals: [
        {
          from: '00:00',
          to: toTimeInGivenTimezoneString,
        },
      ],
      date: toTimeInGivenTimezone.toFormat('dd/MM/yyyy'),
    });
  } else {
    intervalsInGivenTimezone.push({
      day: fromTimeInGivenTimezone.weekdayLong,
      intervals: [
        {
          from: fromTimeInGivenTimezoneString,
          to: toTimeInGivenTimezoneString,
        },
      ],
      date: fromTimeInGivenTimezone.toFormat('dd/MM/yyyy'),
    });
  }

  return intervalsInGivenTimezone;
};

const convertUTCIntervalsToAnotherTimezoneUsingDate = (
  date,
  from,
  to,
  timezone
) => {
  const fromDateTime = DateTime.utc().set({
    day: date.split('/')[0],
    month: date.split('/')[1],
    year: date.split('/')[2],
    hour: from.split(':')[0],
    minute: from.split(':')[1],
    second: 0,
  });

  let isTODaySameAsFromDay = true;

  if (to === '0:00') {
    isTODaySameAsFromDay = false;
  }

  const year = date.split('/')[2];
  const month = date.split('/')[1];
  const day = date.split('/')[0];

  const toDateTime = DateTime.utc().set({
    day: isTODaySameAsFromDay ? Number(day) : fromDateTime.day + 1,
    month: Number(month),
    year: Number(year),
    hour: to.split(':')[0],
    minute: to.split(':')[1],
    second: 0,
  });

  const fromTimeInGivenTimezone = fromDateTime.setZone(timezone);
  const toTimeInGivenTimezone = toDateTime.setZone(timezone);

  const fromTimeInGivenTimezoneString =
    fromTimeInGivenTimezone.toFormat('HH:mm');

  const toTimeInGivenTimezoneString =
    toTimeInGivenTimezone.toFormat('HH:mm');

  const intervalsInGivenTimezone = [];

  if (fromTimeInGivenTimezone.day < toTimeInGivenTimezone.day) {
    intervalsInGivenTimezone.push({
      date: fromTimeInGivenTimezone.toFormat('dd/MM/yyyy'),
      intervals: [
        {
          from: fromTimeInGivenTimezoneString,
          to: '00:00',
        },
      ],
    });

    const nextDay = toTimeInGivenTimezone.toFormat('dd/MM/yyyy');

    intervalsInGivenTimezone.push({
      date: nextDay,
      intervals: [
        {
          from: '00:00',

          to: toTimeInGivenTimezoneString,
        },
      ],
    });
  } else {
    intervalsInGivenTimezone.push({
      date: fromTimeInGivenTimezone.toFormat('dd/MM/yyyy'),
      intervals: [
        {
          from: fromTimeInGivenTimezoneString,
          to: toTimeInGivenTimezoneString,
        },
      ],
    });
  }
  console.log('fromDateTime: ', fromDateTime);

  return intervalsInGivenTimezone;
};

const convertAvailabilityToGivenTimezone = async (
  availabilityArrayInUTC,
  timezone
) => {
  const availabilityArrayInGivenTimezone = [];
  await Promise.all(
    availabilityArrayInUTC.map(async (availability) => {
      const { day: availableDayStr, intervals } = availability;

      await Promise.all(
        intervals.map(async (interval) => {
          const { from, to } = interval;

          const allIntervals =
            await convertUTCIntervalsToAnotherTimezoneUsingDay(
              availableDayStr,
              from,
              to,
              timezone
            );

          logger.info('allIntervals: ', JSON.stringify(allIntervals));

          await Promise.all(
            allIntervals.map(async (computedIntervalObj) => {
              const {
                day,
                intervals: computedInterval,
                date,
              } = computedIntervalObj;

              const isDayPresent =
                availabilityArrayInGivenTimezone.findIndex(
                  (item) => item.day === day
                );

              if (isDayPresent !== -1) {
                const lastIntervalInIsDayPresent = getTheLatestInterval(
                  availabilityArrayInGivenTimezone?.[isDayPresent]
                    ?.intervals,
                  timezone
                );

                const firstIntervalInComputedInterval =
                  computedInterval[0];

                if (
                  lastIntervalInIsDayPresent.to ===
                  firstIntervalInComputedInterval.from
                ) {
                  lastIntervalInIsDayPresent.to =
                    firstIntervalInComputedInterval.to;
                } else if (
                  firstIntervalInComputedInterval.to ===
                  lastIntervalInIsDayPresent.from
                ) {
                  lastIntervalInIsDayPresent.from =
                    firstIntervalInComputedInterval.from;
                } else {
                  availabilityArrayInGivenTimezone[
                    isDayPresent
                  ].intervals.push(...computedInterval);
                }
              } else {
                availabilityArrayInGivenTimezone.push({
                  day,
                  intervals: computedInterval,
                  date,
                });
              }
            })
          );
        })
      );
    })
  );

  return availabilityArrayInGivenTimezone;
};

const convertUnAvailabilityToGivenTimezone = async (
  unAvailableDatesArrayInUTC,
  timezone
) => {
  const unAvailableDatesArrayInGivenTimezone = [];
  await Promise.all(
    unAvailableDatesArrayInUTC.map(async (availability) => {
      const { date, intervals } = availability;

      await Promise.all(
        intervals.map(async (interval) => {
          const { from, to } = interval;
          const allIntervals =
            await convertUTCIntervalsToAnotherTimezoneUsingDate(
              date,
              from,
              to,
              timezone
            );

          logger.info('allIntervals: ', JSON.stringify(allIntervals));

          await Promise.all(
            allIntervals.map(async (computedIntervalObj) => {
              const { date: singleDate, intervals: computedInterval } =
                computedIntervalObj;

              const isDatePresent =
                unAvailableDatesArrayInGivenTimezone.findIndex(
                  (item) => item.date === singleDate
                );

              if (isDatePresent !== -1) {
                const lastIntervalInIsDatePresent =
                  unAvailableDatesArrayInGivenTimezone[isDatePresent]
                    .intervals[
                    unAvailableDatesArrayInGivenTimezone[isDatePresent]
                      .intervals.length - 1
                  ];
                const firstIntervalInComputedInterval =
                  computedInterval[0];

                if (
                  lastIntervalInIsDatePresent.to ===
                  firstIntervalInComputedInterval.from
                ) {
                  lastIntervalInIsDatePresent.to =
                    firstIntervalInComputedInterval.to;
                } else {
                  unAvailableDatesArrayInGivenTimezone[
                    isDatePresent
                  ].intervals.push(...computedInterval);
                }
              } else {
                unAvailableDatesArrayInGivenTimezone.push({
                  date: singleDate,
                  intervals: computedInterval,
                });
              }
            })
          );
        })
      );
    })
  );
  return unAvailableDatesArrayInGivenTimezone;
};

const checkIfAvailableSlotForGivenTimeAndDayForSession = (
  sessionObject,
  sessionStartTimeInUTC,
  sessionEndTimeInUTC
) => {
  const { availability, unAvailableDates } = sessionObject;

  const day = sessionStartTimeInUTC.weekdayLong;

  const isDayAvailable = availability.find((item) => item.day === day);

  const unAvailableDatesArrayInUTC = unAvailableDates;

  const unAvailableDatesAndIntervalsMap = {};

  unAvailableDatesArrayInUTC.forEach((unAvailableObj) => {
    const { date, intervals } = unAvailableObj;
    unAvailableDatesAndIntervalsMap[date] = intervals;
  });

  if (!isDayAvailable) {
    logger.error('Session not available: Set day is not available');
    throw new ToUserError(
      'Session slot not available',
      PRODUCT_ERROR.NOT_AVAILABLE
    );
  }

  const intervals = isDayAvailable.intervals;

  const isSlotAvailable = intervals.find((interval) => {
    const isDayUnavailable =
      unAvailableDatesAndIntervalsMap[
        sessionStartTimeInUTC.toFormat('dd/MM/yyyy')
      ];

    if (isDayUnavailable) {
      const unAvailableIntervalsMap = {};
      isDayUnavailable.forEach((unavailableInterval) => {
        const { from, to } = unavailableInterval;

        const fromTime = DateTime.fromFormat(from, 'HH:mm', {
          zone: 'utc',
        }).set({
          day: sessionStartTimeInUTC.day,
          month: sessionStartTimeInUTC.month,
          year: sessionStartTimeInUTC.year,
        });

        const toTime = DateTime.fromFormat(to, 'HH:mm', {
          zone: 'utc',
        }).set({
          day: sessionEndTimeInUTC.day,
          month: sessionEndTimeInUTC.month,
          year: sessionEndTimeInUTC.year,
        });
        unAvailableIntervalsMap[fromTime] = toTime;
      });

      if (
        isTimeSlotOverlapping(
          sessionStartTimeInUTC,
          sessionEndTimeInUTC,
          unAvailableIntervalsMap,
          'UTC'
        )
      ) {
        logger.error('Session not available: Time slot has overlapped');
        throw new ToUserError(
          'Session slot not available',
          PRODUCT_ERROR.NOT_AVAILABLE
        );
      }
    }
    const { from, to } = interval;

    // setting the from and to time to the same day as the sessionStartTimeInUTC to compare
    const fromTime = DateTime.fromFormat(from, 'HH:mm', {
      zone: 'utc',
    }).set({
      day: sessionStartTimeInUTC.day,
      month: sessionStartTimeInUTC.month,
      year: sessionStartTimeInUTC.year,
    });

    const toTime = DateTime.fromFormat(to, 'HH:mm', {
      zone: 'utc',
    }).set({
      day: sessionEndTimeInUTC.day,
      month: sessionEndTimeInUTC.month,
      year: sessionEndTimeInUTC.year,
    });

    if (
      sessionStartTimeInUTC >= fromTime &&
      sessionEndTimeInUTC <= toTime
    ) {
      return true;
    }
    return false;
  });

  if (!isSlotAvailable) {
    logger.error(
      'Session not available: Slot not available at given time'
    );
    throw new ToUserError(
      'Session slot not available',
      PRODUCT_ERROR.NOT_AVAILABLE
    );
  }

  return true;
};

const checkIfSessionIsAvailableToBookForSessionAndCM = async (
  sessionObject,
  sessionStartTimeInUTC,
  sessionEndTimeInUTC
) => {
  const { hostLearnerObjectId } = sessionObject.hostInfo;

  // STEP - 1 check if the host is available at the given time
  const sessionAttendee = await SessionAttendeesModel.findOne({
    hostLearnerObjectId,
    sessionStartTime: sessionStartTimeInUTC,
    sessionEndTime: sessionEndTimeInUTC,
    status: { $nin: [COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.CANCELLED] },
  });

  // double check with JH regarding this
  if (sessionAttendee) {
    logger.error(
      'Session not available: CM already has an existing session at this time'
    );
    throw new ToUserError(
      'Session slot not available',
      PRODUCT_ERROR.NOT_AVAILABLE
    );
  }

  // STEP - 2 check if session has a available slot at the given time
  checkIfAvailableSlotForGivenTimeAndDayForSession(
    sessionObject,
    sessionStartTimeInUTC,
    sessionEndTimeInUTC
  );

  return true;
};

const generateSlug = async (communityId) => {
  const urlShortCodeRand = makeUrlShortCode(COMMUNITY_SHORT_CODE_LEN);
  // check if there is already a resource with the same slug
  const folderExistWithSameSlug =
    await CommunityFoldersModel.countDocuments({
      resourceSlug: `/${urlShortCodeRand}`,
      communityObjectId: communityId,
    });

  if (folderExistWithSameSlug > 0) {
    //then there exists a same resource slug
    // eslint-disable-next-line no-unused-vars
    return generateSlug(communityId);
  }
  return urlShortCodeRand;
};

const createOneOnOneSession = async (params, communityObjectId) => {
  // get community short code and code from community object id
  const community = await CommunityModel.findById(communityObjectId, {
    communityShortCode: 1,
    code: 1,
    baseCurrency: 1,
  }).lean();

  if (!community) {
    throw new ParamError('Community not found with the given id');
  }

  entityCurrencyUtil.validateEntityCurrencyWithCommunityBaseCurrency(
    params.currency,
    community.baseCurrency
  );

  await discountValidationForEntities(
    community.code,
    params.newDiscountsToApply,
    params.discountsToAdd
  );

  if (params.coverMediaItems && params?.coverMediaItems?.length > 0) {
    const coverMediaItemsUpdateData =
      await getUpdateDataForMultipleCoverMediaItems({
        communityId: communityObjectId,
        entityType: COVER_MEDIA_ENTITY_TYPES.SESSION,
        coverMediaItems: params.coverMediaItems,
      });

    Object.assign(params, coverMediaItemsUpdateData);
  }

  logger.info(
    'Creating community One on session with the given params: ',
    params
  );

  const pricing = await pricingService.validateAndFormatPricing(
    params,
    false,
    null,
    community
  );

  if (pricing) {
    params.amount = pricing.amount;
    params.currency = pricing.currency;
    params.pricingConfig = pricing.pricingConfig;
    params.access = pricing.access;
  }

  let communitySession = (
    await CommunityFoldersModel.create(params)
  ).toObject();

  await SyncProductDataService.syncProductData({
    productType: PRODUCT_TYPE.SESSION,
    entity: communitySession,
  });

  communitySession.slug = communitySession.resourceSlug;

  communitySession = await discountCreationForEntities(
    community.code,
    null,
    communitySession,
    PURCHASE_TYPE.SESSION,
    params?.createdBy,
    CommunityFoldersModel,
    params.newDiscountsToApply,
    params.discountsToRemove,
    params.discountsToAdd,
    params.discountsToDisable
  );

  if (hasVideoCoverMediaItems(communitySession.coverMediaItems)) {
    await verifyVideoCoverMediaItems({
      entityObjectId: communitySession._id,
      coverMediaItems: communitySession.coverMediaItems,
    });
  }

  logger.info('Final Community One on session object: ', communitySession);

  return communitySession;
};

const updateOneOnOneSession = async (
  params,
  sessionId,
  communityObjectId,
  learnerObjectId
) => {
  const now = DateTime.utc();

  const [session, community, publisheProductCount] = await Promise.all([
    CommunityFoldersModel.findOne({
      _id: sessionId,
      communityObjectId,
    }).lean(),
    CommunityModel.findById(communityObjectId).lean(),
    CommunityFoldersModel.countDocuments({
      communityObjectId,
      status: COMMUNITY_FOLDER_STATUS.PUBLISHED,
    }),
  ]);

  const oldStatus = session.status;

  if (!session) {
    throw new ParamError('Session not found with the given id');
  }

  if (!session.hostInfo?.hostLearnerObjectId) {
    throw new ParamError('Host info is required');
  }

  if (!community) {
    throw new ParamError('Community not found with the given id');
  }

  if (
    session.status !== COMMUNITY_FOLDER_STATUS.PUBLISHED &&
    params.status === COMMUNITY_FOLDER_STATUS.PUBLISHED
  ) {
    await PublishProductUsageService.checkPublishProductLimit(
      community._id,
      PURCHASE_TYPE.SESSION,
      session.createdAt,
      community
    );
  }

  entityCurrencyUtil.validateEntityCurrencyWithCommunityBaseCurrency(
    params.currency,
    community.baseCurrency
  );

  const { hostInfo } = session;

  const hostLearnerInfo = await learnersModel.findById(
    hostInfo?.hostLearnerObjectId
  );

  await discountValidationForEntities(
    community.code,
    params.newDiscountsToApply,
    params.discountsToAdd
  );

  if (params.coverMediaItems && params?.coverMediaItems?.length > 0) {
    const coverMediaItemsUpdateData =
      await getUpdateDataForMultipleCoverMediaItems({
        communityId: communityObjectId,
        entityType: COVER_MEDIA_ENTITY_TYPES.SESSION,
        coverMediaItems: params.coverMediaItems,
      });

    Object.assign(params, coverMediaItemsUpdateData);
  }

  logger.info(
    'Updating community One on session with the given params: ',
    params
  );

  const isUpdatedTitle = params?.title && session.title !== params.title;
  const previousTitle = isUpdatedTitle ? session.title : null;

  const isUpdatedLocation =
    params?.location?.locationValue &&
    JSON.stringify(session.location.locationValue) !==
      JSON.stringify(params.location.locationValue);

  if (isUpdatedTitle || isUpdatedLocation) {
    const updatedArray = [];
    const sessionAttendees = await SessionAttendeesModel.find({
      sessionObjectId: sessionId,
      sessionEndTime: { $gte: now },
      status: COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.BOOKED,
    }).populate('attendeeLearnerObjectId');

    if (sessionAttendees.length > 0) {
      if (isUpdatedLocation) {
        await SessionAttendeesModel.updateMany(
          {
            sessionObjectId: sessionId,
            sessionEndTime: { $gte: now },
            status: COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.BOOKED,
          },
          {
            $set: {
              location: params.location,
            },
          }
        );
      }

      if (isUpdatedTitle) {
        updatedArray.push(
          `the title has been changed from ${session.title} to ${params.title}`
        );
      }

      if (isUpdatedLocation) {
        updatedArray.push(
          `the session location (stated above is the updated location)`
        );
      }

      // Notes: Move the session update mail notification to services/product/productChangeLog.service.js
    }

    const mailType =
      SESSION_MAIL_TYPES.MANAGER_COMMUNITY_SESSION_UPDATED_V2;

    const toMail = [hostLearnerInfo?.email ?? ''];
    const toMailName = [hostLearnerInfo.firstName ?? ''];
    const mailData = {
      product_title_in_subject: session.title, // always use the previous title for mail subject title
      previous_title: previousTitle, // this field might be null if the title didnt get updated
      product_title: params?.title ?? session.title,
      is_location_changed: isUpdatedLocation,
      product_link: `${NAS_IO_FRONTEND_URL}/portal/sessions/manage?id=${sessionId}`,
      host: hostLearnerInfo?.firstName ?? '',
      community_name: community?.title,
      community_profile_image:
        community.thumbnailImgData.desktopImgData.src,
    };

    const locationType = params?.location?.type ?? session.location.type;
    const locationValue =
      params?.location?.locationValue ?? session.location.locationValue;
    if (locationType === SESSION_TYPES.ONLINE) {
      mailData.product_live_link = locationValue;
    } else {
      mailData.product_in_person_location = locationValue;
    }

    // we do not want to stop the process if the mail is not sent
    try {
      // Use retrieveManagerMailConfig for consistent fromMail and fromMailName logic
      const managerEmailConfig = mailUtils.retrieveManagerMailConfig(
        community?.title,
        community?.link,
        community?.createdBy,
        community?.By,
        mailType
      );

      await sendEmail({
        email: community?.managerEmail,
        mailType,
        toMailName,
        toMail,
        fromMail: managerEmailConfig.fromMail,
        fromMailName: managerEmailConfig.fromMailName,
        data: mailData,
        mailCourse: 'All',
        mailCourseOffer: 'All',
      });
    } catch (error) {
      logger.error(
        'Error in sending mail to the manager for the updated session',
        error
      );
      // still resolving the promise cuz we do not want to stop the process
    }
  }
  if (params?.resourceSlug) {
    // check if a folder exists with the same resourceSlug apart from the current folder
    let folderWithSameResourceSlug = await CommunityFoldersModel.findOne({
      resourceSlug: params.resourceSlug,
      communityObjectId,
      _id: { $ne: sessionId },
    });

    folderWithSameResourceSlug =
      await checkIfResourcesPostsOrEventsWithSlugExists(
        params.resourceSlug,
        communityObjectId,
        ['folder']
      );

    if (folderWithSameResourceSlug) {
      throw new ToUserError(
        'Another session found with the same URL. Please update the URL for this session.',
        PRODUCT_ERROR.DUPLICATE_SESSION_SLUG
      );
    }

    if (params.resourceSlug !== session.resourceSlug) {
      await checkActiveCampaignForSlugChange(community._id, sessionId);
    }
  }

  const pricing = await pricingService.validateAndFormatPricing(
    params,
    false,
    session,
    community
  );

  if (pricing) {
    params.amount = pricing.amount;
    params.currency = pricing.currency;
    params.pricingConfig = pricing.pricingConfig;
    params.access = pricing.access;
  }

  let updatedData = await CommunityFoldersModel.findByIdAndUpdate(
    sessionId,
    params,
    {
      new: true,
    }
  ).lean();

  await SyncProductDataService.syncProductData({
    productType: PRODUCT_TYPE.SESSION,
    entity: updatedData,
  });

  // Track field changes for session updates using existing logic
  let changeLogId;
  if (isUpdatedTitle || isUpdatedLocation) {
    const fieldsChanged = [];
    if (isUpdatedTitle) fieldsChanged.push('title');
    if (isUpdatedLocation) fieldsChanged.push('location');

    // Prepare metadata with previous session name if title changed
    const metadata = {};
    if (isUpdatedTitle) {
      metadata.previous_title = session.title;
    }
    if (isUpdatedLocation) {
      metadata.is_location_changed = true;
    }

    const changeLog = await ProductChangeLogService.addCommunityProductLog(
      {
        communityObjectId,
        communityCode: community.code,
        productType: PRODUCT_TYPE.SESSION,
        entityObjectId: sessionId,
        changeLogType: PRODUCT_CHANGE_LOG_TYPE.FIELD_UPDATED,
        fieldsChanged,
        beforeData: session,
        afterData: updatedData,
        operatorLearnerObjectId: learnerObjectId,
        metadata,
      }
    );
    changeLogId = changeLog._id;
  }

  if (params.coverMediaItems && params?.coverMediaItems?.length > 0) {
    // delete removed video cover media items by marking folder item status as deleted.
    const oldCoverMediaItems = session?.coverMediaItems ?? [];
    const newCoverMediaItems = updatedData?.coverMediaItems ?? [];
    if (
      Array.isArray(oldCoverMediaItems) &&
      hasVideoCoverMediaItems(oldCoverMediaItems)
    ) {
      await deleteRemovedVideoCoverMediaItems({
        oldCoverMediaItems,
        newCoverMediaItems,
      });
    }
  }

  const isUpdatedToPublished =
    params.status === COMMUNITY_FOLDER_STATUS.PUBLISHED &&
    session.status === COMMUNITY_FOLDER_STATUS.UNPUBLISHED;

  if (isUpdatedToPublished) {
    await ActionEventService.sendMilestoneEvent({
      actionEventType: MILESTONE_ACTIVITY_TYPES.MILESTONE_FIRST_SESSION,
      communityCode: community.code,
      communityObjectId,
      learnerObjectId,
    });

    // Add product change log for session publish
    const changeLog = await ProductChangeLogService.addCommunityProductLog(
      {
        communityObjectId,
        communityCode: community.code,
        entityObjectId: sessionId,
        productType: PRODUCT_TYPE.SESSION,
        changeLogType: PRODUCT_CHANGE_LOG_TYPE.PRODUCT_PUBLISHED,
        operatorLearnerObjectId: learnerObjectId,
      }
    );
    changeLogId = changeLog._id;
  } else if (params.status === COMMUNITY_FOLDER_STATUS.UNPUBLISHED) {
    await affiliateProductService.disableAffiliateProduct({
      communityObjectId,
      entityType: PURCHASE_TYPE.SESSION,
      entityObjectId: sessionId,
    });
  }

  if (
    params.newDiscountsToApply ||
    params.discountsToRemove ||
    params.discountsToAdd ||
    params.discountsToDisable
  ) {
    logger.info(
      'Updating community folder with the given params:',
      params
    );

    if (updatedData.resourceSlug) {
      updatedData.slug = updatedData.resourceSlug;
    }

    session.slug = session.resourceSlug;

    updatedData = await discountCreationForEntities(
      community.code,
      session,
      updatedData,
      PURCHASE_TYPE.SESSION,
      params?.createdBy,
      CommunityFoldersModel,
      params?.newDiscountsToApply,
      params?.discountsToRemove,
      params?.discountsToAdd,
      params?.discountsToDisable
    );
  }

  // revalidate landing page cache
  const isStatusUpdated = oldStatus !== updatedData.status;
  if (isStatusUpdated) {
    const isPublishAction =
      updatedData.status === COMMUNITY_FOLDER_STATUS.PUBLISHED;

    const purgeCommunityLandingPage = isPublishAction
      ? publisheProductCount === 0 // i.e first product to be published in the community ( for community landing page tabs )
      : publisheProductCount === 1; // i.e unpublishing the only published product ( for community landing page tabs )

    await purgeEntityLandingPageCache({
      community,
      purgeCommunityLandingPage,
      entityType: ENTITY_LANDING_PAGE.SESSION,
      entitySlug: updatedData.resourceSlug,
    });
  }

  return {
    ...updatedData,
    changeLogId,
  };
};

const getOneOnOneSession = async (
  currentLearnerObjectId,
  sessionId,
  isPublicPage,
  timezoneInParams,
  isCommunityManager
) => {
  try {
    const sessionInfo = await CommunityFoldersModel.findById(sessionId);

    if (!sessionInfo) {
      throw new ParamError('Session not found with the given id');
    }
    const timezone =
      sessionInfo?.timezoneChosenForAvailability || timezoneInParams;

    // check if timezone is correct

    const isValidTimezone = Info.isValidIANAZone(timezone);

    if (!isValidTimezone && timezone) {
      throw new ParamError(
        'Invalid Parameters, wrong timezone or invalid timezone'
      );
    }
    const sessionInfoObj = {
      ...sessionInfo.toObject(),
    };

    if (!sessionInfo) {
      throw new ParamError('Session not found with the given id');
    }
    // if it is a public page then do not send the availability and just send the sessionObj
    if (isPublicPage) {
      return sessionInfo;
    }

    // if it is not a public page then send the availability (compute it based on the timezone given) and send back the availabilty and unavailability

    /*
    "availability": [
      {
        "day": "Sunday",
        "intervals": [
          {
            "from": "11:30",
            "to": "12:30"
          },
          {
            "from": "23:30",
            "to": "23:59"
          }
        ]
      },
      {
        "day": "Monday",
        "intervals": [
          {
            "from": "00:00",
            "to": "07:30"
          }
        ]
      }
    ]
    */
    // the above is the availability array that we get from the db and it is in UTC and now we have to convert it to the timezone given
    // and then send it back to the frontend
    const availabilityArrayInUTC = sessionInfoObj.availability;

    const unAvailableDatesArrayInUTC = sessionInfoObj.unAvailableDates;

    // convert the time to UTC

    const availabilityArrayInGivenTimezone =
      await convertAvailabilityToGivenTimezone(
        availabilityArrayInUTC,
        timezone
      );
    const unAvailableDatesArrayInGivenTimezone =
      await convertUnAvailabilityToGivenTimezone(
        unAvailableDatesArrayInUTC,
        timezone
      );

    sessionInfoObj.availability = availabilityArrayInGivenTimezone;

    sessionInfoObj.unAvailableDates = unAvailableDatesArrayInGivenTimezone;

    const hostInformation = sessionInfoObj?.hostInfo || {};

    const { hostLearnerObjectId } = hostInformation;

    if (hostLearnerObjectId) {
      const hostInfo = await learnersModel
        .findById(hostLearnerObjectId)
        .select('name profileImage firstName lastName')
        .lean();

      sessionInfoObj.hostInfo = {
        ...hostInformation,
        hostLearnerObjectId,
        hostName: hostInfo?.name,
        profileImage: hostInfo?.profileImage,
        firstName: hostInfo?.firstName,
        lastName: hostInfo?.lastName,
        hostTitle: hostInformation?.hostTitle,
        hostBio: hostInformation?.hostBio,
      };

      if (isCommunityManager) {
        // TODO: DEV-3969 DEV-3968 add connected calendar info
        const userInfo = await UserModel.findOne({
          learner: new ObjectId(hostLearnerObjectId),
          isActive: true,
        })
          .populate('calendarAccounts.calendarAccountObjectId')
          .select('calendarAccounts')
          .lean();
        const isCurrentUser =
          currentLearnerObjectId.toString() ===
          hostLearnerObjectId.toString();
        const calendarAccounts = (userInfo.calendarAccounts ?? []).map(
          (access) => {
            return {
              platform: access.platform,
              email: access.calendarAccountObjectId?.email,
              isCurrentUser,
            };
          }
        );
        sessionInfoObj.hostInfo.calendarAccounts = calendarAccounts;
      }
    }

    if (isCommunityManager) {
      const community = await CommunityModel.findById(
        sessionInfoObj.communityObjectId,
        {
          code: 1,
        }
      ).lean();

      if (!community) {
        throw new ResourceNotFoundError('Community not found');
      }

      const discountsApplied =
        await discountService.retrieveAllDiscountsRelatedToEntity(
          sessionInfoObj,
          community.code
        );

      sessionInfoObj.discountsApplied = discountsApplied;
    } else {
      delete sessionInfoObj.discountsApplied;
    }
    return sessionInfoObj;
  } catch (error) {
    logger.error(
      'error in getting one on one session',
      error,
      error.stack
    );
    throw error;
  }
};

const getOneOnOneSessionSlots = async (
  oneOneOneSession,
  timezone,
  rangeStart,
  rangeEnd
) => {
  const hostInfo = oneOneOneSession.hostInfo;
  const { hostLearnerObjectId } = hostInfo || {};
  const availabilityArrayInUTC = oneOneOneSession.availability;
  const unAvailableDatesArrayInUTC = oneOneOneSession.unAvailableDates;

  const minimumNoticeInDaysForBooking =
    oneOneOneSession.minimumNoticeInDaysForBooking;
  const [
    availabilityArrayInGivenTimezone = [],
    unAvailableDatesArrayInGivenTimezone = [],
  ] = await Promise.all([
    convertAvailabilityToGivenTimezone(availabilityArrayInUTC, timezone),
    convertUnAvailabilityToGivenTimezone(
      unAvailableDatesArrayInUTC,
      timezone
    ),
  ]);

  const occupiedCalendarSlotsInGivenTimezoneMap =
    await getHostOccupiedCalendarSlotMap(hostLearnerObjectId, timezone);

  const unAvailableDatesAndIntervalsMap = {};

  unAvailableDatesArrayInGivenTimezone.forEach((unAvailableObj) => {
    const { date, intervals } = unAvailableObj;
    let calendarSlots = [];
    if (occupiedCalendarSlotsInGivenTimezoneMap.has(date)) {
      calendarSlots = occupiedCalendarSlotsInGivenTimezoneMap.get(date);
      occupiedCalendarSlotsInGivenTimezoneMap.delete(date);
    }
    unAvailableDatesAndIntervalsMap[date] = [
      ...intervals,
      ...calendarSlots,
    ];
  });

  occupiedCalendarSlotsInGivenTimezoneMap.forEach((value, key) => {
    unAvailableDatesAndIntervalsMap[key] = value;
  });
  // get the current date
  const currentDate = DateTime.now().setZone(timezone);

  let startDateTime = DateTime.fromFormat(rangeStart, 'dd/MM/yyyy', {
    zone: timezone,
  });
  const bookedSlotsMap = {};

  const bookedSlotsByHost = await SessionAttendeesModel.find({
    hostLearnerObjectId,
    status: {
      $in: [
        COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.PENDING,
        COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.BOOKED,
      ],
    },
    sessionStartTime: {
      $gte: startDateTime.toJSDate(),
    },
  }).lean();

  bookedSlotsByHost.forEach((item) => {
    const { sessionStartTime, sessionEndTime } = item;
    // get the date of the sessionStartTime
    const sessionStartTimeInTimezone = DateTime.fromJSDate(
      sessionStartTime,
      {
        zone: timezone,
      }
    );

    const sessionEndTimeInTimezone = DateTime.fromJSDate(sessionEndTime, {
      zone: timezone,
    });

    bookedSlotsMap[sessionStartTimeInTimezone.toFormat('dd/MM/yyyy')] = {
      ...bookedSlotsMap[sessionStartTimeInTimezone.toFormat('dd/MM/yyyy')],
      [String(sessionStartTimeInTimezone)]: String(
        sessionEndTimeInTimezone
      ),
    };
  });
  const durationIntervalInMinutes =
    oneOneOneSession.durationIntervalInMinutes;
  const days = [];

  // start
  if (
    currentDate.plus({ days: minimumNoticeInDaysForBooking }) >
    startDateTime
  ) {
    // set the startDateTime to skip the days of minimumNoticeInDaysForBooking
    startDateTime = currentDate.plus({
      days: minimumNoticeInDaysForBooking,
    });
  }

  const endDateTime = DateTime.fromFormat(rangeEnd, 'dd/MM/yyyy', {
    zone: timezone,
  });

  let currentDateTime = startDateTime;

  // loop through the range and get the slots
  while (currentDateTime <= endDateTime) {
    const day = currentDateTime.weekdayLong;

    // check if day has any upcoming bookings

    const isDayAvailable = availabilityArrayInGivenTimezone.find(
      (item) => item.day === day
    );

    if (isDayAvailable) {
      const isDayUnavailable =
        unAvailableDatesAndIntervalsMap[
          currentDateTime.toFormat('dd/MM/yyyy')
        ];

      const dayBookedSlots =
        bookedSlotsMap[currentDateTime.toFormat('dd/MM/yyyy')] || {};

      const unAvailableIntervalsMap = {};
      if (isDayUnavailable) {
        // eslint-disable-next-line no-loop-func
        isDayUnavailable.forEach((interval) => {
          const { from, to } = interval;
          const fromTime = DateTime.fromFormat(from, 'HH:mm', {
            zone: timezone,
          }).set({
            day: currentDateTime.day,
            month: currentDateTime.month,
            year: currentDateTime.year,
          });

          let toTime = DateTime.fromFormat(to, 'HH:mm', {
            zone: timezone,
          }).set({
            day: currentDateTime.day,
            month: currentDateTime.month,
            year: currentDateTime.year,
          });

          if (toTime.hour === 0 && toTime.minute === 0) {
            toTime = toTime.plus({ days: 1 });
          }

          unAvailableIntervalsMap[fromTime] = toTime;
        });
      }

      const bookedSlotsIntervalsMap = {};
      if (Object.keys(dayBookedSlots).length > 0) {
        Object.keys(dayBookedSlots).forEach((key) => {
          console.log(key);
          bookedSlotsIntervalsMap[key] = dayBookedSlots[key];
        });
      }

      // intervals should be based on the duration interval in minutes and the start time and end time
      /*
              [
                {
                  "status": "available",
                  "start_time": "2024-02-01T17:00:00+05:30",
                },
                {
                  "status": "available",
                  "start_time": "2024-02-01T17:30:00+05:30",
                }
              ]

              if the duration interval is 30 minutes and the from time is 17:00 and to time is 18:00 then the above should be the output
          */

      const intervalsInGivenDay = [];
      // eslint-disable-next-line no-loop-func
      isDayAvailable.intervals.forEach((interval) => {
        const { from, to } = interval;

        const fromDate = isDayAvailable.date;

        const fromDateInfo = DateTime.fromFormat(fromDate, 'dd/MM/yyyy', {
          zone: timezone,
        }).set({
          hour: from.split(':')[0],
          minute: from.split(':')[1],
        });

        let fromTime = DateTime.fromFormat(from, 'HH:mm', {
          zone: timezone,
        }).set({
          day: currentDateTime.day,
          month: currentDateTime.month,
          year: currentDateTime.year,
        });

        // Calculating offsets in hours for clearer comparison
        const currentDateTimeOffsetHours = currentDateTime.offset / 60;
        const adjustedDateTimeOffsetHours = fromDateInfo.offset / 60;

        // Determine the direction and magnitude of the adjustment

        let toTime = DateTime.fromFormat(to, 'HH:mm', {
          zone: timezone,
        }).set({
          day: currentDateTime.day,
          month: currentDateTime.month,
          year: currentDateTime.year,
        });

        if (currentDateTimeOffsetHours > adjustedDateTimeOffsetHours) {
          // The offset has become more positive: DST ends, so local time "springs forward"
          const hoursToAdjust =
            currentDateTimeOffsetHours - adjustedDateTimeOffsetHours;
          fromTime = fromTime.plus({
            hours: hoursToAdjust,
          });

          toTime = toTime.plus({
            hours: hoursToAdjust,
          });
        } else if (
          adjustedDateTimeOffsetHours > currentDateTimeOffsetHours
        ) {
          // The offset has become less negative: DST starts, so local time "falls back"
          const hoursToAdjust =
            adjustedDateTimeOffsetHours - currentDateTimeOffsetHours;
          fromTime = fromTime.minus({
            hours: hoursToAdjust,
          });

          toTime = toTime.minus({
            hours: hoursToAdjust,
          });
        }
        // Check toTime instead of fromTime to exclude sessions that are currently running
        if (
          toTime <
          currentDate.plus({ days: minimumNoticeInDaysForBooking })
        ) {
          return;
        }

        let currentTime = fromTime;

        if (toTime.hour === 0 && toTime.minute === 0) {
          toTime = toTime.plus({ days: 1 });
        }
        while (currentTime <= toTime) {
          let isSlotBooked = false;
          let isSlotUnavailableForCurrentTime = false;
          // make sure that the current time plus + durationInInterval should not cross the toTime
          if (
            currentTime.plus({ minutes: durationIntervalInMinutes }) >
            toTime
          ) {
            // if does then break the loop
            break;
          }

          // check if the current time is less than the startDateTime that means session is already started so need to skip this
          if (startDateTime > currentTime) {
            isSlotUnavailableForCurrentTime = true;
          }

          if (
            isTimeSlotOverlapping(
              currentTime,
              currentTime.plus({
                minutes: durationIntervalInMinutes,
              }),
              bookedSlotsIntervalsMap,
              timezone
            )
          ) {
            isSlotBooked = true;
          }

          // check if the current time is in the bookedSlotsIntervalsMap

          // need to check if bookedSlotsIntervalsMap's 'fromTime' to 'toTime' has any overlap with the current time

          // check if the current time is in the unAvailableIntervalsMap
          if (
            !isTimeSlotOverlapping(
              currentTime,
              currentTime.plus({
                minutes: durationIntervalInMinutes,
              }),
              unAvailableIntervalsMap,
              timezone
            ) &&
            !isSlotBooked &&
            !isSlotUnavailableForCurrentTime
          ) {
            intervalsInGivenDay.push({
              status: 'available',
              start_time: currentTime.toFormat("yyyy-MM-dd'T'HH:mm:ssZZ"),
            });
          }
          currentTime = currentTime.plus({
            minutes: durationIntervalInMinutes,
          });
        }
      });

      if (intervalsInGivenDay.length > 0) {
        days.push({
          date: currentDateTime.toFormat('dd/MM/yyyy'),
          day,
          status: 'available',
          spots: intervalsInGivenDay,
        });
      }
    }

    currentDateTime = currentDateTime.plus({ days: 1 });
  }

  return {
    today: currentDate.toFormat('dd/MM/yyyy'),
    availabilityTimezone: timezone,
    days,
  };
};

const getHostInfo = async (hostObjectId) => {
  const hostSessions = await CommunityFoldersModel.findOne({
    'hostInfo.hostLearnerObjectId': hostObjectId,
  }).select('hostInfo');

  if (!hostSessions) {
    throw new ResourceNotFoundError(
      'No hostInfo found with the given host id'
    );
  }

  return hostSessions;
};

const validationForSessionBooking = async (
  body,
  sessionObject,
  communityObjectId
) => {
  const { sessionStartTime, sessionEndTime, timezoneOfAttendee } = body;

  const {
    durationIntervalInMinutes,
    minimumNoticeInDaysForBooking,
    stopAcceptingBookings,
  } = sessionObject;

  if (stopAcceptingBookings) {
    throw new ToUserError(
      'Session is not accepting bookings',
      PRODUCT_ERROR.BOOKINGS_NOT_ALLOWED
    );
  }
  const sessionStartTimeInUTC = convertToUTC(
    new Date(sessionStartTime),
    timezoneOfAttendee
  );

  const sessionEndTimeInUTC = convertToUTC(
    new Date(sessionEndTime),
    timezoneOfAttendee
  );

  // STEP -1 CHECK IF SESSION is within the minimumNoticeInDaysForBooking of the session and the difference is = durationIntervalInMinutes

  const differenceInMinutes = sessionEndTimeInUTC.diff(
    sessionStartTimeInUTC,
    'minutes'
  );

  if (
    Number(differenceInMinutes.minutes) !==
    Number(durationIntervalInMinutes)
  ) {
    throw new ParamError(
      'Session end time and start time difference is not equal to the duration interval'
    );
  }

  const currentDate = DateTime.now().toUTC();

  const currentDatePlusMinimumTime = currentDate.plus({
    days: minimumNoticeInDaysForBooking,
  });

  if (currentDatePlusMinimumTime > sessionStartTimeInUTC) {
    throw new ParamError(
      'Session cannot be booked within the minimum notice days'
    );
  }

  // STEP-2 CHECK IF ATTENDEE ALREADY HAS AN UPCOMING SESSION FOR THE SAME SESSION OBJECT

  const currentSessionAttendee = await SessionAttendeesModel.findOne({
    sessionObjectId: sessionObject._id,
    sessionStartTime: sessionStartTimeInUTC,
    sessionEndTime: sessionEndTimeInUTC,
    communityObjectId,
    status: {
      $in: [
        COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.PENDING,
        COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.BOOKED,
      ],
    },
  }).lean();

  if (!currentSessionAttendee) {
    return;
  }

  const upcomingSessionForSameAttendee =
    await SessionAttendeesModel.findOne({
      _id: { $ne: currentSessionAttendee._id },
      sessionObjectId: sessionObject._id,
      attendeeLearnerObjectId: body.attendeeLearnerObjectId,
      status: {
        $ne: COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.CANCELLED,
      },
      isPending: false,
      sessionEndTime: { $gte: new Date() },
    }).lean();

  if (upcomingSessionForSameAttendee) {
    throw new ToUserError(
      'You already have an upcoming slot for the same session',
      PRODUCT_ERROR.UPCOMING_BOOKING_EXISTS_ALREADY
    );
  }

  // STEP-3 CHECK IF SESSION START TIME ALREADY HAS AN ATTENDEE

  // IF IT IS THE SAME USER THEN ALLOW THE BOOKING and return the sessionAttendee
  if (
    String(currentSessionAttendee.attendeeLearnerObjectId) ===
    body.attendeeLearnerObjectId
  ) {
    return;
  }

  if (currentSessionAttendee) {
    logger.error('Session already booked by another user');
    throw new ToUserError(
      'Session slot is already booked',
      PRODUCT_ERROR.ALREADY_BOOKED
    );
  }

  // STEP-4 CHECK IF CM AND SESSION IS ALLOWED TO HAVE SESSIONSTART TIME AND SESSION END TIME (checking the availability slots and availability of the CM)
  await checkIfSessionIsAvailableToBookForSessionAndCM(
    sessionObject,
    sessionStartTimeInUTC,
    sessionEndTimeInUTC
  );
};

const generateSessionAttendeeObject = (
  body,
  sessionObject,
  communityObjectId
) => {
  const applicationQuestionsConfig =
    sessionObject.applicationConfigDataFields || [];

  // CASTING THE APPLICATION DATA TO THE SCHEMA for the sessionAttendee

  const sessionAttendeeObject = {
    ...sessionAttendeesSchema.cast(body),
    communityObjectId,
    hostLearnerObjectId: sessionObject.hostInfo.hostLearnerObjectId,
    location: sessionObject.location,
  };

  logger.info(
    `sessionAttendeeObject on generate session attendee object ${JSON.stringify(
      sessionAttendeeObject
    )}`
  );

  logger.info(`applicationData ${JSON.stringify(body?.applicationData)}`);
  if (
    body.applicationData &&
    Object.keys(body.applicationData).length > 0
  ) {
    sessionAttendeeObject.applicationInfo = body.applicationData;
    // get the application questions and store the answers in the field applicationInfo

    const applicationInfo = [];

    applicationQuestionsConfig.forEach((dataField) => {
      const { fieldName } = dataField;

      logger.info(`fieldName ${fieldName} for the applicationData`);
      const answer = body.applicationData[fieldName];
      logger.info(`answer for the field ${fieldName} is ${answer}`);
      const convertedAnswer = convertAnswerToProperType(dataField, answer);

      logger.info(
        `convertedAnswer for the field ${fieldName} is ${JSON.stringify(
          convertedAnswer
        )}`
      );
      applicationInfo.push({
        label: dataField?.label,
        fieldDataType: dataField?.fieldDataType,
        isRequired: dataField?.isRequired,
        answer: convertedAnswer,
      });
    });
    sessionAttendeeObject.applicationInfo = applicationInfo;
  }

  return sessionAttendeeObject;
};

const formatSessionTestEmailVariables = async (session) => {
  const { timezoneChosenForAvailability, durationIntervalInMinutes } =
    session;

  const { startTimeISOString, timezoneOffset } = getEventTimeInfo({
    start: DateTime.now().toUTC().toJSDate(),
    end: DateTime.now()
      .toUTC()
      .plus({ minutes: durationIntervalInMinutes })
      .toJSDate(),
    timezone: timezoneChosenForAvailability,
  });

  const sessionEndTimeInHoursMinutesAndMeridiem = DateTime.utc()
    .plus({
      minutes: durationIntervalInMinutes,
    })
    .setZone(timezoneChosenForAvailability)
    .toFormat('h:mma');

  const formattedTimezone = `GMT${DateTime.utc()
    .setZone(timezoneChosenForAvailability)
    .toFormat('Z')}`;

  const hostLearnerInfo = await learnersModel.findById(
    session.hostInfo.hostLearnerObjectId
  );
  const data = {
    event_name: session.title,
    event_link: session.location.locationValue,
    event_description: session?.description,
    event_type:
      session?.location.type === 'online'
        ? 'live'
        : session?.location.type,
    event_in_person_location:
      session?.location?.type === SESSION_TYPES.IN_PERSON
        ? session?.location?.locationValue
        : '',
    event_live_link:
      session?.location?.type === SESSION_TYPES.ONLINE
        ? session?.location?.locationValue
        : '',
    event_duration: String(durationIntervalInMinutes),
    event_start_date_iso: startTimeISOString,
    session_end_date: sessionEndTimeInHoursMinutesAndMeridiem,
    timezone: formattedTimezone,
    timezoneOffset,
    ics_uid: String(session?._id),
    ics_sequence: 0,
    ics_summary: session?.title,
    ics_event_start: formatICSDateTimeString(DateTime.utc().toJSDate()),
    ics_event_end: formatICSDateTimeString(
      DateTime.utc()
        .plus({ minutes: durationIntervalInMinutes })
        .toJSDate()
    ),
    ics_description: session?.description,
    ics_location: session?.location?.locationValue,
    ics_alarm_description: session?.title,
    event_host_first_name: hostLearnerInfo?.firstName,
    session_title: session.title,
    host_name: hostLearnerInfo?.firstName,
  };

  return data;
};

const formatAndSendSessionConfirmationEmailToUserAndCM = async ({
  sessionAttendee,
  session,
  mongodbSession,
  config = {},
}) => {
  const {
    sessionStartTime,
    sessionEndTime,
    attendeeLearnerObjectId,
    checkoutId,
  } = sessionAttendee;

  const {
    title: titleOfTheSession,
    location,
    timezoneChosenForAvailability,
  } = session;
  const { type: typeOfSession, locationValue } = location;

  const attendeeLearnerInfo = await learnersModel.findById(
    attendeeLearnerObjectId
  );
  if (!attendeeLearnerInfo) {
    throw new ResourceNotFoundError(
      'No attendee learner found with the given id'
    );
  }

  const [hostLearnerInfo, hostUserInfo] = await Promise.all([
    learnersModel.findById(session.hostInfo?.hostLearnerObjectId).lean(),
    UserModel.findOne({
      learner: new ObjectId(session.hostInfo?.hostLearnerObjectId),
      isActive: true,
    })
      .populate(
        'calendarAccounts.calendarAccountObjectId calendarforWritingEvents'
      )
      .select('calendarAccounts calendarforWritingEvents')
      .lean(),
  ]);
  if (!hostLearnerInfo || !hostUserInfo) {
    throw new ResourceNotFoundError(
      `No host learner or user found with the given learner id ${session.hostInfo?.hostLearnerObjectId}`
    );
  }

  const community = await CommunityModel.findById(
    session.communityObjectId
  );

  if (!community) {
    throw new ResourceNotFoundError(
      'No community found with the given id'
    );
  }

  const toMail = [attendeeLearnerInfo.email];
  const toMailName = [attendeeLearnerInfo.firstName ?? ''];
  const sessionStartTimeInUTC =
    convertToUTCForAnyTimezone(sessionStartTime);
  const sessionEndTimeInUTC = convertToUTCForAnyTimezone(sessionEndTime);

  const {
    startTimeISOString,
    duration,
    timezoneOffset,
    endTimeISOString,
  } = getEventTimeInfo({
    start: sessionAttendee?.sessionStartTime,
    end: sessionAttendee?.sessionEndTime,
    timezone: sessionAttendee?.timezoneOfAttendee,
  });

  const sessionEndTimeInUserTimezone = sessionEndTimeInUTC.setZone(
    sessionAttendee.timezoneOfAttendee
  );

  const sessionStartTimeInUserTimezone = sessionStartTimeInUTC.setZone(
    sessionAttendee.timezoneOfAttendee
  );

  const sessionEndTimeInHoursMinutesAndMeridiem =
    sessionEndTimeInUserTimezone.toFormat('h:mma', {
      locale: attendeeLearnerInfo.languagePreference ?? 'en',
    });

  const sessionStartTimeInFormat = sessionStartTimeInUserTimezone.toFormat(
    'EEEE, dd LLL',
    {
      locale: attendeeLearnerInfo.languagePreference ?? 'en',
    }
  );

  const sessionStartTimeInHoursMinutesAndMeridiem =
    sessionStartTimeInUserTimezone.toFormat('HH:mm a', {
      locale: attendeeLearnerObjectId?.languagePreference ?? 'en',
    });

  const attendeeName =
    attendeeLearnerInfo?.firstName ??
    attendeeLearnerInfo.email?.split('@')[0] ??
    '';
  // need timezone text in this format: Singapore GMT+8
  const formattedTimezone = `GMT${DateTime.utc()
    .setZone(sessionAttendee?.timezoneOfAttendee)
    .toFormat('Z')}`;

  const data = {
    event_name: session.title,
    event_link: session.location.locationValue,
    event_description: session?.description,
    event_type:
      session?.location.type === 'online' ? 'live' : session?.type,
    event_in_person_location:
      session?.location?.type === SESSION_TYPES.IN_PERSON
        ? session?.location?.locationValue
        : '',
    event_live_link:
      session?.location?.type === SESSION_TYPES.ONLINE
        ? session?.location?.locationValue
        : '',
    event_duration: duration.minutes,
    event_start_date_iso: startTimeISOString,
    event_end_date_iso: endTimeISOString,
    sessionStartDate: sessionStartTimeInFormat,
    sessionStartTime: sessionStartTimeInHoursMinutesAndMeridiem,
    session_end_date: sessionEndTimeInHoursMinutesAndMeridiem,
    timezone: formattedTimezone,
    timezoneOffset,
    ics_uid: String(sessionAttendee?._id),
    ics_sequence: 0,
    ics_organizer_name: `${hostLearnerInfo?.firstName ?? ''} ${
      hostLearnerInfo?.lastName ?? ''
    }`,
    ics_organizer_email: hostLearnerInfo?.email,
    ics_summary: session?.title,
    ics_event_start: formatICSDateTimeString(
      sessionAttendee?.sessionStartTime
    ),
    ics_event_end: formatICSDateTimeString(
      sessionAttendee?.sessionEndTime
    ),
    ics_description: session?.description,
    ics_location: session?.location?.locationValue,
    ics_alarm_description: session?.title,
    event_host_first_name: hostLearnerInfo?.firstName,
    session_title: titleOfTheSession,
    name: attendeeName,
  };

  const communityVariables = {
    community_code: community.code,
    community_name: community.title,
    community_link: `${REROUTE_MEMBER_LINK}?&activeCommunityId=${community._id}&memberExperience=1`,
    community_profile_image:
      community?.thumbnailImgData?.mobileImgData?.src ??
      DEFAULT_IMAGES.COMMUNITY_PROFILE_IMAGE,
  };

  const mailData = {
    ...data,
    ...communityVariables,
  };

  const managerEmailConfig = mailUtils.retrieveManagerMailConfig(
    community.title,
    community.link,
    hostLearnerInfo?.email,
    hostLearnerInfo?.firstName
  );

  await notificationCommonService.sendMailToQueue(
    SESSION_MAIL_TYPES.MEMBER_COMMUNITY_SESSION_CONFIRMATION,
    community?.code ?? 'All',
    session?._id ?? 'All',
    toMail,
    toMailName,
    mailData,
    `You have booked for ${session?.title}`,
    managerEmailConfig,
    config
  );

  // CM related data
  const sessionStartTimeInCMTimezone = sessionStartTimeInUTC.setZone(
    timezoneChosenForAvailability
  );
  const sessionEndTimeInCMTimezone = sessionEndTimeInUTC.setZone(
    timezoneChosenForAvailability
  );
  const sessionStartTimeInHoursMinutesAndMeridiemCM =
    sessionStartTimeInCMTimezone.toFormat('h:mma', {
      locale: hostLearnerInfo.languagePreference ?? 'en',
    });
  const sessionEndTimeInHoursMinutesAndMeridiemCM =
    sessionEndTimeInCMTimezone.toFormat('h:mma', {
      locale: hostLearnerInfo.languagePreference ?? 'en',
    });
  const sessionStartDateInDayMonthFormatCM =
    sessionStartTimeInCMTimezone.toFormat('EEEE, dd LLL', {
      locale: hostLearnerInfo.languagePreference ?? 'en',
    });
  const timezoneTextCM = getTimezoneText(timezoneChosenForAvailability);

  const cmMailData = {
    member_name: attendeeName,
    email: attendeeLearnerInfo?.email,
    sessionTitle: titleOfTheSession,
    sessionType: typeOfSession,
    sessionLocation: locationValue,
    sessionStartDate: sessionStartDateInDayMonthFormatCM,
    sessionStartTime: sessionStartTimeInHoursMinutesAndMeridiemCM,
    sessionEndTime: sessionEndTimeInHoursMinutesAndMeridiemCM,
    timezone: timezoneTextCM,
    applicationBody: sessionAttendee?.applicationInfo,
    community_name: community?.title,
    community_profile_image: community.thumbnailImgData.desktopImgData.src,
    session_title: titleOfTheSession,
    type: typeOfSession,
    locationValue,
    ics_uid: String(sessionAttendee?._id),
    ics_sequence: 0,
    ics_organizer_name: `${hostLearnerInfo?.firstName ?? ''} ${
      hostLearnerInfo?.lastName ?? ''
    }`,
    ics_organizer_email: hostLearnerInfo?.email,
    ics_summary: session?.title,
    ics_event_start: formatICSDateTimeString(
      sessionAttendee?.sessionStartTime
    ),
    ics_event_end: formatICSDateTimeString(
      sessionAttendee?.sessionEndTime
    ),
    ics_description: session?.description,
    ics_location: session?.location?.locationValue,
    ics_alarm_description: session?.title,
  };

  const checkoutInfo = await RawTransactionModel.findOne({
    purchasedId: checkoutId,
    communityObjectId: session.communityObjectId,
    transactionType: TRANSACTION_TYPE.INBOUND,
  }).lean();

  if (checkoutInfo?.originalAmount) {
    cmMailData.amount = checkoutInfo.originalAmount / 100;
    cmMailData.currency = checkoutInfo.originalCurrency;
  }

  // TODO: Comment this out first. Calendar createEvent facing duplication error.
  if (hostUserInfo.calendarforWritingEvents) {
    const writeCalendarAccountObjectId =
      hostUserInfo.calendarforWritingEvents.calendarAccountObjectId;
    const calendarAccount = (hostUserInfo.calendarAccounts ?? []).filter(
      (account) =>
        writeCalendarAccountObjectId.toString() ===
        account.calendarAccountObjectId._id.toString()
    )[0]?.calendarAccountObjectId;

    try {
      if (!calendarAccount) {
        throw new InternalError(
          `Missing calendar account for the given calendarforWritingEvents ${hostUserInfo.calendarforWritingEvents}`
        );
      }
      const calendarEvent = await addEventsInCalendar(
        hostUserInfo.calendarforWritingEvents,
        calendarAccount,
        data,
        attendeeLearnerInfo
      );
      await SessionAttendeesModel.findByIdAndUpdate(
        sessionAttendee._id,
        {
          calendarEvent,
        },
        { new: true, session: mongodbSession }
      ).lean();
    } catch (error) {
      await googleCalendarAlert({
        sessionAttendee,
        error,
        calendarAccount,
      });
    }
  }

  await notificationCommonService.sendMailToQueue(
    SESSION_MAIL_TYPES.MANAGER_COMMUNITY_SESSION_CONFIRMED,
    community?.code ?? 'All',
    session?._id ?? 'All',
    [hostLearnerInfo?.email],
    [hostLearnerInfo?.firstName],
    cmMailData
  );
};

const formatAndSendSessionCancellationEmailToUserAndCM = async ({
  sessionAttendee,
  session,
}) => {
  // name, session name, host name, session start time, session end time, timezonetext, session start date
  const { sessionStartTime, sessionEndTime, attendeeLearnerObjectId } =
    sessionAttendee;

  const {
    title: titleOfTheSession,
    location,
    timezoneChosenForAvailability,
  } = session;
  const { locationValue } = location;

  const attendeeLearnerInfo = await learnersModel.findById(
    attendeeLearnerObjectId
  );
  if (!attendeeLearnerInfo) {
    throw new ResourceNotFoundError(
      'No attendee learner found with the given id'
    );
  }

  const hostLearnerInfo = await learnersModel.findById(
    session.hostInfo.hostLearnerObjectId
  );
  if (!hostLearnerInfo) {
    throw new ResourceNotFoundError(
      'No host learner found with the given id'
    );
  }

  const community = await CommunityModel.findById(
    session.communityObjectId
  );

  if (!community) {
    throw new ResourceNotFoundError(
      'No community found with the given id'
    );
  }

  const sessionStartTimeInUTC =
    convertToUTCForAnyTimezone(sessionStartTime);
  const sessionEndTimeInUTC = convertToUTCForAnyTimezone(sessionEndTime);
  const attendeeName = attendeeLearnerInfo?.firstName;

  // Notes: move the mail notification for session cancelled to services/product/productChangeLog.service.js

  // CM related data

  const sessionStartTimeInCMTimezone = sessionStartTimeInUTC.setZone(
    timezoneChosenForAvailability
  );
  const sessionEndTimeInCMTimezone = sessionEndTimeInUTC.setZone(
    timezoneChosenForAvailability
  );
  const sessionStartTimeInHoursMinutesAndMeridiemCM =
    sessionStartTimeInCMTimezone.toFormat('HH:mm a', {
      locale: hostLearnerInfo.languagePreference ?? 'en',
    });
  const sessionEndTimeInHoursMinutesAndMeridiemCM =
    sessionEndTimeInCMTimezone.toFormat('HH:mm a', {
      locale: hostLearnerInfo.languagePreference ?? 'en',
    });
  const sessionStartDateInDayMonthFormatCM =
    sessionStartTimeInCMTimezone.toFormat('EEEE, dd LLL', {
      locale: hostLearnerInfo.languagePreference ?? 'en',
    });
  const timezoneTextCM = getTimezoneText(timezoneChosenForAvailability);

  // Use retrieveManagerMailConfig for consistent fromMail and fromMailName logic
  const managerEmailConfig = mailUtils.retrieveManagerMailConfig(
    community?.title,
    community?.link,
    community?.createdBy,
    community?.By,
    SESSION_MAIL_TYPES.MANAGER_COMMUNITY_SESSION_CANCELLED_V2
  );

  const mailRequestForCM = {
    mailType: SESSION_MAIL_TYPES.MANAGER_COMMUNITY_SESSION_CANCELLED_V2,
    mailCourse: community?.code ?? 'All',
    mailCourseOffer: session?._id ?? 'All',
    toMail: [hostLearnerInfo?.email],
    toMailName: [hostLearnerInfo?.firstName],
    fromMail: managerEmailConfig.fromMail,
    fromMailName: managerEmailConfig.fromMailName,
    data: {
      name: attendeeName,
      email: attendeeLearnerInfo?.email,
      product_title: titleOfTheSession,
      sessionTitle: titleOfTheSession,
      sessionLocation: locationValue,
      sessionStartDate: sessionStartDateInDayMonthFormatCM,
      sessionStartTime: sessionStartTimeInHoursMinutesAndMeridiemCM,
      sessionEndTime: sessionEndTimeInHoursMinutesAndMeridiemCM,
      timezone: timezoneTextCM,
      community_name: community?.title,
      community_profile_image:
        community.thumbnailImgData.desktopImgData.src,
      session_host: hostLearnerInfo?.firstName,
      session_duration: session.durationIntervalInMinutes,
      price: priceUtil.getPriceDisplayForMail(
        session.amount,
        session.currency
      ),
      product_image: session.thumbnail,
    },
  };

  await sendEmail(mailRequestForCM);
};

const createSessionBooking = async (
  session,
  sessionAttendeeObject,
  communityObjectId,
  addonTransaction,
  mongodbSession
) => {
  const existingAttendee = await SessionAttendeesModel.findOne({
    sessionObjectId: session._id,
    sessionStartTime: sessionAttendeeObject.sessionStartTime,
    sessionEndTime: sessionAttendeeObject.sessionEndTime,
    communityObjectId,
    attendeeLearnerObjectId: sessionAttendeeObject.attendeeLearnerObjectId,
  }).populate('communityObjectId attendeeLearnerObjectId');

  const generateReceipt = addonTransaction && addonTransaction.amount > 0;

  if (existingAttendee) {
    const updateQuery = {
      $set: {
        status: sessionAttendeeObject.status,
        isPending: sessionAttendeeObject.isPending,
      },
    };

    if (
      sessionAttendeeObject.applicationInfo &&
      Object.keys(sessionAttendeeObject.applicationInfo).length !== 0
    ) {
      updateQuery.$set.applicationInfo =
        sessionAttendeeObject.applicationInfo;
    }

    if (
      existingAttendee.status !==
      COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.PENDING
    ) {
      updateQuery.$push = {
        statusHistory: {
          status: existingAttendee.status,
          updatedByLearnerObjectId:
            sessionAttendeeObject.attendeeLearnerObjectId,
          timestamp: existingAttendee.updatedAt,
        },
      };
    }
    const updatedAttendee = await SessionAttendeesModel.findOneAndUpdate(
      {
        sessionObjectId: session._id,
        sessionStartTime: sessionAttendeeObject.sessionStartTime,
        sessionEndTime: sessionAttendeeObject.sessionEndTime,
        communityObjectId,
        attendeeLearnerObjectId:
          sessionAttendeeObject.attendeeLearnerObjectId,
      },
      updateQuery,
      {
        new: true,
        session: mongodbSession,
      }
    ).lean();

    if (!updatedAttendee) {
      throw new ResourceNotFoundError(
        `Error in updating the session attendee ${existingAttendee._id}`
      );
    }

    if (
      updatedAttendee.status ===
      COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.BOOKED
    ) {
      await CommunityFoldersModel.findOneAndUpdate(
        { _id: session._id },
        { $inc: { accessCount: 1 } }
      );

      const config = receiptService.generateReceiptConfig({
        purchasedId: addonTransaction?._id,
        purchaseType: PURCHASE_TYPE.SESSION,
        entityObjectId: session._id,
        communityObjectId: session.communityObjectId,
        learnerObjectId: sessionAttendeeObject.attendeeLearnerObjectId,
        generateReceipt,
      });

      await formatAndSendSessionConfirmationEmailToUserAndCM({
        sessionAttendee: updatedAttendee,
        session,
        mongodbSession,
        config,
      });
    }

    return updatedAttendee;
  }

  // this will handle the pending status and the conflict error if multiple requests are made at the same time
  const [sessionAttendee] = await SessionAttendeesModel.create(
    [sessionAttendeeObject],
    { session: mongodbSession }
  );

  await batchMetadataService.add({
    batchMetadataModelType: BATCH_METADATA_MODEL_TYPE.SESSION_ATTENDEE,
    entityObjectId: session._id,
    communityObjectId,
    addedObjectId: sessionAttendee._id,
  });

  if (
    sessionAttendee.status ===
    COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.BOOKED
  ) {
    if (
      (addonTransaction && addonTransaction.amount === 0) ||
      !addonTransaction
    ) {
      const learner = await learnersModel
        .findById(sessionAttendeeObject.attendeeLearnerObjectId)
        .lean();

      await ActionEventService.sendFreeAddonActionEvent({
        actionEventType: ADDON_ACTION_EVENT_TYPES.SESSION_SIGNUP,
        entityObjectId: session._id,
        entityCollection: COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.FOLDER,
        communityObjectId,
        addonTransaction,
        learner,
      });
    }
    await CommunityFoldersModel.findOneAndUpdate(
      { _id: session._id },
      { $inc: { accessCount: 1 } }
    );

    const config = receiptService.generateReceiptConfig({
      purchasedId: addonTransaction?._id,
      purchaseType: PURCHASE_TYPE.SESSION,
      entityObjectId: session._id,
      communityObjectId: session.communityObjectId,
      learnerObjectId: sessionAttendeeObject.attendeeLearnerObjectId,
      generateReceipt,
    });

    await formatAndSendSessionConfirmationEmailToUserAndCM({
      sessionAttendee,
      session,
      mongodbSession,
      config,
    });
  }

  return sessionAttendee;
};

const bookSessionService = async (body, communityObjectId) => {
  try {
    const session = await CommunityFoldersModel.findOne({
      _id: body.sessionObjectId,
      communityObjectId,
    }).lean();

    if (!session) {
      throw new ResourceNotFoundError(
        'No session found with the given session id'
      );
    }

    // await validationForSessionBooking(body, session, communityObjectId);

    const sessionAttendeeObject = await generateSessionAttendeeObject(
      body,
      session,
      communityObjectId
    );

    logger.info(
      `sessionAttendeeObject ${JSON.stringify(
        sessionAttendeeObject
      )} for creating the session attendee object of the session ${
        session._id
      } and community ${communityObjectId}`
    );

    const addonTransaction = await communityAddonTransactionsModel
      .findById(sessionAttendeeObject.checkoutId)
      .lean();

    const sessionAttendee = await createSessionBooking(
      session,
      sessionAttendeeObject,
      communityObjectId,
      addonTransaction
    );

    return sessionAttendee;
  } catch (error) {
    // if document with same unique key exists then throw a conflict error
    // refer to this https://medium.com/@roshan.waa/troubleshooting-mongoerror-e11000-duplicate-key-error-in-mongodb-with-javascript-example-1c3ff25b6fd2
    if (error.code === 11000) {
      throw new ToUserError(
        'Session slot is already booked',
        PRODUCT_ERROR.ALREADY_BOOKED
      );
    }
    logger.error('error in booking session', error, error.stack);
    throw error;
  }
};

const generateSessionAttendeesPipeline = ({
  filter,
  pageSize,
  pageNo,
  projection,
  order,
}) => {
  const skip = (pageNo - 1) * pageSize;
  const sortBy = order === 'asc' ? 1 : -1;
  const sessionAttendeesAggregate = [
    {
      $match: filter,
    },
    {
      $sort: {
        createdAt: sortBy,
      },
    },
    {
      $skip: skip,
    },
    {
      $limit: pageSize,
    },
    ...MongoDbUtils.lookupAndUnwind(
      'learners',
      'attendeeLearnerObjectId',
      '_id',
      'attendeeLearnerObjectId',
      true
    ),
    ...MongoDbUtils.lookupAndUnwind(
      'learners',
      'hostLearnerObjectId',
      '_id',
      'hostLearnerObjectId',
      true
    ),
    ...MongoDbUtils.lookupAndUnwind(
      'revenue_transactions',
      'checkoutId',
      'purchasedId',
      'transaction',
      true
    ),
    {
      $project: projection,
    },
  ];

  return sessionAttendeesAggregate;
};
const generateSessionAttendeesFilterForSearch = async ({
  search,
  type,
  session,
  communityObjectId,
}) => {
  const now = DateTime.now().toUTC();
  const status = type;

  const searchWithEscapedRegexSign = RegexUtils.escapeRegExp(search);

  const filter = {
    sessionObjectId: session._id,
    communityObjectId: new ObjectId(communityObjectId),
    status: {
      $ne: COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.PENDING,
    },
  };

  if (status === 'UPCOMING') {
    filter.sessionEndTime = {
      $gte: now,
    };
    filter.status = COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.BOOKED;
  } else if (status === 'PAST') {
    filter.sessionEndTime = {
      $lt: now,
    };
    filter.status = COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.BOOKED;
  } else if (status === 'CANCELLED') {
    filter.status = COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.CANCELLED;
  }

  if (searchWithEscapedRegexSign !== '') {
    const learnerRelatedToSearchResult =
      await retrieveLearnerObjectIdsViaSearch(
        searchWithEscapedRegexSign,
        communityObjectId
      );

    filter.attendeeLearnerObjectId = {
      $in: learnerRelatedToSearchResult,
    };
  }

  return filter;
};

// CMP
const getSessionBookings = async (
  sessionId,
  communityObjectId,
  limit,
  page,
  type,
  search,
  sortBy,
  sortOrder
) => {
  const now = DateTime.now().toUTC();
  const session = await CommunityFoldersModel.findOne({
    _id: sessionId,
    communityObjectId,
  });

  if (!session) {
    throw new ResourceNotFoundError(
      'No session found with the given session id'
    );
  }

  const skip = (page - 1) * limit;
  const status = type;

  const searchWithEscapedRegexSign = RegexUtils.escapeRegExp(search);

  const filter = {
    sessionObjectId: session._id,
    communityObjectId: new ObjectId(communityObjectId),
    status: {
      $ne: COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.PENDING,
    },
  };

  if (status === 'UPCOMING') {
    filter.sessionEndTime = {
      $gte: now,
    };
    filter.status = COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.BOOKED;
  } else if (status === 'PAST') {
    filter.sessionEndTime = {
      $lt: now,
    };
    filter.status = COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.BOOKED;
  } else if (status === 'CANCELLED') {
    filter.status = COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.CANCELLED;
  }

  if (searchWithEscapedRegexSign !== '') {
    const learnerRelatedToSearchResult =
      await retrieveLearnerObjectIdsViaSearch(
        searchWithEscapedRegexSign,
        communityObjectId
      );

    filter.attendeeLearnerObjectId = {
      $in: learnerRelatedToSearchResult,
    };
  }

  const sessionAttendeesAggregate = [
    {
      $match: filter,
    },
    {
      $sort: {
        [sortBy]: Number(sortOrder),
      },
    },
    {
      $skip: skip,
    },
    {
      $limit: limit,
    },
    ...MongoDbUtils.lookupAndUnwind(
      'learners',
      'attendeeLearnerObjectId',
      '_id',
      'attendeeLearnerObjectId',
      true
    ),
    ...MongoDbUtils.lookupAndUnwind(
      'learners',
      'hostLearnerObjectId',
      '_id',
      'hostLearnerObjectId',
      true
    ),
    ...MongoDbUtils.lookupAndUnwind(
      'revenue_transactions',
      'checkoutId',
      'purchasedId',
      'transaction',
      true
    ),
    {
      $match: {
        'transaction.transactionType': { $ne: TRANSACTION_TYPE.OUTBOUND },
      },
    },
    {
      $project: {
        sessionObjectId: 1,
        sessionStartTime: 1,
        sessionEndTime: 1,
        status: 1,
        type: 1,
        isPending: 1,
        createdAt: 1,
        updatedAt: 1,
        attendeeLearnerObjectId: 1,
        hostLearnerObjectId: 1,
        timezoneOfAttendee: 1,
        transactionId: '$transaction._id',
        applicationInfo: 1,
      },
    },
  ];

  if (status === 'UPCOMING' || status === 'PAST') {
    // remove the $addFields from the aggregate
    sessionAttendeesAggregate.push({
      $addFields: {
        status: {
          $cond: {
            if: {
              $gte: ['$sessionEndTime', new Date()],
            },
            then: 'UPCOMING',
            else: 'PAST',
          },
        },
      },
    });
  } else if (status !== 'CANCELLED') {
    // add the $addFields to the aggregate where the past ones are marked as PAST, UPCOMING as UPCOMING and the cancelled ones as CANCELLED
    sessionAttendeesAggregate.push({
      $addFields: {
        status: {
          $cond: {
            if: {
              $eq: [
                '$status',
                COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.CANCELLED,
              ],
            },
            then: 'CANCELLED',
            else: {
              $cond: {
                if: {
                  $gte: ['$sessionEndTime', new Date()],
                },
                then: 'UPCOMING',
                else: 'PAST',
              },
            },
          },
        },
      },
    });
  }

  const sessionAttendees = await SessionAttendeesModel.aggregate(
    sessionAttendeesAggregate
  );

  const totalAttendees = await SessionAttendeesModel.countDocuments(
    filter
  );

  const metadata = {
    total: totalAttendees,
    limit,
    page,
    pages: Math.ceil(totalAttendees / limit),
  };

  return {
    sessionAttendees,
    metadata,
  };
};

const cancelSession = async ({
  sessionId,
  communityObjectId,
  requestRefund,
  reasonForRefund = null,
  bookingId,
  transactionId = null,
  userInfo = null,
}) => {
  const session = await CommunityFoldersModel.findOne({
    _id: sessionId,
    communityObjectId,
  }).lean();

  if (!session) {
    throw new ResourceNotFoundError(
      'No session found with the given session id'
    );
  }

  const sessionAttendee = await SessionAttendeesModel.findOne({
    _id: bookingId,
    sessionObjectId: session._id,
    communityObjectId,
    status: COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.BOOKED,
  });

  if (!sessionAttendee) {
    throw new ResourceNotFoundError(
      'No session attendee found with the given session id'
    );
  }

  await SessionAttendeesModel.updateOne(
    { _id: sessionAttendee._id },
    {
      $set: {
        status: COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.CANCELLED,
        isPending: false,
      },
      $push: {
        statusHistory: {
          status: sessionAttendee.status,
          updatedByEmail: userInfo?.email ?? 'SYSTEM',
          updatedByLearnerObjectId: userInfo?.learner?._id,
        },
      },
    },
    {
      writeConcern: {
        w: 'majority',
      },
    }
  );

  if (sessionAttendee.calendarEvent) {
    const hostUserInfo = await UserModel.findOne({
      learner: new ObjectId(session.hostInfo?.hostLearnerObjectId),
      isActive: true,
    })
      .populate(
        'calendarAccounts.calendarAccountObjectId calendarforWritingEvents'
      )
      .select('calendarAccounts calendarforWritingEvents')
      .lean();

    if (!hostUserInfo) {
      throw new ResourceNotFoundError('Host user info not found');
    }

    if (hostUserInfo.calendarforWritingEvents) {
      const writeCalendarAccountObjectId =
        hostUserInfo.calendarforWritingEvents.calendarAccountObjectId;

      const calendarAccount = (hostUserInfo.calendarAccounts ?? []).find(
        (account) =>
          writeCalendarAccountObjectId.toString() ===
          account.calendarAccountObjectId._id.toString()
      )?.calendarAccountObjectId;

      if (!calendarAccount) {
        throw new InternalError(
          `Missing calendar account for the given calendarforWritingEvents ${hostUserInfo.calendarforWritingEvents}`
        );
      }

      await deleteEventsInCalendar(
        calendarAccount,
        sessionAttendee.calendarEvent
      );
    }
  }

  const aggregate = [
    {
      $match: {
        _id: sessionAttendee._id,
        isPending: false,
      },
    },
    ...MongoDbUtils.lookupAndUnwind(
      'learners',
      'attendeeLearnerObjectId',
      '_id',
      'attendeeLearnerObjectId',
      true
    ),
    ...MongoDbUtils.lookupAndUnwind(
      'learners',
      'hostLearnerObjectId',
      '_id',
      'hostLearnerObjectId',
      true
    ),
    ...MongoDbUtils.lookupAndUnwind(
      'revenue_transactions',
      'checkoutId',
      'purchasedId',
      'transaction',
      true
    ),
    {
      $addFields: {
        // show transactionId when transaction.transactionType == INBOUND
        transactionId: {
          $cond: {
            if: {
              $eq: [
                '$transaction.transactionType',
                TRANSACTION_TYPE.INBOUND,
              ],
            },
            then: '$transaction._id',
            else: null,
          },
        },
      },
    },
    {
      $project: {
        sessionObjectId: 1,
        sessionStartTime: 1,
        sessionEndTime: 1,
        status: 1,
        type: 1,
        isPending: 1,
        createdAt: 1,
        updatedAt: 1,
        attendeeLearnerObjectId: 1,
        hostLearnerObjectId: 1,
        timezoneOfAttendee: 1,
        applicationInfo: 1,
        transactionId: 1,
      },
    },
  ];

  const sessionAttendeeWithAdditionalInfo = (
    await SessionAttendeesModel.aggregate(aggregate)
      .read('primary')
      .readConcern('majority')
  )[0];

  const result = {
    sessionAttendee: sessionAttendeeWithAdditionalInfo,
  };

  if (requestRefund) {
    const refundResponse = await RefundService.requestRefund({
      communityId: communityObjectId,
      transactionId,
      emailRequested: userInfo?.email,
      learnerRequestedId: userInfo?.learner?._id,
      refundReason: reasonForRefund,
    });

    result.refundData = refundResponse;
  }

  // Add product deletion log for session booking cancellation
  const changeLog = await ProductChangeLogService.addCommunityProductLog({
    communityObjectId: session.communityObjectId,
    productType: PRODUCT_TYPE.SESSION,
    entityObjectId: session._id,
    changeLogType: PRODUCT_CHANGE_LOG_TYPE.PRODUCT_DELETED,
    operatorLearnerObjectId: userInfo?.learner?._id,
    metadata: { sessionAttendeeWithAdditionalInfo },
  });

  await formatAndSendSessionCancellationEmailToUserAndCM({
    sessionAttendee: sessionAttendeeWithAdditionalInfo,
    session,
  });

  return { ...result, changeLogId: changeLog._id };
};

// landing page
const getBookings = async (
  status,
  sessionId,
  userInfo,
  communityObjectId
) => {
  const now = DateTime.utc();
  const filter = {
    attendeeLearnerObjectId: new ObjectId(userInfo?.learner?._id),
    isPending: false,
    status: {
      $ne: COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.CANCELLED,
    },
  };

  if (status === 'UPCOMING') {
    filter.sessionEndTime = {
      $gte: now,
    };
  } else if (status === 'PAST') {
    filter.sessionEndTime = {
      $lt: now,
    };
  }

  if (sessionId) {
    filter.sessionObjectId = new ObjectId(sessionId);
  }
  if (communityObjectId) {
    filter.communityObjectId = new ObjectId(communityObjectId);
  }

  const aggregate = [
    {
      $match: filter,
    },
    ...MongoDbUtils.lookupAndUnwind(
      'community_folders',
      'sessionObjectId',
      '_id',
      'sessionObjectId',
      true
    ),
    ...MongoDbUtils.lookupAndUnwind(
      'learners',
      'attendeeLearnerObjectId',
      '_id',
      'attendeeLearnerObjectId',
      true
    ),
    ...MongoDbUtils.lookupAndUnwind(
      'learners',
      'hostLearnerObjectId',
      '_id',
      'hostLearnerObjectId',
      true
    ),
    {
      $project: {
        communityObjectId: 1,
        'sessionObjectId.title': 1,
        'sessionObjectId._id': 1,
        'sessionObjectId.location': 1,
        'sessionObjectId.description': 1,
        'sessionObjectId.resourceSlug': 1,
        'sessionObjectId.thumbnail': 1,
        'hostLearnerObjectId.firstName': 1,
        'hostLearnerObjectId.lastName': 1,
        'hostLearnerObjectId.profileImage': 1,
        attendeeLearnerObjectId: 1,
        status: 1,
        sessionEndTime: 1,
        sessionStartTime: 1,
        timezoneOfAttendee: 1,
      },
    },
  ];

  const bookingInfo = await SessionAttendeesModel.aggregate(aggregate);
  return bookingInfo;
};

const getCommunityUpcomingBookings = async ({
  userFromRequest,
  communityObjectId,
}) => {
  if (!userFromRequest) {
    return [];
  }
  return getBookings(
    'UPCOMING',
    undefined,
    userFromRequest,
    communityObjectId
  );
};

const sendSessionAttendeesBookingsReminder = async () => {
  const now = DateTime.utc();

  const mailType =
    SESSION_MAIL_TYPES.MEMBER_COMMUNITY_SESSION_REMINDER_24H;

  const sessionAttendees = await SessionAttendeesModel.find({
    status: COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.BOOKED,
    sessionStartTime: {
      $gte: now.plus({ days: 1 }).toJSDate(),
      $lte: now.plus({ days: 1, minutes: 5 }).toJSDate(),
    },
  }).populate('sessionObjectId');

  for await (const sessionAttendee of sessionAttendees) {
    const { sessionObjectId } = sessionAttendee;
    const { communityObjectId } = sessionObjectId;
    const community = await CommunityModel.findById(communityObjectId);

    if (!community) {
      throw new ResourceNotFoundError(
        'No community found with the given id'
      );
    }

    const hostLearnerInfo = await learnersModel.findById(
      sessionObjectId.hostInfo.hostLearnerObjectId
    );

    if (!hostLearnerInfo) {
      throw new ResourceNotFoundError(
        'No host learner found with the given id'
      );
    }

    const attendeeLearnerInfo = await learnersModel.findById(
      sessionAttendee.attendeeLearnerObjectId
    );

    if (!attendeeLearnerInfo) {
      throw new ResourceNotFoundError(
        'No attendee learner found with the given id'
      );
    }

    const sessionStartTimeInUTC = convertToUTCForAnyTimezone(
      sessionAttendee.sessionStartTime
    );
    const sessionEndTimeInUTC = convertToUTCForAnyTimezone(
      sessionAttendee.sessionEndTime
    );

    const { startTimeISOString, duration, timezoneOffset } =
      getEventTimeInfo({
        start: sessionAttendee.sessionStartTime,
        end: sessionAttendee.sessionEndTime,
        timezone: sessionAttendee.timezoneOfAttendee,
      });

    const sessionStartTimeInUserTimezone = sessionStartTimeInUTC.setZone(
      sessionAttendee.timezoneOfAttendee
    );
    const sessionStartTimeInFormat =
      sessionStartTimeInUserTimezone.toFormat('EEEE, dd LLL', {
        locale: attendeeLearnerInfo.languagePreference ?? 'en',
      });

    const sessionEndTimeInUserTimezone = sessionEndTimeInUTC.setZone(
      sessionAttendee.timezoneOfAttendee
    );

    const sessionEndTimeInHoursMinutesAndMeridiem =
      sessionEndTimeInUserTimezone.toFormat('hh:mma', {
        locale: attendeeLearnerInfo.languagePreference ?? 'en',
      });

    const sessionStartTimeInHoursMinutesAndMeridiem =
      sessionStartTimeInUserTimezone.toFormat('HH:mm a', {
        locale: attendeeLearnerInfo?.languagePreference ?? 'en',
      });
    // set format to 12 hr and hh:mm a

    const formattedTimezone = `GMT${DateTime.utc()
      .setZone(sessionAttendee.timezoneOfAttendee)
      .toFormat('Z')}`;

    const data = {
      event_name: sessionObjectId.title,
      event_link: sessionObjectId?.location?.locationValue,
      event_description: sessionObjectId.description,
      event_type:
        sessionObjectId?.location.type === 'online'
          ? 'live'
          : sessionObjectId?.type,
      event_in_person_location:
        sessionObjectId?.location?.type === SESSION_TYPES.IN_PERSON
          ? sessionObjectId?.location?.locationValue
          : '',
      event_live_link:
        sessionObjectId.location.type === SESSION_TYPES.ONLINE
          ? sessionObjectId.location.locationValue
          : '',
      event_duration: duration.minutes,
      event_start_date_iso: startTimeISOString,
      sessionStartDate: sessionStartTimeInFormat,
      sessionStartTime: sessionStartTimeInHoursMinutesAndMeridiem,
      session_end_date: sessionEndTimeInHoursMinutesAndMeridiem,
      timezone: formattedTimezone,
      timezoneOffset,
      ics_uid: String(sessionObjectId._id),
      ics_sequence: 0,
      ics_organizer_name: `${hostLearnerInfo?.firstName ?? ''} ${
        hostLearnerInfo?.lastName ?? ''
      }`,
      ics_organizer_email: hostLearnerInfo.email,
      ics_summary: sessionObjectId.title,
      ics_event_start: formatICSDateTimeString(
        sessionAttendee.sessionStartTime
      ),
      ics_event_end: formatICSDateTimeString(
        sessionAttendee.sessionEndTime
      ),
      ics_description: sessionObjectId.description,
      ics_location: sessionObjectId.location.locationValue,
      ics_alarm_description: sessionObjectId.title,
      event_host_first_name: hostLearnerInfo?.firstName,
      host_name: hostLearnerInfo?.firstName,
      session_title: sessionObjectId?.title,
      name: attendeeLearnerInfo?.firstName,
      community_code: community?.code,
      community_name: community?.title,
      community_link: `${REROUTE_MEMBER_LINK}?&activeCommunityId=${community._id}&memberExperience=1`,
      community_profile_image:
        community.thumbnailImgData?.mobileImgData?.src ??
        DEFAULT_IMAGES.COMMUNITY_PROFILE_IMAGE,
    };

    const mailRequestForStudent = {
      mailType,
      mailCourse: community.code,
      mailCourseOffer: sessionObjectId._id,
      toMail: [attendeeLearnerInfo.email],
      toMailName: [attendeeLearnerInfo.firstName ?? ''],
      data,
      mailSubject: `Get ready for ${sessionObjectId.title} in 24 hours`,
    };

    mailRequestForStudent['replyToMail'] = hostLearnerInfo.email;
    mailRequestForStudent['replyToMailName'] =
      hostLearnerInfo.firstName ?? '';

    await sendEmail(mailRequestForStudent);
  }

  return sessionAttendees;
};

const getFileNameForExport = async (communityId, sessionId) => {
  const [community, session] = await Promise.all([
    CommunityModel.findById(communityId),
    CommunityFoldersModel.findOne({ _id: sessionId }),
  ]);

  const [currentDate, currentTime] = new Date().toISOString().split('T');
  const dateTime = `${currentDate.replace(/-/g, '')}_${currentTime
    .split('.')[0]
    .replace(/:/g, '')}`;

  return `${community?.code}_session_${session?.resourceSlug.replace(
    '/',
    ''
  )}_${dateTime}.csv`;
};

const generateSessionAttendeesCsvStream = async ({
  search,
  type,
  pageSize,
  order,
  communityId,
  sessionId,
}) => {
  try {
    const session = await CommunityFoldersModel.findById(sessionId);

    if (!session) {
      throw new ResourceNotFoundError(
        'No session found with the given session id'
      );
    }
    const filter = await generateSessionAttendeesFilterForSearch({
      search,
      type,
      communityObjectId: communityId,
      sessionId,
      session,
    });

    const { timezoneChosenForAvailability } = session;
    const csvStream = new Readable({
      objectMode: true,
      read() {},
    });

    const header = `No, Session Title,Member Email, Session Date, Session time, Currency, Amount, Local Currency, Local Amount, Date of Booking, Time of booking, first name, last name, location`;

    const sessionAttendeesApplicationDocs =
      await SessionAttendeesModel.find({
        ...filter,
        // createdAt should be more than past 30 days ( product requirement)
        createdAt: {
          $gte: DateTime.now().minus({ days: 30 }).toJSDate(),
        },
        applicationInfo: { $exists: true, $ne: [] },
      }).select('applicationInfo');

    let allHeaders = header;
    if (sessionAttendeesApplicationDocs?.length > 0) {
      // get all unique applicationInfo labels

      const applicationInfoKeys = sessionAttendeesApplicationDocs?.reduce(
        (acc, { applicationInfo }) => {
          const keys = applicationInfo.map(
            ({ label }) => 'Booking question: ' + label
          );
          return [...acc, ...keys];
        },
        []
      );

      const headerWithApplicationInfo = `${header},${applicationInfoKeys.join(
        ','
      )}`;

      allHeaders = headerWithApplicationInfo;
    } else {
      allHeaders = header;
    }

    csvStream.push(allHeaders);

    let pageNo = 1;
    let hasNextPage = true;

    const projection = {
      firstName: '$attendeeLearnerObjectId.firstName',
      lastName: '$attendeeLearnerObjectId.lastName',
      email: '$attendeeLearnerObjectId.email',
      sessionStartTime: 1,
      sessionEndTime: 1,
      transactionId: 1,
      amount: '$transaction.originalAmount',
      currency: '$transaction.originalCurrency',
      localAmount: '$transaction.localAmount',
      localCurrency: '$transaction.localCurrency',
      createdAt: 1,
      location: '$location.locationValue',
      applicationInfo: 1,
    };

    let count = 0;
    const getNextPage = async () => {
      const pipelineQuery = generateSessionAttendeesPipeline({
        filter,
        pageSize,
        pageNo,
        projection,
        order,
      });

      const sessionAttendees = await SessionAttendeesModel.aggregate(
        pipelineQuery
      );

      if (sessionAttendees.length === 0) {
        hasNextPage = false;
        csvStream.push(null);
        return;
      }
      sessionAttendees.forEach(
        ({
          firstName,
          lastName,
          email,
          sessionStartTime,
          sessionEndTime,
          amount,
          currency,
          localAmount,
          localCurrency,
          createdAt,
          location,
          applicationInfo = [],
        }) => {
          // const get current row Index
          const sessionStartTimeInCMTimezone = DateTime.fromJSDate(
            sessionStartTime
          ).setZone(timezoneChosenForAvailability);
          const sessionEndTimeInCMTimezone = DateTime.fromJSDate(
            sessionEndTime
          ).setZone(timezoneChosenForAvailability);

          const row = `${count},${
            session.title
          },${email}, ${sessionStartTimeInCMTimezone.toFormat(
            'dd/MM/yyyy'
          )}, ${sessionStartTimeInCMTimezone.toFormat(
            'HH:mm'
          )}-${sessionEndTimeInCMTimezone.toFormat(
            'HH:mm'
          )}, ${currency}, ${amount / 100}, ${localCurrency}, ${
            localAmount / 100
          }, ${DateTime.fromJSDate(createdAt).toFormat(
            'dd/MM/yyyy'
          )}, ${DateTime.fromJSDate(createdAt).toFormat(
            'HH:mm'
          )}, ${firstName}, ${lastName}, ${location}`;

          let rowWithApplicationInfo = row;
          if (applicationInfo.length > 0) {
            const headerArray = allHeaders.split(',');
            // check if the csv header already has application[index].label in it if yes then add the row data to the csv otherwise add the header and the row data

            // check if the header already has the applicationInfoKeys and get the index of all the labels

            const headerInfoObj = {};
            applicationInfo.forEach(({ label: key }) => {
              if (headerArray.includes(`Booking question: ${key}`)) {
                headerInfoObj['Booking question: ' + key] = {
                  key: 'Booking question: ' + key,
                  index: headerArray.indexOf(`Booking question: ${key}`),
                };
              }
            });

            // now with the help of the headerInfoObj add the row data to the csv

            const currentRowInfo = rowWithApplicationInfo.split(',');
            applicationInfo.forEach((applicationDataInfo) => {
              const key = 'Booking question: ' + applicationDataInfo.label;
              if (headerInfoObj[key]) {
                currentRowInfo[headerInfoObj[key].index] =
                  applicationDataInfo.answer;
              }
            });

            rowWithApplicationInfo = currentRowInfo.join(',');
          }
          csvStream.push('\n' + rowWithApplicationInfo);
          count += 1;
        }
      );

      pageNo += 1;

      hasNextPage = sessionAttendees.length === pageSize;
    };

    csvStream._read = () => {
      if (hasNextPage) {
        getNextPage();
      } else {
        csvStream.push(null);
      }
    };

    return csvStream;
  } catch (error) {
    logger.error('error in generating csv stream', error, error.stack);
    throw new Error('Error generating session attendees CSV stream');
  }
};

module.exports = {
  createOneOnOneSession,
  updateOneOnOneSession,
  getOneOnOneSession,
  getOneOnOneSessionSlots,
  getHostInfo,
  bookSessionService,
  getSessionBookings,
  cancelSession,
  validationForSessionBooking,
  generateSessionAttendeeObject,
  createSessionBooking,
  getBookings,
  sendSessionAttendeesBookingsReminder,
  getFileNameForExport,
  generateSessionAttendeesCsvStream,
  formatSessionTestEmailVariables,
  getCommunityUpcomingBookings,
  generateSlug,
};
