/* eslint-disable no-param-reassign */
const URL = require('url').URL;

const status = require('http-status');
const Learner = require('../models/learners.model');
const Trainer = require('../models/trainers.model');
const Membership = require('../models/membership/membership.model');

const logger = require('./logger.service');
const {
  handleDocUpdateErr,
  handleFindingPersonErr,
  handleInvalidProfileTypeErr,
  handleNoPersonErr,
} = require('../handlers/error.handler');
const { deleteAsset } = require('./photo_upload.service');
const communityService = require('../communitiesAPI/services/common/community.service');
const countryInfoMappingService = require('./countryInfoMapping/countryInfoMapping.service');
const getInspiredService = require('./getInspired/getInspired.service');
const { getS3Key } = require('../utils/url_handling');
const { USER_BUCKET_LOCATION } = require('../constants/users');
const {
  SOCIAL_MEDIA_DOMAINS,
  SOCIAL_MEDIA_TYPES,
} = require('../constants/common');
const {
  checkPhoneNumber,
  getFormattedNumber,
} = require('../utils/phoneNumber.util');
const axios = require('../clients/axios.client');
const { WHATSAPP_BOT_URL } = require('../config');
const { flushLearnerCache } = require('./redisCache/redisCache.service');
const usersModel = require('../models/users.model');
const scrapeSocialMediaService = require('./scrapeSocialMediaInfo/scrapeSocialMediaInfo.service');

const stringIsAValidUrl = (s) => {
  try {
    // eslint-disable-next-line no-unused-vars
    const link = new URL(s);
    return true;
  } catch (err) {
    return false;
  }
};

const getUrl = (data) => {
  const link = data.link.trim();
  if (stringIsAValidUrl(link)) {
    return link;
  }
  switch (data.type) {
    case SOCIAL_MEDIA_TYPES.INSTAGRAM:
      return `${SOCIAL_MEDIA_DOMAINS.INSTAGRAM}/${link}`;
    case SOCIAL_MEDIA_TYPES.YOUTUBE:
      return `${SOCIAL_MEDIA_DOMAINS.YOUTUBE}/@${link}`;
    case SOCIAL_MEDIA_TYPES.TIKTOK:
      return `${SOCIAL_MEDIA_DOMAINS.TIKTOK}/@${link}`;
    case SOCIAL_MEDIA_TYPES.TWITTER:
      return `${SOCIAL_MEDIA_DOMAINS.TWITTER}/${link}`;
    case SOCIAL_MEDIA_TYPES.SNAPCHAT:
      return `${SOCIAL_MEDIA_DOMAINS.SNAPCHAT}/add/${link}`;

    case SOCIAL_MEDIA_TYPES.LINKEDIN:
      return `${SOCIAL_MEDIA_DOMAINS.LINKEDIN}/in/${link}`;
    case SOCIAL_MEDIA_TYPES.FACEBOOK:
      return `${SOCIAL_MEDIA_DOMAINS.FACEBOOK}/${link}`;
    case SOCIAL_MEDIA_TYPES.DISCORD:
    default:
      return null;
  }
};

const getUsername = (data) => {
  const link = data.link.trim();
  if (!stringIsAValidUrl(link)) {
    return link;
  }
  const pathname = new URL(link).pathname.replace('/', '');
  const pathsArr = pathname.split('/');
  switch (data.type) {
    case SOCIAL_MEDIA_TYPES.INSTAGRAM:
      return pathsArr[0];
    case SOCIAL_MEDIA_TYPES.YOUTUBE:
      return pathsArr[0].replace('@', '');
    case SOCIAL_MEDIA_TYPES.TIKTOK:
      return pathsArr[0].replace('@', '');
    case SOCIAL_MEDIA_TYPES.TWITTER:
      return pathsArr[0];
    case SOCIAL_MEDIA_TYPES.SNAPCHAT:
      if (pathsArr[0] === 'add') {
        return pathsArr[1];
      }
      return pathsArr[0];
    case SOCIAL_MEDIA_TYPES.DISCORD:
      return pathsArr[0];
    case SOCIAL_MEDIA_TYPES.LINKEDIN:
    case SOCIAL_MEDIA_TYPES.FACEBOOK:
    default:
      return null;
  }
};

const getFormattedSocialMedia = (socialMediaDetails) => {
  if (!socialMediaDetails.link) {
    return;
  }
  const data = { ...socialMediaDetails };
  data.url = getUrl(socialMediaDetails);
  data.username = getUsername(socialMediaDetails);
  return data;
};

const updateTrainerOrLearnerProfile = async (user, body) => {
  let person;
  let Model;
  let filter = {};
  const isWhatsappUser = user?.isWhatsappSignupUser;
  let isSocialMediaUpdated = false;
  logger.info('REQUEST BODY:', body);

  if (user.learner_role) {
    Model = Learner;
    filter = { _id: user.learner._id };
  } else if (user.instructor_role) {
    Model = Trainer;
    filter = { _id: user.trainer._id };
  } else {
    const err = new Error(
      'Neither a learner nor trainer for User' + user.user_id
    );
    err.status = status.NOT_ACCEPTABLE;
    throw err;
  }

  try {
    person = await Model.findOne(filter).lean();
  } catch (err) {
    handleFindingPersonErr(err);
  }

  if (!person) {
    logger.info('Person not found');
    const err = new Error('User not found');
    err.status = status.NOT_FOUND;
    throw err;
  }

  let toUpdateMembership = false;
  let needToFlushCache = false;
  const membershipSet = {};
  if (body.firstName || body.firstName === '') {
    person.firstName = body.firstName;
    toUpdateMembership = true;
    needToFlushCache = true;
  }
  if (body.lastName || body.lastName === '') {
    person.lastName = body.lastName;
    toUpdateMembership = true;
    needToFlushCache = true;
  }
  if (toUpdateMembership) {
    membershipSet.name = `${person.firstName} ${person.lastName}`.trim();
  }

  if (body.countryCode) {
    toUpdateMembership = true;

    const countryFromCache =
      await countryInfoMappingService.getCountryInfoByCodeFromDBorCache(
        body.countryCode
      );

    if (countryFromCache) {
      membershipSet.countryInfo = {
        name: countryFromCache.country,
        id: countryFromCache.countryId,
        code: countryFromCache.countryCode,
      };
      person.countryId = countryFromCache.countryId;
    }
  } else if (body.countryId) {
    person.countryId = body.countryId;
    toUpdateMembership = true;
    let countryFromCache =
      countryInfoMappingService.getCountryInfoByIdFromMemoryCache(
        body.countryId
      );
    if (!countryFromCache) {
      countryFromCache =
        await countryInfoMappingService.getCountryInfoByFallbackCountryIdFromDB(
          body.countryId
        );
    }
    if (countryFromCache) {
      membershipSet.countryInfo = {
        name: countryFromCache.country,
        id: countryFromCache.countryId,
        code: countryFromCache.countryCode,
      };
    }
  }
  if (body.description || body.description === '') {
    const charLen = body.description.length;
    if (charLen > 140) {
      const error = new Error(
        'Description longer than 140 characters - ' +
          charLen +
          ' characters'
      );
      error.status = status.BAD_REQUEST;
      throw error;
    }
    person.description = body.description;
  }

  // added for consistency reasons, TO CHECK WITH ADITYA IF WE SHOULD KEEP IT
  if (body.longDescription || body.longDescription === '') {
    const charLen = body.longDescription.length;
    if (charLen > 250) {
      const error = new Error(
        'Long description longer than 250 characters - ' +
          charLen +
          ' characters'
      );
      error.status = status.BAD_REQUEST;
      throw error;
    }
    person.longDescription = body.longDescription;
  }

  const creations = [];

  if (body.creations) {
    body.creations.forEach((item) => {
      const link = item.widget.link;
      let firstSubString = '';
      if (link.includes('www.youtube.com/watch?v=')) {
        firstSubString = 'www.youtube.com/watch?v=';
      }
      if (link.includes('https://youtu.be/')) {
        firstSubString = 'https://youtu.be/';
      }
      const id = link.substring(firstSubString.length, link.length);
      creations.push({
        title: item.title,
        widget: {
          type: 'youtube',
          link:
            firstSubString === ''
              ? link
              : `https://www.youtube.com/embed/${id}`,
        },
        description: item.description,
      });
    });
    person.creations = creations;
    logger.info('new creation:', person.creations);
    logger.info('newPerson: ', person);
  }

  if (body.socialMedia) {
    isSocialMediaUpdated = true;
    const socialMediaMap = new Map();

    body.socialMedia.forEach((newSocialMedia) => {
      const { type, url, username, link } = newSocialMedia;
      const formattedData =
        url || username
          ? newSocialMedia
          : getFormattedSocialMedia(newSocialMedia);

      if (!url && !username && !link) {
        // remove ALL entries for this type
        socialMediaMap.delete(type);
      } else {
        if (!socialMediaMap.has(type)) {
          socialMediaMap.set(type, []);
        }

        socialMediaMap.get(type).push(formattedData);
      }
    });

    person.socialMedia = [...socialMediaMap.values()].flat();

    if (person.socialMedia.length > 0) {
      membershipSet['accountExtraInfo.hasSocialMediaLinks'] = true;
    } else {
      membershipSet['accountExtraInfo.hasSocialMediaLinks'] = false;
    }
  }

  if (body.extraInfo) {
    person.extraInfo = body.extraInfo;
  }

  if (body.address) {
    person.address = body.address;
  }

  if (body.corporateEmail) {
    person.corporateEmail = body.corporateEmail;
  }
  person.corporateEmailSameAsLoginEmail =
    !!body.corporateEmailSameAsLoginEmail;

  if (body.corporateEmailSameAsLoginEmail) person.corporateEmail = '';

  if (body.phoneNumber) {
    // check if the phone number exist in the database already
    const phoneNumber = getFormattedNumber(body.phoneNumber);
    const oldPhoneNumber = person.phoneNumber;
    logger.info('phoneNumber: enter by the user ', phoneNumber);
    if (!checkPhoneNumber(phoneNumber)) {
      const err = new Error('Invalid phone number');
      err.status = status.BAD_REQUEST;
      throw err;
    }

    person.phoneNumber = phoneNumber;
    toUpdateMembership = true;
    needToFlushCache = true;
    membershipSet.phoneNumber = phoneNumber;

    // if the user is a whatsapp user, then the whatsapp number should be the same as the phone number
    if (isWhatsappUser && user?.learner_role) {
      try {
        axios.post(
          WHATSAPP_BOT_URL + '/removeUserFromAllConnectedGroups',
          {
            phoneNumber,
            oldPhoneNumber,
            learnerId: person?.learnerId,
          }
        );
      } catch (err) {
        logger.error('Error in removing user from whatsapp groups', err);
      }
    }
  }

  if (body.whatsAppNumber) {
    person.whatsAppNumber = body.whatsAppNumber;
  }

  if (
    body.whatsAppNumberSameAsPhoneNumber !== undefined &&
    body.whatsAppNumberSameAsPhoneNumber !== null
  ) {
    person.whatsAppNumberSameAsPhoneNumber =
      !!body.whatsAppNumberSameAsPhoneNumber;
  }

  if (body.whatsAppNumberSameAsPhoneNumber) person.whatsAppNumber = '';

  if (body.contentStyle) {
    person.contentStyle = body.contentStyle;
  }

  person.hasSubscribedToCommentEmails =
    body.hasSubscribedToCommentEmails || false;

  if (body.subtitlePreference) {
    person.subtitlePreference = body.subtitlePreference;
  }

  if (body.languagePreference) {
    person.languagePreference = body.languagePreference.toLowerCase();
    await communityService.updateCommunityLanguagePreference({
      learner: person,
      languagePreference: body.languagePreference.toLowerCase(),
    });
    needToFlushCache = true;
  }

  if (body.profileImage && body.profileImage !== '') {
    person.profileImage = body.profileImage;
    toUpdateMembership = true;
    membershipSet.profileImage = body.profileImage;
    membershipSet['accountExtraInfo.hasCustomProfileImage'] = true;
  }

  let saved;
  try {
    saved = await Model.findByIdAndUpdate(person._id, person, {
      new: true,
    }).lean();
  } catch (err) {
    handleDocUpdateErr(err);
  }

  if (toUpdateMembership) {
    await Membership.updateMany(
      {
        learnerObjectId: saved._id,
      },
      {
        $set: membershipSet,
      }
    );
  }

  if (isSocialMediaUpdated) {
    const userInfo = await usersModel
      .findOne({ learner: saved._id })
      .select('_id')
      .lean();
    await getInspiredService.generateAITemplateForUsersCommunities({
      userObjectId: userInfo._id,
      learnerObjectId: saved._id,
    });
  }

  if (body.communityId) {
    await scrapeSocialMediaService.scrapeSocialMediaInfo({
      learnerObjectId: saved._id,
      communityId: body.communityId,
    });
  }
  if (Model === Learner && needToFlushCache) {
    await flushLearnerCache(person._id);
  }
  return saved;
};

const getProfileByRole = async (user) => {
  try {
    let person;
    let Model;
    let filter = {};

    if (user.learner_role) {
      Model = Learner;
      filter = { _id: user.learner._id };
    } else if (user.instructor_role) {
      Model = Trainer;
      filter = { _id: user.trainer._id };
    } else {
      handleInvalidProfileTypeErr();
    }

    try {
      person = await Model.findOne(filter);
    } catch (err) {
      handleFindingPersonErr(err);
    }

    if (!person) {
      handleNoPersonErr();
    }

    return person;
  } catch (err) {
    handleDocUpdateErr(err);
  }
};

const updateContentInProfile = async (
  user,
  contentType,
  body,
  updateContentArr
) => {
  try {
    let person;
    let Model;
    let filter = {};

    logger.info('REQUEST BODY:', body);

    if (user.learner_role) {
      Model = Learner;
      filter = { _id: user.learner._id };
    } else if (user.instructor_role) {
      Model = Trainer;
      filter = { _id: user.trainer._id };
    } else {
      handleInvalidProfileTypeErr();
    }

    try {
      person = await Model.findOne(filter);
    } catch (err) {
      handleFindingPersonErr(err);
    }

    if (!person) {
      handleNoPersonErr();
    }

    // cb param
    updateContentArr(person, contentType, body);

    logger.info('Updating user profile...');
    return await person.save();
  } catch (err) {
    handleDocUpdateErr(err);
  }
};

const addContentItemToProfile = (user, contentType, body) => {
  // eslint-disable-next-line no-shadow
  const updateContents = (doc, contentType, body) => {
    doc[contentType].push(body);
  };

  return updateContentInProfile(user, contentType, body, updateContents);
};

const updateContentItemInProfile = (user, contentType, body) => {
  const updateContents = (doc) => {
    doc[contentType] = doc[contentType].map((item) => {
      if (item._id === body._id) {
        item.title = body.title;
        item.description = body.description;
        item.imageLink = body.imageLink;

        return item;
      }
      return item;
    });
  };

  return updateContentInProfile(user, contentType, body, updateContents);
};

const deleteContentItemFromProfile = (user, contentType, body) => {
  // eslint-disable-next-line no-shadow
  const updateContents = async (doc, contentType, body) => {
    const contentItems = [];
    let imageUrlToBeDeleted;
    doc[contentType].forEach((item) => {
      if (item._id === body.contentId) {
        imageUrlToBeDeleted = item.imageLink;
      } else {
        contentItems.push(item);
      }
    });

    // eslint-disable-next-line no-param-reassign
    doc[contentType] = contentItems;

    if (imageUrlToBeDeleted) {
      const assetKeyToDelete = getS3Key(
        imageUrlToBeDeleted,
        USER_BUCKET_LOCATION
      );

      await deleteAsset(assetKeyToDelete);
    }
  };

  return updateContentInProfile(user, contentType, body, updateContents);
};

module.exports = {
  getFormattedSocialMedia,
  getProfileByRole,
  updateTrainerOrLearnerProfile,
  addContentItemToProfile,
  updateContentItemInProfile,
  deleteContentItemFromProfile,
};
