const {
  INTERVALS,
  FEATURE_LIST_ID,
  FEATURE_LIST_NAME,
} = require('../../constants/common');

// Feature Permission Type constants
const FEATURE_PERM_TYPE = {
  FREE: 'FREE',
  PRO: 'PRO',
  PLATINUM: 'PLATINUM',
};

/**
 * Creates a feature configuration object
 * @param {string} feature - Feature name
 * @param {boolean} allowed - Whether feature is allowed
 * @param {number} [limit=0] - Usage limit (0 = unlimited)
 * @param {string} [interval=null] - Reset interval (e.g., 'month')
 * @param {number} [intervalCount=null] - Number of intervals
 * @returns {Object} Feature configuration object
 */
function createFeature(
  featureName,
  allowed,
  limit = 0,
  interval = null,
  intervalCount = null
) {
  return {
    featureName,
    allowed,
    limit,
    interval,
    intervalCount,
    isUnlimited: !!(allowed && limit === -1),
  };
}

// FREE plan features
const FEATURE_LIST_FREE = {
  [FEATURE_LIST_ID.GET_INSPIRED]: createFeature(
    FEATURE_LIST_NAME.GET_INSPIRED,
    false
  ),
  [FEATURE_LIST_ID.ZERO_LINK]: createFeature(
    FEATURE_LIST_NAME.ZERO_LINK,
    false
  ),
  [FEATURE_LIST_ID.MAGIC_ADS]: createFeature(
    FEATURE_LIST_NAME.MAGIC_ADS,
    false
  ),
  [FEATURE_LIST_ID.CUSTOM_PRODUCT_EMAILS]: createFeature(
    FEATURE_LIST_NAME.CUSTOM_PRODUCT_EMAILS,
    false
  ),
  [FEATURE_LIST_ID.MAGIC_REACH_PRODUCT_EMBED]: createFeature(
    FEATURE_LIST_NAME.MAGIC_REACH_PRODUCT_EMBED,
    false
  ),
  [FEATURE_LIST_ID.EMBED_BUSINESS_WIDGETS]: createFeature(
    FEATURE_LIST_NAME.EMBED_BUSINESS_WIDGETS,
    false
  ),
  [FEATURE_LIST_ID.AFFILIATES]: createFeature(
    FEATURE_LIST_NAME.AFFILIATES,
    false
  ),
  [FEATURE_LIST_ID.PIXEL_TRACKING]: createFeature(
    FEATURE_LIST_NAME.PIXEL_TRACKING,
    false
  ),
  [FEATURE_LIST_ID.EVENT_QR_CODE]: createFeature(
    FEATURE_LIST_NAME.EVENT_QR_CODE,
    false
  ),
  [FEATURE_LIST_ID.TRAFFIC_ANALYTICS]: createFeature(
    FEATURE_LIST_NAME.TRAFFIC_ANALYTICS,
    false
  ),
  [FEATURE_LIST_ID.STORAGE]: createFeature(
    FEATURE_LIST_NAME.STORAGE,
    true,
    1 // in GB
  ),
  [FEATURE_LIST_ID.MEMBER]: createFeature(
    FEATURE_LIST_NAME.MEMBER,
    true,
    500
  ),
  [FEATURE_LIST_ID.MANAGER]: createFeature(
    FEATURE_LIST_NAME.MANAGER,
    false,
    0
  ),
  [FEATURE_LIST_ID.MAGIC_REACH_WHATSAPP_MSG]: createFeature(
    FEATURE_LIST_NAME.MAGIC_REACH_WHATSAPP_MSG,
    false,
    0
  ),
  [FEATURE_LIST_ID.AI_COFOUNDER_MESSAGE]: createFeature(
    FEATURE_LIST_NAME.AI_COFOUNDER_MESSAGE,
    true,
    5,
    INTERVALS.DAY,
    1
  ),
  [FEATURE_LIST_ID.AI_COFOUNDER_PRODUCT_CREATION]: createFeature(
    FEATURE_LIST_NAME.AI_COFOUNDER_PRODUCT_CREATION,
    false
  ),
  [FEATURE_LIST_ID.PRODUCT_PUBLISH]: createFeature(
    FEATURE_LIST_NAME.PRODUCT_PUBLISH,
    false,
    0
  ),
  [FEATURE_LIST_ID.DEDICATED_HUMAN_SUPPORT]: createFeature(
    FEATURE_LIST_NAME.DEDICATED_HUMAN_SUPPORT,
    false
  ),
  [FEATURE_LIST_ID.VERIFICATION_BADGE]: createFeature(
    FEATURE_LIST_NAME.VERIFICATION_BADGE,
    false
  ),
  [FEATURE_LIST_ID.MAGIC_LEADS]: createFeature(
    FEATURE_LIST_NAME.MAGIC_LEADS,
    false
  ),
  [FEATURE_LIST_ID.AI_COFOUNDER_ADS_GENERATION]: createFeature(
    FEATURE_LIST_NAME.AI_COFOUNDER_ADS_GENERATION,
    true,
    1
  ),
  [FEATURE_LIST_ID.AI_COFOUNDER_GENERATION_LEADS_LIMIT]: createFeature(
    FEATURE_LIST_NAME.AI_COFOUNDER_GENERATION_LEADS_LIMIT,
    true,
    4,
    INTERVALS.MONTH,
    1
  ),
  [FEATURE_LIST_ID.AI_COFOUNDER_DISPLAY_LEADS_LIMIT]: createFeature(
    FEATURE_LIST_NAME.AI_COFOUNDER_DISPLAY_LEADS_LIMIT,
    true,
    2
  ),
  [FEATURE_LIST_ID.MAGIC_LEADS_SEARCH_LIMIT]: createFeature(
    FEATURE_LIST_NAME.MAGIC_LEADS_SEARCH_LIMIT,
    true,
    1,
    INTERVALS.MONTH,
    1
  ),
  [FEATURE_LIST_ID.MAGIC_LEADS_RESULTS_PER_SEARCH]: createFeature(
    FEATURE_LIST_NAME.MAGIC_LEADS_RESULTS_PER_SEARCH,
    true,
    5
  ),
};

// PRO plan features
const FEATURE_LIST_PRO = {
  [FEATURE_LIST_ID.GET_INSPIRED]: createFeature(
    FEATURE_LIST_NAME.GET_INSPIRED,
    true
  ),
  [FEATURE_LIST_ID.ZERO_LINK]: createFeature(
    FEATURE_LIST_NAME.ZERO_LINK,
    true
  ),
  [FEATURE_LIST_ID.MAGIC_ADS]: createFeature(
    FEATURE_LIST_NAME.MAGIC_ADS,
    false
  ),
  [FEATURE_LIST_ID.CUSTOM_PRODUCT_EMAILS]: createFeature(
    FEATURE_LIST_NAME.CUSTOM_PRODUCT_EMAILS,
    true
  ),
  [FEATURE_LIST_ID.MAGIC_REACH_PRODUCT_EMBED]: createFeature(
    FEATURE_LIST_NAME.MAGIC_REACH_PRODUCT_EMBED,
    true
  ),
  [FEATURE_LIST_ID.EMBED_BUSINESS_WIDGETS]: createFeature(
    FEATURE_LIST_NAME.EMBED_BUSINESS_WIDGETS,
    true
  ),
  [FEATURE_LIST_ID.AFFILIATES]: createFeature(
    FEATURE_LIST_NAME.AFFILIATES,
    false
  ),
  [FEATURE_LIST_ID.PIXEL_TRACKING]: createFeature(
    FEATURE_LIST_NAME.PIXEL_TRACKING,
    true
  ),
  [FEATURE_LIST_ID.EVENT_QR_CODE]: createFeature(
    FEATURE_LIST_NAME.EVENT_QR_CODE,
    false
  ),
  [FEATURE_LIST_ID.TRAFFIC_ANALYTICS]: createFeature(
    FEATURE_LIST_NAME.TRAFFIC_ANALYTICS,
    true
  ),
  [FEATURE_LIST_ID.STORAGE]: createFeature(
    FEATURE_LIST_NAME.STORAGE,
    true,
    50 // in GB
  ),
  [FEATURE_LIST_ID.MEMBER]: createFeature(
    FEATURE_LIST_NAME.MEMBER,
    true,
    10000
  ),
  [FEATURE_LIST_ID.MANAGER]: createFeature(
    FEATURE_LIST_NAME.MANAGER,
    true,
    3
  ),
  [FEATURE_LIST_ID.MAGIC_REACH_WHATSAPP_MSG]: createFeature(
    FEATURE_LIST_NAME.MAGIC_REACH_WHATSAPP_MSG,
    true,
    500,
    INTERVALS.MONTH,
    1
  ),
  [FEATURE_LIST_ID.AI_COFOUNDER_MESSAGE]: createFeature(
    FEATURE_LIST_NAME.AI_COFOUNDER_MESSAGE,
    true,
    100,
    INTERVALS.DAY,
    1
  ),
  [FEATURE_LIST_ID.AI_COFOUNDER_PRODUCT_CREATION]: createFeature(
    FEATURE_LIST_NAME.AI_COFOUNDER_PRODUCT_CREATION,
    true
  ),
  [FEATURE_LIST_ID.PRODUCT_PUBLISH]: createFeature(
    FEATURE_LIST_NAME.PRODUCT_PUBLISH,
    true,
    -1 // Unlimited
  ),
  [FEATURE_LIST_ID.DEDICATED_HUMAN_SUPPORT]: createFeature(
    FEATURE_LIST_NAME.DEDICATED_HUMAN_SUPPORT,
    false
  ),
  [FEATURE_LIST_ID.VERIFICATION_BADGE]: createFeature(
    FEATURE_LIST_NAME.VERIFICATION_BADGE,
    true
  ),
  [FEATURE_LIST_ID.MAGIC_LEADS]: createFeature(
    FEATURE_LIST_NAME.MAGIC_LEADS,
    false
  ),
  [FEATURE_LIST_ID.AI_COFOUNDER_ADS_GENERATION]: createFeature(
    FEATURE_LIST_NAME.AI_COFOUNDER_ADS_GENERATION,
    true,
    3
  ),
  [FEATURE_LIST_ID.AI_COFOUNDER_GENERATION_LEADS_LIMIT]: createFeature(
    FEATURE_LIST_NAME.AI_COFOUNDER_GENERATION_LEADS_LIMIT,
    true,
    4,
    INTERVALS.MONTH,
    1
  ),
  [FEATURE_LIST_ID.AI_COFOUNDER_DISPLAY_LEADS_LIMIT]: createFeature(
    FEATURE_LIST_NAME.AI_COFOUNDER_DISPLAY_LEADS_LIMIT,
    true,
    4
  ),
  [FEATURE_LIST_ID.MAGIC_LEADS_SEARCH_LIMIT]: createFeature(
    FEATURE_LIST_NAME.MAGIC_LEADS_SEARCH_LIMIT,
    true,
    10,
    INTERVALS.MONTH,
    1
  ),
  [FEATURE_LIST_ID.MAGIC_LEADS_RESULTS_PER_SEARCH]: createFeature(
    FEATURE_LIST_NAME.MAGIC_LEADS_RESULTS_PER_SEARCH,
    true,
    20
  ),
};

// PLATINUM plan features
const FEATURE_LIST_PLATINUM = {
  [FEATURE_LIST_ID.GET_INSPIRED]: createFeature(
    FEATURE_LIST_NAME.GET_INSPIRED,
    true
  ),
  [FEATURE_LIST_ID.ZERO_LINK]: createFeature(
    FEATURE_LIST_NAME.ZERO_LINK,
    true
  ),
  [FEATURE_LIST_ID.MAGIC_ADS]: createFeature(
    FEATURE_LIST_NAME.MAGIC_ADS,
    true
  ),
  [FEATURE_LIST_ID.CUSTOM_PRODUCT_EMAILS]: createFeature(
    FEATURE_LIST_NAME.CUSTOM_PRODUCT_EMAILS,
    true
  ),
  [FEATURE_LIST_ID.MAGIC_REACH_PRODUCT_EMBED]: createFeature(
    FEATURE_LIST_NAME.MAGIC_REACH_PRODUCT_EMBED,
    true
  ),
  [FEATURE_LIST_ID.EMBED_BUSINESS_WIDGETS]: createFeature(
    FEATURE_LIST_NAME.EMBED_BUSINESS_WIDGETS,
    true
  ),
  [FEATURE_LIST_ID.AFFILIATES]: createFeature(
    FEATURE_LIST_NAME.AFFILIATES,
    true
  ),
  [FEATURE_LIST_ID.PIXEL_TRACKING]: createFeature(
    FEATURE_LIST_NAME.PIXEL_TRACKING,
    true
  ),
  [FEATURE_LIST_ID.EVENT_QR_CODE]: createFeature(
    FEATURE_LIST_NAME.EVENT_QR_CODE,
    true
  ),
  [FEATURE_LIST_ID.TRAFFIC_ANALYTICS]: createFeature(
    FEATURE_LIST_NAME.TRAFFIC_ANALYTICS,
    true
  ),
  [FEATURE_LIST_ID.STORAGE]: createFeature(
    FEATURE_LIST_NAME.STORAGE,
    true,
    100 // in GB
  ),
  [FEATURE_LIST_ID.MEMBER]: createFeature(
    FEATURE_LIST_NAME.MEMBER,
    true,
    100000
  ),
  [FEATURE_LIST_ID.MANAGER]: createFeature(
    FEATURE_LIST_NAME.MANAGER,
    true,
    10
  ),
  [FEATURE_LIST_ID.MAGIC_REACH_WHATSAPP_MSG]: createFeature(
    FEATURE_LIST_NAME.MAGIC_REACH_WHATSAPP_MSG,
    true,
    1000,
    INTERVALS.MONTH,
    1
  ),
  [FEATURE_LIST_ID.AI_COFOUNDER_MESSAGE]: createFeature(
    FEATURE_LIST_NAME.AI_COFOUNDER_MESSAGE,
    true,
    300,
    INTERVALS.DAY,
    1
  ),
  [FEATURE_LIST_ID.AI_COFOUNDER_PRODUCT_CREATION]: createFeature(
    FEATURE_LIST_NAME.AI_COFOUNDER_PRODUCT_CREATION,
    true
  ),
  [FEATURE_LIST_ID.PRODUCT_PUBLISH]: createFeature(
    FEATURE_LIST_NAME.PRODUCT_PUBLISH,
    true,
    -1 // Unlimited
  ),
  [FEATURE_LIST_ID.DEDICATED_HUMAN_SUPPORT]: createFeature(
    FEATURE_LIST_NAME.DEDICATED_HUMAN_SUPPORT,
    true
  ),
  [FEATURE_LIST_ID.VERIFICATION_BADGE]: createFeature(
    FEATURE_LIST_NAME.VERIFICATION_BADGE,
    true
  ),
  [FEATURE_LIST_ID.MAGIC_LEADS]: createFeature(
    FEATURE_LIST_NAME.MAGIC_LEADS,
    false
  ),
  [FEATURE_LIST_ID.AI_COFOUNDER_ADS_GENERATION]: createFeature(
    FEATURE_LIST_NAME.AI_COFOUNDER_ADS_GENERATION,
    true,
    3
  ),
  [FEATURE_LIST_ID.AI_COFOUNDER_GENERATION_LEADS_LIMIT]: createFeature(
    FEATURE_LIST_NAME.AI_COFOUNDER_GENERATION_LEADS_LIMIT,
    true,
    4,
    INTERVALS.MONTH,
    1
  ),
  [FEATURE_LIST_ID.AI_COFOUNDER_DISPLAY_LEADS_LIMIT]: createFeature(
    FEATURE_LIST_NAME.AI_COFOUNDER_DISPLAY_LEADS_LIMIT,
    true,
    4
  ),
  [FEATURE_LIST_ID.MAGIC_LEADS_SEARCH_LIMIT]: createFeature(
    FEATURE_LIST_NAME.MAGIC_LEADS_SEARCH_LIMIT,
    true,
    30,
    INTERVALS.MONTH,
    1
  ),
  [FEATURE_LIST_ID.MAGIC_LEADS_RESULTS_PER_SEARCH]: createFeature(
    FEATURE_LIST_NAME.MAGIC_LEADS_RESULTS_PER_SEARCH,
    true,
    50
  ),
};

// Central feature config store
const FEATURE_PERM_DEFAULT_CONFIGS = {
  [FEATURE_PERM_TYPE.FREE]: FEATURE_LIST_FREE,
  [FEATURE_PERM_TYPE.PRO]: FEATURE_LIST_PRO,
  [FEATURE_PERM_TYPE.PLATINUM]: FEATURE_LIST_PLATINUM,
};

/**
 * Core Feature Permission Manager - handles business logic only
 */
class FeaturePermissionManager {
  /**
   * @param {string} currentPlanType - Community's current plan tier
   * @param {[Object]} grandfatherFeatures - Grandfathered features from database
   */
  constructor(currentPlanType, grandfatherFeatures = []) {
    this.grandfatherFeatures = {};
    this.featurePermType = null;
    this.finalFeatures = null;

    this._reformatGrandfaterFeature(grandfatherFeatures);
    this._computefeaturePermType(currentPlanType);
    this._computeFinalFeatures();
  }

  /**
   * Reformat the array of grandFather features to an object
   * @private
   */
  _reformatGrandfaterFeature(grandfatherFeatures) {
    grandfatherFeatures.forEach((permission) => {
      this.grandfatherFeatures[permission.featureId] = permission;
    });
  }

  /**
   * Compute features type based on current tier plan [Might want to cater for region next time]
   * @private
   */
  _computefeaturePermType(planType) {
    this.featurePermType = Object.values(FEATURE_PERM_TYPE).includes(
      planType
    )
      ? planType
      : FEATURE_PERM_TYPE.FREE; // Default to FREE
  }

  /**
   * Check if a feature from grandfather data is better than current plan
   * @private
   */
  // eslint-disable-next-line class-methods-use-this
  _isGrandfatherFeatureBetter(grandfatherFeature, currentFeaturePerms) {
    // If grandfather feature doesn't allow it, auto lose
    if (!grandfatherFeature || !grandfatherFeature.allowed) {
      return false;
    }
    // If current plan doesn't allow it, grandfather wins
    if (!currentFeaturePerms || !currentFeaturePerms.allowed) {
      return true;
    }

    if (currentFeaturePerms.isUnlimited) {
      return false;
    }

    if (grandfatherFeature.isUnlimited) {
      return true;
    }

    // Both allow the feature and both are not unlimited, so compare limits
    const grandfatherLimit = grandfatherFeature.limit || 0;
    const currentLimit = currentFeaturePerms.limit || 0;

    // Higher limit wins
    return grandfatherLimit > currentLimit;
  }

  /**
   * Compute final features combining current feeature permissions + grandfather features
   * @private
   */
  _computeFinalFeatures() {
    const configurations =
      FEATURE_PERM_DEFAULT_CONFIGS[this.featurePermType];

    const features = {};

    Object.keys(configurations).forEach((id) => {
      const featureId = parseInt(id, 10);
      const currentFeaturePerms = configurations[id];
      const grandfatherFeature = this.grandfatherFeatures[featureId];

      const isGrandfathered = this._isGrandfatherFeatureBetter(
        grandfatherFeature,
        currentFeaturePerms
      );

      const finalConfig = isGrandfathered
        ? { ...grandfatherFeature, isGrandfathered }
        : { ...currentFeaturePerms, isGrandfathered };
      features[featureId] = finalConfig;
    });
    this.finalFeatures = features;
  }

  /**
   * Get storage limit in Bytes
   * @returns {number} Storage limit in Bytes
   */
  _getStorageLimitInBytes() {
    return (
      (this.finalFeatures[FEATURE_LIST_ID.STORAGE]?.limit ?? 0) *
      1024 *
      1024 *
      1024
    );
  }

  /**
   * Check if a specific feature is grandfathered
   * @param {number} featureId - Feature ID to check
   * @returns {boolean} Whether this feature is using grandfather privileges
   */
  isFeatureGrandfathered(featureId) {
    return this.finalFeatures[featureId]?.isGrandfathered ?? false;
  }

  /**
   * Check if a feature is allowed
   * @param {number} featureId - Feature ID from FEATURE_LIST_ID
   * @param {string|null} [baseCurrency=null] - Base currency (e.g., 'INR')
   * @returns {boolean} Whether the feature is allowed
   */
  isFeatureAllowed(featureId, baseCurrency = null) {
    if (
      featureId === FEATURE_LIST_ID.ZERO_LINK &&
      baseCurrency === 'INR'
    ) {
      return false;
    }
    return this.finalFeatures[featureId]?.allowed ?? false;
  }

  /**
   * Get is feature unlimited
   * @param {number} featureId - Feature ID from FEATURE_LIST_ID
   * @returns {boolean} isUnlimited
   */
  isFeatureUnlimited(featureId) {
    return this.finalFeatures[featureId]?.isUnlimited ?? false;
  }

  /**
   * Get feature limit
   * @param {number} featureId - Feature ID from FEATURE_LIST_ID
   * @returns {number} Feature limit
   */
  getFeatureLimit(featureId) {
    if (featureId === FEATURE_LIST_ID.STORAGE) {
      return this._getStorageLimitInBytes();
    }
    return this.finalFeatures[featureId]?.limit ?? 0;
  }

  /**
   * Get complete feature configuration
   * @param {number} featureId - Feature ID from FEATURE_LIST_ID
   * @returns {Object|null} Feature configuration object
   */
  getFeature(featureId) {
    if (!this.finalFeatures[featureId]) return null;
    return {
      featureId,
      ...this.finalFeatures[featureId],
    };
  }

  /**
   * Get the feature permission type (e.g., FREE, PRO, PLATINUM)
   * @returns {string} Feature permission type
   */
  getFeaturePermType() {
    return this.featurePermType;
  }

  /**
   * Get all features with detailed information (for response builder)
   * @returns {Object[]} Array of feature objects with detailed info
   */
  getAllFeaturesDetailed() {
    return Object.entries(this.finalFeatures).map(([id, feature]) => ({
      featureId: parseInt(id, 10),
      ...feature,
    }));
  }

  static retrieveFeaturePermissionDefaultConfigs() {
    // Return a deep copy instead of the original
    return JSON.parse(JSON.stringify(FEATURE_PERM_DEFAULT_CONFIGS));
  }

  /**
   * Get boolean indicating if usage has maxed the limit
   * @param {number} featureId - Feature ID from FEATURE_LIST_ID
   * @returns {boolean} usage >= limit if not unlimited
   */
  hasMaxedFeatureLimit(featureId, usage) {
    if (this.isFeatureUnlimited(featureId)) {
      return false;
    }
    return usage >= this.getFeatureLimit(featureId);
  }

  /**
   * Get boolean indicating if usage has overshot the limit
   * @param {number} featureId - Feature ID from FEATURE_LIST_ID
   * @returns {boolean} usage > limit if not unlimited
   */
  hasOvershotFeatureLimit(featureId, usage) {
    if (this.isFeatureUnlimited(featureId)) {
      return false;
    }
    return usage > this.getFeatureLimit(featureId);
  }
}

module.exports = FeaturePermissionManager;
