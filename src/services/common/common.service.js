const logger = require('../logger.service');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const { ParamError } = require('../../utils/error.util');

const paginate = async (
  Model,
  pageNo,
  pageSize,
  sortBy,
  sortOrder,
  params = {},
  filterParams = {},
  populateParams = ''
) => {
  const skip = (pageNo - 1) * pageSize;
  logger.info('Paginating with the given params:', params);
  logger.info(
    'Limit: ',
    pageSize,
    ' Offset : ',
    skip,
    ' Sort by ',
    sortBy,
    ' in ',
    sortOrder
  );

  let totalCount;
  let result;

  try {
    totalCount = await Model.countDocuments({ ...params });
  } catch (error) {
    logger.error('Error finding the count of all records:', error);
    throw new Error('Error finding the count of all records: ' + error);
  }
  try {
    if (typeof sortBy === 'string') {
      result = await Model.find({ ...params }, { ...filterParams })
        .sort({ sortBy: sortOrder, _id: sortOrder })
        .skip(skip)
        .limit(pageSize)
        .populate(populateParams);
    } else if (Array.isArray(sortBy)) {
      const sortParams = {};
      sortBy.forEach((sortByParam) => {
        sortParams[sortByParam] = sortOrder;
      });
      result = await Model.find({ ...params }, { ...filterParams })
        .sort(sortParams)
        .skip(skip)
        .limit(pageSize)
        .populate(populateParams);
    }
  } catch (error) {
    logger.error('Error finding the paginated records:', error);
    throw new Error('Error finding the paginated records: ' + error);
  }

  return {
    totalCount,
    paginatedList: result.map((doc) => {
      return doc.toObject();
    }),
  };
};

const count = async (Model, params = {}) => {
  logger.info('Counting with the given params:', params);

  let totalCount;

  try {
    totalCount = await Model.find({ ...params }).count();
  } catch (error) {
    logger.error('Error finding the count of all records:', error);
    throw new Error('Error finding the count of all records: ' + error);
  }

  return {
    totalCount,
  };
};

const retrieveActiveCommunity = async (
  communityObjectId,
  projection = {}
) => {
  const community = await CommunityModel.findOne(
    {
      _id: communityObjectId,
      isActive: true,
    },
    projection
  ).lean();

  if (!community) {
    throw new ParamError('Community not found');
  }

  return community;
};

module.exports = { paginate, count, retrieveActiveCommunity };
