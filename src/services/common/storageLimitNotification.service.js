const { ENTITY_TYPE: PLAN_TYPE } = require('../plan/constants');
const {
  LIMITATION_MAIL_NOTIFICATION_TYPES,
  DEFAULT_IMAGES,
} = require('../mail/constants');
const {
  retrieveCommunityManagerInfo,
} = require('../communityNotification/email/common.service');
const {
  PAYMENT_MAILER_URL,
  NAS_IO_FRONTEND_URL,
} = require('@/src/config');
const { sendMessageToSQSQueue } = require('@/src/handlers/sqs.handler');
const CommunityModel = require('@/src/communitiesAPI/models/community.model');
const logger = require('../logger.service');
const { MOBILE_NOTIFICATION_TYPES } = require('@/src/constants/common');
const {
  sendMobileNotificationToQueue,
} = require('../notification/mobileNotifications.service');
const {
  getCommunityPermissions,
  getFeaturePlanPermissions,
} = require('../featurePermissions/communityPermissions.service');

function formatBytesToGB(bytes, showGB = true) {
  const gb = 1000 ** 3;

  const valueInGB = bytes / gb;

  if (valueInGB < 1) {
    return `${valueInGB.toFixed(1)}${showGB ? ' GB' : ''}`;
  }
  return `${valueInGB.toFixed(0)}${showGB ? ' GB' : ''}`;
}

const getCommonVariables = (community) => {
  const commonVariables = {
    community_code: community.code,
    community_name: community.title,
    community_profile_image:
      community?.thumbnailImgData?.mobileImgData?.src ??
      DEFAULT_IMAGES.COMMUNITY_PROFILE_IMAGE,
  };

  return commonVariables;
};
const getMailVariables = (community, mailType, customData, planType) => {
  const commonVariables = getCommonVariables(community);

  const mailVariables = {
    ...commonVariables,
  };

  const { percentage } = customData;

  mailVariables.percentage = `${percentage}%`;

  switch (mailType) {
    case LIMITATION_MAIL_NOTIFICATION_TYPES.FREE_PRO_STORAGE_LIMIT_HIT_LESS_THAN_100:
    case LIMITATION_MAIL_NOTIFICATION_TYPES.FREE_PRO_STORAGE_LIMIT_HIT_100:
      mailVariables.current_usage = customData.current_usage;
      mailVariables.storage_limit = customData.storage_limit;
      mailVariables.extra_storage = customData.extra_storage;
      break;
    case LIMITATION_MAIL_NOTIFICATION_TYPES.PLATINUM_STORAGE_LIMIT_HIT_LESS_THAN_100:
    case LIMITATION_MAIL_NOTIFICATION_TYPES.PLATINUM_STORAGE_LIMIT_HIT_100:
      mailVariables.current_usage = customData.current_usage;
      mailVariables.storage_limit = customData.storage_limit;
      break;
    default:
      break;
  }

  mailVariables.upgrade_link =
    planType === PLAN_TYPE.PRO
      ? `${NAS_IO_FRONTEND_URL}/portal/settings?activeCommunityId=${community._id}&tab=plan_billing&changePlan=true`
      : `${NAS_IO_FRONTEND_URL}/portal?activeCommunityId=${community._id}&startProSignup=true`;

  mailVariables.communityId = String(community._id);

  return mailVariables;
};
const getMailTypeBasedOnPlan = (planType, isMoreThanLimit) => {
  const {
    PLATINUM_STORAGE_LIMIT_HIT_100,
    PLATINUM_STORAGE_LIMIT_HIT_LESS_THAN_100,
    FREE_PRO_STORAGE_LIMIT_HIT_100,
    FREE_PRO_STORAGE_LIMIT_HIT_LESS_THAN_100,
  } = LIMITATION_MAIL_NOTIFICATION_TYPES;

  if (planType === PLAN_TYPE.PLATINUM) {
    return isMoreThanLimit
      ? PLATINUM_STORAGE_LIMIT_HIT_100
      : PLATINUM_STORAGE_LIMIT_HIT_LESS_THAN_100;
  }

  // For PRO and FREE fall into the same mapping
  return isMoreThanLimit
    ? FREE_PRO_STORAGE_LIMIT_HIT_100
    : FREE_PRO_STORAGE_LIMIT_HIT_LESS_THAN_100;
};

const getMobileNotificationTypeBasedOnPlan = (
  planType,
  is80Percent,
  isMoreThanLimit
) => {
  const {
    PLATINUM_STORAGE_LIMIT_HIT_100,
    PLATINUM_STORAGE_LIMIT_HIT_80,
    PLATINUM_STORAGE_LIMIT_HIT_90,
    FREE_PRO_STORAGE_LIMIT_HIT_100,
    FREE_PRO_STORAGE_LIMIT_HIT_80,
    FREE_PRO_STORAGE_LIMIT_HIT_90,
  } = MOBILE_NOTIFICATION_TYPES;

  const isPlatinum = planType === PLAN_TYPE.PLATINUM;

  if (isMoreThanLimit) {
    return isPlatinum
      ? PLATINUM_STORAGE_LIMIT_HIT_100
      : FREE_PRO_STORAGE_LIMIT_HIT_100;
  }

  if (isPlatinum) {
    return is80Percent
      ? PLATINUM_STORAGE_LIMIT_HIT_80
      : PLATINUM_STORAGE_LIMIT_HIT_90;
  }

  return is80Percent
    ? FREE_PRO_STORAGE_LIMIT_HIT_80
    : FREE_PRO_STORAGE_LIMIT_HIT_90;
};

const getNewStorageLimitBasedOnPlan = async (planType) => {
  const featurePermissionsPlan = await getFeaturePlanPermissions();
  switch (planType) {
    case PLAN_TYPE.PLATINUM:
      return null;
    case PLAN_TYPE.PRO:
      return featurePermissionsPlan.PLATINUM.STORAGE.limit * 1000 ** 3; // Convert GB to Bytes
    default:
      // for FREE
      return featurePermissionsPlan.PRO.STORAGE.limit * 1000 ** 3; // Convert GB to Bytes
  }
};

const getMobileVariables = async (planType, customData) => {
  // eslint-disable-next-line camelcase
  const { percentage, current_usage, is80Percent } = customData;

  const upgradedPlanTypeMap = {
    [PLAN_TYPE.PLATINUM]: '',
    [PLAN_TYPE.PRO]: 'Platinum',
    FREE: 'Pro',
  };

  const upgradeXStorage = planType === PLAN_TYPE.PRO ? '2x' : '50x';
  const emoji = is80Percent ? '📂' : '🚨';

  const mobileVariables = {
    percentage,
    // eslint-disable-next-line camelcase
    current_usage: `${current_usage}`,
    upgradeXStorage,
    upgradedPlanType: upgradedPlanTypeMap[planType],
    emoji,
  };

  return mobileVariables;
};

const sendStorageNotification = async ({
  community,
  percentage,
  newUsage,
  planType,
  is80Percent = false,
  storageLimit,
  isMoreThanLimit = false,
}) => {
  const mailType = getMailTypeBasedOnPlan(planType, isMoreThanLimit);

  const mobileNotificationType = getMobileNotificationTypeBasedOnPlan(
    planType,
    is80Percent,
    isMoreThanLimit
  );

  const extraStorage = await getNewStorageLimitBasedOnPlan(planType);
  const mailVariables = getMailVariables(
    community,
    mailType,
    {
      current_usage: formatBytesToGB(newUsage, false),
      storage_limit: formatBytesToGB(storageLimit),
      extra_storage: formatBytesToGB(extraStorage),
      percentage,
    },
    planType
  );

  const mobileVariables = await getMobileVariables(planType, {
    percentage: `${percentage}%`,
    current_usage: formatBytesToGB(newUsage),
    is80Percent,
  });

  const managerInfo = await retrieveCommunityManagerInfo(
    community.code,
    mailType
  );

  const requestData = {
    mailType,
    mailCourse: community.code ?? 'All',
    mailCourseOffer: 'All',
    requesterServiceName: 'LPBE',
  };
  await Promise.all(
    managerInfo.emails.map(async (email, index) => {
      requestData.toMail = [email];
      requestData.toMailName = [managerInfo.names[index]];
      requestData.data = {
        ...mailVariables,
        first_name: managerInfo.names[index],
      };

      const message = {
        data: requestData,
        requestor: 'LPBE',
      };
      await sendMessageToSQSQueue({
        queueUrl: PAYMENT_MAILER_URL,
        messageBody: message,
      });
    })
  );

  await sendMobileNotificationToQueue(
    mobileNotificationType,
    managerInfo.learnerIds,
    'global',
    mobileVariables
  );
};

exports.notifyStorageLimit = async ({
  communityObjectId,
  sizeInBytes,
  currentUsage,
  newUsage,
}) => {
  try {
    if (!sizeInBytes || sizeInBytes <= 0) return;

    const community = await CommunityModel.findById(
      communityObjectId
    ).lean();

    const planType = community?.config?.planType || 'FREE';

    // Define plan-wise storage limits
    const featurePermissions = await getCommunityPermissions(
      communityObjectId
    );

    const STORAGE_LIMITS = featurePermissions.features.find(
      (feature) => feature.featureName === 'STORAGE'
    );
    const storageLimitInBytes = STORAGE_LIMITS.limit * 1000 ** 3; // Convert GB to Bytes
    const storageLimit = storageLimitInBytes;

    // Calculate percentages
    const currentPercent = (currentUsage / storageLimit) * 100;
    const newPercent = (newUsage / storageLimit) * 100;

    const isMoreThanLimit = newUsage >= storageLimit;

    // Trigger stateless threshold notifications
    if (currentPercent < 80 && newPercent >= 80 && newPercent < 90) {
      await sendStorageNotification({
        community,
        newUsage,
        planType,
        percentage: 80,
        is80Percent: true,
        storageLimit,
      });
    }

    if (currentPercent < 90 && newPercent >= 90) {
      await sendStorageNotification({
        community,
        newUsage,
        planType,
        percentage: 90,
        storageLimit,
      });
    }

    if (isMoreThanLimit) {
      await sendStorageNotification({
        community,
        newUsage,
        planType,
        percentage: 100,
        isMoreThanLimit: true,
        storageLimit,
      });
    }
  } catch (error) {
    logger.error('Error in notifyStorageLimit:', error, error.stack);
    // no need to throw error here, just log it ( we don't want to block the upload process )
  }
};
