const communityService = require('./community.service');
const subscriptionService = require('./subscription.service');
const discountService = require('./discount.service');
const learnerService = require('./learner.service');
const purchaseTransactionService = require('./purchaseTransaction.service');
const membershipService = require('./membership.service');
const paymentProviderService = require('./paymentProvider');
const commonSubscriptionService = require('../../common/subscription.service');
const signupPayloadInformationSubscriptionService = require('../../communitySignup/signup/payloadInformation/subscription.service');
const signupValidationDiscountService = require('../../communitySignup/signup/validation/discount.service');
const rawTransactionService = require('./rawTransaction.service');
const actionEventService = require('../../actionEvent');
const PrimaryMongooseConnection = require('../../../rpc/primaryMongooseConnection');
const PaymentBackendRpc = require('../../../rpc/paymentBackend');
const subscriptionPaymentMethodService = require('../../subscriptions/paymentMethod');
const logger = require('../../logger.service');
const { ParamError } = require('../../../utils/error.util');
const {
  PAYMENT_PROVIDER,
  PAYMENT_RESPONSE_ACTION,
  COMMUNITY_SUBSCRIPTION_STATUSES,
} = require('../../../constants/common');

const paymentBackendRpc = new PaymentBackendRpc();

async function updateSubscriptionAndMembership({
  subscription,
  purchaseTransaction,
  newPurchaseTransaction,
  priceId,
  previousDiscountDetails,
  discount,
  session,
}) {
  const [updatedSubscription, updatedMembership, updatedDiscount] =
    await Promise.all([
      subscriptionService.updateSubscription({
        subscription,
        previousPurchaseTransaction: purchaseTransaction,
        purchaseTransaction: newPurchaseTransaction,
        priceId,
        previousDiscountDetails,
        session,
      }),
      membershipService.updateMembershipPaymentInfo({
        subscription,
        purchaseTransaction: newPurchaseTransaction,
        discount,
        session,
      }),
      discountService.updateDiscountUsage({ discount, session }),
    ]);

  return {
    updatedSubscription,
    updatedMembership,
    updatedDiscount,
  };
}

async function updateSubscriptionPlan({
  subscription,
  community,
  purchaseTransaction,
  priceId,
  subscriptionWithMetadata,
  previousDiscountDetails,
  session,
}) {
  const { paymentProvider } = subscription;
  const { metadata } = subscriptionWithMetadata;
  const { discount } = metadata;

  let newPurchaseTransaction =
    await purchaseTransactionService.addNewPurchaseTransaction({
      purchaseTransaction,
      community,
      subscriptionWithMetadata,
      session,
    });

  switch (paymentProvider) {
    case PAYMENT_PROVIDER.EBANX:
      newPurchaseTransaction =
        await purchaseTransactionService.updateRenewalAmountForEbanx({
          purchaseTransaction,
          newPurchaseTransaction,
          session,
        });
      break;
    case PAYMENT_PROVIDER.PAYPAL: {
      // For paypal change plan, member need to do auth for new plan activation
      // So here will just update paypal plan id to new purchase transaction
      const paypalPlan = await paymentProviderService.changePlan({
        community,
        subscription,
        purchaseTransaction: newPurchaseTransaction,
        priceId,
        discount,
        paymentBackendRpc,
      });

      newPurchaseTransaction =
        await purchaseTransactionService.updatePaypalPlanId({
          newPurchaseTransaction,
          paypalPlanId: paypalPlan.paypalPlanId,
          session,
        });
      return paypalPlan;
    }
    default:
      break;
  }

  // Lock all relevant documents within the session before calling the 3rd party API
  // This ensures that any changes are made within the transaction context
  // and are rolled back if the 3rd party API call fails, maintaining data consistency
  await updateSubscriptionAndMembership({
    subscription,
    purchaseTransaction,
    newPurchaseTransaction,
    priceId,
    previousDiscountDetails,
    discount,
    session,
  });

  const result = await paymentProviderService.changePlan({
    community,
    subscription,
    purchaseTransaction: newPurchaseTransaction,
    priceId,
    discount,
    paymentBackendRpc,
  });

  if (result.action === PAYMENT_RESPONSE_ACTION.NONE) {
    await actionEventService.sendMembershipChangePlanActionEvent({
      updatedPurchaseTransaction: newPurchaseTransaction,
      community,
    });
  }

  return result;
}

async function validatePayloadInformation({
  priceId,
  subscription,
  community,
  learner,
  purchaseTransaction,
  rawTransaction,
  subscriptionWithMetadata,
  allowSameInterval = false,
}) {
  const { _id: learnerObjectId } = learner;
  const { _id: communityObjectId } = community;
  const { interval, intervalCount } = subscription;
  const { metadata } = subscriptionWithMetadata;
  const { discount, price } = metadata;

  await commonSubscriptionService.checkIfCanChangePlan({
    subscription,
    purchaseTransaction,
    community,
    currentPaymentMethod: null,
    price,
    existsRawTransactionForPurchaseTransaction: rawTransaction,
    throwError: true,
  });

  if (
    learnerObjectId.toString() !== subscription.learnerObjectId.toString()
  ) {
    throw new ParamError('Invalid subscription');
  }

  if (subscription.stripePriceId === priceId) {
    throw new ParamError('Subscription is already subscribed to the plan');
  }

  const { recurring } = price;

  if (
    !allowSameInterval &&
    recurring.interval === interval &&
    recurring.interval_count === intervalCount
  ) {
    throw new ParamError('Cannot change plan with the same interval');
  }

  if (discount) {
    if (discount.isFullyDiscounted && discount.isForeverDuration) {
      throw new ParamError(
        '100% off discount is not allowed. Please contact us if you need help.'
      );
    } else if (discount.isFreeTrial) {
      throw new ParamError(
        'free trial discount is not allowed. Please contact us if you need help.'
      );
    }

    await signupValidationDiscountService.validateDiscount(
      subscriptionWithMetadata,
      communityObjectId,
      learner
    );
  }
}

async function retrievePayloadInformation({
  subscriptionObjectId,
  priceId,
  discountCode,
}) {
  const subscription = await subscriptionService.retrieveSubscription({
    subscriptionObjectId,
  });

  const [
    community,
    learner,
    purchaseTransaction,
    rawTransaction,
    paymentMethod,
  ] = await Promise.all([
    communityService.retrieveCommunity({
      subscription,
    }),
    learnerService.retrieveLearner({ subscription }),
    purchaseTransactionService.retrievePurchaseTransaction({
      subscription,
    }),
    rawTransactionService.retrieveRawTransaction({ subscription }),
    subscription.paymentProvider === PAYMENT_PROVIDER.STRIPE_US
      ? subscriptionPaymentMethodService.StripePaymentMethodService.getCurrentPaymentMethod(
          subscription.stripeSubscriptionId,
          subscription.paymentProvider
        )
      : null,
  ]);

  const previousDiscountDetails =
    await discountService.retrievePreviousDiscountDetails({
      purchaseTransaction,
      subscription,
    });

  const signupSubscriptionItem = {
    priceId,
    discountCode,
  };

  const paymentMethodCountryCode = paymentMethod?.card?.country;

  const subscriptionWithMetadata =
    await signupPayloadInformationSubscriptionService.retrieveSubscriptionInfo(
      {
        item: signupSubscriptionItem,
        community,
        existingSubscription: subscription,
        memberInfo: {
          paymentMethodCountryCode,
        },
        paymentProvider: subscription.paymentProvider,
        paymentBackendRpc,
      }
    );

  return {
    subscription,
    community,
    learner,
    purchaseTransaction,
    rawTransaction,
    subscriptionWithMetadata,
    previousDiscountDetails,
  };
}

exports.changePlan = async ({
  subscriptionObjectId,
  learnerObjectId,
  priceId,
  discountCode,
  allowSameInterval = false,
}) => {
  await paymentBackendRpc.init();

  const {
    subscription,
    community,
    learner,
    purchaseTransaction,
    rawTransaction,
    subscriptionWithMetadata,
    previousDiscountDetails,
  } = await retrievePayloadInformation({
    subscriptionObjectId,
    priceId,
    discountCode,
  });

  await validatePayloadInformation({
    priceId,
    subscription,
    community,
    learner,
    purchaseTransaction,
    rawTransaction,
    subscriptionWithMetadata,
    allowSameInterval,
  });

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    const result = await updateSubscriptionPlan({
      subscription,
      community,
      purchaseTransaction,
      priceId,
      subscriptionWithMetadata,
      previousDiscountDetails,
      session,
    });

    await session.commitTransaction();

    return result;
  } catch (err) {
    logger.error(`changePlan: error: ${err.message}, stack: ${err.stack}`);
    await session.abortTransaction();
    throw err;
  } finally {
    await session.endSession();
  }
};

exports.changePlanUpdate = async (purchaseTransactionObjectId) => {
  const newPurchaseTransaction =
    await purchaseTransactionService.retrievePurchaseTransactionById({
      purchaseTransactionId: purchaseTransactionObjectId,
    });

  const {
    subscription,
    purchaseTransaction,
    subscriptionWithMetadata,
    previousDiscountDetails,
    community,
  } = await retrievePayloadInformation({
    subscriptionObjectId: newPurchaseTransaction.subscriptionObjectId,
    priceId: newPurchaseTransaction.stripePriceId,
    discountCode: newPurchaseTransaction.promoCodeStripeId,
  });

  const { metadata } = subscriptionWithMetadata;
  const { discount } = metadata;

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    const result = await updateSubscriptionAndMembership({
      subscription,
      purchaseTransaction,
      newPurchaseTransaction,
      priceId: newPurchaseTransaction.stripePriceId,
      previousDiscountDetails,
      discount,
      session,
    });

    await session.commitTransaction();

    await actionEventService.sendMembershipChangePlanActionEvent({
      updatedPurchaseTransaction: newPurchaseTransaction,
      community,
    });

    return result;
  } catch (err) {
    logger.error(
      `changePlanUpdate: error: ${err.message}, stack: ${err.stack}`
    );
    await session.abortTransaction();
    throw err;
  } finally {
    await session.endSession();
  }
};

exports.revertPlanChange = async ({
  subscriptionObjectId,
  learnerObjectId,
}) => {
  const subscription = await subscriptionService.retrieveSubscription({
    subscriptionObjectId,
  });

  // Validate ownership
  if (
    learnerObjectId.toString() !== subscription.learnerObjectId.toString()
  ) {
    throw new ParamError('Invalid subscription');
  }

  // Validate subscription has plan history to revert for current billing cycle
  const currentBillingCycle = subscription.billingCycle || 1;
  const relevantPlanHistory =
    subscription.planHistory?.filter(
      (history) => history.billingCycle === currentBillingCycle
    ) || [];

  if (relevantPlanHistory.length === 0) {
    throw new ParamError(
      'No plan changes found to revert for current billing cycle'
    );
  }

  // Validate subscription is currently active
  if (subscription.status !== COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT) {
    throw new ParamError(
      'Subscription must be active to revert plan change'
    );
  }

  const result = await subscriptionService.revertSubscriptionPlanChange(
    subscription
  );

  return result;
};
