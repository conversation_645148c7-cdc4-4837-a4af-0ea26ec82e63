const SubscriptionModel = require('../../../communitiesAPI/models/communitySubscriptions.model');
const { ParamError } = require('../../../utils/error.util');
const logger = require('../../logger.service');
const PrimaryMongooseConnection = require('../../../rpc/primaryMongooseConnection');
const PaymentBackendRpc = require('../../../rpc/paymentBackend');
const paymentProviderService = require('./paymentProvider');
const communityService = require('./community.service');
const purchaseTransactionService = require('./purchaseTransaction.service');
const {
  COMMUNITY_SUBSCRIPTION_STATUSES,
} = require('../../../constants/common');

exports.retrieveSubscription = async ({ subscriptionObjectId }) => {
  const subscription = await SubscriptionModel.findById(
    subscriptionObjectId
  ).lean();

  if (!subscription) {
    throw new ParamError('Invalid subscription object id');
  }

  return subscription;
};

exports.updateSubscription = async ({
  subscription,
  previousPurchaseTransaction,
  purchaseTransaction,
  priceId,
  previousDiscountDetails,
  session,
}) => {
  const {
    _id: purchaseTransactionObjectId,
    interval,
    interval_count: intervalCount,
  } = purchaseTransaction;

  const {
    interval: previousInterval,
    interval_count: previousIntervalCount,
    local_currency: previousLocalCurrency,
    local_amount: previousLocalAmount,
    full_local_amount: previousFullLocalAmount,
  } = previousPurchaseTransaction;

  const {
    _id: subscriptionObjectId,
    stripePriceId,
    communitySignupId,
    nextBillingDate,
    billingCycle = 1,
  } = subscription;

  const planHistory = {
    stripePriceId,
    intervalCount: previousIntervalCount,
    interval: previousInterval,
    nextBillingDate,
    communitySignupId,
    changedDateTime: new Date(),
    billingCycle,
    planAmount: previousFullLocalAmount ?? previousLocalAmount,
    planCurrency: previousLocalCurrency,
  };

  if (previousDiscountDetails) {
    planHistory.discountDetails = previousDiscountDetails;
  }

  logger.info(`updateSubscription: ${purchaseTransactionObjectId}`);

  const updatedSubscription = await SubscriptionModel.findByIdAndUpdate(
    subscriptionObjectId,
    {
      $set: {
        interval,
        intervalCount,
        communitySignupId: purchaseTransactionObjectId.toString(),
        stripePriceId: priceId,
        stripePrice: purchaseTransaction.local_amount,
        amount: purchaseTransaction.amount,
      },
      $push: {
        planHistory,
      },
    },
    { new: true, session }
  ).lean();

  return updatedSubscription;
};

exports.revertSubscriptionPlanChange = async (subscription) => {
  const now = new Date();
  const BUFFER_HOURS = 24;
  const BUFFER_MS = BUFFER_HOURS * 60 * 60 * 1000;

  logger.info('Reverting subscription plan change', {
    subscriptionId: subscription._id,
    currentBillingCycle: subscription.billingCycle || 1,
    totalPlanHistoryLength: subscription.planHistory?.length || 0,
    relevantPlanHistoryLength: 0, // Will be updated after filtering
  });

  // Check if subscription is in current status
  if (subscription.status !== COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT) {
    throw new ParamError(
      'Subscription must be active to revert plan change'
    );
  }

  // Check if has plan history for current billing cycle
  const currentBillingCycle = subscription.billingCycle || 1;
  const relevantPlanHistory =
    subscription.planHistory?.filter(
      (history) => history.billingCycle === currentBillingCycle
    ) || [];

  logger.info('Filtered plan history by billing cycle', {
    subscriptionId: subscription._id,
    currentBillingCycle,
    relevantPlanHistoryLength: relevantPlanHistory.length,
  });

  if (relevantPlanHistory.length === 0) {
    throw new ParamError(
      'No plan changes to revoke for current billing cycle'
    );
  }

  // Check if within 24 hours of next billing date
  if (subscription.nextBillingDate) {
    const billingDeadline = new Date(
      subscription.nextBillingDate.getTime() - BUFFER_MS
    );
    if (now >= billingDeadline) {
      throw new ParamError(
        `Cannot revoke plan changes within ${BUFFER_HOURS} hours of next billing date due to payment provider processing`
      );
    }
  }

  // Get the last relevant plan history entry (previous plan from current billing cycle)
  const lastHistory = relevantPlanHistory[relevantPlanHistory.length - 1];

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();
  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    // Restore previous plan settings from history
    const updatedSubscription = await SubscriptionModel.findByIdAndUpdate(
      subscription._id,
      {
        $set: {
          stripePriceId: lastHistory.stripePriceId,
          interval: lastHistory.interval,
          intervalCount: lastHistory.intervalCount,
          stripePrice: lastHistory.planAmount,
          amount: lastHistory.planAmount,
          stripeCurrency: lastHistory.planCurrency,
          currency: lastHistory.planCurrency,
          nextBillingDate: lastHistory.nextBillingDate,
          billingCycle: lastHistory.billingCycle,
          communitySignupId: lastHistory.communitySignupId,
        },
        $pop: { planHistory: 1 }, // Remove the last history entry
      },
      { new: true, session }
    ).lean();

    if (!updatedSubscription) {
      throw new ParamError('Failed to revert subscription plan change');
    }

    // Update payment provider subscription back to previous plan
    const [community, oldPurchaseTransaction] = await Promise.all([
      communityService.retrieveCommunity({
        subscription: updatedSubscription,
      }),
      purchaseTransactionService.retrievePurchaseTransactionById({
        purchaseTransactionId: lastHistory.communitySignupId,
      }),
    ]);

    const paymentBackendRpc = new PaymentBackendRpc();
    await paymentBackendRpc.init();

    await paymentProviderService.changePlan({
      community,
      subscription: updatedSubscription,
      purchaseTransaction: oldPurchaseTransaction,
      priceId: lastHistory.stripePriceId,
      discount: lastHistory.discountDetails || null,
      paymentBackendRpc,
    });

    await session.commitTransaction();

    logger.info('Successfully reverted subscription plan change', {
      subscriptionId: subscription._id,
      revertedToPriceId: lastHistory.stripePriceId,
      revertedToAmount: lastHistory.planAmount,
    });

    return updatedSubscription;
  } catch (err) {
    logger.error('Failed to revert subscription plan change:', {
      error: err.message,
      subscriptionId: subscription._id,
      stack: err.stack,
    });
    await session.abortTransaction();
    throw err;
  } finally {
    await session.endSession();
  }
};
