const mongoose = require('mongoose');

const { ObjectId } = mongoose.Types;
const { DateTime } = require('luxon');

const AddonTransactionsModel = require('../../communitiesAPI/models/communityAddonTransactions.model');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const EventModel = require('../../communitiesAPI/models/communityEvents.model');
const LearnerModel = require('../../models/learners.model');
const PurchaseTransactionModel = require('../../communitiesAPI/models/communityPurchaseTransactions.model');
const SubscriptionModel = require('../../communitiesAPI/models/communitySubscriptions.model');

const AuthServiceRpc = require('../../rpc/authService.rpc');

const fraudService = require('../fraud');
const pricingService = require('../pricing');
const logger = require('../logger.service');
const formatVariableDataService = require('../mail/formatVariableData.service');
const { isValidURL } = require('../../utils/url_handling');
const mailUtils = require('../../utils/mail.util');
const nameUtils = require('../../utils/name.util');
const slugUtils = require('../../utils/slug.util');
const entityCurrencyUtils = require('../../utils/entityCurrency.util');
const {
  getTimeBeforeStartTime,
} = require('../../communitiesAPI/utils/eventUtils');
const { ParamError, ToUserError } = require('../../utils/error.util');
const { EVENT_ERROR } = require('../../constants/errorCode');
const {
  EVENT_STATUS,
  EVENT_PAYMENT_STATUSES,
  COMMUNITY_EVENT_ACCESS_TYPES,
  COMMUNITY_ONE_TIME_PAYMENT_ENTITIES,
} = require('../../communitiesAPI/constants');
const {
  communityEnrolmentStatuses,
  EVENT_TYPES,
  MAX_QUANTITY_PER_PURCHASE,
} = require('../../constants/common');
const { EVENT_MAIL_TYPES } = require('../mail/constants');
const notificationCommonService = require('../communityNotification/email/common.service');
const {
  getUpdateDataForMultipleCoverMediaItems,
} = require('../../utils/multipleCoverMediaItems.util');
const {
  COVER_MEDIA_ENTITY_TYPES,
} = require('../../constants/coverMediaItems.constant');
const {
  checkActiveCampaignForSlugChange,
} = require('../../utils/metaAds.util');

// Code refactoring to prevent circular dependencies. These functions were taken from event/index.js
async function getLearnerTimezone(event, community, learner) {
  let timezone = learner?.timezone;

  let newCommunity = community;
  try {
    if (!timezone) {
      if (!newCommunity) {
        newCommunity = await CommunityModel.findById(
          event?.communities?.[0]
        ).lean();
      }
      if (event?.access === COMMUNITY_EVENT_ACCESS_TYPES.FREE) {
        const communityPurchaseTransaction =
          await PurchaseTransactionModel.findOne({
            email: learner?.email,
            community_code: newCommunity?.code,
            'payment_details.complete_payment': 1,
          }).lean();

        timezone = communityPurchaseTransaction?.timezone;
      } else if (event?.access === COMMUNITY_EVENT_ACCESS_TYPES.PAID) {
        const eventTransaction = await AddonTransactionsModel.findOne({
          entityCollection: COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.EVENT,
          entityObjectId: new ObjectId(event?._id),
          learnerObjectId: learner?._id,
          'payment_details.status': EVENT_PAYMENT_STATUSES.SUCCESS,
        }).lean();
        timezone = eventTransaction?.timezone;
      }

      if (!timezone) {
        timezone = newCommunity?.timezone;
      }
    }

    if (!timezone) {
      timezone = 'UTC';
    }
  } catch (error) {
    logger.error('Error while getting learner timezone', error);
    timezone = 'UTC';
  }

  return timezone;
}

exports.formatAndSendEventTypeEmail = async ({
  mailType,
  learnerObjectId,
  event,
  learner = null,
  community,
  additionalFields = {},
  owner = null,
  isManagerEmail = false,
  config = {},
}) => {
  let timezone = 'UTC';
  let languagePreference = 'en';
  let newLearner = learner;
  let emailToken;

  if (!newLearner) {
    newLearner = await LearnerModel.findById(learnerObjectId);
    if (!newLearner) {
      logger.info(`Learner with ObjectId ${learnerObjectId} not found`);
      return 'Could not find learner';
    }
  }
  const name = nameUtils.getName(
    newLearner?.firstName,
    newLearner?.lastName,
    newLearner?.email
  );

  const toMail = [newLearner.email];
  const toMailName = [name];

  timezone =
    (await getLearnerTimezone(event, community, newLearner)) || 'UTC';
  languagePreference = newLearner?.languagePreference || 'en';

  if (
    [
      EVENT_MAIL_TYPES.MEMBER_COMMUNITY_EVENT_PENDING_APPROVAL,
      EVENT_MAIL_TYPES.MEMBER_COMMUNITY_EVENT_APPROVED,
      EVENT_MAIL_TYPES.MEMBER_COMMUNITY_EVENT_UPDATE,
      EVENT_MAIL_TYPES.MEMBER_COMMUNITY_EVENT_RSVP,
    ].includes(mailType)
  ) {
    const authServiceRpc = new AuthServiceRpc();
    const { token } = await authServiceRpc.generateEmailToken(toMail[0]);
    emailToken = token;
  }

  const eventVariables = formatVariableDataService.formatEventData({
    community,
    event,
    timezone,
    owner,
    languagePreference,
    emailToken,
  });
  const communityVariables = formatVariableDataService.formatCommunityData(
    {
      community,
      emailToken,
    }
  );

  const emailData = {
    name: newLearner?.firstName ?? '',
    email: newLearner?.email,
    event_attendee_profile_image: newLearner?.profileImage,
    ...communityVariables,
    ...eventVariables,
    ...additionalFields,
  };

  const communityOwnerEmail = owner?.email;
  const communityOwnerName = owner
    ? nameUtils.getName(owner?.firstName, owner?.lastName, owner?.email)
    : community['By'];

  const managerEmailConfig = mailUtils.retrieveManagerMailConfig(
    community.title,
    community.link,
    community.createdBy || communityOwnerEmail,
    communityOwnerName,
    mailType
  );

  await notificationCommonService.sendMailToQueue(
    mailType,
    community?.code ?? 'All',
    event?._id ?? 'All',
    toMail,
    toMailName,
    emailData,
    null,
    isManagerEmail ? null : managerEmailConfig,
    config
  );
};

exports.checkEventForFraud = async ({
  updatedPayload,
  community,
  eventId,
}) => {
  const contentList = [];
  const contentSourceList = [];
  if (updatedPayload?.title) {
    contentList.push(updatedPayload.title);
    contentSourceList.push('title');
  }
  if (updatedPayload?.description) {
    contentList.push(updatedPayload.description);
    contentSourceList.push('description');
  }
  if (contentList.length > 0) {
    const fraudEngine = new fraudService.FraudEngine({
      communityId: community._id,
      eventName: fraudService.INTERESTED_EVENTS.UPDATE_CONTENT,
      entityType: 'event',
      entityId: eventId,
      data: {
        content: contentList.join(', '),
        contentSource: contentSourceList.join(' & '),
      },
      checksToPerform: [fraudService.COMMON_FRAUD_CHECKS.FREE_INPUT],
      autoConsequencesToApply: [
        fraudService.COMMON_CONSEQUENCES.RESTRICT_CHECKOUT,
        fraudService.COMMON_CONSEQUENCES.RESTRICT_CUSTOM_EMAIL,
        fraudService.COMMON_CONSEQUENCES.RESTRICT_MAGIC_REACH,
        fraudService.COMMON_CONSEQUENCES.NOT_INDEXABLE,
        fraudService.COMMON_CONSEQUENCES.RECOMMEND_DEACTIVATE,
      ],
    });

    try {
      await fraudEngine.performCheck();
    } catch (error) {
      logger.error('Error in fraud check:', error.message, error.stack);
    }
  }
};

async function checkIfExistsPendingSubscription(community) {
  const { code: communityCode } = community;

  const existPendingSubscription = await SubscriptionModel.exists({
    communityCode,
    status: communityEnrolmentStatuses.PENDING,
  }).read('primary');

  return !!existPendingSubscription;
}

const validateEventParams = async ({
  community,
  existingEvent,
  params = {},
}) => {
  entityCurrencyUtils.validateEntityCurrencyWithCommunityBaseCurrency(
    params.currency,
    community.baseCurrency
  );

  if (
    params.applicationConfigDataFields &&
    params.applicationConfigDataFields.length > 0
  ) {
    if (
      params.applicationConfigDataFields.some(
        (field) => field.label === ''
      )
    ) {
      throw new ParamError('Empty question not allowed');
    }
  }

  const currentTime = DateTime.local().setZone('UTC');
  if (params.startTime && params.startTime < currentTime) {
    throw new ParamError('Start time cannot be in past!');
  }

  const existsPendingSubscription = await checkIfExistsPendingSubscription(
    community
  );

  if (
    params?.requiresApproval &&
    (community.request_approval || existsPendingSubscription)
  ) {
    // check if community has an application form enabled if yes then throw an error
    throw new ToUserError(
      'Approval cannot be enabled to an event in a community with request approval enabled',
      EVENT_ERROR.CANNOT_ADD_EVENT_APPROVAL
    );
  }

  if (params.endTime && params.endTime < currentTime) {
    throw new ParamError('End time cannot be in past!');
  }

  if (
    params.startTime &&
    params.startTime > (params.endTime ?? existingEvent?.endTime)
  ) {
    throw new ParamError('Start time cannot be before the end time!');
  }

  if (
    params.endTime &&
    params.endTime < (params.startTime ?? existingEvent?.startTime)
  ) {
    throw new ParamError('End Time cannot be before the startTime!');
  }
  if (
    params.type === EVENT_TYPES.LIVE &&
    !!params.liveLink &&
    !isValidURL(params.liveLink)
  ) {
    throw new ToUserError(
      'Invalid live link',
      EVENT_ERROR.INVALID_EVENT_LIVE_LINK
    );
  }

  if (params.recordingLink?.length && !isValidURL(params.recordingLink)) {
    const error = new ToUserError(
      'Invalid recording link',
      EVENT_ERROR.INVALID_EVENT_RECORDING_LINK
    );
    throw error;
  }

  if (params.chatGroupLink?.length && !isValidURL(params.chatGroupLink)) {
    const error = new ToUserError(
      'Invalid chat group link',
      EVENT_ERROR.INVALID_CHAT_GROUP_LINK
    );
    throw error;
  }

  if (params.description && params.description.length > 5000) {
    throw new ParamError('Description cannot exceed 5000 characters');
  }

  if (
    params.maxQuantityPerPurchase &&
    params.maxQuantityPerPurchase > MAX_QUANTITY_PER_PURCHASE
  ) {
    throw new ToUserError(
      'The maximum purchase limit cannot be set beyond the allowed threshold',
      EVENT_ERROR.EVENT_SET_MAX_PURCHASE_QUANTITY_LIMIT_REACHED
    );
  }

  if (params.slug) {
    const slug = params.slug;

    slugUtils.validateSlug(slug);

    const communityObjectId = community._id;
    if (slug.charAt(0) !== '/') {
      throw new ParamError("Slug should start with '/'");
    }

    await checkActiveCampaignForSlugChange(
      community._id,
      existingEvent?._id
    );

    //To check if this condition is needed
    if (slug !== existingEvent?.slug) {
      // slug given, validate uniquiness
      const anotherExistingEvent = await EventModel.exists({
        communities: communityObjectId,
        slug,
        status: {
          $in: [EVENT_STATUS.DRAFT, EVENT_STATUS.PUBLISHED, 'Active'],
        },
      });

      logger.info(
        `Is there another event in community ${communityObjectId} with slug ${slug}: ${existingEvent}`
      );
      if (anotherExistingEvent) {
        throw new ToUserError(
          'The event URL is used before. Please put a different URL.',
          EVENT_ERROR.DUPLICATE_EVENT_SLUG
        );
      }
    } else {
      logger.info(
        `Slug ${slug} already belongs to existing event specified ${existingEvent?._id}`
      );
    }
  }
};

exports.validateAndFormatEventParams = async (
  params = {},
  community,
  existingEvent = null,
  options = {
    formatPricing: true,
  }
) => {
  if (Object.keys(params).length === 0 && params.constructor === Object) {
    return;
  }

  logger.info('Validating and formatting Community event params:', params);
  await validateEventParams({ params, community, existingEvent });
  const paramsToUpdate = { ...params, communities: [community._id] };

  if (
    Array.isArray(paramsToUpdate.coverMediaItems) &&
    paramsToUpdate.coverMediaItems.length
  ) {
    const coverMediaItemsUpdateData =
      await getUpdateDataForMultipleCoverMediaItems({
        communityId: community._id,
        entityType: COVER_MEDIA_ENTITY_TYPES.EVENT,
        coverMediaItems: paramsToUpdate.coverMediaItems,
      });

    Object.assign(paramsToUpdate, coverMediaItemsUpdateData);
  }

  if (options.formatPricing) {
    const pricing = await pricingService.validateAndFormatPricing(
      paramsToUpdate,
      true,
      existingEvent,
      community
    );

    if (pricing) {
      paramsToUpdate.amount = pricing.amount;
      paramsToUpdate.currency = pricing.currency;
      paramsToUpdate.pricingConfig = pricing.pricingConfig;
      paramsToUpdate.access = pricing.access;
    }
  }

  if (paramsToUpdate.startTime) {
    const timeBeforeStartTime = await getTimeBeforeStartTime(
      paramsToUpdate.startTime
    );
    paramsToUpdate.timeBeforeStartTime = timeBeforeStartTime;
  }

  return paramsToUpdate;
};
