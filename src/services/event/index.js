const mongoose = require('mongoose');

const { ObjectId } = mongoose.Types;
const { Readable } = require('stream');
const { DateTime } = require('luxon');
const PrimaryMongooseConnection = require('../../rpc/primaryMongooseConnection');
const CommunityEventsModel = require('../../communitiesAPI/models/communityEvents.model');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const CommunityDiscountModel = require('../../communitiesAPI/models/communityDiscounts.model');
const EventAttendeesModel = require('../../communitiesAPI/models/eventAttendees.model');
const MembershipModel = require('../../models/membership/membership.model');
const LearnerModel = require('../../models/learners.model');
const AddonTransactionsModel = require('../../communitiesAPI/models/communityAddonTransactions.model');
const CommunityRoleModel = require('../../communitiesAPI/models/communityRole.model');
const { aclRoles } = require('../../communitiesAPI/constants');
const eventCommonService = require('./common.service');
const RegexUtils = require('../../utils/regex.util');
const MongodbUtils = require('../../utils/mongodb.util');
const EventUtils = require('../../utils/event.util');
const logger = require('../logger.service');
const {
  CURRENCY_WITH_NON_DECIMAL_POINTS,
  CONFIG_TYPES,
  POST_APPROVAL_PROCESS_TYPE,
  ADDON_ACTION_EVENT_TYPES,
  PURCHASE_TYPE,
  EVENT_TYPES,
  PENDING_TRANSACTION_ENTITY_TYPE,
  MOBILE_NOTIFICATION_TYPES,
  MOBILE_NOTIFICATION_RECIPIENTS_SCOPES,
} = require('../../constants/common');
const {
  COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES,
  CENTS_PER_DOLLAR,
  COMMUNITY_ONE_TIME_PAYMENT_ENTITIES,
  COMMUNITY_EVENT_QR_CODE_TICKET_V1,
} = require('../../communitiesAPI/constants');
const ActionEventService = require('../actionEvent');
const { EVENT_MAIL_TYPES } = require('../mail/constants');
const { getConfigByTypeFromCache } = require('../config.service');
const {
  ResourceNotFoundError,
  ParamError,
  AlreadyProcessedError,
  ForbiddenError,
} = require('../../utils/error.util');
const paymentService = require('../payment');
const revenueTransactionService = require('../revenueTransaction');
const formatVariableDataService = require('../mail/formatVariableData.service');
const PendingTransactionService = require('../communitySignup/common/pendingTransaction.service');
const receiptService = require('../receipts/receipts.service');
const {
  sendMobileNotification,
} = require('../notification/mobileNotifications.service');
const { NAS_IO_FRONTEND_URL } = require('../../config');

async function retrieveEmailsViaSearch(search, communityObjectId) {
  const config = await getConfigByTypeFromCache(
    CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
  );

  const maxGram = config?.envVarData?.MEMBERSHIP_SEARCH_MAX_GRAM || 15;

  let path;
  if (search.length > maxGram) {
    path = [{ value: 'email', multi: 'keywordAnalyzer' }, 'name'];
  } else {
    path = ['name', 'email'];
  }

  const memberships = await MembershipModel.aggregate([
    {
      $search: {
        index: 'membershipIndex',
        compound: {
          must: [
            {
              regex: {
                query: search,
                path,
                allowAnalyzedField: true,
              },
            },
          ],
          filter: [
            {
              equals: {
                path: 'communityObjectId',
                value: new ObjectId(communityObjectId),
              },
            },
          ],
        },
      },
    },
    {
      $project: {
        _id: 0,
        email: 1,
      },
    },
  ]);

  return memberships.map(({ email }) => email);
}

exports.generateEventAttendeesFilter = async ({
  search = '',
  status = ['ALL'],
  eventId,
  communityId,
  isCheckedIn,
  registrationDateFrom,
  registrationDateTo,
  startObjectId = null,
  endObjectId = null,
}) => {
  const filter = {
    eventObjectId: new ObjectId(eventId),
  };

  if (startObjectId && endObjectId) {
    filter._id = {
      $gte: MongodbUtils.toObjectId(startObjectId),
      $lte: MongodbUtils.toObjectId(endObjectId),
    };
  }

  const joinedStatus = status.join();

  if (joinedStatus !== 'ALL' && joinedStatus !== '') {
    filter.status = { $in: status };
  } else {
    const statusWithoutRemoved = Object.values(
      COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES
    ).filter((value) => value !== 'REMOVED');
    // Ignore all removed attendees
    filter.status = { $in: statusWithoutRemoved };
  }

  const searchWithEscapedRegexSign = RegexUtils.escapeRegExp(search);

  if (searchWithEscapedRegexSign !== '') {
    const emailsRelatedToSearchResult = await retrieveEmailsViaSearch(
      searchWithEscapedRegexSign,
      communityId
    );

    filter.$or = [];

    filter.$or.push({
      email: { $in: emailsRelatedToSearchResult },
    });

    const regexPattern = new RegExp(searchWithEscapedRegexSign, 'i');

    filter.$or.push({
      ticketReference: regexPattern,
    });

    filter.$or.push({
      'ticketReferences.ticketReference': regexPattern,
    });
  }

  const isCheckedIns =
    isCheckedIn?.map((checkIn) => JSON.parse(checkIn)) ?? [];

  if (
    isCheckedIn != null &&
    isCheckedIn !== '' &&
    isCheckedIns.length > 0
  ) {
    filter.$and = [
      {
        'ticketReferences.isCheckedIn': {
          $in: isCheckedIns,
        },
      },
      { status: COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING },
    ];
  }

  if (registrationDateFrom) {
    if (!filter.createdAt) {
      filter.createdAt = {};
    }

    filter.createdAt.$gte = new Date(registrationDateFrom);
  }

  if (registrationDateTo) {
    if (!filter.createdAt) {
      filter.createdAt = {};
    }

    filter.createdAt.$lte = new Date(registrationDateTo);
  }

  logger.info(`generateEventAttendeesFilter: ${JSON.stringify(filter)}`);

  return filter;
};

function generateEventAttendeesPipeline({
  filter,
  pageSize,
  pageNo,
  sortBy,
  sortOrder,
  projection,
}) {
  const pipelineQuery = [
    {
      $match: filter,
    },
    {
      $sort: { [sortBy]: sortOrder },
    },
    {
      $skip: (pageNo - 1) * pageSize,
    },
    {
      $limit: pageSize,
    },
    ...MongodbUtils.lookupAndUnwind(
      'learners',
      'learnerObjectId',
      '_id',
      'learner'
    ),
    ...MongodbUtils.lookupAndUnwind(
      'community_addon_transactions',
      'eventCheckoutId',
      '_id',
      'transaction'
    ),
    {
      $project: projection,
    },
  ];

  logger.info(
    `generateEventAttendeesPipeline: pipeline query: ${JSON.stringify(
      pipelineQuery
    )}`
  );

  return pipelineQuery;
}

function generateTicketsCountPipeline({ filter }) {
  const pipelineQuery = [
    {
      $match: filter,
    },
    {
      $project: {
        quantity: {
          $ifNull: ['$quantity', 1],
        },
      },
    },
    {
      $group: {
        _id: null,
        total: {
          $sum: '$quantity',
        },
      },
    },
  ];

  logger.info(
    `generateTicketsCountPipeline: pipeline query: ${JSON.stringify(
      pipelineQuery
    )}`
  );

  return pipelineQuery;
}

exports.retrieveEventAndAttendees = async ({
  search,
  pageSize,
  pageNo,
  sortBy,
  sortOrder,
  status,
  communityId,
  eventId,
  isCheckedIn,
  registrationDateFrom,
  registrationDateTo,
}) => {
  const filter = await this.generateEventAttendeesFilter({
    search,
    status,
    communityId,
    eventId,
    isCheckedIn,
    registrationDateFrom,
    registrationDateTo,
  });

  const projection = {
    name: {
      $concat: ['$learner.firstName', ' ', '$learner.lastName'],
    },
    profileImage: '$learner.profileImage',
    email: '$learner.email',
    status: 1,
    createdAt: 1,
    ticketReference: 1,
    ticketReferences: 1,
    applicationInfo: 1,
    quantity: 1,
  };

  const pipelineQuery = generateEventAttendeesPipeline({
    filter,
    pageSize,
    pageNo,
    sortBy,
    sortOrder,
    projection,
  });

  const ticketsCountPipeline = generateTicketsCountPipeline({ filter });

  const [attendees, totalAttendees, totalTickets] = await Promise.all([
    EventAttendeesModel.aggregate(pipelineQuery),
    EventAttendeesModel.countDocuments(filter),
    EventAttendeesModel.aggregate(ticketsCountPipeline),
  ]);

  const metadata = {
    totalTickets: totalTickets[0]?.total ?? 0,
    total: totalAttendees,
    limit: pageSize,
    page: pageNo,
    pages: Math.ceil(totalAttendees / pageSize),
  };

  return {
    attendees,
    metadata,
  };
};

async function retrieveAddonTransaction(eventAttendee, addonTransaction) {
  if (addonTransaction) {
    return addonTransaction;
  }

  if (eventAttendee.eventCheckoutId) {
    const addonTransactionFromEventAttendee =
      await AddonTransactionsModel.findById(
        eventAttendee.eventCheckoutId
      ).lean();

    return addonTransactionFromEventAttendee;
  }

  return null;
}

async function retrieveLearner(learnerObjectId) {
  const learner = await LearnerModel.findById(learnerObjectId).lean();
  return learner;
}

async function retrieveCommunity(communityObjectId) {
  const community = await CommunityModel.findById(
    communityObjectId
  ).lean();
  return community;
}

exports.updateEventAttendeeStatus = async ({
  eventId,
  attendeeId,
  learnerObjectId,
  email,
  isManager = false,
  status = COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING,
  session = undefined,
  applicationInfo = [],
  requiresApproval = false,
  addonTransaction = null,
  fromApproval = false,
  isInPersonEvent = false,
  isFromCops = false,
}) => {
  const eventAttendee = await EventAttendeesModel.findById(
    attendeeId
  ).lean();

  if (!eventAttendee) {
    throw new ResourceNotFoundError(
      `Event attendee for ${attendeeId} not found`
    );
  }

  if (eventAttendee.status === status) {
    throw new AlreadyProcessedError(
      `Event attendee for ${attendeeId} status is already ${status}`
    );
  }

  if (eventAttendee.eventObjectId.toString() !== eventId.toString()) {
    throw new ParamError(
      `Event attendee for ${attendeeId} does not belong to eventId: ${eventId}`
    );
  }

  if (
    !isManager &&
    !isFromCops &&
    eventAttendee.learnerObjectId.toString() !== learnerObjectId.toString()
  ) {
    throw new ParamError(
      `Event attendee for ${attendeeId} does not belong to learnerObjectId: ${learnerObjectId}`
    );
  }

  const eventAttendeeEmail = eventAttendee.email;

  const newStatusHistory = {
    status: eventAttendee.status,
    timestamp:
      eventAttendee.lastModifiedTimeStamp ?? eventAttendee.updatedAt,
    updatedByEmail: email,
    updatedByLearnerObjectId: learnerObjectId,
  };

  const currentDate = new Date();

  const updateFilter = {
    status,
  };

  // For new register but exists event attendee (not going or rejected)
  if (addonTransaction) {
    const {
      amount,
      currency,
      local_amount: localAmount,
      local_currency: localCurrency,
      _id: addonTransactionObjectId,
      quantity = 1,
      communityObjectId,
    } = addonTransaction;

    const multipleTicketReferenceInfo =
      await EventUtils.generateTicketReferences({
        eventObjectId: eventId,
        communityObjectId,
        learnerObjectId,
        quantity,
        shouldCreateQrCodeTicket: isInPersonEvent,
      });

    const mainTicketReference =
      multipleTicketReferenceInfo[0].ticketReference;

    updateFilter.amount = amount;
    updateFilter.currency = currency;
    updateFilter.local_amount = localAmount;
    updateFilter.local_currency = localCurrency;
    updateFilter.eventCheckoutId = addonTransactionObjectId;
    updateFilter.approvalReviewDate = null;
    updateFilter.ticketReference = mainTicketReference;
    updateFilter.ticketReferences = multipleTicketReferenceInfo;
    updateFilter.quantity = quantity;
  }

  if (applicationInfo.length > 0) {
    updateFilter.applicationInfo = applicationInfo;
  }

  if (requiresApproval) {
    updateFilter.status = COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING;
    updateFilter.approvalReviewDate = null;
  }

  // From approval api
  if (fromApproval) {
    updateFilter.approvalReviewDate = currentDate;

    const eventData = await CommunityEventsModel.findById(
      eventId,
      'type communities'
    ).lean();

    if (!eventData) {
      throw new ResourceNotFoundError(`Event for ${eventId} not found`);
    }

    const communityObjectId = eventData.communities[0];

    // if fromApproval flow & status is going & location is inPerson,
    // then check if eventAttendee has qrCodeSrc. if yes, nothing to do.
    // if no, then generate qrCodeSrc and update eventAttendee with qrCodeSrc.

    if (status === COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING) {
      const { ticketReferences } = eventAttendee;
      const isQrCodeMissingInAnyTicRef = ticketReferences.some(
        (ticketReference) => !ticketReference.qrCodeSrc
      );

      if (isQrCodeMissingInAnyTicRef) {
        if (eventData.type === EVENT_TYPES.INPERSON) {
          const updatedTicketReferences =
            await EventUtils.addQrCodeToTicketReferences({
              ticketReferences,
              eventObjectId: eventId,
              communityObjectId,
              learnerObjectId,
            });

          updateFilter.ticketReferences = updatedTicketReferences;
        }
      }
    }

    if (status === COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.REJECTED) {
      await PendingTransactionService.removePendingTransaction({
        entityObjectId: eventId,
        communityObjectId,
        email: eventAttendeeEmail,
        entityType: PENDING_TRANSACTION_ENTITY_TYPE.EVENT,
      });
    }
  }

  const updatedAttendee = await EventAttendeesModel.findOneAndUpdate(
    {
      _id: attendeeId,
      eventObjectId: eventId,
      status: { $ne: status },
    },
    {
      $set: updateFilter,
      $push: {
        statusHistory: newStatusHistory,
      },
    },
    { new: true, session }
  ).lean();

  if (!updatedAttendee) {
    throw new ResourceNotFoundError(
      `Event attendee for ${attendeeId} not found`
    );
  }

  logger.info(
    `updateEventAttendeeStatus: updated event: ${JSON.stringify(
      updatedAttendee
    )}`
  );

  if (status === COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.NOT_GOING) {
    const [eventAttendeeAddonTransaction, learner, community] =
      await Promise.all([
        retrieveAddonTransaction(eventAttendee, addonTransaction),
        retrieveLearner(learnerObjectId),
        retrieveCommunity(eventAttendee.communityObjectId),
      ]);

    await ActionEventService.sendAddonActionEvent({
      actionEventType: ADDON_ACTION_EVENT_TYPES.EVENT_ACCESS_REMOVED,
      actionEventCreatedAt: updatedAttendee.lastModifiedTimeStamp,
      addonTransaction: eventAttendeeAddonTransaction,
      learner,
      entityObjectId: eventAttendee.eventObjectId,
      entityType: PURCHASE_TYPE.EVENT,
      community,
      additionalInfo: {
        removalReason: 'Member not going to the event',
      },
    });
  }

  return updatedAttendee;
};

function getAmountInDollarOrCents(amount, currency) {
  if (amount == null || amount === 0) return amount;

  const canDivideBy100 =
    !CURRENCY_WITH_NON_DECIMAL_POINTS.includes(currency);

  return canDivideBy100 ? amount / CENTS_PER_DOLLAR : amount;
}

exports.retrieveCsvFilename = async (communityId, eventId) => {
  const [community, event] = await Promise.all([
    CommunityModel.findById(communityId, { code: 1 }).lean(),
    CommunityEventsModel.findById(eventId, { slug: 1 }).lean(),
  ]);

  if (!community || !event) {
    throw new ParamError('Invalid communityId or eventId');
  }

  const [currentDate, currentTime] = new Date().toISOString().split('T');
  const dateTime = `${currentDate.replace(/-/g, '')}_${currentTime
    .split('.')[0]
    .replace(/:/g, '')}`;

  return `${community.code}_event_${
    event.slug?.replace('/', '') ?? ''
  }_${dateTime}.csv`;
};

function convertDateTimeToLocalTimeZone(dateTime, timezone) {
  const luxonDateTime = DateTime.fromJSDate(dateTime);
  const localDateTime = luxonDateTime.setZone(timezone);
  const formattedDateTime = localDateTime.toFormat('yyyy-MM-dd HH:mm:ss');
  return formattedDateTime;
}

function retrieveStatusLabel(status) {
  switch (status) {
    case COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING:
      return 'Going';
    case COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.NOT_GOING:
      return 'Not going';
    case COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING:
      return 'Pending';
    case COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.REJECTED:
      return 'Rejected';
    case COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.REMOVED:
      return 'Removed';
    default:
      return '';
  }
}

async function retrieveAllEventQuestions(filter) {
  const allEventQuestions = await EventAttendeesModel.aggregate([
    {
      $match: {
        ...filter,
        applicationInfo: {
          $exists: true,
          $ne: [],
        },
      },
    },
    {
      $unwind: {
        path: '$applicationInfo',
        preserveNullAndEmptyArrays: false,
      },
    },
    {
      $group: {
        _id: null,
        applicationQuestions: {
          $addToSet: '$applicationInfo.label',
        },
      },
    },
  ]);

  const allQuestions = allEventQuestions?.[0]?.applicationQuestions ?? [];

  return allQuestions.map((question) => `"Qn: ${question}"`);
}

function retrieveCsvQuestionsAndAnswers(
  allEventQuestions,
  applicationInfo
) {
  if (
    !allEventQuestions ||
    allEventQuestions.length === 0 ||
    !applicationInfo ||
    applicationInfo.length === 0
  ) {
    return null;
  }

  const csvAnswers = allEventQuestions.map((question) => {
    const applicationAnswer = applicationInfo.find(
      (info) => question === `"Qn: ${info.label}"`
    );

    if (Array.isArray(applicationAnswer?.answer)) {
      applicationAnswer.answer = applicationAnswer.answer.join(', ');
    } else if (applicationAnswer?.answer?.label) {
      applicationAnswer.answer = applicationAnswer.answer.label;
    }

    return `"${applicationAnswer?.answer ?? ''}"`;
  });

  return csvAnswers;
}

exports.generateEventAttendeesCsvStream = async ({
  search,
  pageSize,
  sortBy,
  sortOrder,
  status,
  communityId,
  eventId,
  isCheckedIn,
  registrationDateFrom,
  registrationDateTo,
}) => {
  try {
    const [filter, event, community] = await Promise.all([
      this.generateEventAttendeesFilter({
        search,
        status,
        communityId,
        eventId,
        isCheckedIn,
        registrationDateFrom,
        registrationDateTo,
      }),
      CommunityEventsModel.findById(eventId, { title: 1 }).lean(),
      CommunityModel.findById(communityId, { timezone: 1 }).lean(),
    ]);

    const allEventQuestions = await retrieveAllEventQuestions(filter);

    if (!event || !community) {
      throw new ParamError('Invalid eventId or communityId');
    }

    const csvStream = new Readable({
      objectMode: true,
      read() {},
    });

    const joinedAllQuestions =
      allEventQuestions.length > 0
        ? `,${allEventQuestions.join(',')}`
        : '';

    csvStream.push(
      `Ticket ID,Event Name,First Name,Last Name,Member Email,Member Phone Number,Registration DateTime,Status,Last Updated DateTime, Purchase Type, Quantity,Paid Currency,Paid Amount,Discount Code,Discount Amount${joinedAllQuestions}\n`
    );

    let pageNo = 1;
    let hasNextPage = true;

    const projection = {
      firstName: '$learner.firstName',
      lastName: '$learner.lastName',
      email: '$learner.email',
      phoneNumber: '$learner.phoneNumber',
      status: 1,
      createdAt: 1,
      ticketReference: 1,
      lastModifiedTimeStamp: 1,
      updatedAt: 1,
      local_currency: 1,
      local_amount: 1,
      discountObjectId: '$transaction.discount',
      fullLocalAmount: '$transaction.full_local_amount',
      applicationInfo: 1,
      ticketReferences: 1,
      purchaseType: 1,
      quantity: 1,
    };

    const discountCache = new Map();

    const getNextPage = async () => {
      const pipelineQuery = generateEventAttendeesPipeline({
        filter,
        pageSize,
        pageNo,
        sortBy,
        sortOrder,
        projection,
      });

      const eventAttendees = await EventAttendeesModel.aggregate(
        pipelineQuery
      );

      const discountObjectIds = eventAttendees
        .filter(
          ({ discountObjectId }) =>
            discountObjectId != null &&
            !discountCache.has(discountObjectId)
        )
        .map(({ discountObjectId }) => new ObjectId(discountObjectId));

      const discounts = await CommunityDiscountModel.find(
        {
          _id: { $in: discountObjectIds },
        },
        { code: 1 }
      ).lean();

      discounts.forEach(({ _id: discountId, code }) => {
        discountCache.set(discountId.toString(), code);
      });

      if (!eventAttendees.length) {
        hasNextPage = false;
        csvStream.push(null);
        return;
      }
      eventAttendees.forEach(
        ({
          firstName,
          lastName,
          email,
          phoneNumber,
          status:
            eventAttendeeStatus = COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING,
          createdAt,
          ticketReference,
          lastModifiedTimeStamp,
          updatedAt,
          local_amount: localAmount,
          local_currency: localCurrency = '',
          discountObjectId = '',
          fullLocalAmount,
          applicationInfo,
          purchaseType,
          quantity = 1,
        }) => {
          const discountCode = discountCache.get(discountObjectId);

          const csvAnswers = retrieveCsvQuestionsAndAnswers(
            allEventQuestions,
            applicationInfo
          );

          let joinedCsvAnswer = '';

          if (csvAnswers) {
            joinedCsvAnswer = `,${csvAnswers.join(',')}`;
          }

          const csvRow = `${ticketReference},${event.title},${
            firstName ?? ''
          },${lastName ?? ''},${email ?? ''},${
            phoneNumber ?? ''
          },${convertDateTimeToLocalTimeZone(
            createdAt,
            community.timezone
          )},${retrieveStatusLabel(
            eventAttendeeStatus
          )},${convertDateTimeToLocalTimeZone(
            lastModifiedTimeStamp ?? updatedAt,
            community.timezone
          )},${purchaseType},${quantity},${localCurrency},${
            getAmountInDollarOrCents(localAmount, localCurrency) ?? ''
          },${discountCode ?? ''},${
            fullLocalAmount != null ? fullLocalAmount - localAmount : ''
          }${joinedCsvAnswer}\n`;

          csvStream.push(csvRow);
        }
      );

      pageNo += 1;
      hasNextPage = eventAttendees.length === pageSize;
    };

    csvStream._read = () => {
      if (hasNextPage) {
        getNextPage();
      } else {
        csvStream.push(null);
      }
    };

    return csvStream;
  } catch (err) {
    logger.error(`generateEventAttendeesCsvStream: ${err}`);
    throw new Error('Error generating event attendees CSV stream');
  }
};

async function updateEventAttendeeWithEmptyTicketReferences(
  attendeeId,
  ticketReferences,
  checkIn,
  updatedByLearnerObjectId,
  isViaQrCode = false
) {
  if (ticketReferences.length !== 1) {
    throw new ParamError(
      `Only accept 1 ticket reference: ${ticketReferences}`
    );
  }

  const ticketReference = {
    ticketReference: ticketReferences[0],
    isCheckedIn: checkIn,
    updatedByLearnerObjectId,
    updatedAt: new Date(),
  };

  if (isViaQrCode) {
    ticketReference.isViaQrCode = isViaQrCode;
  }

  const updatedAttendee = await EventAttendeesModel.findByIdAndUpdate(
    attendeeId,
    {
      $push: {
        ticketReferences: ticketReference,
      },
    },
    {
      new: true,
    }
  ).lean();

  return updatedAttendee;
}

exports.checkInTicketReferences = async ({
  attendeeId,
  ticketReferences,
  checkIn,
  updatedByLearnerObjectId,
  checkInAllTickets = false,
  isViaQrCode = false,
}) => {
  if (!checkInAllTickets && ticketReferences.length === 0) {
    throw new ParamError(
      'Cannot accept empty array for ticket references'
    );
  }

  const eventAttendee = await EventAttendeesModel.findById(attendeeId, {
    ticketReference: 1,
    ticketReferences: 1,
    status: 1,
  }).lean();

  if (!eventAttendee) {
    throw new ParamError(`Invalid attendee id for ${attendeeId}`);
  }

  if (
    eventAttendee.status !== COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING
  ) {
    throw new ParamError(
      `${eventAttendee.status} status is not allowed to check in`
    );
  }

  // Handle legacy data where ticketReferences does not existed
  if (
    !eventAttendee.ticketReferences ||
    eventAttendee.ticketReferences.length === 0
  ) {
    const finalTicketReferences =
      ticketReferences.length === 0
        ? [eventAttendee.ticketReference]
        : ticketReferences;

    const updatedAttendee =
      await updateEventAttendeeWithEmptyTicketReferences(
        attendeeId,
        finalTicketReferences,
        checkIn,
        updatedByLearnerObjectId,
        isViaQrCode
      );

    return updatedAttendee;
  }

  const updateFields = {
    'ticketReferences.$[element].isCheckedIn': checkIn,
    'ticketReferences.$[element].updatedByLearnerObjectId':
      updatedByLearnerObjectId,
    'ticketReferences.$[element].updatedAt': new Date(),
  };

  if (isViaQrCode) {
    updateFields['ticketReferences.$[element].isViaQrCode'] = isViaQrCode;
  }

  const arrayFilters = checkInAllTickets
    ? [{ 'element.isCheckedIn': { $ne: checkIn } }]
    : [{ 'element.ticketReference': { $in: ticketReferences } }];

  const updatedAttendee = await EventAttendeesModel.findByIdAndUpdate(
    attendeeId,
    {
      $set: updateFields,
    },
    {
      arrayFilters,
      new: true,
    }
  ).lean();

  return updatedAttendee;
};

exports.sendEventEmail = async ({
  event,
  eventAttendeeObjectId,
  addonTransactionObjectId,
  learnerObjectId,
  eventAttendeePurchaseType,
  ticketReference,
  ticketReferences,
  mailType = EVENT_MAIL_TYPES.MEMBER_COMMUNITY_EVENT_RSVP,
  session = null,
  quantity = 1,
  config = {},
}) => {
  const [learner, community] = await Promise.all([
    LearnerModel.findById(learnerObjectId).lean(),
    CommunityModel.findById(event?.communities?.[0]).lean(),
  ]);

  let addonTransactionData;

  // TODO - @AmanMinhas: Potential Legacy code. Verify if addonTransactionObjectId is required.
  // Money info is not sent with ticket anymore in new design. Verify with PM.
  if (addonTransactionObjectId) {
    addonTransactionData = await AddonTransactionsModel.findById(
      addonTransactionObjectId
    )
      .session(session)
      .setOptions({ readPreference: 'primary' })
      .lean();
  }

  try {
    const additionalFields =
      formatVariableDataService.getMemberEventRsvpAdditionalFields({
        addonTransactionData,
        eventAttendeeObjectId,
        eventAttendeePurchaseType,
        ticketReference,
        ticketReferences,
        quantity,
      });

    await eventCommonService.formatAndSendEventTypeEmail({
      mailType,
      learner,
      learnerObjectId,
      event,
      community,
      additionalFields,
      config,
    });
  } catch (err) {
    logger.error('Error sending event email to member: ', err);
    throw err;
  }
};

exports.approveOrRejectEventAttendee = async ({
  eventId,
  attendeeId,
  status,
  learnerObjectId,
  email,
  isManager,
  sendApprovalEmail = true,
}) => {
  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    const [updatedAttendee, event] = await Promise.all([
      this.updateEventAttendeeStatus({
        eventId,
        attendeeId,
        learnerObjectId,
        email,
        isManager,
        status,
        fromApproval: true,
        session,
      }),
      CommunityEventsModel.findById(eventId).lean(),
    ]);

    if (!event) {
      throw new ParamError(`Invalid event id ${eventId}`);
    }

    let mailType = EVENT_MAIL_TYPES.MEMBER_COMMUNITY_EVENT_APPROVED;

    if (status === COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.REJECTED) {
      if (updatedAttendee.amount > 0) {
        await paymentService.RefundService.refundAddon({
          type: POST_APPROVAL_PROCESS_TYPE.EVENT,
          addonTransactionObjectId: updatedAttendee.eventCheckoutId,
        });
      }

      mailType = EVENT_MAIL_TYPES.MEMBER_COMMUNITY_EVENT_REJECTED;
    }

    const learner = await LearnerModel.findById(
      updatedAttendee.learnerObjectId
    ).lean();

    if (
      updatedAttendee.status ===
      COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING
    ) {
      const isFree =
        updatedAttendee.amount === 0 || !updatedAttendee.eventCheckoutId;

      if (isFree) {
        await ActionEventService.sendFreeAddonActionEvent({
          actionEventType: ADDON_ACTION_EVENT_TYPES.EVENT_SIGNUP,
          entityObjectId: event._id,
          entityCollection: COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.EVENT,
          communityObjectId: event.communities[0],
          learner,
          quantity: updatedAttendee.quantity,
        });
      } else {
        await revenueTransactionService.sendRawTransaction(
          updatedAttendee.eventCheckoutId,
          PURCHASE_TYPE.EVENT
        );
      }
    }

    await session.commitTransaction();

    const generateReceipt =
      updatedAttendee.status ===
        COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING &&
      updatedAttendee.amount > 0;

    const config = receiptService.generateReceiptConfig({
      purchasedId: updatedAttendee.eventCheckoutId,
      purchaseType: PURCHASE_TYPE.EVENT,
      entityObjectId: event._id,
      communityObjectId: event.communities[0],
      learnerObjectId: updatedAttendee.learnerObjectId,
      generateReceipt,
    });

    try {
      if (sendApprovalEmail) {
        await this.sendEventEmail({
          event,
          eventAttendeeObjectId: updatedAttendee._id,
          addonTransactionObjectId: updatedAttendee.eventCheckoutId,
          learnerObjectId: updatedAttendee.learnerObjectId,
          eventAttendeePurchaseType: updatedAttendee.purchaseType,
          ticketReference: updatedAttendee.ticketReference,
          ticketReferences: updatedAttendee.ticketReferences,
          mailType,
          quantity: updatedAttendee.quantity,
          config,
        });
      }
    } catch (err) {
      logger.error(`approveOrRejectEventAttendee: ${err.message}`);
    }

    const existsPendingAttendee = await EventAttendeesModel.findOne({
      eventObjectId: eventId,
      status: COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING,
    })
      .read('primary')
      .lean();

    const requiresApprovalEmail =
      !!existsPendingAttendee || (event.requiresApproval ?? false);
    updatedAttendee.requiresApprovalEmail = requiresApprovalEmail;

    return updatedAttendee;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};

/**
 * Checkin attendee by scanning QR Code
 * @param {Object} params - Input params
 * @param {String} params.eventObjectId - Event ObjectId
 * @param {String} params.communityObjectId - Community ObjectId
 * @param {String} params.scannedToken - Scanned QR Code token
 * @returns {Promise<EventAttendee>} - Updated event attendee data
 */
exports.scanQrToCheckinAttendee = async (params) => {
  const {
    eventObjectId,
    communityObjectId,
    scannedToken,
    updatedByLearnerObjectId, // learnerObjectId is the user (BC) who is performing checking in
  } = params;

  // decode scannedToken to extract data from scannedQrCodeInfo
  const scannedQrCodeInfo = EventUtils.decodeQrCodeStr(scannedToken);

  // validate
  if (
    !scannedQrCodeInfo.version ||
    eventObjectId !== scannedQrCodeInfo.eventObjectId ||
    communityObjectId !== scannedQrCodeInfo.communityObjectId
  ) {
    logger.error(
      `[scanQrToCheckinAttendee] Invalid scanned token for event QR Code: ${scannedToken}`
    );
    throw new ParamError('Invalid ticket.');
  }

  switch (scannedQrCodeInfo.version) {
    case COMMUNITY_EVENT_QR_CODE_TICKET_V1: {
      const { ticketReference, learnerObjectId: attendeeLearnerObjectId } =
        scannedQrCodeInfo;

      // get event attendee id
      const eventAttendeeFilter = {
        eventObjectId,
        'ticketReferences.ticketReference': ticketReference,
        communityObjectId,
        status: COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING,
      };

      const eventAttendee = await EventAttendeesModel.findOne(
        eventAttendeeFilter
      ).lean();

      if (!eventAttendee) {
        throw new ResourceNotFoundError(
          `Event attendee for ${attendeeLearnerObjectId} not found`
        );
      }

      const learner = await LearnerModel.findById(
        eventAttendee.learnerObjectId
      )
        .select('_id email profileImage firstName lastName')
        .lean();

      const currentTicketReference = eventAttendee.ticketReferences?.find(
        (ticketReferenceData) => {
          return ticketReferenceData.ticketReference === ticketReference;
        }
      );

      const alreadyCheckedIn =
        currentTicketReference?.isCheckedIn ?? false;

      if (alreadyCheckedIn) {
        return {
          ...eventAttendee,
          learnerData: learner,
          alreadyCheckedIn,
        };
      }

      // check-in
      const updatedAttendee = await this.checkInTicketReferences({
        attendeeId: eventAttendee._id,
        ticketReferences: [ticketReference],
        checkIn: true,
        updatedByLearnerObjectId,
        isViaQrCode: true,
      });

      // return updatedAttendee and learner;
      return {
        ...updatedAttendee,
        learnerData: learner,
        alreadyCheckedIn,
      };
    }
    default:
      logger.error(
        `[scanQrToCheckinAttendee] Invalid QR Code version: ${scannedToken}`
      );
      throw new ParamError('Invalid ticket.');
  }
};

/**
 * Get single event attendee
 * @param {String} eventId - Event ObjectId
 * @param {String} attendeeId - Event attendee ObjectId
 * @param {String} requestingLearnerObjectId - Learner ObjectId of the user requesting the data
 * @returns {Promise<Object>} - Event attendee data
 */
exports.getSingleEventAttendee = async ({
  eventId,
  attendeeId,
  requestingLearnerObjectId,
}) => {
  // get event and event attendee by id using CommunityEventsModel & EventAttendeesModel (parallelly)
  const [eventData, eventAttendeeData] = await Promise.all([
    CommunityEventsModel.findById(eventId, {
      _id: 1,
      type: 1,
      communities: 1,
    }).lean(),
    EventAttendeesModel.findById(attendeeId).lean(),
  ]);

  // validations
  if (!eventData) {
    throw new ResourceNotFoundError('Event not found');
  }

  if (!eventAttendeeData) {
    throw new ResourceNotFoundError('Event attendee not found');
  }

  const { learnerObjectId } = eventAttendeeData;

  // if attendeeLeanerObjectId is not the same as requestingLearnerObjectId, throw error.
  if (learnerObjectId.toString() !== requestingLearnerObjectId) {
    throw new ForbiddenError('User is not authorized to access!');
  }

  // if event is not inPerson, return eventAttendeeData. OR if eventAttendeeData status is not going, return eventAttendeeData.
  if (
    eventData.type !== EVENT_TYPES.INPERSON ||
    eventAttendeeData.status !==
      COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING
  ) {
    return eventAttendeeData;
  }
  // if event is inPerson & status is going, validate qrCodeSrc is available for every ticket reference.
  const { ticketReferences } = eventAttendeeData;
  const isQrCodeMissingInAnyTicRef = ticketReferences.some(
    (ticketReference) => !ticketReference.qrCodeSrc
  );

  // if available, return eventAttendeeData.
  if (!isQrCodeMissingInAnyTicRef) {
    return eventAttendeeData;
  }

  // if unavailable, generate qrCodeSrc for ticket references where it is missing.
  const { _id: eventObjectId, communities } = eventData;
  const communityObjectId = communities[0];
  const updatedTicketReferences =
    await EventUtils.addQrCodeToTicketReferences({
      ticketReferences,
      eventObjectId,
      communityObjectId,
      learnerObjectId: requestingLearnerObjectId,
    });

  // update eventAttendeeData and return
  const updatedEventAttendeeData =
    await EventAttendeesModel.findByIdAndUpdate(
      attendeeId,
      {
        ticketReferences: updatedTicketReferences,
      },
      { new: true }
    ).lean();

  return updatedEventAttendeeData;
};

/**
 * Send mobile notification to community admins about pending event attendee
 * @param {Object} params - Input params
 * @param {String} params.eventId - Event ObjectId
 * @param {Object} params.eventAttendee - Event attendee data
 * @returns {Promise<void>}
 */
exports.sendPendingEventAttendeeMobileNotification = async ({
  event,
  eventAttendee,
}) => {
  try {
    const eventId = event._id;
    const communityId = event?.communities?.[0];
    const community = await CommunityModel.findById(communityId, {
      code: 1,
      title: 1,
    }).lean();

    if (!event || !community) {
      logger.error(
        `Invalid event or community for notification: eventId=${eventId}, communityId=${communityId}`
      );
      return;
    }

    // Get admin users for this community
    const communityRoles = await CommunityRoleModel.aggregate([
      {
        $match: {
          communityCode: community.code,
          role: aclRoles.ADMIN,
        },
      },
      ...MongodbUtils.lookupAndUnwind(
        'users',
        'userObjectId',
        '_id',
        'user'
      ),
      {
        $project: {
          userId: '$user.user_id',
        },
      },
    ]);

    const adminUserIds = communityRoles
      .map((admin) => admin.userId)
      .filter(Boolean);

    if (adminUserIds.length === 0) {
      logger.info(
        `No admin userIds found for community: ${community.code}`
      );
      return;
    }

    // Get learner info for better notification message
    const learner = await LearnerModel.findById(
      eventAttendee.learnerObjectId,
      { firstName: 1, lastName: 1, email: 1 }
    ).lean();

    // learner name is firstName lastName if avaialble or email if not.
    const learnerName =
      learner.firstName || learner.lastName
        ? [learner.firstName, learner.lastName].join(' ')
        : learner.email;

    const eventTitle = event.title;

    // Send notification to all admins
    await sendMobileNotification(
      MOBILE_NOTIFICATION_TYPES.NEW_EVENT_APPLICATION,
      adminUserIds,
      MOBILE_NOTIFICATION_RECIPIENTS_SCOPES.GLOBAL,
      {
        title: `New application for ${eventTitle}`,
        body: `${learnerName} has requested to join your event. Review now!`,
        communityId,
        eventId,
        attendeeId: eventAttendee._id,
        eventTitle,
        learnerName,
        link: `${NAS_IO_FRONTEND_URL}/mb/communities/${communityId}/events/${eventId}`,
      }
    );

    logger.info(
      `Sent mobile notifications to ${adminUserIds.length} community admins for new event attendee in ${community.code}.`
    );
  } catch (error) {
    logger.error(
      `Error sending event attendee approval notification: ${error.message}`,
      error
    );
  }
};

exports.sendRsvpEmailToAttendee = async ({ attendeeId }) => {
  const eventAttendee = await EventAttendeesModel.findById(
    attendeeId
  ).lean();

  if (!eventAttendee) {
    throw new ResourceNotFoundError(
      `Event attendee for ${attendeeId} not found`
    );
  }

  if (
    eventAttendee.status !== COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING
  ) {
    throw new ParamError(
      `Event attendee for ${attendeeId} is not in going status`
    );
  }

  const eventId = eventAttendee.eventObjectId;
  const event = await CommunityEventsModel.findById(eventId).lean();

  if (!event) {
    throw new ResourceNotFoundError(`Event for ${eventId} not found`);
  }

  if (event.endTime < new Date()) {
    throw new ParamError('Event has already ended');
  }

  await this.sendEventEmail({
    event,
    eventAttendeeObjectId: eventAttendee._id,
    learnerObjectId: eventAttendee.learnerObjectId,
    eventAttendeePurchaseType: eventAttendee.purchaseType,
    ticketReference: eventAttendee.ticketReference,
    ticketReferences: eventAttendee.ticketReferences,
    mailType: EVENT_MAIL_TYPES.MEMBER_COMMUNITY_EVENT_RSVP,
    quantity: eventAttendee.quantity,
  });
};
