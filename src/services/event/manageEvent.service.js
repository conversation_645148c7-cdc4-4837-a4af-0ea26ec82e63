const PublishProductUsageService = require('@services/featurePermissions/publishProductUsage.service');
const EventModel = require('../../communitiesAPI/models/communityEvents.model');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const actionEventService = require('../actionEvent');
const communityHostService = require('../../communitiesAPI/services/web/communityHost.service');
const eventCommonService = require('./common.service');
const eventDiscordService = require('./discord.service');
const eventIcsService = require('./ics.service');

const { affiliateProductService } = require('../affiliate');
const {
  removeEmailReminderForAbandonedCheckout,
} = require('../abandonedCarts/abandonedCarts.service');

const {
  discountValidationForEntities,
  discountCreationForEntities,
} = require('../../communitiesAPI/services/common/communityDiscounts.service');
const {
  PURCHASE_TYPE,
  MILESTONE_ACTIVITY_TYPES,
} = require('../../constants/common');
const { EVENT_STATUS } = require('../../communitiesAPI/constants');
const {
  EVENT_MAIL_TYPES,
  MAIL_RECORD_TYPE,
} = require('../mail/constants');
const {
  InternalError,
  ParamError,
  ResourceNotFoundError,
} = require('../../utils/error.util');
const slugUtils = require('../../utils/slug.util');
const { withTransaction } = require('../../utils/mongodb.util');
const SyncProductDataService = require('../product/syncProductData.service');
const ProductChangeLogService = require('../product/productChangeLog.service');

const {
  generateDuplicateEvents,
  sendMaxLoopCountReachedAlert,
} = require('../../utils/events/generateDuplicateEvents.util');
const MailContentModel = require('../../models/notificationBackend/mailContent.model');
const {
  hasVideoCoverMediaItems,
} = require('../../utils/multipleCoverMediaItems.util');
const {
  verifyVideoCoverMediaItems,
  duplicateCoverMediaItems,
} = require('../coverMediaItems/common.service');
const PrimaryMongooseConnection = require('../../rpc/primaryMongooseConnection');
const {
  PRODUCT_TYPE,
  PRODUCT_CHANGE_LOG_TYPE,
} = require('../product/constants');
const {
  purgeEntityLandingPageCache,
  ENTITY_LANDING_PAGE,
} = require('../../utils/memberPortalLinks.utils');
const AiCofounderProductCreationService = require('../featurePermissions/aiCofounderProductCreation.service');

const eventCommunityValidation = (event, community) => {
  if (!event || !community) {
    throw new ParamError('Event or Community does not exists');
  }
  const communities = event.communities.filter((eventCommmunity) => {
    return eventCommmunity.toString() === community._id.toString();
  });

  if (!communities.length) {
    throw new ParamError('Event does not belong to specified Community');
  }
};

const executeFirstPublishTriggers = async (community, event) => {
  const eventIcs = await eventIcsService.fetchIcsFileUrl(event);
  const finalEvent = await EventModel.findByIdAndUpdate(
    event?._id,
    {
      icsFileLink: eventIcs ?? null,
    },
    {
      new: true,
    }
  ).lean();

  await Promise.all([
    eventCommonService.formatAndSendEventTypeEmail({
      mailType: EVENT_MAIL_TYPES.MANAGER_COMMUNITY_EVENT_CREATED_V2,
      event: finalEvent,
      community,
      learnerObjectId: finalEvent.createdByLearnerObjectId,
    }),
    actionEventService.sendMilestoneEvent({
      actionEventType: MILESTONE_ACTIVITY_TYPES.MILESTONE_FIRST_EVENT,
      communityCode: community.code,
      communityObjectId: community._id,
      learnerObjectId: finalEvent.createdByLearnerObjectId,
    }),
    // Remove the mobile notification as it will be replaced by new notifications
    verifyVideoCoverMediaItems({
      entityObjectId: finalEvent._id,
      coverMediaItems: finalEvent.coverMediaItems,
    }),
  ]);
};

const generateSlugForEventFromParams = async ({
  params = {},
  communityId,
}) => {
  const { title: eventTitle } = params;
  const eventFilters = {
    communities: communityId,
    status: {
      $in: [EVENT_STATUS.DRAFT, EVENT_STATUS.PUBLISHED, 'Active'],
    },
  };
  const slug = await slugUtils.generateSlug(
    eventTitle,
    EventModel,
    eventFilters
  );

  return slug;
};

const createDraftEventFromParams = async (params) => {
  const { community, eventParams, options = {} } = params;

  const draftEventParams =
    await eventCommonService.validateAndFormatEventParams(
      eventParams,
      community,
      null,
      {
        formatPricing: !options?.isDuplicatingAnEvent, // price formatting not required if duplicating an event, since it's copied from original event.
      }
    );

  if (options?.generateSlug) {
    draftEventParams.slug = await generateSlugForEventFromParams({
      params: draftEventParams,
      communityId: community._id,
    });
  }

  // duplicaate video cover media items
  if (options.isDuplicatingAnEvent) {
    if (
      draftEventParams.coverMediaItems &&
      hasVideoCoverMediaItems(draftEventParams.coverMediaItems)
    ) {
      draftEventParams.coverMediaItems = await duplicateCoverMediaItems({
        oldCoverMediaItems: draftEventParams.coverMediaItems,
      });
    }
  }

  draftEventParams.isActive = false;
  draftEventParams.status = EVENT_STATUS.DRAFT;
  draftEventParams.isFirstPublished = true;

  return draftEventParams;
};

const duplicateFormattedEmailsForNewDraftEvents = async ({
  community,
  event,
  createdEvents,
  session,
}) => {
  const mailContents = await MailContentModel.find({
    mailType: {
      $in: [
        EVENT_MAIL_TYPES.MEMBER_COMMUNITY_EVENT_REMINDER_1H,
        EVENT_MAIL_TYPES.MEMBER_COMMUNITY_EVENT_REMINDER_24H,
        EVENT_MAIL_TYPES.MEMBER_COMMUNITY_EVENT_RSVP,
      ],
    },
    mailCourse: community.code,
    mailCourseOffer: event._id.toString(),
    recordType: MAIL_RECORD_TYPE.OVERRIDE,
  }).lean();

  const toCreate = [];
  createdEvents.forEach((newEvent) => {
    mailContents.forEach((mailContent) => {
      const newDoc = {
        ...mailContent,
        mailCourseOffer: newEvent._id.toString(),
      };
      delete newDoc._id;
      delete newDoc.createdAt;
      delete newDoc.updatedAt;
      toCreate.push(newDoc);
    });
  });
  await MailContentModel.create(toCreate, { session });
};

const updateEventAndSyncData = async (
  eventId,
  updatePayload,
  community
) => {
  return withTransaction(async (session) => {
    const updatedEvent = await EventModel.findByIdAndUpdate(
      eventId,
      updatePayload,
      { session, new: true }
    ).lean();

    await SyncProductDataService.syncProductData({
      productType: PRODUCT_TYPE.EVENT,
      entity: updatedEvent,
      session,
    });

    if (updatedEvent.status === EVENT_STATUS.PUBLISHED) {
      const changeLog =
        await ProductChangeLogService.addCommunityProductLog({
          communityObjectId: updatedEvent.communities?.[0],
          communityCode: community.code,
          entityObjectId: eventId,
          productType: PRODUCT_TYPE.EVENT,
          changeLogType: PRODUCT_CHANGE_LOG_TYPE.PRODUCT_PUBLISHED,
          operatorLearnerObjectId: updatedEvent.createdByLearnerObjectId,
        });
      return { ...updatedEvent, changeLogId: changeLog._id };
    }
    return updatedEvent;
  });
};

exports.publishCommunityEvent = async (communityId, eventId) => {
  const [event, community, publishedEventsCount] = await Promise.all([
    EventModel.findById(eventId, {
      _id: 1,
      isActive: 1,
      status: 1,
      communities: 1,
      discordEventId: 1,
      isFirstPublished: 1,
      createdByLearnerObjectId: 1,
      title: 1,
      startTime: 1,
      endTime: 1,
      type: 1,
      liveLink: 1,
      inPersonLocationMetadata: 1,
      slug: 1,
      createdAt: 1,
    }).lean(),
    CommunityModel.findById(communityId).lean(),
    EventModel.countDocuments({
      communities: communityId,
      status: EVENT_STATUS.PUBLISHED,
    }),
  ]);

  eventCommunityValidation(event, community);

  if (event?.status === EVENT_STATUS.DELETED) {
    throw new ParamError('Event has been deleted');
  }
  if (event?.status === EVENT_STATUS.PUBLISHED) {
    throw new ParamError('Event has been already been published');
  }

  await PublishProductUsageService.checkPublishProductLimit(
    communityId,
    PURCHASE_TYPE.EVENT,
    event.createdAt,
    community
  );

  const updatePayload = {
    status: EVENT_STATUS.PUBLISHED,
    isActive: true,
    isFirstPublished: false,
  };

  if (!community?.isDemo) {
    try {
      const discordEventId = await eventDiscordService.createDiscordEvent(
        community,
        event
      );
      updatePayload.discordEventId = discordEventId;
    } catch {
      throw new InternalError('Error creating discord event');
    }
  }
  const updatedEvent = await updateEventAndSyncData(
    eventId,
    updatePayload,
    community
  );

  if (event.isFirstPublished) {
    await executeFirstPublishTriggers(community, event);
  }

  if (!community?.isDemo) {
    await purgeEntityLandingPageCache({
      community,
      purgeCommunityLandingPage: publishedEventsCount === 0, // i.e publishing first community event ( for tabs on community landing page )
      entityType: ENTITY_LANDING_PAGE.EVENT,
      entitySlug: event.slug,
    });

    // to move to worker pool
    eventCommonService.checkEventForFraud({
      community,
      eventId: event._id,
      updatedPayload: event,
    });
  }
  return updatedEvent;
};

exports.unpublishCommunityEvent = async (communityId, eventId) => {
  const [event, community, publishedEventsCount] = await Promise.all([
    EventModel.findById(eventId, {
      _id: 1,
      isActive: 1,
      status: 1,
      communities: 1,
      discordEventId: 1,
      slug: 1,
    }).lean(),
    CommunityModel.findById(communityId).lean(),
    EventModel.countDocuments({
      communities: communityId,
      status: EVENT_STATUS.PUBLISHED,
    }),
  ]);

  eventCommunityValidation(event, community);

  if (event?.status === EVENT_STATUS.DELETED) {
    throw new ParamError('Event has been deleted');
  }
  if (event?.status === EVENT_STATUS.DRAFT) {
    throw new ParamError('Event has been already been unpublished');
  }

  const updateEventData = {
    status: EVENT_STATUS.DRAFT,
    isActive: false,
  };

  // check if discordEventId exists
  if (event?.discordEventId && !community?.isDemo) {
    try {
      await eventDiscordService.deleteDiscordEvent(
        community,
        event.discordEventId
      );
      updateEventData.discordEventId = null;
    } catch {
      throw new InternalError('Error deleting discord event');
    }
  }

  await updateEventAndSyncData(eventId, updateEventData, community);

  await Promise.all([
    affiliateProductService.disableAffiliateProduct({
      communityObjectId: communityId,
      entityType: PURCHASE_TYPE.EVENT,
      entityObjectId: eventId,
    }),
    removeEmailReminderForAbandonedCheckout({
      entityType: PURCHASE_TYPE.EVENT,
      entityObjectId: eventId,
    }),
    purgeEntityLandingPageCache({
      community,
      purgeCommunityLandingPage: publishedEventsCount === 1, // i.e unpublishing the only published event ( for tabs on community landing page )
      entityType: ENTITY_LANDING_PAGE.EVENT,
      entitySlug: event.slug,
    }),
  ]);
};

exports.createCommunityEventDraft = async (communityId, params = {}) => {
  const community = await CommunityModel.findById(communityId).lean();
  if (!community) {
    throw new ResourceNotFoundError('Community not found');
  }

  if (Object.keys(params).length === 0 && params.constructor === Object) {
    throw new ParamError('No params to update');
  }

  await AiCofounderProductCreationService.checkProductCreationEligibility(
    communityId,
    params.templateLibraryId,
    community
  );

  const paramsToUpdate = await createDraftEventFromParams({
    community,
    eventParams: params,
  });

  if (!paramsToUpdate.slug) {
    paramsToUpdate.slug = await generateSlugForEventFromParams({
      params: paramsToUpdate,
      communityId,
    });
  }

  await discountValidationForEntities(
    community.code,
    params.newDiscountsToApply,
    params.discountsToAdd
  );

  if (paramsToUpdate.isNewHost && paramsToUpdate.host) {
    const guestHostParams = {
      firstName: paramsToUpdate.host.firstName,
      lastName: paramsToUpdate.host.lastName ?? '',
      profileImage: paramsToUpdate.host.profileImage,
      communityObjectId: community._id,
    };
    await communityHostService.createGuestHost(guestHostParams);
  }
  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  let event;
  try {
    event = (
      await EventModel.create([paramsToUpdate], { session })
    )[0].toObject();

    await SyncProductDataService.syncProductData({
      productType: PRODUCT_TYPE.EVENT,
      entity: event,
      session,
    });

    event = await discountCreationForEntities(
      community.code,
      null,
      event,
      PURCHASE_TYPE.EVENT,
      params?.createdBy,
      EventModel,
      params.newDiscountsToApply,
      params.discountsToRemove,
      params.discountsToAdd,
      params.discountsToDisable,
      session
    );

    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }

  if (hasVideoCoverMediaItems(event.coverMediaItems)) {
    await verifyVideoCoverMediaItems({
      entityObjectId: event._id,
      coverMediaItems: event.coverMediaItems,
    });
  }

  return event;
};

exports.createEventDuplicates = async ({
  eventObjectId,
  communityObjectId,
  duplicationCriteria,
}) => {
  const [event, community] = await Promise.all([
    EventModel.findById(eventObjectId).lean(),
    CommunityModel.findById(communityObjectId).lean(),
  ]);

  // validate event and community exist and event is part of community.
  eventCommunityValidation(event, community);

  // validate event is published.
  if (event.status !== EVENT_STATUS.PUBLISHED) {
    throw new ParamError('Event is not published');
  }

  // generate duplicated events with valid startTime and endTime
  const { events: duplicatedEventParamsArr, maxLoopCountReached } =
    generateDuplicateEvents(event, duplicationCriteria);

  if (maxLoopCountReached) {
    await sendMaxLoopCountReachedAlert({ event, duplicationCriteria });
    throw new ParamError('Max loop count reached');
  }

  if (!duplicatedEventParamsArr.length) {
    return [];
  }

  // create draft event objects to insert
  const draftEvents = await Promise.all(
    duplicatedEventParamsArr.map((duplicatedEventParams) =>
      createDraftEventFromParams({
        community,
        eventParams: duplicatedEventParams,
        options: {
          generateSlug: true,
          isDuplicatingAnEvent: true,
        },
      })
    )
  );

  // Use withTransaction to create all or none.
  const result = await withTransaction(async (session) => {
    const createdEvents = [];
    for await (const draftEvent of draftEvents) {
      // eslint-disable-next-line no-await-in-loop
      const docs = await EventModel.create([draftEvent], { session });
      const newEvent = docs[0].toObject();
      await SyncProductDataService.syncProductData({
        productType: PRODUCT_TYPE.EVENT,
        entity: newEvent,
        session,
      });
      createdEvents.push(newEvent);
    }
    await duplicateFormattedEmailsForNewDraftEvents({
      community,
      event,
      createdEvents,
      session,
    });
    return createdEvents;
  });

  return result;
};
