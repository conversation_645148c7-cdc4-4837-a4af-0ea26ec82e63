const { ObjectId } = require('mongoose').Types;
const { DateTime } = require('luxon');

const Program = require('../../models/program/program.model');
const ProgramItem = require('../../models/program/programItem.model');
const ProgramParticipant = require('../../models/program/programParticipant.model');

const upsellService = require('./upsell.service');
const commonService = require('./common.service');
const getParticipantsService = require('./getParticipants.service');
const getProgramItemsService = require('./getProgramItems.service');
const leaderboardService = require('./leaderboard.service');
const discountService = require('../../communitiesAPI/services/common/communityDiscounts.service');
const membershipService = require('../membership');
const { affiliateService } = require('../affiliate');
const {
  getAddonPriceInLocalCurrency,
} = require('../../communitiesAPI/services/common/communityAddonPrice.service');

const {
  ParamError,
  ResourceNotFoundError,
} = require('../../utils/error.util');

const {
  PARTICIPANT_PROGRAM_STATUS,
  PARTICIPANT_PROGRAM_ITEM_STATUS,
  PROGRAM_TO_PROGRAM_ITEM_MAP,
  PROGRAM_ITEM_TYPE,
  PROGRAM_STATUS,
  ACCESS_TYPE,
  PROGRAM_TYPE,
  PROGRAM_SCHEDULE_STATUS,
  PROGRAM_CHALLENGE_TYPE,
  PROGRAM_ITEM_STATUS,
} = require('./constants');
const {
  UPSELL_SOURCE_ENTITY_TYPE,
  PURCHASE_TYPE,
} = require('../../constants/common');
const {
  generateCoverMediaItems,
} = require('../../utils/multipleCoverMediaItems.util');
const {
  COVER_MEDIA_ENTITY_TYPES,
} = require('../../constants/coverMediaItems.constant');

async function getDiscountDetails(program, communityCode) {
  const discountsAppliedArr =
    await discountService.retrieveAllDiscountsRelatedToEntity(
      program,
      communityCode
    );
  return discountsAppliedArr;
}

const getPrograms = async ({
  community,
  learner,
  type,
  statuses,
  scheduleStatuses,
  challengeType,
  limit,
  skip,
  isCommunityManager = false,
}) => {
  const filters = {
    communityObjectId: community._id,
    type,
  };
  if (statuses?.length > 0) {
    filters.status = { $in: statuses };
  } else {
    filters.status = { $ne: PROGRAM_STATUS.DELETED };
  }
  if (scheduleStatuses?.length > 0) {
    filters.scheduleStatus = { $in: scheduleStatuses };
  }
  if (challengeType) {
    filters.challengeType = challengeType;
  }

  const [programs, total] = await Promise.all([
    Program.find(filters, {
      _id: 1,
      title: 1,
      type: 1,
      startTime: 1,
      endTime: 1,
      status: 1,
      scheduleStatus: 1,
      access: 1,
      pricingConfig: 1,
      cover: 1,
      coverMediaItems: 1,
      slug: 1,
      durationInDays: 1,
      'additionalSettings.hideParticipantCountOnLP': 1,
      challengeType: 1,
    })
      .sort({ status: -1, scheduleStatus: 1, createdAt: -1 })
      .limit(limit)
      .skip(skip)
      .lean(),
    Program.countDocuments(filters),
  ]);

  const results = await Promise.all(
    programs.map(async (program) => {
      const [
        participantCount,
        participant,
        participantProfilePics,
        coverMediaItems,
      ] = await Promise.all([
        commonService.countParticipants({
          programId: program._id,
        }),
        (async () => {
          if (!learner) {
            return null;
          }
          return ProgramParticipant.findOne({
            programObjectId: program._id,
            learnerObjectId: learner._id,
            status: { $ne: PARTICIPANT_PROGRAM_STATUS.SYSTEM_REMOVED },
          });
        })(),
        commonService.getSomeParticipantProfilePics({
          community,
          programId: program._id,
          limit: 5,
        }),
        generateCoverMediaItems({
          entity: program,
          entityType: COVER_MEDIA_ENTITY_TYPES.CHALLENGE,
          isCommunityManager,
        }),
      ]);

      return {
        ...program,
        coverMediaItems,
        participantCount,
        joined: participant !== null,
        participantStatus: participant?.status,
        participantProfilePics,
      };
    })
  );
  return {
    results,
    total,
  };
};

const getUpcomingChallenges = async ({
  community,
  learner,
  limit,
  skip,
}) => {
  return getPrograms({
    community,
    learner,
    type: PROGRAM_TYPE.CHALLENGE,
    statuses: [PROGRAM_STATUS.PUBLISHED],
    scheduleStatuses: [PROGRAM_SCHEDULE_STATUS.UPCOMING],
    challengeType: PROGRAM_CHALLENGE_TYPE.FIXED,
    limit,
    skip,
  });
};

const getOngoingChallenges = async ({
  community,
  learner,
  limit,
  skip,
}) => {
  const programs = await getPrograms({
    community,
    learner,
    type: PROGRAM_TYPE.CHALLENGE,
    statuses: [PROGRAM_STATUS.PUBLISHED],
    scheduleStatuses: [PROGRAM_SCHEDULE_STATUS.ONGOING],
    limit,
    skip,
    getOngoingChallenge: true,
  });

  // Only return the challenge that is
  // 1. Fixed time, and is ongoing status
  // or 2. Always on challenge that user is participated and not complete yet
  const filteredPrograms = programs.results.reduce((result, program) => {
    if (
      program.challengeType === PROGRAM_CHALLENGE_TYPE.ALWAYS_ON &&
      program.participantStatus !== PARTICIPANT_PROGRAM_STATUS.PARTICIPATED
    ) {
      return result;
    }

    result.push(program);
    return result;
  }, []);

  return { results: filteredPrograms, total: filteredPrograms.length };
};

// eslint-disable-next-line no-unused-vars
const getLastCompletedCheckpoint = async ({
  community,
  participant,
  program,
}) => {
  if (participant.items?.length > 0) {
    const lastCompletedCheckpointId =
      participant.items[participant.items.length - 1].itemObjectId;
    const lastCompleteCheckpoint = await ProgramItem.findOne(
      {
        _id: lastCompletedCheckpointId,
      },
      { index: 1 }
    );
    return getProgramItemsService.getProgramItem({
      community,
      filters: {
        programObjectId: program._id,
        index: lastCompleteCheckpoint.index + 1,
      },
    });
  }
};

const getFirstIncompleteCheckpoint = async ({
  community,
  participant,
  programItems,
}) => {
  const completedCheckpointSet = new Set();
  for (let i = 0; i < participant.items?.length; i++) {
    completedCheckpointSet.add(
      participant.items[i].itemObjectId.toString()
    );
  }
  let firstIncompleteCheckpointId = programItems[0]._id;
  for (let i = 0; i < programItems.length; i++) {
    if (!completedCheckpointSet.has(programItems[i]._id.toString())) {
      firstIncompleteCheckpointId = programItems[i]._id;
      break;
    }
  }
  return getProgramItemsService.getProgramItem({
    community,
    filters: {
      _id: firstIncompleteCheckpointId,
    },
  });
};

const getParticipantData = async ({
  community,
  learner,
  program,
  participant,
  programItems,
}) => {
  const result = {
    status: participant.status,
    _id: participant._id,
    items: participant.items,
    completedCount: participant.completedCount,
  };
  const now = DateTime.utc();
  let currentProgramItem;
  if (program.challengeSpecific?.stepByStepUnlocking === true) {
    currentProgramItem = await getFirstIncompleteCheckpoint({
      community,
      participant,
      programItems,
    });
    if (
      !currentProgramItem ||
      DateTime.fromJSDate(currentProgramItem.startTime) > now
    ) {
      currentProgramItem = await getProgramItemsService.getProgramItem({
        community,
        filters: {
          programObjectId: program._id,
          startTime: { $lte: now },
          endTime: { $gte: now },
        },
      });
    }
  } else {
    currentProgramItem = await getProgramItemsService.getProgramItem({
      community,
      filters: {
        programObjectId: program._id,
        startTime: { $lte: now },
        endTime: { $gte: now },
      },
    });
  }
  // if (!currentProgramItem) {
  //   if (DateTime.fromJSDate(program.startTime) > now) {
  //     currentProgramItem = await getProgramItemsService.getProgramItem({
  //       community,
  //       filters: {
  //         programObjectId: program._id,
  //         index: 1,
  //       },
  //     });
  //   } else if (DateTime.fromJSDate(program.endTime) < now) {
  //     currentProgramItem = await getProgramItemsService.getProgramItem({
  //       community,
  //       filters: {
  //         programObjectId: program._id,
  //         index: programItems.length,
  //       },
  //     });
  //   }
  // }

  if (currentProgramItem) {
    const participantProgramItem =
      await getParticipantsService.getParticipantProgramItem({
        community,
        learner,
        participantId: participant._id,
        programId: program._id,
        programItemId: currentProgramItem._id,
        needProgramData: false,
      });
    participantProgramItem[
      `${PROGRAM_TO_PROGRAM_ITEM_MAP[program.type].toLowerCase()}Data`
    ] = currentProgramItem;
    result[`current${PROGRAM_TO_PROGRAM_ITEM_MAP[program.type]}Data`] =
      participantProgramItem;

    const nextProgramItem = await ProgramItem.findOne({
      programObjectId: program._id,
      index: currentProgramItem.index + 1,
    });
    if (nextProgramItem) {
      result[`next${PROGRAM_TO_PROGRAM_ITEM_MAP[program.type]}Data`] =
        nextProgramItem;
    }
  }

  if (leaderboardService.isLeaderboardEnabled(program)) {
    result.leaderboardInfo =
      await leaderboardService.getParticipantLeaderboardPointsAndRank({
        program,
        participant,
      });
  }

  return result;
};

async function getMostRecentProgramItemsDetails({
  program,
  items,
  ongoingProgramItem,
}) {
  const results = [];
  const now = DateTime.utc();
  if (!program.startTime || DateTime.fromJSDate(program.startTime) > now) {
    return results;
  }
  if (items.length > 0 && items[0].startTime) {
    if (DateTime.fromJSDate(items[0].startTime) > now) {
      return results;
    }
  }

  const programIdsToCount = [];
  const indexes = [];
  if (ongoingProgramItem) {
    programIdsToCount.push(ongoingProgramItem._id);
  }
  let lastItemId;
  for (let i = 0; i < items.length; i++) {
    if (
      ongoingProgramItem &&
      items[i]._id.toString() === ongoingProgramItem._id.toString()
    ) {
      indexes.push(i);
      if (lastItemId) {
        programIdsToCount.push(lastItemId);
        indexes.push(i - 1);
      }
    }
    lastItemId = items[i]._id;
  }
  if (programIdsToCount.length === 0) {
    if (items.length >= 2) {
      programIdsToCount.push(items[items.length - 1]._id);
      indexes.push(items.length - 1);
      programIdsToCount.push(items[items.length - 2]._id);
      indexes.push(items.length - 2);
    } else if (items.length === 1) {
      programIdsToCount.push(items[0]._id);
      indexes.push(0);
    }
  }

  const completedCounts = await Promise.all(
    programIdsToCount.map((programItemId) =>
      commonService.countParticipants({
        programId: program._id,
        filterProgramItemIds: [programItemId],
        filterProgramItemStatuses: [
          PARTICIPANT_PROGRAM_ITEM_STATUS.COMPLETED,
          PARTICIPANT_PROGRAM_ITEM_STATUS.LATE_COMPLETED,
        ],
      })
    )
  );

  for (let i = 0; i < indexes.length; i++) {
    results.push({
      ...items[indexes[i]],
      completedCount: completedCounts[i],
    });
  }
  return results;
}

async function getPriceDetails({
  ip,
  program,
  community,
  selectedAmount,
  paymentMethodCountryCode,
  paymentProvider,
}) {
  const addon = program;
  addon.amount = program.pricingConfig.amount;
  addon.currency = program.pricingConfig.currency;
  return getAddonPriceInLocalCurrency({
    ip,
    addon,
    communityObjectId: community._id,
    selectedAmount,
    paymentMethodCountryCode,
    paymentProvider,
  });
}

function getChatGroupType(chatGroupLink) {
  const typeMap = {
    whatsapp: 'whatsapp',
    discord: 'discord',
    telegram: 'telegram',
    messenger: 'messenger',
  };

  const patterns = {
    [typeMap.whatsapp]:
      /^https?:\/\/(www\.)?wa\.me\/?|^https?:\/\/(www\.)?whatsapp\.com\//,
    [typeMap.discord]:
      /^https?:\/\/(www\.)?discord\.com\/?|^https?:\/\/(www\.)?discord\.gg\//,
    [typeMap.telegram]:
      /^https?:\/\/(www\.)?t\.me\/?|^https?:\/\/(www\.)?telegram\.me\/?|^https?:\/\/(www\.)?telegram\.org\//,
    [typeMap.messenger]:
      /^https?:\/\/(www\.)?m\.me\/?|^https?:\/\/(www\.)?facebook\.com\/messages\/?|^https?:\/\/(www\.)?messenger\.com\/t\/?|^https?:\/\/m\.me\/j\//,
  };

  for (const [service, pattern] of Object.entries(patterns)) {
    if (pattern.test(chatGroupLink)) {
      return service;
    }
  }
  return 'other';
}

const getProgram = async ({
  ip,
  community,
  learner,
  programId,
  needProgramItems = true,
  selectedAmount = null,
  showUpsell = false,
  affiliateCode = null,
  isCommunityManager = false,
  paymentMethodCountryCode = null,
  paymentProvider = null,
}) => {
  const [program, isManager, activeSubscription] = await Promise.all([
    commonService.getProgram(programId),
    membershipService.getService.isCommunityManager({
      communityCode: community.code,
      email: learner?.email,
    }),
    membershipService.getService.getActiveSubscriptionFromEmail({
      email: learner?.email,
      communityCode: community.code,
    }),
  ]);

  if (program.communityObjectId.toString() !== community._id.toString()) {
    throw new ParamError('Program not belong to community');
  }

  const [
    affiliateInfo,
    participantCount,
    participantProfilePics,
    programItemsWithUpsell,
    programItems,
    participant,
    ongoingProgramItem,
    discountApplied,
    coverVideo = null,
    coverMediaItems,
  ] = await Promise.all([
    affiliateService.retrieveAffiliateInfo({
      communityObjectId: program.communityObjectId,
      affiliateCode,
      entityObjectId: program._id,
      entityType: PURCHASE_TYPE.CHALLENGE,
    }),
    commonService.countParticipants({
      programId,
    }),
    commonService.getSomeParticipantProfilePics({
      community,
      programId,
      limit: 5,
    }),
    getProgramItemsService.getProgramItems({
      programId,
      type: PROGRAM_TO_PROGRAM_ITEM_MAP[program.type].toLowerCase(),
      showUpsell,
    }),
    getProgramItemsService.getProgramItems({
      programId,
      type: PROGRAM_TO_PROGRAM_ITEM_MAP[program.type].toLowerCase(),
    }),
    ProgramParticipant.findOne({
      programObjectId: new ObjectId(programId),
      learnerObjectId: learner?._id,
      status: { $ne: PARTICIPANT_PROGRAM_STATUS.SYSTEM_REMOVED },
    }),
    commonService.getOngoingProgramItem({
      program,
      type: PROGRAM_TO_PROGRAM_ITEM_MAP[program.type].toLowerCase(),
      projection: {
        _id: 1,
        index: 1,
        title: 1,
      },
    }),
    getDiscountDetails(program, community.code),
    commonService.getCoverVideo({
      communityObjectId: community._id,
      programObjectId: program._id,
      coverVideo: program.coverVideo,
    }),
    generateCoverMediaItems({
      entity: program,
      entityType: COVER_MEDIA_ENTITY_TYPES.CHALLENGE,
      isCommunityManager,
    }),
  ]);

  const programItemsObjectIds = programItems.map(
    ({ _id: programItemObjectId }) => programItemObjectId
  );

  const [hasChallengeUpsell, hasCheckpointsUpsell] = await Promise.all([
    upsellService.hasUpsell(
      [program._id],
      UPSELL_SOURCE_ENTITY_TYPE.CHALLENGE
    ),
    upsellService.hasUpsell(programItemsObjectIds, [
      UPSELL_SOURCE_ENTITY_TYPE.CHECKPOINT,
    ]),
  ]);

  program.affiliateInfo = affiliateInfo;

  program.existsUpsell = hasChallengeUpsell || hasCheckpointsUpsell;
  program.hasUpsell = hasChallengeUpsell;
  program.participantCount = participantCount;
  program.participantProfilePics = participantProfilePics;

  if (isManager) {
    program.discountsApplied = discountApplied;
  }

  const mostRecentProgramItems = await getMostRecentProgramItemsDetails({
    program,
    items: programItems,
    ongoingProgramItem,
  });
  program[`mostRecent${PROGRAM_TO_PROGRAM_ITEM_MAP[program.type]}s`] =
    mostRecentProgramItems;

  if (participant && activeSubscription) {
    program.joined = true;
    program.participantData = await getParticipantData({
      community,
      learner,
      program,
      participant,
      programItems,
    });
  } else {
    program.joined = false;
  }
  if (needProgramItems === true) {
    program.items = programItemsWithUpsell;
  }
  if (program.access === ACCESS_TYPE.PAID) {
    program.priceDetails = await getPriceDetails({
      ip,
      program,
      community,
      selectedAmount,
      paymentMethodCountryCode,
      paymentProvider,
    });
  }
  if (
    program.type === PROGRAM_TYPE.CHALLENGE &&
    program.challengeSpecific
  ) {
    program.challengeSpecific.checkpointCount = programItems.length;
    if (
      program.challengeSpecific.areAllCheckpointsNotEmpty === undefined
    ) {
      program.challengeSpecific.areAllCheckpointsNotEmpty = false;
    }

    program.challengeSpecific.hasFilledUpCheckpoints =
      program.challengeSpecific.hasFilledUpCheckpoints ?? false;

    if (program.participantData?.currentCheckpointData) {
      program.participantData.currentCheckpointData.challengeData = {
        title: program.title,
        startTime: program.startTime,
        endTime: program.endTime,
        status: program.status,
        cover: program.cover,
      };
    }
  }

  if (coverVideo) {
    program.coverVideo = coverVideo;
  }
  program.coverMediaItems = coverMediaItems;

  if (program.canJoinAfterStart === undefined) {
    program.canJoinAfterStart = false;
  }

  if (program.additionalSettings?.chatGroupLink) {
    program.additionalSettings.chatGroupType = getChatGroupType(
      program.additionalSettings.chatGroupLink
    );
  }

  return program;
};

const getSearchBySlugPipeline = ({ community, slug }) => {
  const pipeline = [
    {
      $search: {
        index: 'program_slug_index',
        compound: {
          must: {
            text: {
              path: 'slug',
              query: slug.toLowerCase(),
            },
          },
          filter: {
            equals: {
              path: 'communityObjectId',
              value: community._id,
            },
          },
        },
      },
    },
    {
      $match: {
        status: { $ne: PROGRAM_STATUS.DELETED },
      },
    },
  ];
  return pipeline;
};

const getProgramBySlugLite = async ({ community, slug }) => {
  const pipeline = getSearchBySlugPipeline({ community, slug });
  const result = await Program.aggregate(pipeline);
  if (result?.length) {
    return result[0];
  }
  return null;
};

const getProgramBySlug = async ({
  ip,
  community,
  slug,
  learner,
  selectedAmount = null,
  affiliateCode = null,
  isCommunityManager = false,
  paymentMethodCountryCode = null,
  paymentProvider = null,
}) => {
  const pipeline = getSearchBySlugPipeline({ community, slug });
  const result = await Program.aggregate(pipeline);
  let program;
  if (result?.length) {
    program = result[0];
  }
  if (!program) {
    throw new ResourceNotFoundError('Program not found');
  }

  program.isLeaderboardEnabled =
    leaderboardService.isLeaderboardEnabled(program);

  const [
    affiliateInfo,
    participantCount,
    participantProfilePics,
    programItemsBrief,
    participant,
    pastProgramItemsCount,
    ongoingProgramItem,
    coverVideo = null,
    coverMediaItems,
  ] = await Promise.all([
    affiliateService.retrieveAffiliateInfo({
      communityObjectId: program.communityObjectId,
      affiliateCode,
      entityObjectId: program._id,
      entityType: PURCHASE_TYPE.CHALLENGE,
    }),
    commonService.countParticipants({
      programId: program._id,
    }),
    commonService.getSomeParticipantProfilePics({
      community,
      programId: program._id,
      limit: 5,
    }),
    getProgramItemsService.getProgramItemsBrief({
      programId: program._id,
      type: PROGRAM_TO_PROGRAM_ITEM_MAP[program.type].toLowerCase(),
    }),
    ProgramParticipant.findOne({
      programObjectId: program._id,
      learnerObjectId: learner?._id,
      status: { $ne: PARTICIPANT_PROGRAM_STATUS.SYSTEM_REMOVED },
    }),
    ProgramItem.countDocuments({
      programObjectId: program._id,
      endTime: { $lte: new Date() },
    }),
    commonService.getOngoingProgramItem({
      program,
      type: PROGRAM_TO_PROGRAM_ITEM_MAP[program.type].toLowerCase(),
      projection: {
        _id: 1,
        index: 1,
        title: 1,
      },
    }),
    // TODO - @AmanMinhas remove after release.
    commonService.getCoverVideo({
      communityObjectId: community._id,
      programObjectId: program._id,
      coverVideo: program.coverVideo,
    }),
    generateCoverMediaItems({
      entity: program,
      entityType: COVER_MEDIA_ENTITY_TYPES.CHALLENGE,
      isCommunityManager,
    }),
  ]);

  program.affiliateInfo = affiliateInfo;
  program.coverMediaItems = coverMediaItems;

  program[`past${PROGRAM_TO_PROGRAM_ITEM_MAP[program.type]}sCount`] =
    pastProgramItemsCount;
  program.participantCount = participantCount;
  program.participantProfilePics = participantProfilePics;
  program[
    `${PROGRAM_TO_PROGRAM_ITEM_MAP[program.type].toLowerCase()}sBrief`
  ] = programItemsBrief;
  if (program.access === ACCESS_TYPE.PAID) {
    program.priceDetails = await getPriceDetails({
      ip,
      program,
      community,
      selectedAmount,
      paymentMethodCountryCode,
      paymentProvider,
    });
  }
  if (program.canJoinAfterStart === undefined) {
    program.canJoinAfterStart = false;
  }
  if (
    program.type === PROGRAM_TYPE.CHALLENGE &&
    program.challengeSpecific
  ) {
    program.challengeSpecific.checkpointCount =
      await getProgramItemsService.countProgramItems({
        programId: program._id,
        type: PROGRAM_ITEM_TYPE.CHECKPOINT,
      });
  }
  if (ongoingProgramItem) {
    const nextProgramItem = await ProgramItem.findOne(
      {
        programObjectId: program._id,
        index: ongoingProgramItem.index + 1,
        status: PROGRAM_ITEM_STATUS.ACTIVE,
      },
      { startTime: 1 }
    );
    if (nextProgramItem) {
      program[
        `next${PROGRAM_TO_PROGRAM_ITEM_MAP[program.type]}StartTime`
      ] = nextProgramItem.startTime;
    }
  }

  if (coverVideo) {
    program.coverVideo = coverVideo;
  }

  if (participant) {
    program.joined = true;
    const programItems = await getProgramItemsService.getProgramItems({
      programId: program._id,
      type: PROGRAM_TO_PROGRAM_ITEM_MAP[program.type].toLowerCase(),
      showUpsell: false,
    });
    program.participantData = await getParticipantData({
      community,
      learner,
      program,
      participant,
      programItems,
    });
  } else {
    program.joined = false;
  }

  if (program.additionalSettings?.chatGroupLink) {
    program.additionalSettings.chatGroupType = getChatGroupType(
      program.additionalSettings.chatGroupLink
    );
  }

  return program;
};

module.exports = {
  getPrograms,
  getUpcomingChallenges,
  getOngoingChallenges,
  getProgram,
  getProgramBySlug,
  getProgramBySlugLite,
  getFirstIncompleteCheckpoint,
};
