const { DateTime } = require('luxon');
const { ObjectId } = require('mongoose').Types;

const PublishProductUsageService = require('@services/featurePermissions/publishProductUsage.service');

const Program = require('../../models/program/program.model');
const ProgramItem = require('../../models/program/programItem.model');
const CommunityFolderItems = require('../../communitiesAPI/models/communityFolderItems.model');
const TemplateLibraryModel = require('@/src/models/getInspired/templateLibrary.model');

const {
  discountValidationForEntities,
  discountCreationForEntities,
} = require('../../communitiesAPI/services/common/communityDiscounts.service');
const shortUrlService = require('../../communitiesAPI/services/web/shortUrl.service');

const commonService = require('./common.service');
const getProgramItemsService = require('./getProgramItems.service');
const {
  addCheckpointEmailSchedules,
  addChallengeAndCheckpointNotificationSchedule,
} = require('./scheduledNotification.service');

const {
  checkIfResourcesPostsOrEventsWithSlugExists,
} = require('../../communitiesAPI/services/common/utils');
const { isValidURL } = require('../../utils/url_handling');
const {
  ParamError,
  ToUserError,
  DBError,
} = require('../../utils/error.util');
const entityCurrencyUtil = require('../../utils/entityCurrency.util');
const fraudService = require('../fraud');
const {
  getUpdateDataForMultipleCoverMediaItems,
  hasVideoCoverMediaItems,
  generateCoverMediaItems,
} = require('../../utils/multipleCoverMediaItems.util');

const { FRONTEND_APP_LINK } = require('../../config');

const CLEAN_SLUG_REGEX = /[.\s]+/g;

const {
  PROGRAM_TYPE,
  ACCESS_TYPE,
  PROGRAM_STATUS,
  PROGRAM_ITEM_TYPE,
  LEADERBOARD_TYPE,
  PROGRAM_CHALLENGE_TYPE,
  PROGRAM_SCHEDULE_STATUS,
  PROGRAM_ITEM_STATUS,
} = require('./constants');
const { PROGRAM_ERROR } = require('../../constants/errorCode');

const CHECKPOINT_TITLE_PREFIX = {
  DAY: 'Day',
  WEEK: 'Week',
  MONTH: 'Month',
  CHECKPOINT: 'Checkpoint',
};

const logger = require('../logger.service');
const PrimaryMongooseConnection = require('../../rpc/primaryMongooseConnection');
const communityFolderItemsModel = require('../../communitiesAPI/models/communityFolderItems.model');
const {
  communityLibraryStatusMap,
  FOLDER_ITEM_STATUS,
} = require('../../communitiesAPI/constants');
const pricingService = require('../pricing');
const { affiliateProductService } = require('../affiliate');
const { PURCHASE_TYPE } = require('../../constants/common');
const {
  COVER_MEDIA_ENTITY_TYPES,
  COVER_MEDIA_TYPES,
} = require('../../constants/coverMediaItems.constant');
const {
  verifyVideoCoverMediaItems,
  deleteCoverMediaItems,
  deleteRemovedVideoCoverMediaItems,
} = require('../coverMediaItems/common.service');
const SyncProductDataService = require('../product/syncProductData.service');
const ProductChangeLogService = require('../product/productChangeLog.service');
const {
  PRODUCT_TYPE,
  PRODUCT_CHANGE_LOG_TYPE,
} = require('../product/constants');
const {
  purgeEntityLandingPageCache,
  ENTITY_LANDING_PAGE,
} = require('../../utils/memberPortalLinks.utils');
const AiCofounderProductCreationService = require('../featurePermissions/aiCofounderProductCreation.service');
const {
  checkActiveCampaignForSlugChange,
} = require('../../utils/metaAds.util');

function getProgramDurationInDays({ startTime, endTime }) {
  return parseInt(endTime.diff(startTime, 'days').toObject().days, 10);
}

function validateSchedule({ payload, currentProgram }) {
  if (
    payload?.challengeType !== PROGRAM_CHALLENGE_TYPE.FIXED &&
    currentProgram?.challengeType !== PROGRAM_CHALLENGE_TYPE.FIXED
  ) {
    return {};
  }
  const now = DateTime.utc();
  let startTime;
  let endTime;

  if (currentProgram && currentProgram.startTime) {
    startTime = DateTime.fromJSDate(currentProgram.startTime);
    if (
      startTime < now &&
      currentProgram.status === PROGRAM_STATUS.PUBLISHED
    ) {
      if (
        payload.startTime ||
        payload.endTime ||
        payload.challengeDurationInDays
      ) {
        throw new ParamError(
          'Schedule cannot be updated for published ongoing programs'
        );
      }
    }
    // if (
    //   startTime < now &&
    //   payload.status === PROGRAM_STATUS.PUBLISHED &&
    //   currentProgram.status === PROGRAM_STATUS.DRAFT
    // ) {
    //   throw new ParamError(
    //     'Program cannot be published with start time in the past'
    //   );
    // }
  }

  if (payload.startTime) {
    startTime = DateTime.fromJSDate(payload.startTime);
    if (!payload.endTime) {
      if (currentProgram && currentProgram.endTime) {
        endTime = currentProgram.endTime;
      } else {
        throw new ParamError('End time is required');
      }
    } else {
      endTime = DateTime.fromJSDate(payload.endTime);
    }
    if (startTime < now) {
      throw new ParamError('Start time cannot be in the past');
    }
  } else if (payload.endTime) {
    endTime = DateTime.fromJSDate(payload.endTime);
    if (currentProgram && currentProgram.startTime) {
      startTime = DateTime.fromJSDate(currentProgram.startTime);
    } else {
      throw new ParamError('Start time is required');
    }
  } else if (payload.challengeDurationInDays) {
    startTime = now.plus({ days: 1 }).startOf('hour').plus({ hours: 1 });
    endTime = startTime.plus({ days: payload.challengeDurationInDays });
  } else if (
    currentProgram &&
    currentProgram.startTime &&
    currentProgram.endTime
  ) {
    startTime = DateTime.fromJSDate(currentProgram.startTime);
    endTime = DateTime.fromJSDate(currentProgram.endTime);
  }
  if (endTime < startTime) {
    throw new ParamError('End time cannot be before start time');
  }

  return {
    startTime,
    endTime,
  };
}

function validateChatGroupLink(link) {
  if (!isValidURL(link)) {
    const error = new ToUserError(
      'Invalid chat group link',
      PROGRAM_ERROR.INVALID_CHAT_GROUP_LINK
    );
    throw error;
  }
}

function getDefaultCheckpointPrefix(checkpointDuration) {
  switch (checkpointDuration) {
    case 1:
      return CHECKPOINT_TITLE_PREFIX.DAY;
    case 7:
      return CHECKPOINT_TITLE_PREFIX.WEEK;
    case 30:
      return CHECKPOINT_TITLE_PREFIX.MONTH;
    default:
      return CHECKPOINT_TITLE_PREFIX.CHECKPOINT;
  }
}

function generateRandomString(length) {
  const characters = '0123456789';
  let result = '';

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    result += characters.charAt(randomIndex);
  }

  return result;
}

async function slugTaken({ slug, community }) {
  const program = await Program.findOne({
    slug,
    status: { $ne: PROGRAM_STATUS.DELETED },
    communityObjectId: community._id,
  });
  if (program) {
    return true;
  }
  const otherProductWithSlugExists =
    await checkIfResourcesPostsOrEventsWithSlugExists(slug, community._id);
  return otherProductWithSlugExists;
}

async function getSlug({ title, community }) {
  const regex = /[^a-zA-Z.\d\s]/g;
  const titleCleaned = title.trim().replace(regex, '');

  let slug = titleCleaned.replace(CLEAN_SLUG_REGEX, '-');
  slug = `/${slug.toLowerCase()}`;
  let taken = await slugTaken({ slug, community });
  let finalSlug = slug;

  while (taken) {
    finalSlug = `${slug}-${generateRandomString(7)}`;
    // eslint-disable-next-line no-await-in-loop
    taken = await slugTaken({ slug: finalSlug, community });
  }

  return finalSlug;
}

async function validateSlug({ slug, community }) {
  const taken = await slugTaken({ slug, community });
  if (taken) {
    throw new ToUserError(
      'Link has already taken by another offering of your community',
      PROGRAM_ERROR.DUPLICATE_SLUG
    );
  }
}

function validateCheckpointDuration(
  checkpointDurationInDays,
  programDurationInDays
) {
  if (checkpointDurationInDays > programDurationInDays) {
    throw new ParamError(
      'Checkpoint duration cannot be greater than program duration'
    );
  }
}

const validateAndGetCreatePayload = async ({
  payload,
  programType,
  community,
  createdBy,
}) => {
  const title = payload.title?.trim();
  if (title?.length === 0) {
    throw new ParamError('Title is required');
  }
  if (title.trim().length > 100) {
    throw new ParamError('Title is too long. Limit is 100 characters.');
  }

  const communityObjectId = community._id;
  const toDBPayload = {
    communityObjectId,
    title,
    description: payload.description,
    descriptionHTML: payload.descriptionHTML,
    cover: payload.cover,
    access: payload.access,
    type: programType,
    challengeType: payload.challengeType,
    createdBy: createdBy.email,
    hostEmail: createdBy.email,
    status: PROGRAM_STATUS.DRAFT,
    canJoinAfterStart: false,
    additionalSettings: {
      trackResponseTimePoints: true,
    },
    feedConfig: {
      enabled: true,
    },
    leaderboardConfig: {
      enabled: false,
    },
  };

  if (
    Array.isArray(payload.coverMediaItems) &&
    payload.coverMediaItems.length
  ) {
    const coverMediaItemsUpdateData =
      await getUpdateDataForMultipleCoverMediaItems({
        communityId: communityObjectId,
        entityType: COVER_MEDIA_ENTITY_TYPES.CHALLENGE,
        coverMediaItems: payload.coverMediaItems,
      });
    Object.assign(toDBPayload, coverMediaItemsUpdateData);
  }

  const { startTime, endTime } = validateSchedule({
    payload,
    currentProgram: null,
  });

  if (startTime && endTime) {
    toDBPayload.startTime = startTime;
    toDBPayload.endTime = endTime;
    toDBPayload.durationInDays = getProgramDurationInDays({
      startTime,
      endTime,
    });
  } else if (payload.challengeDurationInDays) {
    toDBPayload.durationInDays = payload.challengeDurationInDays;
  }

  if (payload.chatGroupLink) {
    validateChatGroupLink(payload.chatGroupLink);
    toDBPayload.additionalSettings.chatGroupLink = payload.chatGroupLink;
  }

  entityCurrencyUtil.validateEntityCurrencyWithCommunityBaseCurrency(
    payload.pricingCurrency,
    community.baseCurrency
  );

  const pricing = await pricingService.validateAndFormatPricing(
    payload,
    false,
    null,
    community
  );

  if (pricing) {
    toDBPayload.pricingConfig = {
      ...pricing.pricingConfig,
      amount: pricing.amount,
      currency: pricing.currency,
    };
    toDBPayload.access = pricing.access;
  }

  await discountValidationForEntities(
    community.code,
    payload.newDiscountsToApply,
    payload.existingDiscountsToAdd
  );

  if (programType === PROGRAM_TYPE.CHALLENGE) {
    const challengeSpecific = {
      stepByStepUnlocking: payload.stepByStepUnlocking ?? false,
    };
    if (payload.checkpointDurationInDays) {
      validateCheckpointDuration(
        payload.checkpointDurationInDays,
        toDBPayload.durationInDays
      );
      challengeSpecific.checkpointDurationInDays =
        payload.checkpointDurationInDays;
    }
    toDBPayload.challengeSpecific = challengeSpecific;
  }

  if (
    payload.enableCheckpointSubmissionAfterDeadline ||
    payload.enableCheckpointSubmissionAfterDeadline === false
  ) {
    toDBPayload[
      'challengeSpecific.enableCheckpointSubmissionAfterDeadline'
    ] = payload.enableCheckpointSubmissionAfterDeadline;
  }

  if (payload.templateLibraryId) {
    toDBPayload.templateLibraryId = payload.templateLibraryId;
  }

  return toDBPayload;
};

function validateLeaderboardConfigs(leaderboardConfigs) {
  const leaderboardTypes = Object.values(LEADERBOARD_TYPE);

  const uniqueLeaderboardConfigTypes = new Set(
    leaderboardConfigs.map(({ type }) => type)
  );

  if (uniqueLeaderboardConfigTypes.size !== leaderboardConfigs.length) {
    throw new ParamError(
      'Invalid leaderboard configs due to duplicate types'
    );
  }

  const invalidLeaderboardConfigs = leaderboardConfigs.some(
    (leaderboardConfig) =>
      !leaderboardTypes.includes(leaderboardConfig.type)
  );

  if (invalidLeaderboardConfigs) {
    throw new ParamError('Invalid leaderboard configs');
  }

  const leaderboardConfigCache = leaderboardConfigs.reduce(
    (acc, leaderboardConfig) => {
      acc.set(leaderboardConfig.type, leaderboardConfig.enabled);
      return acc;
    },
    new Map()
  );

  const hasFastestOrSequentialLeaderboard =
    leaderboardConfigCache.get(
      LEADERBOARD_TYPE.FASTEST_CHECKPOINT_COMPLETION
    ) ||
    leaderboardConfigCache.get(
      LEADERBOARD_TYPE.THREE_SEQUENTIAL_CONSECUTIVE_ON_TIME_COMPLETION
    );

  const hasCompletionLeaderboard = leaderboardConfigCache.get(
    LEADERBOARD_TYPE.COMPLETION
  );

  if (hasFastestOrSequentialLeaderboard && !hasCompletionLeaderboard) {
    throw new ParamError('Invalid leaderboard configs');
  }

  const hasCelebrationLeaderboard = leaderboardConfigCache.get(
    LEADERBOARD_TYPE.CELEBRATION_ON_FEED
  );

  const hasCommentLeaderboard = leaderboardConfigCache.get(
    LEADERBOARD_TYPE.COMMENT_ON_FEED
  );

  if (hasCelebrationLeaderboard !== hasCommentLeaderboard) {
    throw new ParamError('Invalid leaderboard configs');
  }
}

const validateAndGetUpdatePayload = async ({
  payload,
  currentProgram,
  community,
  updatedBy,
}) => {
  const toDBPayload = {
    updatedBy: updatedBy.email,
  };

  if (
    payload.enableCheckpointSubmissionAfterDeadline ||
    payload.enableCheckpointSubmissionAfterDeadline === false
  ) {
    toDBPayload[
      'challengeSpecific.enableCheckpointSubmissionAfterDeadline'
    ] = payload.enableCheckpointSubmissionAfterDeadline;
  }

  if (payload.title) {
    if (payload.title.trim().length > 100) {
      throw new ParamError('Title is too long. Limit is 100 characters.');
    }
    toDBPayload.title = payload.title;
  }
  if (payload.description) {
    toDBPayload.description = payload.description;
  }
  if (payload.descriptionHTML) {
    toDBPayload.descriptionHTML = payload.descriptionHTML;
  }
  if (payload.cover) {
    toDBPayload.cover = payload.cover;
  }
  if (
    Array.isArray(payload.coverMediaItems) &&
    payload.coverMediaItems.length
  ) {
    const coverMediaItemsUpdateData =
      await getUpdateDataForMultipleCoverMediaItems({
        communityId: currentProgram.communityObjectId,
        entityType: COVER_MEDIA_ENTITY_TYPES.CHALLENGE,
        coverMediaItems: payload.coverMediaItems,
      });

    Object.assign(toDBPayload, coverMediaItemsUpdateData);
  }
  if (payload.access) {
    toDBPayload.access = payload.access;
  }
  if (payload.status) {
    toDBPayload.status = payload.status;
  }
  if (payload.hostEmail) {
    toDBPayload.hostEmail = payload.hostEmail;
  }
  if (payload.canJoinAfterStart !== undefined) {
    toDBPayload.canJoinAfterStart = payload.canJoinAfterStart;
  }

  if (payload.hideParticipantCountOnLP !== undefined) {
    toDBPayload['additionalSettings.hideParticipantCountOnLP'] =
      payload.hideParticipantCountOnLP;
  }
  const { startTime, endTime } = validateSchedule({
    payload,
    currentProgram,
  });
  if (startTime && endTime) {
    toDBPayload.startTime = startTime;
    toDBPayload.endTime = endTime;
    toDBPayload.durationInDays = getProgramDurationInDays({
      startTime,
      endTime,
    });
  }

  if (payload.slug) {
    const finalSlug = payload.slug
      .toLowerCase()
      .replace(CLEAN_SLUG_REGEX, '-');
    if (finalSlug !== currentProgram.slug) {
      await validateSlug({ slug: finalSlug, community });
      await checkActiveCampaignForSlugChange(
        community._id,
        currentProgram._id
      );
      toDBPayload.slug = finalSlug;
    }
  }

  if (payload.chatGroupLink && payload.chatGroupLink.trim() !== '') {
    validateChatGroupLink(payload.chatGroupLink);
    toDBPayload['additionalSettings.chatGroupLink'] =
      payload.chatGroupLink;
  } else if (payload.chatGroupLink === '') {
    toDBPayload['additionalSettings.chatGroupLink'] = null;
  }

  if (payload.instructions) {
    toDBPayload['additionalSettings.instructions'] = payload.instructions;
  }
  if (payload.instructionsHTML) {
    toDBPayload['additionalSettings.instructionsHTML'] =
      payload.instructionsHTML;
  }
  if (payload.rewards) {
    toDBPayload['additionalSettings.rewards'] = payload.rewards;
  }
  if (payload.rewardsHTML) {
    toDBPayload['additionalSettings.rewardsHTML'] = payload.rewardsHTML;
  }

  entityCurrencyUtil.validateEntityCurrencyWithCommunityBaseCurrency(
    payload.pricingCurrency,
    community.baseCurrency
  );

  const pricing = await pricingService.validateAndFormatPricing(
    payload,
    false,
    currentProgram,
    community
  );

  if (pricing) {
    toDBPayload.pricingConfig = {
      ...pricing.pricingConfig,
      amount: pricing.amount,
      currency: pricing.currency,
    };
    toDBPayload.access = pricing.access;
  }

  await discountValidationForEntities(
    community.code,
    payload.newDiscountsToApply,
    payload.existingDiscountsToAdd
  );

  if (payload.leaderboardConfig) {
    if (payload.leaderboardConfig.enabled === undefined) {
      throw new ParamError('enabled is required');
    }
    if (payload.leaderboardConfig.type === undefined) {
      throw new ParamError('type is required');
    }
    toDBPayload.leaderboardConfig = payload.leaderboardConfig;
    if (
      payload.leaderboardConfig.enabled === true &&
      payload.leaderboardConfig.type ===
        LEADERBOARD_TYPE.FASTEST_CHECKPOINT_COMPLETION
    ) {
      toDBPayload['additionalSettings.trackResponseTimePoints'] = true;
    }

    if (!payload.leaderboardConfigs) {
      const enabled = payload.leaderboardConfig.enabled;

      toDBPayload.leaderboardConfigs = [
        { type: LEADERBOARD_TYPE.FASTEST_CHECKPOINT_COMPLETION, enabled },
        {
          type: LEADERBOARD_TYPE.THREE_SEQUENTIAL_CONSECUTIVE_ON_TIME_COMPLETION,
          enabled: false,
        },
        { type: LEADERBOARD_TYPE.COMPLETION, enabled },
        { type: LEADERBOARD_TYPE.CELEBRATION_ON_FEED, enabled: false },
        { type: LEADERBOARD_TYPE.COMMENT_ON_FEED, enabled: false },
      ];
    }
  }

  if (payload.leaderboardConfigs) {
    validateLeaderboardConfigs(payload.leaderboardConfigs);

    toDBPayload.leaderboardConfigs = payload.leaderboardConfigs;
  }

  if (payload.feedConfig) {
    if (payload.feedConfig.enabled === undefined) {
      throw new ParamError('enabled is required');
    }
    toDBPayload.feedConfig = payload.feedConfig;
  }

  if (payload.coverVideo) {
    // verify if there is a media item with the given id

    const { mediaObjectId } = payload.coverVideo || {};

    if (mediaObjectId) {
      const mediaItem = await communityFolderItemsModel.findOne({
        _id: new ObjectId(mediaObjectId),
      });

      if (mediaItem) {
        toDBPayload.coverVideo = {
          mediaObjectId: String(mediaItem._id),
        };
      }
    } else if (!mediaObjectId) {
      // delete the cover video
      toDBPayload.coverVideo = {};
      await communityFolderItemsModel.updateOne(
        {
          communityFolderObjectId: currentProgram._id,
          isCoverVideo: true,
        },
        {
          $set: {
            status: communityLibraryStatusMap.DELETED,
          },
        }
      );
    }
  }
  if (currentProgram.type === PROGRAM_TYPE.CHALLENGE) {
    if (payload.checkpointDurationInDays) {
      validateCheckpointDuration(
        payload.checkpointDurationInDays,
        toDBPayload.durationInDays
      );
      toDBPayload['challengeSpecific.checkpointDurationInDays'] =
        payload.checkpointDurationInDays;
    }
    if (payload.stepByStepUnlocking !== undefined) {
      toDBPayload['challengeSpecific.stepByStepUnlocking'] =
        payload.stepByStepUnlocking;
    }
  }

  if (
    currentProgram.challengeType === PROGRAM_CHALLENGE_TYPE.ALWAYS_ON &&
    toDBPayload.status === PROGRAM_STATUS.PUBLISHED
  ) {
    toDBPayload.scheduleStatus = PROGRAM_SCHEDULE_STATUS.ONGOING;
  }
  return toDBPayload;
};

async function applyDiscounts({
  communityCode,
  createdBy,
  oldProgram,
  updatedProgram,
  payload,
  programType,
}) {
  const purchaseType = programType.toUpperCase();
  const updatedProgramWithDiscount = await discountCreationForEntities(
    communityCode,
    oldProgram,
    updatedProgram,
    purchaseType,
    createdBy,
    Program,
    payload.newDiscountsToApply,
    payload.currentDiscountsToRemove,
    payload.existingDiscountsToApply,
    payload.currentDiscountsToDisable
  );
  return updatedProgramWithDiscount;
}

const generateChallengeCheckpoints = async ({
  checkpointDurationInDays,
  oldCheckpointDurationInDays,
  challengeDurationInDays,
  startTime,
  programObjectId,
  session,
}) => {
  const results = {};
  const currentCheckpoints = await getProgramItemsService.getProgramItems({
    programId: programObjectId,
    type: PROGRAM_ITEM_TYPE.CHECKPOINT,
    filters: {
      isWelcomeCheckpoint: { $ne: true },
    },
  });
  const desirableCheckpointCount = Math.floor(
    challengeDurationInDays / checkpointDurationInDays
  );

  if (currentCheckpoints.length > desirableCheckpointCount) {
    const checkpointsToDelete = currentCheckpoints.slice(
      desirableCheckpointCount
    );
    const checkpointIdsToDelete = checkpointsToDelete.map(
      (checkpoint) => checkpoint._id
    );
    await ProgramItem.deleteMany(
      {
        _id: {
          $in: checkpointIdsToDelete,
        },
      },
      {
        session,
      }
    );
    results.deleted = checkpointsToDelete.length;
  }

  const operations = ProgramItem.collection.initializeUnorderedBulkOp();
  let checkpointStartTime = startTime;

  let checkpointIndex = 1;
  while (checkpointIndex <= desirableCheckpointCount) {
    const $set = {
      type: 'checkpoint',
      startTime: checkpointStartTime,
      endTime: checkpointStartTime.plus({
        days: checkpointDurationInDays,
      }),
      status: PROGRAM_ITEM_STATUS.ACTIVE,
    };
    if (currentCheckpoints.length >= checkpointIndex) {
      const currentCheckpoint = currentCheckpoints[checkpointIndex - 1];
      const defaultOldTitle = `${getDefaultCheckpointPrefix(
        oldCheckpointDurationInDays ?? checkpointDurationInDays
      )} ${checkpointIndex}`;
      if (currentCheckpoint.title === defaultOldTitle) {
        $set.title = `${getDefaultCheckpointPrefix(
          checkpointDurationInDays
        )} ${checkpointIndex}`;
      }
    } else {
      $set.title = `${getDefaultCheckpointPrefix(
        checkpointDurationInDays
      )} ${checkpointIndex}`;
      $set.submissionRequired = false;
    }

    operations
      .find({
        programObjectId,
        index: checkpointIndex,
      })
      .upsert()
      .updateOne({ $set });
    checkpointStartTime = checkpointStartTime.plus({
      days: checkpointDurationInDays,
    });
    checkpointIndex += 1;
  }
  const operationResults = await operations.execute({
    session,
    readPreference: 'primary',
  });
  results.inserted = operationResults.insertedCount;
  results.upserted = operationResults.upsertedCount;
  results.modified = operationResults.modifiedCount;
  results.checkpointCount = desirableCheckpointCount;
  return results;
};

const generateDefaultAlwaysOnChallengeCheckpoints = async ({
  programObjectId,
  programTitle,
  templateLibrary = null,
  session,
}) => {
  const defaultCheckpointsToGenerate =
    templateLibrary?.metadata?.checkpoints?.length ?? 3;

  // Default checkpoints for always on challenge
  // 1. Welcome to {challenge title}
  // 2. Checkpoint 1
  // 3. Checkpoint 2
  const operations = ProgramItem.collection.initializeUnorderedBulkOp({
    readPreference: 'primary',
  });
  let checkpointIndex = 1;

  let $set = {
    type: 'checkpoint',
    unlockAfterXDays: 0,
    title: `Welcome to ${programTitle}`,
    submissionRequired: false,
    status: PROGRAM_ITEM_STATUS.ACTIVE,
  };
  operations.insert({
    programObjectId,
    index: checkpointIndex,
    ...$set,
  });

  checkpointIndex += 1;

  while (checkpointIndex <= defaultCheckpointsToGenerate) {
    $set = {
      type: 'checkpoint',
      title: `Checkpoint ${checkpointIndex - 1}`,
      unlockAfterXDays: checkpointIndex - 1,
      status: PROGRAM_ITEM_STATUS.ACTIVE,
    };
    operations.insert({
      programObjectId,
      index: checkpointIndex,
      ...$set,
    });
    checkpointIndex += 1;
  }
  const operationResults = await operations.execute({ session });
  if (operationResults.insertedCount !== defaultCheckpointsToGenerate) {
    throw new DBError(
      `Invalid insert count for always on challenge checkpoints`
    );
  }

  const challengeCheckpoints =
    await getProgramItemsService.getProgramItems({
      programId: programObjectId,
      type: PROGRAM_ITEM_TYPE.CHECKPOINT,
    });
  return challengeCheckpoints;
};

// eslint-disable-next-line no-unused-vars
async function addShortUrl({ community, program }) {
  const shortUrlData = await shortUrlService.addShortUrl(
    program.title,
    community.link,
    // TODO: Need to adjust link if this function becomes relevant again
    `${FRONTEND_APP_LINK}/communities/${community._id}/programs?id=${program._id}`
  );
  return shortUrlData;
}

// eslint-disable-next-line no-unused-vars
async function checkProgramForFraud({
  updatedPayload,
  community,
  programType,
  programId,
}) {
  try {
    let content = '';
    let contentSource = '';
    if (updatedPayload.title) {
      content = updatedPayload.title;
      contentSource = 'title';
    }
    if (updatedPayload.description) {
      content = `${content}, ${fraudService.extractLexicalTextForFraud(
        updatedPayload.description.root
      )}`;
      contentSource = `${contentSource} & description`;
    }
    if (updatedPayload.additionalSettings?.instructions) {
      content = `${content}, ${fraudService.extractLexicalTextForFraud(
        updatedPayload.additionalSettings.instructions.root
      )}`;
      contentSource = `${contentSource} & instructions`;
    }
    if (updatedPayload.additionalSettings?.rewards) {
      content = `${content}, ${fraudService.extractLexicalTextForFraud(
        updatedPayload.additionalSettings.rewards.root
      )}`;
      contentSource = `${contentSource} & rewards`;
    }
    const fraudEngine = new fraudService.FraudEngine({
      communityId: community._id,
      eventName: fraudService.INTERESTED_EVENTS.UPDATE_CONTENT,
      entityType: programType,
      entityId: programId,
      data: {
        content,
        contentSource,
      },
      checksToPerform: [fraudService.COMMON_FRAUD_CHECKS.FREE_INPUT],
      autoConsequencesToApply: [
        fraudService.COMMON_CONSEQUENCES.RESTRICT_CHECKOUT,
        fraudService.COMMON_CONSEQUENCES.RESTRICT_CUSTOM_EMAIL,
        fraudService.COMMON_CONSEQUENCES.RESTRICT_MAGIC_REACH,
        fraudService.COMMON_CONSEQUENCES.NOT_INDEXABLE,
        fraudService.COMMON_CONSEQUENCES.RECOMMEND_DEACTIVATE,
      ],
    });
    await fraudEngine.performCheck();
  } catch (error) {
    logger.error('Error in fraud check:', error.message, error.stack);
  }
}

const createProgram = async ({
  community,
  programPayload,
  programType = PROGRAM_TYPE.CHALLENGE,
  createdBy,
  createCheckpoints = true,
}) => {
  await AiCofounderProductCreationService.checkProductCreationEligibility(
    community._id,
    programPayload.templateLibraryId,
    community
  );

  const toDBPayload = await validateAndGetCreatePayload({
    payload: programPayload,
    programType,
    community,
    createdBy,
  });

  const [slug, templateLibrary] = await Promise.all([
    getSlug({
      title: programPayload.title,
      community,
    }),
    programPayload.templateLibraryId
      ? TemplateLibraryModel.findOne({
          communityObjectId: community._id,
          _id: programPayload.templateLibraryId,
        }).lean()
      : null,
  ]);

  toDBPayload.slug = slug;

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();
  let program;
  let challengeCheckpoints;
  try {
    const createdPrograms = await Program.create([toDBPayload], {
      session,
    });
    program = createdPrograms[0]?.toObject();

    await SyncProductDataService.syncProductData({
      productType: PRODUCT_TYPE.CHALLENGE,
      entity: program,
      session,
    });

    if (hasVideoCoverMediaItems(program.coverMediaItems)) {
      await verifyVideoCoverMediaItems({
        entityObjectId: program._id,
        coverMediaItems: program.coverMediaItems,
      });
    }

    if (programType === PROGRAM_TYPE.CHALLENGE) {
      if (programPayload.checkpointDurationInDays) {
        const results = await generateChallengeCheckpoints({
          checkpointDurationInDays:
            programPayload.checkpointDurationInDays,
          startTime: toDBPayload.startTime,
          challengeDurationInDays: toDBPayload.durationInDays,
          programObjectId: program._id,
          session,
        });
        if (results.upserted > 0) {
          challengeCheckpoints =
            await getProgramItemsService.getProgramItems({
              programId: program._id,
              type: PROGRAM_ITEM_TYPE.CHECKPOINT,
            });
        }
      } else if (
        programPayload.challengeType ===
          PROGRAM_CHALLENGE_TYPE.ALWAYS_ON &&
        // Because both create and duplicate challenge will call same function here
        // we will use this flag to determine if need to create new checkpoints
        createCheckpoints
      ) {
        challengeCheckpoints =
          await generateDefaultAlwaysOnChallengeCheckpoints({
            programObjectId: program._id,
            programTitle: program.title,
            templateLibrary,
            session,
          });
      }
    }
    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
  if (program.access === ACCESS_TYPE.PAID) {
    program = await applyDiscounts({
      communityCode: community.code,
      createdBy,
      oldProgram: null,
      updatedProgram: program,
      payload: programPayload,
      programType,
    });
  }
  if (challengeCheckpoints) {
    program.checkpoints = challengeCheckpoints;
  }

  // to move to worker pool
  checkProgramForFraud({
    updatedPayload: toDBPayload,
    community,
    programType,
    programId: program._id.toString(),
  });
  return program;
};

function hasProgramScheduleChange({ newDBPayload, currentProgram }) {
  if (!newDBPayload.startTime || !newDBPayload.endTime) {
    return false;
  }
  if (!currentProgram.startTime || !currentProgram.endTime) {
    return true;
  }
  const scheduledChange =
    newDBPayload.startTime.toMillis() !==
      DateTime.fromJSDate(currentProgram.startTime).toMillis() ||
    newDBPayload.endTime.toMillis() !==
      DateTime.fromJSDate(currentProgram.endTime).toMillis();
  return scheduledChange;
}

const updateProgram = async ({
  programId,
  programType = PROGRAM_TYPE.CHALLENGE,
  community,
  programPayload,
  updatedBy,
}) => {
  const [program, publishedProgramsCount] = await Promise.all([
    commonService.getProgram(programId),
    Program.countDocuments({
      communityObjectId: community._id,
      status: PROGRAM_STATUS.PUBLISHED,
    }),
  ]);
  const oldStatus = program.status;
  if (oldStatus === PROGRAM_STATUS.DELETED) {
    throw new ParamError('Challenge is already deleted');
  }

  let changeLogId;
  const [toDBPayload] = await Promise.all([
    validateAndGetUpdatePayload({
      payload: programPayload,
      currentProgram: program,
      community,
      updatedBy,
    }),
    oldStatus !== PROGRAM_STATUS.PUBLISHED &&
    programPayload.status === PROGRAM_STATUS.PUBLISHED
      ? PublishProductUsageService.checkPublishProductLimit(
          community._id,
          PURCHASE_TYPE.CHALLENGE,
          program.createdAt,
          community
        )
      : null,
  ]);

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();
  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();
  let updatedProgram;
  let checkpointCount;
  const hasScheduleChange =
    programPayload.checkpointDurationInDays ||
    hasProgramScheduleChange({
      newDBPayload: toDBPayload,
      currentProgram: program,
    });
  try {
    updatedProgram = await Program.findOneAndUpdate(
      {
        _id: programId,
      },
      {
        $set: toDBPayload,
      },
      {
        new: true,
        session,
      }
    ).lean();

    // Track field changes for challenge title updates
    const isTitleUpdated = program.title !== updatedProgram.title;
    if (isTitleUpdated) {
      const changeLog =
        await ProductChangeLogService.addCommunityProductLog({
          communityObjectId: community._id,
          communityCode: community.code,
          productType: PRODUCT_TYPE.CHALLENGE,
          entityObjectId: programId,
          changeLogType: PRODUCT_CHANGE_LOG_TYPE.FIELD_UPDATED,
          fieldsChanged: ['title'],
          beforeData: program,
          afterData: updatedProgram,
          operatorLearnerObjectId:
            updatedBy?.learner?._id || updatedBy?._id,
          metadata: {
            previous_title: program.title,
          },
        });
      // Ensure changeLogId is properly set on the returned object
      changeLogId = changeLog._id;
    }

    const product = await SyncProductDataService.syncProductData({
      productType: PRODUCT_TYPE.CHALLENGE,
      entity: updatedProgram,
      session,
    });

    if (programType === PROGRAM_TYPE.CHALLENGE) {
      if (toDBPayload.coverMediaItems) {
        // delete removed video cover media items by marking folder item status as deleted.
        const oldCoverMediaItems = program?.coverMediaItems ?? [];
        const newCoverMediaItems = updatedProgram?.coverMediaItems ?? [];
        if (
          Array.isArray(oldCoverMediaItems) &&
          hasVideoCoverMediaItems(oldCoverMediaItems)
        ) {
          await deleteRemovedVideoCoverMediaItems({
            oldCoverMediaItems,
            newCoverMediaItems,
            communityId: community._id,
          });
        }
      }

      if (
        [PROGRAM_STATUS.DRAFT, PROGRAM_STATUS.DELETED].includes(
          toDBPayload.status
        )
      ) {
        await affiliateProductService.disableAffiliateProduct({
          communityObjectId: community._id,
          entityType: PURCHASE_TYPE.CHALLENGE,
          entityObjectId: programId,
          session,
        });

        if (toDBPayload.status === PROGRAM_STATUS.DELETED) {
          // update video folder item status to deleted
          const coverMediaItems = updatedProgram?.coverMediaItems ?? [];
          if (hasVideoCoverMediaItems(coverMediaItems)) {
            await deleteCoverMediaItems({
              coverMediaItems: coverMediaItems ?? [],
              session,
              communityId: community._id,
            });
          }
          // Add product deletion log for challenge deletion
          if (program.challengeType === PROGRAM_CHALLENGE_TYPE.FIXED) {
            const changeLog =
              await ProductChangeLogService.addCommunityProductLog({
                communityObjectId: community._id,
                communityCode: community.code,
                productType: PRODUCT_TYPE.CHALLENGE,
                entityObjectId: programId,
                changeLogType: PRODUCT_CHANGE_LOG_TYPE.PRODUCT_DELETED,
                operatorLearnerObjectId:
                  updatedBy?.learner?._id || updatedBy?._id,
                metadata: { product },
              });
            changeLogId = changeLog._id;
          }
        }
      }

      if (
        hasScheduleChange &&
        program.challengeType === PROGRAM_CHALLENGE_TYPE.FIXED
      ) {
        const results = await generateChallengeCheckpoints({
          checkpointDurationInDays:
            programPayload.checkpointDurationInDays ||
            program.challengeSpecific.checkpointDurationInDays,
          oldCheckpointDurationInDays:
            program.challengeSpecific.checkpointDurationInDays,
          challengeDurationInDays: toDBPayload.durationInDays,
          programObjectId: new ObjectId(programId),
          startTime: toDBPayload.startTime,
          session,
        });
        checkpointCount = results.checkpointCount;
      }
    }

    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }

  if (toDBPayload.leaderboardConfigs) {
    await commonService.sendLeaderboardConfigsChangesToQueue({
      communityObjectId: community._id,
      programObjectId: programId,
      leaderboardConfigs: toDBPayload.leaderboardConfigs,
    });
  }

  if (
    toDBPayload.status !== PROGRAM_STATUS.DELETED &&
    program.access === ACCESS_TYPE.PAID
  ) {
    updatedProgram = await applyDiscounts({
      communityCode: community.code,
      createdBy: updatedBy,
      oldProgram: program,
      updatedProgram,
      payload: programPayload,
      programType,
    });
  } else if (updatedProgram.discountsApplied?.length > 0) {
    updatedProgram.discountsApplied =
      await commonService.getDiscountDetails(
        updatedProgram.discountsApplied
      );
  }

  if (checkpointCount > 0) {
    updatedProgram.checkpoints =
      await getProgramItemsService.getProgramItems({
        programId,
        type: PROGRAM_ITEM_TYPE.CHECKPOINT,
        offset: 0,
        limit: checkpointCount,
      });
  }

  if (updatedProgram.coverVideo) {
    const { mediaObjectId } = updatedProgram.coverVideo || {};

    if (mediaObjectId) {
      updatedProgram.coverVideo = await commonService.getCoverVideo({
        programObjectId: programId,
        communityObjectId: community._id,
        coverVideo: updatedProgram.coverVideo,
      });
    }
  }

  if (updatedProgram.coverMediaItems) {
    updatedProgram.coverMediaItems = await generateCoverMediaItems({
      entity: program,
      entityType: COVER_MEDIA_ENTITY_TYPES.CHALLENGE,
      isCommunityManager: true,
    });
  }

  if (program.type === PROGRAM_TYPE.CHALLENGE) {
    updatedProgram.challengeSpecific.checkpointCount =
      await ProgramItem.countDocuments({
        programObjectId: programId,
        type: PROGRAM_ITEM_TYPE.CHECKPOINT,
      });
  }

  const isStatusUpdated = oldStatus !== updatedProgram.status;
  let purgeCommunityLandingPage = false;
  if (isStatusUpdated) {
    const isPublishAction =
      updatedProgram.status === PROGRAM_STATUS.PUBLISHED;

    purgeCommunityLandingPage = isPublishAction
      ? publishedProgramsCount === 0 // i.e first challenge to be published in the community ( for community landing page tabs )
      : publishedProgramsCount === 1; // i.e unpublishing the only published challenge ( for community landing page tabs )
  }

  await purgeEntityLandingPageCache({
    community,
    purgeCommunityLandingPage,
    entityType: ENTITY_LANDING_PAGE.CHALLENGE,
    entitySlug: program.slug,
  });

  if (
    (oldStatus !== PROGRAM_STATUS.PUBLISHED &&
      updatedProgram.status === PROGRAM_STATUS.PUBLISHED) ||
    (updatedProgram.status === PROGRAM_STATUS.PUBLISHED &&
      hasScheduleChange)
  ) {
    await addCheckpointEmailSchedules({
      challengeId: programId,
      community,
      program: updatedProgram,
    });
    await addChallengeAndCheckpointNotificationSchedule({
      community,
      program: updatedProgram,
    });

    // Add product change log for challenge publish
    const changeLog = await ProductChangeLogService.addCommunityProductLog(
      {
        communityObjectId: community._id,
        communityCode: community.code,
        entityObjectId: programId,
        productType: PRODUCT_TYPE.CHALLENGE,
        changeLogType: PRODUCT_CHANGE_LOG_TYPE.PRODUCT_PUBLISHED,
        operatorLearnerObjectId: updatedBy?.learner?._id || updatedBy?._id,
      }
    );
    changeLogId = changeLog._id;
  }
  if (updatedProgram.status === PROGRAM_STATUS.PUBLISHED) {
    // to move to worker pool
    checkProgramForFraud({
      updatedPayload: updatedProgram,
      community,
      programType: updatedProgram.type,
      programId: updatedProgram._id.toString(),
    });
  }
  return { ...updatedProgram, changeLogId };
};

async function createAndDuplicateChallengeDetails({
  community,
  sourceChallenge,
  duplicatedBy,
  startTime,
  targetChallengeType,
}) {
  const createProgramPayload = {
    title: `${sourceChallenge.title} (Copy)`,
    cover: sourceChallenge.cover,
    access: sourceChallenge.access,
    status: PROGRAM_STATUS.DRAFT,
    pricingAmount: sourceChallenge.pricingConfig.amount,
    pricingCurrency: sourceChallenge.pricingConfig.currency,
    priceType: sourceChallenge.pricingConfig.priceType,
    suggestedAmount: sourceChallenge.pricingConfig.suggestedAmount,
    minAmount: sourceChallenge.pricingConfig.minAmount,
    challengeType: targetChallengeType ?? sourceChallenge.challengeType,
    leaderboardConfigs: sourceChallenge.leaderboardConfigs,
  };

  let newProgram = await createProgram({
    community,
    programPayload: createProgramPayload,
    programType: sourceChallenge.type,
    createdBy: duplicatedBy,
    createCheckpoints: false,
  });

  let endTime;
  if (newProgram.challengeType === PROGRAM_CHALLENGE_TYPE.FIXED) {
    const now = DateTime.utc();

    if (!startTime) {
      // eslint-disable-next-line no-param-reassign
      startTime = now.plus({ days: 1 }).startOf('hour').plus({ hours: 1 });
    }
    endTime = startTime.plus({
      days: sourceChallenge.durationInDays,
    });
  }

  const updateProgramPayload = {
    description: sourceChallenge.description,
    descriptionHTML: sourceChallenge.descriptionHTML,
    chatGroupLink: sourceChallenge.additionalSettings?.chatGroupLink,
    instructions: sourceChallenge.additionalSettings?.instructions,
    instructionsHTML: sourceChallenge.additionalSettings?.instructionsHTML,
    rewards: sourceChallenge.additionalSettings?.rewards,
    rewardsHTML: sourceChallenge.additionalSettings?.rewardsHTML,
    enableCheckpointSubmissionAfterDeadline:
      sourceChallenge.challengeSpecific
        ?.enableCheckpointSubmissionAfterDeadline,
    startTime: startTime ? startTime.toJSDate() : null,
    endTime: endTime ? endTime.toJSDate() : null,
  };

  if (sourceChallenge.type === PROGRAM_TYPE.CHALLENGE) {
    updateProgramPayload.checkpointDurationInDays =
      sourceChallenge.challengeSpecific.checkpointDurationInDays;
    updateProgramPayload.stepByStepUnlocking =
      sourceChallenge.challengeSpecific.stepByStepUnlocking;
  }
  newProgram = await updateProgram({
    programId: newProgram._id,
    programType: sourceChallenge.type,
    community,
    programPayload: updateProgramPayload,
    updatedBy: duplicatedBy,
  });

  return newProgram;
}

async function duplicateCheckpoints({ sourceChallenge, newChallenge }) {
  const sourceCheckpoints = await getProgramItemsService.getProgramItems({
    programId: sourceChallenge._id,
    type: PROGRAM_ITEM_TYPE.CHECKPOINT,
    projection: {
      index: 1,
      title: 1,
      description: 1,
      submissionRequired: 1,
      type: 1,
      coverVideo: 1,
      submissionQuestions: 1,
      isEmpty: 1,
      attachmentItems: 1,
      unlockAfterXDays: 1,
      isWelcomeCheckpoint: 1,
    },
  });

  // check welcome checkpoint
  const welcomeCheckpoint = sourceCheckpoints.find(
    (checkpoint) => checkpoint?.isWelcomeCheckpoint
  );
  let newWelcomeCheckpoint = {};

  // To prevent there is no array to unshift for welcome checkpoint
  if (!newChallenge.checkpoints) {
    // eslint-disable-next-line no-param-reassign
    newChallenge.checkpoints = [];
  }

  // if welcomecheckpoint exists in source challenge then we need to create it for the new challenge
  // and add it to the new challenge checkpoints
  if (welcomeCheckpoint) {
    newWelcomeCheckpoint = {
      ...welcomeCheckpoint,
      programObjectId: newChallenge._id,
      communityObjectId: newChallenge.communityObjectId,
      isWelcomeCheckpoint: true,
    };
    delete newWelcomeCheckpoint._id;
    delete newWelcomeCheckpoint.createdAt;
    delete newWelcomeCheckpoint.updatedAt;
    delete newWelcomeCheckpoint.__v;
    newWelcomeCheckpoint = await ProgramItem.create(newWelcomeCheckpoint);
    newWelcomeCheckpoint = newWelcomeCheckpoint.toObject();
    // add it in the beginning of the new challenge checkpoints
    newChallenge.checkpoints.unshift(newWelcomeCheckpoint);
  }
  let destinationCheckpoints = newChallenge.checkpoints;

  if (newChallenge.challengeType === PROGRAM_CHALLENGE_TYPE.ALWAYS_ON) {
    const copyCheckpoints = (checkpointList) => {
      const newCheckpoints = [];
      checkpointList.forEach((checkpoint, index) => {
        let unlockAfterXDays = checkpoint.unlockAfterXDays;
        if (
          sourceChallenge.challengeType === PROGRAM_CHALLENGE_TYPE.FIXED
        ) {
          unlockAfterXDays =
            sourceChallenge.challengeSpecific.checkpointDurationInDays *
            (index + 1);
        }
        newCheckpoints.push({
          // Generate a new unique ID (replace with your method to generate IDs)
          index: index + 1,
          title: checkpoint.title,
          type: checkpoint.type,
          unlockAfterXDays,
          programObjectId: newChallenge._id,
          communityObjectId: newChallenge.communityObjectId,
        });
      });
      return newCheckpoints;
    };

    destinationCheckpoints = copyCheckpoints(sourceCheckpoints);
    destinationCheckpoints = await ProgramItem.create(
      destinationCheckpoints
    );
  }

  const currentCoverMediaItemsIds = [];
  const currentAttachmentMediaItemsIds = [];
  sourceCheckpoints.forEach((checkpoint) => {
    if (checkpoint.coverVideo?.mediaObjectId) {
      currentCoverMediaItemsIds.push(checkpoint.coverVideo.mediaObjectId);
    }
    if (checkpoint.attachmentItems?.length > 0) {
      checkpoint.attachmentItems.forEach((attachmentItem) => {
        if (attachmentItem.mediaObjectId) {
          currentAttachmentMediaItemsIds.push(
            attachmentItem.mediaObjectId
          );
        }
      });
    }
  });

  const currentAttachmentMediaItems = await CommunityFolderItems.find({
    _id: { $in: currentAttachmentMediaItemsIds },
  }).lean();

  const currentCoverMediaItems = await CommunityFolderItems.find({
    _id: { $in: currentCoverMediaItemsIds },
  }).lean();
  const currentCoverMediaItemsMap = currentCoverMediaItems.reduce(
    (acc, item) => {
      acc[item._id.toString()] = item;
      return acc;
    },
    {}
  );

  const currentAttachmentMediaItemsMap =
    currentAttachmentMediaItems.reduce((acc, item) => {
      acc[item._id.toString()] = item;
      return acc;
    }, {});
  const newCoverMediaItems = [];
  const newAttachmentMediaItems = [];
  sourceCheckpoints.forEach((checkpoint, index) => {
    if (
      checkpoint.coverVideo?.mediaObjectId &&
      currentCoverMediaItemsMap[checkpoint.coverVideo.mediaObjectId]
    ) {
      const currentMediaItem =
        currentCoverMediaItemsMap[checkpoint.coverVideo.mediaObjectId];
      const newMediaItem = { ...currentMediaItem };
      newMediaItem.communityObjectId = newChallenge.communityObjectId;
      delete newMediaItem._id;
      delete newMediaItem.createdAt;
      delete newMediaItem.updatedAt;
      delete newMediaItem.__v;
      newMediaItem.index = checkpoint.index;
      newMediaItem.communityFolderObjectId =
        destinationCheckpoints[index]._id;
      newCoverMediaItems.push(newMediaItem);
    }

    if (checkpoint.attachmentItems?.length > 0) {
      checkpoint.attachmentItems.forEach((attachmentItem) => {
        if (
          attachmentItem.mediaObjectId &&
          currentAttachmentMediaItemsMap[attachmentItem.mediaObjectId]
        ) {
          const currentMediaItem =
            currentAttachmentMediaItemsMap[attachmentItem.mediaObjectId];
          const newAttachmentMediaItem = { ...currentMediaItem };
          delete newAttachmentMediaItem._id;
          delete newAttachmentMediaItem.createdAt;
          delete newAttachmentMediaItem.updatedAt;
          delete newAttachmentMediaItem.__v;
          newAttachmentMediaItem.index = checkpoint.index;
          newAttachmentMediaItem.communityFolderObjectId =
            destinationCheckpoints[index]._id;
          newAttachmentMediaItems.push(newAttachmentMediaItem);
        }
      });
    }
  });

  const newCoverMediaItemsCreated = await CommunityFolderItems.create(
    newCoverMediaItems
  );

  const newAttachmentMediaItemsCreated = await CommunityFolderItems.create(
    newAttachmentMediaItems
  );

  const newCoverMediaItemsMap = newCoverMediaItemsCreated.reduce(
    (acc, item) => {
      acc[item.communityFolderObjectId.toString()] = item._id;
      return acc;
    },
    {}
  );

  const newAttachmentMediaItemsMap = new Map();

  if (newAttachmentMediaItemsCreated?.length > 0) {
    newAttachmentMediaItemsCreated.forEach((attachmentItem) => {
      const folderId = attachmentItem.communityFolderObjectId.toString();

      // Initialize the array for this folderId if it doesn't exist
      const mediaItems = newAttachmentMediaItemsMap.get(folderId) || [];

      // Add the attachmentItem to the array
      mediaItems.push(attachmentItem);

      // Update the map with the modified array
      newAttachmentMediaItemsMap.set(folderId, mediaItems);
    });
  }

  const operations = ProgramItem.collection.initializeUnorderedBulkOp();
  sourceCheckpoints.forEach((checkpoint, index) => {
    const newCheckpointId = destinationCheckpoints[index]._id;
    const set = {
      description: checkpoint.description,
      submissionRequired: checkpoint.submissionRequired,
      type: checkpoint.type,
      title: checkpoint.title,
      submissionQuestions: checkpoint.submissionQuestions,
      isEmpty: checkpoint.isEmpty,
    };
    if (newCoverMediaItemsMap[newCheckpointId.toString()]) {
      set.coverVideo = {
        mediaObjectId: newCoverMediaItemsMap[newCheckpointId.toString()],
      };
    }

    if (newAttachmentMediaItemsMap.get(newCheckpointId?.toString())) {
      set.attachmentItems = newAttachmentMediaItemsMap
        .get(newCheckpointId.toString())
        .map((attachmentItem) => {
          return {
            mediaObjectId: attachmentItem._id,
          };
        });
    }

    operations
      .find({
        _id: newCheckpointId,
      })
      .updateOne({
        $set: set,
      });
  });
  await operations.execute();
}

async function duplicateCoverVideo({ sourceChallenge, newChallenge }) {
  const coverVideoOnSourceChallenge = sourceChallenge.coverVideo;

  if (coverVideoOnSourceChallenge?.mediaObjectId) {
    const { mediaObjectId } = coverVideoOnSourceChallenge;
    const coverVideo = await communityFolderItemsModel.findOne({
      _id: new ObjectId(mediaObjectId),
    });

    if (coverVideo) {
      const newCoverVideo = {
        ...coverVideo.toObject(),
        communityFolderObjectId: newChallenge._id,
        communityObjectId: newChallenge.communityObjectId,
      };
      delete newCoverVideo._id;
      const newCoverViewFolderItem =
        await communityFolderItemsModel.create(newCoverVideo);
      await Program.updateOne(
        {
          _id: newChallenge._id,
        },
        {
          $set: {
            'coverVideo.mediaObjectId': newCoverViewFolderItem._id,
          },
        }
      );
    }
  }

  return true;
}

async function duplicateChallengeCoverVideoItems({
  sourceChallenge,
  newChallenge,
}) {
  try {
    const newChallengeObjectId = newChallenge._id;
    const oldCoverMediaItems = sourceChallenge?.coverMediaItems ?? [];
    if (!hasVideoCoverMediaItems(oldCoverMediaItems)) {
      await Program.updateOne(
        { _id: newChallengeObjectId },
        { $set: { coverMediaItems: oldCoverMediaItems } }
      );
      return;
    }

    const videoFolderItemIds = oldCoverMediaItems.reduce((acc, item) => {
      if (item.mediaType === COVER_MEDIA_TYPES.VIDEO) {
        acc.push(item.folderItemId);
      }
      return acc;
    }, []);

    const coverVideoFolderItemsFilter = {
      _id: { $in: videoFolderItemIds },
      status: FOLDER_ITEM_STATUS.PUBLISHED,
    };

    const oldVideoFolderItems = await communityFolderItemsModel
      .find(coverVideoFolderItemsFilter)
      .lean();

    const oldToNewFolderItemMap = {};

    const newVideoFolderItemsToInsert = oldVideoFolderItems.map((item) => {
      const newFolderItem = { ...item };
      newFolderItem.communityFolderObjectId = newChallengeObjectId;
      newFolderItem.hasValidFolderObjectId = true;

      delete newFolderItem._id;
      const newFolderItemObjectId = new ObjectId();
      newFolderItem._id = newFolderItemObjectId;
      oldToNewFolderItemMap[item._id.toString()] =
        newFolderItemObjectId.toString();

      return newFolderItem;
    });

    if (newVideoFolderItemsToInsert.length) {
      await communityFolderItemsModel.insertMany(
        newVideoFolderItemsToInsert
      );
    }

    const newCoverMediaItems = oldCoverMediaItems.map((item) => {
      if (item.mediaType === COVER_MEDIA_TYPES.VIDEO) {
        return {
          ...item,
          folderItemId: oldToNewFolderItemMap[item.folderItemId],
        };
      }
      return item;
    });

    // update cover media items in new challenge
    await Program.updateOne(
      { _id: newChallengeObjectId },
      { $set: { coverMediaItems: newCoverMediaItems } }
    );
  } catch (err) {
    logger.error(
      '[Fn:duplicateChallengeCoverVideoItems] Error duplicating challenge cover video items',
      err,
      err.stack
    );
    throw err;
  }
}

const duplicateChallenge = async ({
  community,
  challengeId,
  duplicatedBy,
  targetChallengeType,
}) => {
  const sourceChallenge = await Program.findOne({
    _id: challengeId,
  }).lean();
  if (!sourceChallenge) {
    throw new ParamError('Program not found');
  }
  if (community.baseCurrency !== sourceChallenge.pricingConfig.currency) {
    sourceChallenge.pricingConfig.currency = community.baseCurrency;
  }

  logger.info('Create challenge details');
  const newChallenge = await createAndDuplicateChallengeDetails({
    community,
    sourceChallenge,
    duplicatedBy: duplicatedBy || { email: sourceChallenge.createdBy },
    targetChallengeType,
  });

  logger.info('Duplicate checkpoints');
  await duplicateCheckpoints({
    sourceChallenge,
    newChallenge,
  });
  logger.info('Duplicate cover videos');
  await duplicateCoverVideo({
    sourceChallenge,
    newChallenge,
  });
  logger.info('Duplicate cover media items');
  await duplicateChallengeCoverVideoItems({
    sourceChallenge,
    newChallenge,
  });

  return newChallenge;
};

module.exports = {
  createProgram,
  updateProgram,
  duplicateChallenge,
};
