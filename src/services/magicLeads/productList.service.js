const mongoose = require('mongoose');
const { getProducts } = require('../product/getProductData.service');
const { PRODUCT_STATUS, PRODUCT_TYPE } = require('../product/constants');
const { ICP_PROFILE_STATUS } = require('./constants');

// Models
const CommunityModel = require('../../communitiesAPI/models/community.model');
const IcpProfilesModel = require('../../models/magicLeads/icpProfiles.model');
const IcpLeadMatchesModel = require('../../models/magicLeads/icpLeadMatches.model');
const CommunityEventsModel = require('../../communitiesAPI/models/communityEvents.model');
const CommunityFoldersModel = require('../../communitiesAPI/models/communityFolders.model');
const ProgramModel = require('../../models/program/program.model');
const ProgramItemModel = require('../../models/program/programItem.model');
const CommunityFolderItemModel = require('../../communitiesAPI/models/communityFolderItems.model');
const LearnersModel = require('../../models/learners.model');
const CountryInfoMappingModel = require('../../models/countryInfoMapping.model');
const logger = require('../logger.service');
const { getMonthlyLimitCheck } = require('./magicLeadsUsageCheck.service');
const { getNextMonthFirstDay } = require('./utils');

/**
 * Helper function to recursively extract text from lexical nodes
 */
function extractPlainTextFromLexicalNode(node) {
  if (!node) return '';

  if (Array.isArray(node)) {
    return node.map(extractPlainTextFromLexicalNode).join('');
  }

  // If it's a text node
  if (node.type === 'text' && typeof node.text === 'string') {
    return node.text;
  }

  // If it has children (e.g., paragraph, list, quote, root, etc.)
  if (node.children && Array.isArray(node.children)) {
    const text = node.children
      .map(extractPlainTextFromLexicalNode)
      .join('');
    // Add newlines after block-level nodes
    if (
      node.type === 'paragraph' ||
      node.type === 'list' ||
      node.type === 'quote'
    ) {
      return `${text}\n`;
    }
    return text;
  }

  return '';
}

/**
 * Helper function to extract plain text from lexical format
 */
function extractPlainTextFromLexical(data) {
  if (!data) return '';

  // Handle the case where data has a root property
  if (data.root && typeof data.root === 'object') {
    return extractPlainTextFromLexicalNode(data.root);
  }

  // Handle direct node or array
  return extractPlainTextFromLexicalNode(data);
}

exports.getProductsWithLeadsData = async (params) => {
  const { communityId, pageNo = 1, pageSize = 15 } = params;

  try {
    // Validate communityId
    if (!communityId || communityId.trim() === '') {
      throw new Error('Community ID is required');
    }

    if (!mongoose.Types.ObjectId.isValid(communityId)) {
      throw new Error('Invalid Community ID format');
    }

    // Get community info for membership product (first element)
    const community = await CommunityModel.findOne({
      _id: communityId,
      isActive: true,
    }).lean();
    if (!community) {
      throw new Error('Community not found');
    }

    // Check if this community has ever used Magic Reach before
    const existingLeadMatches = await IcpLeadMatchesModel.findOne({
      communityObjectId: communityId,
    }).lean();

    const hasMagicLeadsFound = !!existingLeadMatches;

    const productsResult = await getProducts({
      communityObjectId: communityId,
      pageNo,
      pageSize,
      status: PRODUCT_STATUS.PUBLISHED,
    });

    const selectedProducts = productsResult.products;

    // TODO: need to get all deleted/draft products that has ICP profile

    const allProducts = [];
    let totalCount = productsResult.meta.total;

    // Only include membership product on first page
    // For first page, the items will always be pageSize+1 to include membership
    if (pageNo === 1) {
      // Create membership product as first element
      const membershipProduct = {
        title: community.title,
        productType: PRODUCT_TYPE.MEMBERSHIP,
        entityObjectId: communityId,
        thumbnail: community.thumbnailImgData?.desktopImgData?.src,
        hasTriggeredFindLeads: false,
        leadsCount: 0,
        outreachCount: 0,
      };

      // Check if membership has active ICP profiles and leads
      const membershipICPs = await IcpProfilesModel.find({
        communityObjectId: communityId,
        productType: PRODUCT_TYPE.MEMBERSHIP,
        entityObjectId: communityId,
        status: ICP_PROFILE_STATUS.ACTIVE,
      }).lean();

      if (membershipICPs.length > 0) {
        membershipProduct.hasTriggeredFindLeads = true;

        // Get leads count and outreach count for all membership ICP profiles
        const icpProfileIds = membershipICPs.map((icp) => icp._id);
        const leadMatches = await IcpLeadMatchesModel.find({
          icpProfileObjectId: { $in: icpProfileIds },
        }).lean();

        membershipProduct.leadsCount = leadMatches.length;
        membershipProduct.outreachCount = leadMatches.reduce(
          (sum, match) => sum + match.outReachCount,
          0
        );
      }

      allProducts.push(membershipProduct);
      totalCount += 1; // Add 1 for membership product
    }

    // Process other products and check for ICP data
    const processedProducts = await Promise.all(
      selectedProducts.map(async (product) => {
        const productData = {
          title: product.title,
          productType: product.productType,
          entityObjectId: product.entityObjectId,
          thumbnail: product.coverImg,
          hasTriggeredFindLeads: false,
          leadsCount: 0,
          outreachCount: 0,
        };

        // Check if this product has active ICP profiles
        const icpProfiles = await IcpProfilesModel.find({
          communityObjectId: communityId,
          productType: product.productType,
          entityObjectId: product.entityObjectId,
          status: ICP_PROFILE_STATUS.ACTIVE,
        }).lean();

        if (icpProfiles.length > 0) {
          productData.hasTriggeredFindLeads = true;

          // Get leads count and outreach count for all ICP profiles
          const icpProfileIds = icpProfiles.map((icp) => icp._id);
          const leadMatches = await IcpLeadMatchesModel.find({
            icpProfileObjectId: { $in: icpProfileIds },
          }).lean();

          productData.leadsCount = leadMatches.length;
          productData.outreachCount = leadMatches.reduce(
            (sum, match) => sum + match.outReachCount,
            0
          );
        }

        return productData;
      })
    );

    // Add processed products to the result
    allProducts.push(...processedProducts);

    // Step 2: Check permissions and limits - handle gracefully when limit exceeded
    const monthlyLimitCheck = await getMonthlyLimitCheck(communityId);

    return {
      products: allProducts,
      metadata: {
        total: totalCount + 1,
        limit: pageSize,
        page: pageNo,
        pages: Math.ceil(totalCount / pageSize),
      },
      searchCountInfo: {
        limit: monthlyLimitCheck.monthlyLimit,
        count: monthlyLimitCheck.currentUsage,
        resetDateTime: getNextMonthFirstDay(),
      },
      hasMagicLeadsFound,
    };
  } catch (error) {
    logger.error(
      `Get products with leads data failed for community: ${communityId}, error: ${error.message}`,
      { error: error.stack, communityId }
    );
    throw error;
  }
};

/**
 * Helper function to get owner country name from country_currency_mapping
 */
const getOwnerCountry = async (countryId) => {
  if (!countryId) return null;

  try {
    const countryMapping = await CountryInfoMappingModel.findOne({
      countryId,
    })
      .select('country')
      .lean();

    return countryMapping?.country || null;
  } catch (error) {
    logger.error('Error fetching owner country:', {
      error: error.message,
      countryId,
    });
    return null;
  }
};

/**
 * Helper function to get community context with owner information
 */
const getCommunityContext = async (communityObjectId) => {
  try {
    // Get community data
    const community = await CommunityModel.findById(communityObjectId)
      .select('title description memberCountries createdBy')
      .lean();

    if (!community) {
      throw new Error('Community not found');
    }

    // Get owner learner data
    const ownerLearner = await LearnersModel.findOne({
      email: community.createdBy,
    })
      .select('firstName lastName description countryId socialMedia')
      .lean();

    let owner = null;
    if (ownerLearner) {
      // Get owner country name
      const ownerCountry = await getOwnerCountry(ownerLearner.countryId);

      owner = {
        firstName: ownerLearner.firstName || '',
        lastName: ownerLearner.lastName || '',
        description: ownerLearner.description || '',
        country: ownerCountry || '',
        socialMedia: ownerLearner.socialMedia || [],
      };
    }

    return {
      title: community.title || '',
      description: community.description || '',
      memberCountries: community.memberCountries || [],
      owner,
    };
  } catch (error) {
    logger.error('Error fetching community context:', {
      error: error.message,
      communityObjectId,
    });
    throw error;
  }
};

/**
 * Lightweight function to get basic product data (title and coverImg) for leads retrieval/generation
 */
exports.getBasicProductData = async (productType, entityObjectId) => {
  try {
    const productData = { title: '', coverImg: null };

    switch (productType) {
      case PRODUCT_TYPE.MEMBERSHIP: {
        const community = await CommunityModel.findById(entityObjectId)
          .select('title thumbnailImgData')
          .lean();
        productData.title = community?.title || '';
        productData.coverImg =
          community?.thumbnailImgData?.desktopImgData?.src || null;
        break;
      }

      case PRODUCT_TYPE.CHALLENGE: {
        const challenge = await ProgramModel.findById(entityObjectId)
          .select('title cover')
          .lean();
        productData.title = challenge?.title || '';
        productData.coverImg = challenge?.cover || null;
        break;
      }

      case PRODUCT_TYPE.EVENT: {
        const event = await CommunityEventsModel.findById(entityObjectId)
          .select('title bannerImg')
          .lean();
        productData.title = event?.title || '';
        productData.coverImg = event?.bannerImg || null;
        break;
      }

      case PRODUCT_TYPE.COURSE:
      case PRODUCT_TYPE.DIGITAL_FILES:
      case PRODUCT_TYPE.SESSION: {
        const folder = await CommunityFoldersModel.findById(entityObjectId)
          .select('title thumbnail')
          .lean();
        productData.title = folder?.title || '';
        productData.coverImg = folder?.thumbnail || null;
        break;
      }

      default:
        throw new Error(`Invalid product type: ${productType}`);
    }

    return productData;
  } catch (error) {
    logger.error('Error fetching basic product data:', {
      error: error.message,
      productType,
      entityObjectId,
    });
    throw error;
  }
};

/**
 * Comprehensive function to get full product data for ICP generation
 */
exports.getProductData = async (
  productType,
  entityObjectId,
  communityObjectId = null
) => {
  try {
    let productData = {};

    // Get base product data based on type
    switch (productType) {
      case PRODUCT_TYPE.MEMBERSHIP: {
        const community = await CommunityModel.findById(entityObjectId)
          .select('title description isPaidCommunity prices')
          .lean();

        if (!community) {
          throw new Error('Community not found');
        }

        productData = {
          title: community.title || '',
          description: community.description || '',
          isPaidCommunity: community.isPaidCommunity || false,
          prices: community.isPaidCommunity
            ? (community.prices || []).map((price) => ({
                ...price,
                cmSetPrice: price.cmSetPrice ? price.cmSetPrice / 100 : 0, // Convert from cents to dollars
                stripePrice: price.stripePrice
                  ? price.stripePrice / 100
                  : 0, // Convert from cents to dollars
              }))
            : [],
        };
        break;
      }

      case PRODUCT_TYPE.CHALLENGE: {
        const challenge = await ProgramModel.findById(
          entityObjectId
        ).lean();
        if (!challenge) {
          throw new Error('Challenge not found');
        }

        // Get checkpoints
        const checkpoints = await ProgramItemModel.find({
          programObjectId: entityObjectId,
        })
          .select('title description')
          .lean();
        const formattedCheckpoints = checkpoints.map((cp) => ({
          title: cp.title || '',
          description: cp.description
            ? extractPlainTextFromLexical(cp.description).trim()
            : '',
        }));
        productData = {
          title: challenge.title || '',
          description: challenge.description
            ? extractPlainTextFromLexical(challenge.description).trim()
            : '',
          pricingConfig: {
            ...challenge.pricingConfig,
            amount: challenge.pricingConfig?.amount
              ? challenge.pricingConfig.amount / 100
              : 0, // Convert from cents to dollars
          },
          challengeType: challenge.challengeType || '',
          startTime: challenge.startTime || null,
          endTime: challenge.endTime || null,
          checkpoints: formattedCheckpoints,
        };
        break;
      }

      case PRODUCT_TYPE.EVENT: {
        const event = await CommunityEventsModel.findById(
          entityObjectId
        ).lean();
        if (!event) {
          throw new Error('Event not found');
        }

        productData = {
          title: event.title || '',
          description: event.description || '',
          currency: event.currency || '',
          pricingConfig: event.pricingConfig || {},
          amount: event.amount ? event.amount / 100 : 0, // Convert from cents to dollars
          startTime: event.startTime || null,
          endTime: event.endTime || null,
          liveLink: event.liveLink || null,
          inPersonLocation: event.inPersonLocation || null,
          host: event.host || {},
        };
        break;
      }

      case PRODUCT_TYPE.COURSE:
      case PRODUCT_TYPE.DIGITAL_FILES:
      case PRODUCT_TYPE.SESSION: {
        const folder = await CommunityFoldersModel.findById(
          entityObjectId
        ).lean();
        if (!folder) {
          throw new Error('Course/Digital File/Session not found');
        }

        // Get sections/items
        const sections = await CommunityFolderItemModel.find({
          communityFolderObjectId: entityObjectId,
        })
          .select('title description')
          .lean();

        productData = {
          title: folder.title || '',
          description: folder.description || '',
          currency: folder.currency || '',
          pricingConfig: {
            ...folder.pricingConfig,
            minAmount: folder.pricingConfig?.minAmount
              ? folder.pricingConfig.minAmount / 100
              : 0, // Convert from cents to dollars
            suggestedAmount: folder.pricingConfig?.suggestedAmount
              ? folder.pricingConfig.suggestedAmount / 100
              : 0, // Convert from cents to dollars
          },
          amount: folder.amount ? folder.amount / 100 : 0, // Convert from cents to dollars
          sections: sections.map((section) => ({
            title: section.title || '',
            description: section.description || '',
          })),
        };

        // Add session-specific fields
        if (productType === PRODUCT_TYPE.SESSION) {
          productData.location = folder.location || {};
          productData.hostInfo = folder.hostInfo || {};
        }
        break;
      }

      default:
        throw new Error(`Invalid product type: ${productType}`);
    }

    // Get community context for all product types
    if (
      communityObjectId ||
      (productType === PRODUCT_TYPE.MEMBERSHIP && entityObjectId)
    ) {
      const communityId = communityObjectId || entityObjectId;
      productData.communityInfo = await getCommunityContext(communityId);
    }

    return productData;
  } catch (error) {
    logger.error('Error fetching product data:', {
      error: error.message,
      productType,
      entityObjectId,
    });
    throw error;
  }
};
