const { v4: uuidv4 } = require('uuid');
const PrimaryMongooseConnection = require('@src/rpc/primaryMongooseConnection');
const { SEARCH_FIELD_TYPE, ICP_PROFILE_STATUS } = require('./constants');
const EnrichedLeadsModel = require('../../models/magicLeads/enrichedLeads.model');
const IcpProfilesModel = require('../../models/magicLeads/icpProfiles.model');
const IcpLeadMatchesModel = require('../../models/magicLeads/icpLeadMatches.model');
const IcpLeadActivityLogsModel = require('../../models/magicLeads/icpLeadActivityLogs.model');
const { ACTIVITY_TYPES } = require('./constants');
const { getBasicProductData } = require('./productList.service');
const logger = require('../logger.service');
const {
  getActiveSubscriptions,
  checkProductPurchases,
} = require('./common.service');
const LearnerModel = require('../../models/learners.model');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const { PRODUCT_TYPE } = require('../product/constants');
const {
  EMAIL_VERIFICATION_STATUS,
  AI_LEAD_PROCESSOR_ACTION_TYPE,
} = require('../../constants/common');
const magicLeadsUsageService = require('../featurePermissions/magicLeadsUsage.service');
const {
  checkLeadsPermissions,
} = require('./magicLeadsUsageCheck.service');
const {
  getProfileImageFromSocialProfiles,
} = require('./leadsRetrieval.service');
const { sendMessageToSQSQueue } = require('../../handlers/sqs.handler');
const { AI_LEAD_PROCESSOR_QUEUE_URL } = require('../../config');
const { getNextMonthFirstDay } = require('./utils');
const { getConfigByTypeFromCache } = require('../config.service');
const { retrieveActiveCommunity } = require('../common/common.service');

const buildAtlasSearchQuery = (
  icpProfile,
  excludeLeadIds = [],
  needToCheckEmailExists
) => {
  const mustClauses = [];

  // Required filter for active leads
  mustClauses.push({
    equals: {
      path: 'isActive',
      value: true,
    },
  });

  // Require email is present and not null
  if (needToCheckEmailExists) {
    mustClauses.push({
      compound: {
        must: [
          {
            exists: {
              path: 'email',
            },
          },
        ],
        mustNot: [
          {
            equals: {
              path: 'email',
              value: null,
            },
          },
        ],
      },
    });
  }

  // Extract search fields by type from target language
  const countryFields =
    icpProfile.searchFields.find(
      (field) => field.type === SEARCH_FIELD_TYPE.COUNTRY
    )?.values || [];
  const jobTitleFields =
    icpProfile.searchFields.find(
      (field) => field.type === SEARCH_FIELD_TYPE.JOB_TITLE
    )?.values || [];
  const industryFields =
    icpProfile.searchFields.find(
      (field) => field.type === SEARCH_FIELD_TYPE.INDUSTRY
    )?.values || [];

  // Extract English search fields if target language is not English
  let englishCountryFields = [];
  let englishJobTitleFields = [];
  let englishIndustryFields = [];

  if (
    icpProfile.targetLanguage &&
    icpProfile.targetLanguage !== 'en' &&
    icpProfile.localization?.en?.searchFields
  ) {
    englishCountryFields =
      icpProfile.localization.en.searchFields.find(
        (field) => field.type === SEARCH_FIELD_TYPE.COUNTRY
      )?.values || [];
    englishJobTitleFields =
      icpProfile.localization.en.searchFields.find(
        (field) => field.type === SEARCH_FIELD_TYPE.JOB_TITLE
      )?.values || [];
    englishIndustryFields =
      icpProfile.localization.en.searchFields.find(
        (field) => field.type === SEARCH_FIELD_TYPE.INDUSTRY
      )?.values || [];
  }

  // Create separate should clauses for each field type
  // This ensures AND logic: must match at least one from EACH field type

  // Country filtering - exact match (combine target language and English values)
  const allCountryValues = [...countryFields, ...englishCountryFields];

  if (allCountryValues.length > 0) {
    // Remove duplicates
    const uniqueCountryValues = [...new Set(allCountryValues)];
    const countryShouldClauses = uniqueCountryValues.map((country) => ({
      phrase: {
        // exact match
        path: 'location.countryName',
        query: country,
      },
    }));

    mustClauses.push({
      compound: {
        should: countryShouldClauses,
        minimumShouldMatch: 1,
      },
    });
  }

  // Job title filtering - fuzzy match (combine target language and English values)
  const allJobTitleValues = [...jobTitleFields, ...englishJobTitleFields];

  if (allJobTitleValues.length > 0) {
    const jobTitleShouldClauses = [];
    // Remove duplicates
    const uniqueJobTitleValues = [...new Set(allJobTitleValues)];

    uniqueJobTitleValues.forEach((title) => {
      // Phrase search - higher priority for exact matches with boost
      jobTitleShouldClauses.push({
        phrase: {
          query: title,
          path: 'jobTitle',
          score: { boost: { value: 5 } },
        },
      });

      // Text search - fallback for fuzzy matches
      jobTitleShouldClauses.push({
        text: {
          query: title,
          path: 'jobTitle',
          fuzzy: {
            maxEdits: 2,
          },
          score: { boost: { value: 1 } },
        },
      });
    });

    mustClauses.push({
      compound: {
        should: jobTitleShouldClauses,
        minimumShouldMatch: 1,
      },
    });
  }

  // Industry filtering - fuzzy match (combine target language and English values)
  const allIndustryValues = [...industryFields, ...englishIndustryFields];

  if (allIndustryValues.length > 0) {
    // Remove duplicates
    const uniqueIndustryValues = [...new Set(allIndustryValues)];
    const industryShouldClauses = uniqueIndustryValues.map((industry) => ({
      text: {
        query: industry,
        path: 'company.industry',
        fuzzy: {
          maxEdits: 2,
        },
      },
    }));

    mustClauses.push({
      compound: {
        should: industryShouldClauses,
        minimumShouldMatch: 1,
      },
    });
  }

  // Add exclusion filter if we have leads to exclude
  if (excludeLeadIds.length > 0) {
    mustClauses.push({
      compound: {
        mustNot: [
          {
            in: {
              path: '_id',
              value: excludeLeadIds,
            },
          },
        ],
      },
    });
  }

  // If no search fields are provided, just return active leads with profile summaries
  const query = {
    compound: {
      must: mustClauses,
    },
    // Sort directly in $search for better performance
    sort: {
      score: { $meta: 'searchScore' },
      'usage.searchCount': 1,
    },
  };

  logger.info('Atlas Search Query Built', query);

  return query;
};

const performVectorSearch = async (
  filteredLeadIds,
  icpVectorContext,
  limit = 20
) => {
  if (!icpVectorContext || !icpVectorContext.embedding) {
    return [];
  }

  const pipeline = [
    {
      $vectorSearch: {
        index: 'enriched_lead_vector_search',
        path: 'aiProfileSummary.embedding',
        queryVector: icpVectorContext.embedding,
        numCandidates: limit * 3,
        limit,
        filter: {
          _id: { $in: filteredLeadIds },
        },
      },
    },
    {
      $addFields: {
        vectorScore: { $meta: 'vectorSearchScore' },
      },
    },
  ];

  return EnrichedLeadsModel.aggregate(pipeline);
};

const resolveLearnerIds = async (emails) => {
  try {
    if (!emails || emails.length === 0) {
      return [];
    }

    const learners = await LearnerModel.find({
      email: { $in: emails },
      isActive: true,
    })
      .select('_id email')
      .lean();

    return learners.map((learner) => ({
      learnerId: learner._id,
      email: learner.email,
    }));
  } catch (error) {
    logger.error('Error resolving learner IDs from emails:', {
      error: error.message,
      emails,
    });
    return [];
  }
};

// Filter out the leads who has been purchase the product or joined the community
const filterConvertedLeads = async (leads, icp) => {
  try {
    if (!leads || leads.length === 0) {
      return [];
    }

    const emails = leads.map((lead) => lead.email).filter(Boolean);
    if (emails.length === 0) {
      return leads;
    }

    if (icp.productType === PRODUCT_TYPE.MEMBERSHIP) {
      const community = await retrieveActiveCommunity(
        icp.communityObjectId
      );
      const communityCode = community.code;

      const subscriptions = await getActiveSubscriptions({
        emails,
        communityCode,
      });

      const filteredLeads = leads.filter(
        (lead) => !subscriptions[lead.email]
      );

      logger.info('Filtered leads for MEMBERSHIP product', {
        originalCount: leads.length,
        filteredCount: filteredLeads.length,
        subscribedCount: leads.length - filteredLeads.length,
      });

      return filteredLeads;
    }
    // Check product purchases for non-membership products
    const learnerIdMappings = await resolveLearnerIds(emails);
    const learnerIds = learnerIdMappings.map(
      (mapping) => mapping.learnerId
    );

    if (learnerIds.length === 0) {
      logger.warn(
        'No learner IDs found for leads, returning original leads'
      );
      return leads;
    }

    const purchases = await checkProductPurchases({
      productType: icp.productType,
      entityObjectId: icp.entityObjectId,
      learnerObjectIds: learnerIds,
    });

    // Create email to learner ID mapping for quick lookup
    const emailToLearnerIdMap = {};
    learnerIdMappings.forEach((mapping) => {
      emailToLearnerIdMap[mapping.email] = mapping.learnerId.toString();
    });

    const filteredLeads = leads.filter((lead) => {
      const learnerId = emailToLearnerIdMap[lead.email];
      return !purchases[learnerId];
    });

    logger.info('Filtered leads for product purchases', {
      originalCount: leads.length,
      filteredCount: filteredLeads.length,
      purchasedCount: leads.length - filteredLeads.length,
      productType: icp.productType,
    });

    return filteredLeads;
  } catch (error) {
    logger.error(
      'Error filtering converted leads, returning original leads:',
      {
        error: error.message,
        icpId: icp._id,
        productType: icp.productType,
      }
    );
    return leads;
  }
};

const findLeadsForIcp = async ({
  icp,
  limit = 50,
  excludeLeadIds = [],
  needToCheckEmailExists = true,
  maxRetries = 5,
  currentRetry = 0,
}) => {
  logger.info(
    `Finding leads for ICP: ${icp._id} with limit ${limit}, excluding ${excludeLeadIds.length} already matched leads`
  );
  const searchQuery = buildAtlasSearchQuery(
    icp,
    excludeLeadIds,
    needToCheckEmailExists
  );

  const pipeline = [
    {
      $search: { index: 'enriched_leads_search', ...searchQuery },
    },
    {
      $limit: limit * 100, // Get reasonable number for vector search
    },
    {
      $project: {
        _id: 1,
        email: 1,
        jobTitle: 1,
        company: 1,
        location: 1,
        fullName: 1,
        profileImage: 1,
        socialProfiles: 1,
        isActive: 1,
        isTestData: 1,
        createdAt: 1,
        verification: 1,
      },
    },
  ];

  // **First stage:**
  // Get leads by countries, job title, industry, and exclude leads who has found for this icp
  // This will get
  // - Platinum: 50 * 100 = 5000 results
  // - Pro: 20 * 100 = 2000 results
  // - Free: 5 * 100 = 500 results
  const filteredLeads = await EnrichedLeadsModel.aggregate(pipeline);
  const filteredLeadIds = filteredLeads.map((lead) => lead._id);

  // Check if we have any filtered leads
  if (filteredLeadIds.length === 0) {
    logger.info(
      'No leads found with Atlas Search - returning empty array'
    );
    return [];
  }

  // **Second stage:**
  // Use vector search on filtered results to get most relevant leads by embedding

  // Apply vector search on filtered results (if we have embeddings)
  let initialLeads = [];
  const leadsCountToVectorSearch = 500;

  // Use vector search if embeddings are available
  // Because the summary in enriched_leads db always in english
  // we will use English context for non-English languages
  let vectorSearchContext = icp.icpVectorSearchContext;
  if (
    icp.targetLanguage &&
    icp.targetLanguage !== 'en' &&
    icp.localization?.en?.icpVectorSearchContext
  ) {
    vectorSearchContext = icp.localization.en.icpVectorSearchContext;
    logger.info(
      `Using English vector search context for target language: ${icp.targetLanguage}`
    );
  }

  // here we will get first 500 leads from vector search
  // This is because we will filter out the leads who has been purchase the product or joined the community
  // so we need to get more leads to make sure we have enough leads to filter out
  const vectorResults = await performVectorSearch(
    filteredLeadIds,
    vectorSearchContext,
    leadsCountToVectorSearch // Get more leads initially to account for filtering
  );
  initialLeads = vectorResults;
  logger.info(`Vector search returned ${vectorResults.length} leads`);

  // **Third stage:**
  // Filter out the leads who has been purchase the product or joined the community
  // Here we can get result for
  // - Platinum: 50 leads
  // - Pro: 20 leads
  // - Free: 5 leads
  const finalLeads = await filterConvertedLeads(initialLeads, icp);

  // **Fourth stage:**
  // Backfill if needed and we haven't exhausted all possible leads
  // We only retry when
  // 1. finalLeads is less than limit
  // 2. initialLeads is equal to leadsCountToVectorSearch: means we have not reached the limit of vector search
  //      >>> if initialLeads.length !== leadsCountToVectorSearch, means the result from search stage has all been filtered out
  //      >>> like we are trying to get 500 leads from search index and vector search, but result only got 400 leads
  //      >>> means all the leads are filtered out, we dont need to retry again
  // 3. currentRetry is less than maxRetries:To prevent infinite loop here, we allow to retry up to 5 times
  if (
    finalLeads.length < limit &&
    initialLeads.length === leadsCountToVectorSearch &&
    currentRetry < maxRetries
  ) {
    const additionalExcludeIds = [
      ...excludeLeadIds,
      ...initialLeads.map((lead) => lead._id),
    ];

    logger.info('Backfilling leads due to filtering', {
      currentCount: finalLeads.length,
      targetCount: limit,
      additionalExcludeIds: additionalExcludeIds.length,
      currentRetry,
      maxRetries,
    });

    const additionalLeads = await findLeadsForIcp({
      icp,
      limit: limit - finalLeads.length,
      excludeLeadIds: additionalExcludeIds,
      needToCheckEmailExists,
      maxRetries,
      currentRetry: currentRetry + 1,
    });

    finalLeads.push(...additionalLeads);
  } else if (currentRetry >= maxRetries && finalLeads.length < limit) {
    logger.warn('Reached maximum retries for backfilling leads', {
      currentCount: finalLeads.length,
      targetCount: limit,
      maxRetries,
    });
    throw new Error('Cannot find more leads for this profile');
  }

  return finalLeads.slice(0, limit);
};

const getExistingMatchedLeadIds = async (icpProfileId) => {
  try {
    const existingMatches = await IcpLeadMatchesModel.find({
      icpProfileObjectId: icpProfileId,
    })
      .select('leadObjectId')
      .lean();

    const existingLeadIds = existingMatches.map(
      (match) => match.leadObjectId
    );

    logger.info('Found existing matched leads for ICP', {
      icpProfileId,
      existingMatchesCount: existingLeadIds.length,
    });

    return existingLeadIds;
  } catch (error) {
    logger.error('Error getting existing matched lead IDs:', error);
    return [];
  }
};

const shouldAddFakeDataForCommunity = async (communityObjectId) => {
  try {
    const config = await getConfigByTypeFromCache(
      'magicLeadsFakeDataCommunities'
    );
    if (!config || !config.value || !config.value.communityObjectIds) {
      return false;
    }
    return config.value.communityObjectIds.includes(
      communityObjectId.toString()
    );
  } catch (error) {
    logger.error('Error checking fake data config:', error);
    return false;
  }
};

const createFakeLeadData = async (baseLead) => {
  try {
    const timestamp = Math.floor(Date.now() / 1000);
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    const uniqueId = `${timestamp}-${randomSuffix}`;

    // Create unique social profiles to avoid duplicate key errors
    const fakeSocialProfiles = [
      {
        platform: 'linkedin',
        url: `https://www.linkedin.com/in/nuseir-yassin-fake-${uniqueId}`,
        profileImageUrl: null,
      },
    ];

    // Create fake lead data based on the last lead
    const fakeLeadData = {
      ...baseLead,
      _id: undefined, // Remove _id to create new document
      fullName: 'Fake Nuseir Yassin',
      firstName: 'Fake Nuseir',
      lastName: 'Yassin',
      email: `tech+fake-${uniqueId}@nas.io`,
      socialProfiles: fakeSocialProfiles,
      isTestData: true,
      isActive: false, // make it inactive so that it will not be treat as a valid lead
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Create and save the fake lead
    const fakeEnrichedLead = new EnrichedLeadsModel(fakeLeadData);
    const savedFakeLead = await fakeEnrichedLead.save();

    logger.info('Fake lead created successfully', {
      id: savedFakeLead._id,
      fullName: savedFakeLead.fullName,
      email: savedFakeLead.email,
    });

    return {
      ...savedFakeLead.toObject(),
      vectorScore: baseLead.vectorScore || 0.8,
    };
  } catch (error) {
    logger.error('Error creating fake lead data:', error);
    throw error;
  }
};

// Update the generateLeadSummaryWithAI to work with new format
const sendAILeadSummarySQSMessage = async (leadSummary) => {
  try {
    const requestId = uuidv4();
    const messageBody = {
      data: {
        actionType:
          AI_LEAD_PROCESSOR_ACTION_TYPE.GENERATE_ICP_LEADS_SUMMARY,
        params: {
          icpLeadsMatchId: leadSummary._id,
        },
      },
      requestor: 'LPBE',
      requestId,
    };

    await sendMessageToSQSQueue({
      queueUrl: AI_LEAD_PROCESSOR_QUEUE_URL,
      messageBody,
    });

    logger.info('AI lead summary SQS message sent', {
      icpLeadsMatchId: leadSummary._id,
      requestId,
    });
  } catch (sqsError) {
    logger.error('Failed to send AI lead summary SQS message', {
      icpLeadsMatchId: leadSummary._id,
      error: sqsError.message,
    });
    // Don't throw error here to avoid breaking the entire lead generation
  }
};

const generateLeadsForICP = async (icpProfileId, leadsLimit = null) => {
  const startTime = Date.now();
  let icpProfile = null;

  try {
    // Step 1: Get ICP profile
    icpProfile = await IcpProfilesModel.findById(icpProfileId).lean();
    if (!icpProfile) {
      throw new Error('ICP profile not found');
    }

    // Step 2: Check permissions and limits
    const { monthlyLimitCheck, permissionCheck, requestedCount } =
      await checkLeadsPermissions(icpProfile.communityObjectId);

    // Use leadsLimit if provided, otherwise use requestedCount from permissions
    const finalRequestedCount = leadsLimit || requestedCount; // leadsLimit is for testing

    // Step 2.5: Get already matched lead IDs to exclude duplicates
    const existingMatchedLeadIds = await getExistingMatchedLeadIds(
      icpProfileId
    );

    // Step 3: NOW generate leads (only after all validations pass)
    const finalLeads = await findLeadsForIcp({
      icp: icpProfile,
      limit: finalRequestedCount,
      excludeLeadIds: existingMatchedLeadIds,
    });

    // Early return if no leads found, but still update status to active
    if (!finalLeads || finalLeads.length === 0) {
      // Auto-promote ICP status from draft to active even if no leads found
      if (icpProfile.status === ICP_PROFILE_STATUS.DRAFT) {
        await IcpProfilesModel.findByIdAndUpdate(icpProfileId, {
          status: ICP_PROFILE_STATUS.ACTIVE,
        });
        logger.info(
          `ICP profile ${icpProfileId} promoted from draft to active (no leads found)`
        );
      }

      return {
        product: {
          productType: icpProfile.productType,
          entityObjectId: icpProfile.entityObjectId,
        },
        icp: {
          _id: icpProfile._id,
          title: icpProfile.title,
          icpSummary: icpProfile.icpSummary,
        },
        leads: [],
        searchCountInfo: {
          limit: monthlyLimitCheck.monthlyLimit,
          count: monthlyLimitCheck.currentUsage,
          resetDateTime: getNextMonthFirstDay(),
        },
        metadata: {
          total: 0,
          limit: permissionCheck.allowedCount,
          page: 1,
          pages: 1,
        },
      };
    }

    // Step 3.5: Add fake lead data if community is in the test list
    const shouldAddFakeData = await shouldAddFakeDataForCommunity(
      icpProfile.communityObjectId
    );

    if (shouldAddFakeData && finalLeads.length > 0) {
      try {
        // Get the last element from finalLeads to use as base
        const baseLead = await EnrichedLeadsModel.findById(
          finalLeads[finalLeads.length - 1]._id
        ).lean();
        const fakeLeadData = await createFakeLeadData(baseLead);

        // Add the fake lead to the finalLeads array
        finalLeads.push(fakeLeadData);

        logger.info('Fake lead added to results', {
          communityObjectId: icpProfile.communityObjectId,
          totalLeads: finalLeads.length,
        });
      } catch (error) {
        logger.error(
          'Failed to add fake lead data, continuing with normal leads:',
          error
        );
      }
    }

    // Step 4: Process leads and save matches within transaction (success case only)
    const primaryMongooseConnection =
      await PrimaryMongooseConnection.connect();
    const session = await primaryMongooseConnection.startSession();
    session.startTransaction();

    let leadsWithSummaries = [];

    try {
      // Step 1: Auto-promote ICP status from draft to active if needed
      if (icpProfile.status === ICP_PROFILE_STATUS.DRAFT) {
        await IcpProfilesModel.findByIdAndUpdate(
          icpProfileId,
          { status: ICP_PROFILE_STATUS.ACTIVE },
          { session }
        );
        logger.info(
          `ICP profile ${icpProfileId} promoted from draft to active`
        );
      }

      // Step 2: Save all lead matches within transaction (sequential to avoid session conflicts)
      leadsWithSummaries = [];
      for (const lead of finalLeads) {
        const match = new IcpLeadMatchesModel({
          icpProfileObjectId: icpProfileId,
          communityObjectId: icpProfile.communityObjectId,
          leadObjectId: lead._id,
          vectorSimilarity: lead.vectorScore || 0,
          outReachCount: 0,
        });
        // eslint-disable-next-line no-await-in-loop
        await match.save({ session });

        // Create activity log for lead found (upsert to prevent duplicates)
        // eslint-disable-next-line no-await-in-loop
        await IcpLeadActivityLogsModel.updateOne(
          {
            leadObjectId: lead._id,
            communityObjectId: icpProfile.communityObjectId,
            activityType: ACTIVITY_TYPES.LEAD_FOUND,
            sourceEntityId: icpProfile.entityObjectId,
          },
          {
            $setOnInsert: {
              leadObjectId: lead._id,
              communityObjectId: icpProfile.communityObjectId,
              activityType: ACTIVITY_TYPES.LEAD_FOUND,
              sourceEntityId: icpProfile.entityObjectId,
              metadata: {
                productType: icpProfile.productType,
                entityObjectId: icpProfile.entityObjectId,
                icpProfileObjectId: icpProfileId,
              },
            },
          },
          {
            upsert: true,
            session,
          }
        );

        leadsWithSummaries.push({
          _id: match._id,
          leadObjectId: lead._id,
          fullName: lead.fullName,
          profileImage: getProfileImageFromSocialProfiles(
            lead.socialProfiles
          ),
          jobTitle: lead.jobTitle,
          emailVerified:
            lead.verification?.email?.status ===
            EMAIL_VERIFICATION_STATUS.VALID,
          city: lead.location?.city || null,
          country: lead.location?.countryName,
          companyName: lead.company?.name,
          companyWebsite: lead.company?.website,
          socialMedias:
            lead.socialProfiles?.length > 0
              ? lead.socialProfiles.map((profile) => ({
                  platform: profile.platform,
                }))
              : [{ platform: 'linkedin' }],
          status: match.status,
        });
      }

      // Record successful usage within the same transaction
      const executionTime = Date.now() - startTime;
      await magicLeadsUsageService.recordMagicLeadsUsage(
        {
          communityObjectId: icpProfile.communityObjectId,
          icpProfileObjectId: icpProfileId,
          generatedLeadsCount: finalLeads.length,
          requestedCount,
          executionTime,
        },
        { session }
      );
      await session.commitTransaction();
    } catch (error) {
      logger.error('Error in store the lead matches:', error);
      await session.abortTransaction();
      throw error;
    } finally {
      await session.endSession();
    }

    // Increase usage search count for each leads
    // Put this in a separate transaction to avoid race condition
    const leadsId = leadsWithSummaries.map((lead) => lead.leadObjectId);
    await EnrichedLeadsModel.updateMany(
      { _id: { $in: leadsId } },
      { $inc: { 'usage.searchCount': 1 } }
    );

    // Send async SQS messages to generate AI summaries for each lead
    const sqsPromises = leadsWithSummaries.map(
      sendAILeadSummarySQSMessage
    );

    // Execute SQS messages in parallel (fire and forget)
    await Promise.allSettled(sqsPromises).catch((error) => {
      logger.error('Error in SQS message batch processing', {
        error: error.message,
      });
    });

    // Get basic product data (title and coverImg only)
    const product = await getBasicProductData(
      icpProfile.productType,
      icpProfile.entityObjectId
    );

    return {
      product: {
        productType: icpProfile.productType,
        entityObjectId: icpProfile.entityObjectId,
        title: product.title,
        thumbnail: product.coverImg,
      },
      icpProfile: {
        _id: icpProfile._id,
        title: icpProfile.title,
        icpSummary: icpProfile.icpSummary,
      },
      leads: leadsWithSummaries.map((leadSummary) => {
        // eslint-disable-next-line no-unused-vars
        const { matchId, ...cleanLeadSummary } = leadSummary;
        return cleanLeadSummary;
      }),
      searchCountInfo: {
        limit: monthlyLimitCheck.monthlyLimit,
        count: monthlyLimitCheck.currentUsage + 1, // +1 because we just used one
        resetDateTime: getNextMonthFirstDay(),
      },
      metadata: {
        total: leadsWithSummaries.length,
        limit: permissionCheck.allowedCount,
        page: 1,
        pages: 1,
      },
    };
  } catch (error) {
    logger.error('Lead generation failed:', error);
    throw error;
  }
};

module.exports = {
  buildAtlasSearchQuery,
  performVectorSearch,
  findLeadsForIcp,
  generateLeadsForICP,
  getExistingMatchedLeadIds,
  shouldAddFakeDataForCommunity,
  createFakeLeadData,
};
