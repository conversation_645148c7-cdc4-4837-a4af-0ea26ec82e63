const openaiClient = require('@/src/clients/openaiV2.client');
const {
  MAX_OUTPUT_TOKEN,
  EVENT_TYPE,
} = require('@/src/services/aiCofounder/ai/constants');
const TemplateLibraryModel = require('@/src/models/getInspired/templateLibrary.model');
const IcpLeadMatchModel = require('@/src/models/magicLeads/icpLeadMatches.model');
const EnrichedLeadModel = require('@/src/models/magicLeads/enrichedLeads.model');
const IcpLeadActivityLogModel = require('@/src/models/magicLeads/icpLeadActivityLogs.model');
const IcpProfileModel = require('@/src/models/magicLeads/icpProfiles.model');
const MagicReachEmailModel = require('@/src/models/magicReach/communityMagicReachEmail.model');
const CommunityRoleModel = require('@/src/communitiesAPI/models/communityRole.model');
const ProductModel = require('@/src/models/product/communityProduct.model');
const LearnerModel = require('@/src/models/learners.model');
const CommunityModel = require('@/src/communitiesAPI/models/community.model');
const { getProductData } = require('./productList.service');
const PrimaryMongooseConnection = require('@/src/rpc/primaryMongooseConnection');
const { NAS_IO_FRONTEND_URL } = require('@/src/config');
const {
  AI_TEMPLATE_PROMPT_TYPE,
  AI_MODEL,
  OUTREACH_PURPOSE,
  ACTIVITY_TYPES,
} = require('./constants');
const { PRODUCT_TYPE } = require('@/src/services/product/constants');
const {
  TEMPLATE_SOURCE_TYPE,
  TEMPLATE_LIBRARY_TYPES,
  MAGIC_LEADS_STATUS,
} = require('@/src/constants/common');
const { aclRoles } = require('@/src/communitiesAPI/constants');
const promptService = require('@/src/services/aiCofounder/ai/prompt');
const magicReachComposeService = require('@/src/services/magicReach/compose.service');
const magicReachSendService = require('@/src/services/magicReach/send.service');
const {
  BUCKET_NAMES,
  PLATFORMS,
  MAGIC_REACH_MESSAGE_STATUS,
  FRAUD_CHECK_STATUS,
} = require('@/src/services/magicReach/constants');
const { ParamError } = require('@/src/utils/error.util');
const logger = require('@/src/services/logger.service');
const commonService = require('./common.service');

const PENDING_REVIEW_REASON = {
  en: 'Cold emails can be sensitive. We’re reviewing yours to help it meet guidelines and land positively. It’ll be sent once approved.',
  es_mx:
    'Los correos electrónicos en frío pueden ser delicados. Estamos revisando el tuyo para ayudar a que cumpla con las pautas y tenga un impacto positivo. Se enviará una vez aprobado.',
  pt_br:
    'E-mails frios podem ser delicados. Estamos revisando o seu para garantir que ele siga as diretrizes e tenha um impacto positivo. Será enviado assim que aprovado.',
  ja: 'コールドメールはデリケートな場合があります。承認される前に、ガイドラインに沿って好印象を与えられるよう、メールを確認しています。',
};

const FAILED_TO_SEND_REASON = {
  en: 'There was an issue sending your email, please try again.',
  es_mx:
    'Hubo un problema al enviar tu correo electrónico, por favor inténtalo de nuevo.',
  pt_br: 'Ocorreu um problema ao enviar seu e-mail, tente novamente.',
  ja: 'メールの送信中に問題が発生しました。もう一度お試しください。',
};

function outreachEmailFunctionCallSchema() {
  const schema = {
    type: 'function',
    name: 'generate_outreach_email_template',
    description:
      'Generate a professional outreach email template with a subject line and body message in a specific language.',
    parameters: {
      type: 'object',
      properties: {
        language: {
          type: 'string',
          description:
            'The language of the email template (e.g., English, Spanish, Portuguese, Japanese).',
          enum: ['en', 'es-mx', 'pt-br', 'ja'],
        },
        subject: {
          type: 'string',
          description:
            'The subject line of the outreach email, optimized to grab attention and encourage opens.',
        },
        message: {
          type: 'string',
          description:
            'The body of the outreach email. Should be concise, persuasive, and tailored for engagement.',
        },
      },
      required: ['language', 'subject', 'message'],
      additionalProperties: false,
    },
  };

  return schema;
}

async function generateAiContextInput({
  lead,
  community,
  product,
  communityOwnerLearner,
  outreachEmailTemplate = null,
}) {
  const communityOwnerInfo = {
    firstName: communityOwnerLearner?.firstName ?? '',
    lastName: communityOwnerLearner?.lastName ?? '',
  };

  const communityLink = `${NAS_IO_FRONTEND_URL}${community.link}`;

  const communityInfo = {
    title: community.title,
    description: community.description,
    link: communityLink,
  };

  const productData = product
    ? await getProductData(product.productType, product.entityObjectId)
    : null;

  const productInfo = product
    ? {
        ...productData,
        link: (() => {
          let basePath = '';
          switch (product.productType) {
            case PRODUCT_TYPE.EVENT:
              basePath = '/events';
              break;
            case PRODUCT_TYPE.CHALLENGE:
              basePath = '/challenges';
              break;
            case PRODUCT_TYPE.DIGITAL_FILES:
              basePath = '/digital-files';
              break;
            case PRODUCT_TYPE.COURSE:
              basePath = '/courses';
              break;
            case PRODUCT_TYPE.SESSION:
              basePath = '/sessions';
              break;
            default:
              basePath = ''; // fallback to community root
          }
          return `${communityLink}${basePath}${product.slug}`;
        })(),
      }
    : null;

  const input = [
    {
      role: 'system',
      content: `Community Owner Info: ${JSON.stringify(
        communityOwnerInfo
      )}`,
    },
    {
      role: 'system',
      content: `Community Info: ${JSON.stringify(communityInfo)}`,
    },
  ];

  if (productInfo) {
    input.push({
      role: 'system',
      content: `Product Info: ${JSON.stringify(productInfo)}`,
    });
  }

  if (lead) {
    input.push({
      role: 'system',
      content: `Lead Info: ${JSON.stringify(lead)}`,
    });
  }

  if (outreachEmailTemplate) {
    input.push({
      role: 'system',
      content: `AI Generated Outreach Email Template: ${JSON.stringify(
        outreachEmailTemplate
      )}`,
    });
  }

  return input;
}

async function outreachEmailFunctionCall({
  communityObjectId,
  icpLeadMatchObjectId,
  outreachPurpose,
  communityOwnerLearner,
  product,
  community,
  lead,
}) {
  const languagePreference =
    communityOwnerLearner?.languagePreference ?? 'en';

  const [promptTemplate] = await Promise.all([
    promptService.getProcessedPromptAndModel({
      templateType:
        AI_TEMPLATE_PROMPT_TYPE.GENERATE_OUTREACH_EMAIL_TEMPLATE,
      source: TEMPLATE_SOURCE_TYPE.MAGIC_LEADS,
      variables: {
        outreachPurpose,
        languagePreference,
      },
    }),
  ]);

  const aiContextInput = await generateAiContextInput({
    lead,
    community,
    product,
    communityOwnerLearner,
  });

  const schema = outreachEmailFunctionCallSchema();

  const response = await openaiClient.responses.create({
    model: promptTemplate.aiModel?.model ?? AI_MODEL.DEFAULT,
    instructions: promptTemplate.prompt,
    input: aiContextInput,
    tools: [schema],
    tool_choice: 'required',
    parallel_tool_calls: false,
    max_tool_calls: 1,
    user: communityObjectId,
    max_output_tokens: MAX_OUTPUT_TOKEN,
    metadata: {
      communityObjectId,
      icpLeadMatchObjectId,
      outreachPurpose,
    },
    truncation: 'auto',
  });

  const responseOutput = response?.output?.find(
    (output) => output.type === EVENT_TYPE.FUNCTION_CALL_START.itemType
  );

  if (!responseOutput) {
    throw new Error('Failed to generate outreach email template');
  }

  const args = JSON.parse(responseOutput.arguments);
  const callId = responseOutput.call_id;
  const responseId = response.id;

  return {
    args,
    callId,
    responseId,
  };
}

async function createOutreachEmailTemplate({
  communityObjectId,
  icpLeadMatchObjectId,
  outreachPurpose,
  args,
  callId,
  responseId,
}) {
  const templateType =
    TEMPLATE_LIBRARY_TYPES[`OUTREACH_EMAIL_${outreachPurpose}`];

  const version = 1;

  const outreachEmail = {
    language: args.language,
    purpose: args.purpose,
    subject: args.subject,
    message: args.message,
  };

  const template = {
    communityObjectId,
    type: templateType,
    sourceObjectId: icpLeadMatchObjectId,
    source: TEMPLATE_SOURCE_TYPE.MAGIC_LEADS,
    isAIGenerated: true,
    version,
    metadata: {
      outreachEmail,
      callId,
      aiResponseId: responseId,
    },
  };

  const createdTemplate = await TemplateLibraryModel.create(template);

  return createdTemplate.toObject();
}

async function retrieveLastMagicReachEmail({ icpLeadMatch }) {
  const lastMagicReachEmail =
    icpLeadMatch.outreachEmailTemplates[
      icpLeadMatch.outreachEmailTemplates.length - 1
    ];

  let magicReachEmail;

  if (lastMagicReachEmail) {
    magicReachEmail = await MagicReachEmailModel.findOne(
      {
        _id: lastMagicReachEmail.magicReachEmailObjectId,
      },
      { title: 1, status: 1, content: 1, fraudInfo: 1, bucketMetas: 1 }
    ).lean();
  }

  return magicReachEmail;
}

function retrieveOutreachEmailStatusReason({
  icpLeadMatch,
  magicReachEmail,
  languagePreference,
}) {
  let reason = '';

  switch (magicReachEmail.fraudInfo?.status) {
    case FRAUD_CHECK_STATUS.REJECTED:
      reason =
        magicReachEmail.fraudInfo?.localizedReason?.[languagePreference] ||
        magicReachEmail.fraudInfo?.localizedReason?.en ||
        magicReachEmail.fraudInfo?.fraudReason?.[languagePreference];
      break;
    case FRAUD_CHECK_STATUS.REVIEWING:
      reason = PENDING_REVIEW_REASON[languagePreference];
      break;
    default:
      break;
  }

  if (icpLeadMatch.status === MAGIC_LEADS_STATUS.FAILED_TO_SEND) {
    reason = FAILED_TO_SEND_REASON[languagePreference];
  }

  return reason;
}

exports.generateOutreachEmailTemplate = async ({
  communityObjectId,
  icpLeadMatchObjectId,
  outreachPurpose,
  languagePreference = 'en',
}) => {
  const templateType =
    TEMPLATE_LIBRARY_TYPES[`OUTREACH_EMAIL_${outreachPurpose}`];

  const [community, latestTemplate, icpLeadMatch, communityOwner] =
    await Promise.all([
      CommunityModel.findOne({
        _id: communityObjectId,
        isActive: true,
      }).lean(),
      TemplateLibraryModel.findOne(
        {
          sourceObjectId: icpLeadMatchObjectId,
          source: TEMPLATE_SOURCE_TYPE.MAGIC_LEADS,
          communityObjectId,
          type: templateType,
        },
        { metadata: 1 }
      )
        .sort({ version: -1 })
        .read('primary')
        .lean(),
      IcpLeadMatchModel.findOne({
        _id: icpLeadMatchObjectId,
        communityObjectId,
      }).lean(),
      CommunityRoleModel.findOne({
        communityObjectId,
        role: aclRoles.OWNER,
      }).lean(),
    ]);

  if (!community) {
    throw new ParamError('Community not found');
  }

  if (!icpLeadMatch) {
    throw new ParamError('ICP Lead Match not found');
  }

  const [outreachCount, enrichedLead] = await Promise.all([
    commonService.getOutreachCountByLead({
      communityObjectId,
      leadObjectId: icpLeadMatch.leadObjectId,
    }),
    EnrichedLeadModel.findOne(
      {
        _id: icpLeadMatch.leadObjectId,
        isActive: true,
      },
      {
        enrichments: 0,
        email: 0,
        usage: 0,
        verification: 0,
        'aiProfileSummary.embedding': 0,
      }
    ).lean(),
  ]);

  if (!enrichedLead) {
    throw new ParamError('Lead not found or lead is inactive');
  }

  if (
    enrichedLead.reachInfo?.optOutFromEmail?.communityCodes?.includes(
      community.code
    )
  ) {
    throw new ParamError('Lead has opted out of email');
  }

  if (outreachCount >= 2) {
    throw new ParamError('Outreach limit exceeded');
  }

  const magicReachEmail = await retrieveLastMagicReachEmail({
    icpLeadMatch,
  });

  let useMagicReachEmail = false;

  if (magicReachEmail) {
    if (magicReachEmail.status === MAGIC_REACH_MESSAGE_STATUS.REVIEWING) {
      // Must use when in REVIEW
      useMagicReachEmail = true;
    } else if (
      magicReachEmail.status !== MAGIC_REACH_MESSAGE_STATUS.SENT
    ) {
      // For other statuses, check outreach purpose
      if (
        magicReachEmail.bucketMetas?.[BUCKET_NAMES.MAGIC_LEADS_OUTREACH]
          ?.outreachPurpose === outreachPurpose
      ) {
        useMagicReachEmail = true;
      } else {
        // Purposes differ → fallback to latest template
        useMagicReachEmail = false;
      }
    }
  }

  if (useMagicReachEmail) {
    const reason = retrieveOutreachEmailStatusReason({
      icpLeadMatch,
      magicReachEmail,
      languagePreference,
    });

    return {
      outreachEmail: {
        subject: magicReachEmail.title,
        content: magicReachEmail.content,
        reason,
      },
      isMagicReachEmail: true,
    };
  }

  if (latestTemplate) {
    return {
      outreachEmail: latestTemplate.metadata.outreachEmail,
      isMagicReachEmail: false,
    };
  }

  const [communityOwnerLearner, icpProfile] = await Promise.all([
    LearnerModel.findOne({
      email: communityOwner.email,
      isActive: true,
    }).lean(),
    IcpProfileModel.findOne({
      _id: icpLeadMatch.icpProfileObjectId,
      communityObjectId,
    }).lean(),
  ]);

  if (!communityOwnerLearner || !icpProfile) {
    throw new ParamError('Community owner or ICP profile not found');
  }

  const product =
    icpProfile.productType !== PRODUCT_TYPE.MEMBERSHIP
      ? await ProductModel.findOne({
          entityObjectId: icpProfile.entityObjectId,
          communityObjectId,
        }).lean()
      : null;

  const { args, callId, responseId } = await outreachEmailFunctionCall({
    communityObjectId,
    icpLeadMatchObjectId,
    outreachPurpose,
    communityOwnerLearner,
    product,
    community,
    lead: enrichedLead,
  });

  const createdTemplate = await createOutreachEmailTemplate({
    communityObjectId,
    icpLeadMatchObjectId,
    outreachPurpose,
    args,
    callId,
    responseId,
  });

  return {
    outreachEmail: createdTemplate.metadata.outreachEmail,
    isMagicReachEmail: false,
  };
};

exports.retrieveOutreachPurposes = async () => {
  return Object.values(OUTREACH_PURPOSE).map(({ type, localizedKey }) => ({
    type,
    localizedKey,
  }));
};

exports.postSendOutreachEmail = async ({
  message,
  icpProfile,
  icpLeadMatch,
  languagePreference = 'en',
}) => {
  const mailSent = [
    FRAUD_CHECK_STATUS.APPROVED,
    FRAUD_CHECK_STATUS.SENT_ON_DETECTION,
  ].includes(message?.fraudInfo?.status);

  let result = {};

  if (mailSent) {
    const matchFilter = {
      leadObjectId: icpLeadMatch.leadObjectId,
      communityObjectId: icpLeadMatch.communityObjectId,
      activityType: ACTIVITY_TYPES.LEAD_OUTREACH_MAIL_SENT,
      sourceEntityId: message._id,
    };

    const existingIcpLeadActivityLog =
      await IcpLeadActivityLogModel.findOne(matchFilter).lean();

    if (existingIcpLeadActivityLog) {
      return;
    }

    const primaryMongooseConnection =
      await PrimaryMongooseConnection.connect();

    const session = await primaryMongooseConnection.startSession();
    session.startTransaction();

    try {
      result = await IcpLeadMatchModel.findOneAndUpdate(
        {
          _id: icpLeadMatch._id,
          communityObjectId: icpLeadMatch.communityObjectId,
          outReachCount: {
            $lt: 2,
          },
        },
        {
          $inc: {
            outReachCount: 1,
          },
          $set: {
            status: MAGIC_LEADS_STATUS.MAIL_SENT,
          },
        },
        { new: true, session }
      ).lean();

      await EnrichedLeadModel.updateOne(
        {
          _id: icpLeadMatch.leadObjectId,
        },
        {
          $set: {
            'usage.lastContactedAt': new Date(),
          },
          $inc: {
            'usage.emailCount': 1,
          },
        },
        { session }
      );

      await IcpLeadActivityLogModel.create(
        [
          {
            ...matchFilter,
            metadata: {
              productType: icpProfile.productType,
              entityObjectId: icpProfile.entityObjectId,
              icpProfileObjectId: icpProfile._id,
            },
          },
        ],
        { session }
      );

      await session.commitTransaction();
    } catch (err) {
      logger.error(`postSendOutreachEmail: ${err.message}, ${err.stack}`);
      await session.abortTransaction();
      throw err;
    } finally {
      await session.endSession();
    }
  } else {
    const isRejected =
      message?.fraudInfo?.status === FRAUD_CHECK_STATUS.REJECTED;

    result = await IcpLeadMatchModel.findOneAndUpdate(
      {
        _id: icpLeadMatch._id,
        communityObjectId: icpLeadMatch.communityObjectId,
      },
      {
        $set: {
          status: isRejected
            ? MAGIC_LEADS_STATUS.REJECTED
            : MAGIC_LEADS_STATUS.UNDER_REVIEW,
        },
      },
      {
        new: true,
      }
    ).lean();
  }

  const reason = retrieveOutreachEmailStatusReason({
    icpLeadMatch,
    magicReachEmail: message,
    languagePreference,
  });

  return {
    ...result,
    reason,
  };
};

exports.sendOutreachEmail = async ({
  communityObjectId,
  icpLeadMatchObjectId,
  outreachPurpose,
  title,
  content,
  userObjectId,
  learnerObjectId,
  languagePreference = 'en',
}) => {
  const templateType =
    TEMPLATE_LIBRARY_TYPES[`OUTREACH_EMAIL_${outreachPurpose}`];

  const [icpLeadMatch, latestTemplate] = await Promise.all([
    IcpLeadMatchModel.findOne({
      _id: icpLeadMatchObjectId,
      communityObjectId,
    }).lean(),
    TemplateLibraryModel.findOne({
      sourceObjectId: icpLeadMatchObjectId,
      source: TEMPLATE_SOURCE_TYPE.MAGIC_LEADS,
      communityObjectId,
      type: templateType,
    })
      .sort({ version: -1 })
      .lean(),
  ]);

  if (!icpLeadMatch || !latestTemplate) {
    throw new ParamError(
      'ICP Lead Match or Outreach Email Template not found'
    );
  }

  const [community, outreachCount, enrichedLead, learner] =
    await Promise.all([
      CommunityModel.findOne({
        _id: communityObjectId,
        isActive: true,
      }).lean(),
      commonService.getOutreachCountByLead({
        communityObjectId,
        leadObjectId: icpLeadMatch.leadObjectId,
      }),
      EnrichedLeadModel.findOne(
        {
          _id: icpLeadMatch.leadObjectId,
          isActive: true,
        },
        {
          enrichments: 0,
          email: 0,
          usage: 0,
          verification: 0,
          'aiProfileSummary.embedding': 0,
        }
      ).lean(),
      LearnerModel.findOne({
        _id: learnerObjectId,
        isActive: true,
      }).lean(),
    ]);

  if (!community) {
    throw new ParamError('Community not found');
  }

  if (!enrichedLead) {
    throw new ParamError('Lead not found or lead is inactive');
  }

  if (!learner) {
    throw new ParamError('Learner not found or learner is inactive');
  }

  if (
    enrichedLead.reachInfo?.optOutFromEmail?.communityCodes?.includes(
      community.code
    )
  ) {
    throw new ParamError('Lead has opted out of email');
  }

  if (outreachCount >= 2) {
    throw new ParamError('Outreach limit exceeded');
  }

  if (icpLeadMatch.status === MAGIC_LEADS_STATUS.UNDER_REVIEW) {
    throw new ParamError('Outreach email is under review');
  }

  const [communityOwnerLearner, icpProfile] = await Promise.all([
    LearnerModel.findOne({
      email: community.createdBy,
      isActive: true,
    }).lean(),
    IcpProfileModel.findOne({
      _id: icpLeadMatch.icpProfileObjectId,
      communityObjectId,
    }).lean(),
  ]);

  if (!communityOwnerLearner || !icpProfile) {
    throw new ParamError('Community owner or ICP profile not found');
  }

  const product =
    icpProfile.productType !== PRODUCT_TYPE.MEMBERSHIP
      ? await ProductModel.findOne({
          entityObjectId: icpProfile.entityObjectId,
          communityObjectId,
        }).lean()
      : null;

  const [magicReachEmail, aiContextInput] = await Promise.all([
    retrieveLastMagicReachEmail({
      icpLeadMatch,
    }),
    generateAiContextInput({
      lead: enrichedLead,
      community,
      product,
      communityOwnerLearner,
      outreachEmailTemplate: latestTemplate.metadata.outreachEmail,
    }),
  ]);

  const replaceMagicReachEmailObjectId =
    magicReachEmail &&
    magicReachEmail.status !== MAGIC_REACH_MESSAGE_STATUS.SENT;

  const messageData = {
    title,
    content,
    communityId: communityObjectId,
    sentBucketName: '',
    sentEmails: [],
    author: userObjectId,
    isDraft: true,
    isDemo: false,
    sentPlatforms: [PLATFORMS.EMAIL],
    selectedBuckets: [BUCKET_NAMES.MAGIC_LEADS_OUTREACH],
    selectedUsers: [],
    unselectedUsers: [],
    bucketFilters: {
      [BUCKET_NAMES.MAGIC_LEADS_OUTREACH]: {
        leadObjectId: icpLeadMatch.leadObjectId,
        icpProfileObjectId: icpLeadMatch.icpProfileObjectId,
        communityId: communityObjectId,
      },
    },
    bucketMetas: {
      [BUCKET_NAMES.MAGIC_LEADS_OUTREACH]: {
        outreachPurpose,
        entityObjectId: icpProfile.entityObjectId,
        productType: icpProfile.productType,
        aiContextInput,
      },
    },
  };

  const messageId = await magicReachComposeService.createMessage({
    authorUserObjectId: userObjectId,
    communityId: communityObjectId,
    messageData,
  });

  let result;

  if (replaceMagicReachEmailObjectId) {
    result = await IcpLeadMatchModel.updateOne(
      {
        _id: icpLeadMatchObjectId,
        communityObjectId,
        outReachCount: {
          $lt: 2,
        },
        'outreachEmailTemplates.magicReachEmailObjectId':
          magicReachEmail._id,
      },
      {
        $set: {
          'outreachEmailTemplates.$.magicReachEmailObjectId': messageId,
        },
      }
    );
  } else {
    result = await IcpLeadMatchModel.updateOne(
      {
        _id: icpLeadMatchObjectId,
        communityObjectId,
        outReachCount: {
          $lt: 2,
        },
      },
      {
        $push: {
          outreachEmailTemplates: {
            templateObjectId: latestTemplate._id,
            magicReachEmailObjectId: messageId,
            outreachPurpose,
            index: icpLeadMatch.outReachCount + 1,
          },
        },
      }
    );
  }

  if (result.modifiedCount === 0) {
    throw new ParamError('Outreach limit exceeded');
  }

  const message = await magicReachSendService.sendMessage({
    communityId: communityObjectId,
    messageId,
    messageData,
  });

  const postSendOutreachEmailResult = await this.postSendOutreachEmail({
    message,
    icpProfile,
    icpLeadMatch,
    languagePreference,
  });

  return postSendOutreachEmailResult;
};
