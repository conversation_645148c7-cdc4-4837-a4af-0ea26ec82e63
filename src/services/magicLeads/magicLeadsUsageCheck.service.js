const FeaturePermissionManager = require('@services/common/featurePermissionManager.service');
const { FEATURE_LIST_ID } = require('@constants/common');
const { retrieveActiveCommunity } = require('../common/common.service');
const magicLeadsUsageService = require('../featurePermissions/magicLeadsUsage.service');
const { ToUserError } = require('../../utils/error.util');
const logger = require('../logger.service');

const getLeadsResultLimit = async (communityObjectId) => {
  try {
    const community = await retrieveActiveCommunity(communityObjectId);

    const featurePermissionManager = new FeaturePermissionManager(
      community.config?.planType,
      community.featurePermissions
    );

    const resultsLimit = featurePermissionManager.getFeatureLimit(
      FEATURE_LIST_ID.MAGIC_LEADS_RESULTS_PER_SEARCH
    );
    const isFeatureAllowed = featurePermissionManager.isFeatureAllowed(
      FEATURE_LIST_ID.MAGIC_LEADS_RESULTS_PER_SEARCH
    );

    if (!isFeatureAllowed) {
      return {
        allowed: false,
        error: 'MAGIC_LEADS_RESULTS_NOT_ALLOWED',
        message:
          'Magic Leads results feature not allowed for current plan',
        limit: 0,
      };
    }

    return {
      allowed: true,
      limit: resultsLimit,
      allowedCount: resultsLimit,
    };
  } catch (error) {
    logger.error('Failed to check Magic Leads results limit', {
      error: error.message,
      communityId: communityObjectId,
    });
    throw error;
  }
};

const checkLeadsPermissions = async (communityObjectId) => {
  try {
    // Get community info for feature permissions
    const community = await retrieveActiveCommunity(communityObjectId);

    const featurePermissionManager = new FeaturePermissionManager(
      community.config?.planType,
      community.featurePermissions
    );

    // Check monthly usage limit BEFORE any lead generation
    const monthlyLimitCheck =
      await magicLeadsUsageService.checkMonthlyLimit({
        communityObjectId,
        featurePermissionManager,
      });

    if (!monthlyLimitCheck.allowed) {
      throw new ToUserError(monthlyLimitCheck.message);
    }

    // Check per-search results limit
    const permissionCheck = await getLeadsResultLimit(
      communityObjectId,
      5 // Default requested count
    );

    if (!permissionCheck.allowed) {
      throw new ToUserError(permissionCheck.message);
    }

    return {
      featurePermissionManager,
      monthlyLimitCheck,
      permissionCheck,
      requestedCount: permissionCheck.allowedCount,
    };
  } catch (error) {
    logger.error('Failed to check leads permissions', {
      error: error.message,
      communityId: communityObjectId,
    });
    throw error;
  }
};

const getFeaturePermissionManager = async (communityObjectId) => {
  const community = await retrieveActiveCommunity(communityObjectId);

  return new FeaturePermissionManager(
    community.config?.planType,
    community.featurePermissions
  );
};

const getMonthlyLimitCheck = async (communityObjectId) => {
  try {
    const community = await retrieveActiveCommunity(communityObjectId);

    const featurePermissionManager = new FeaturePermissionManager(
      community.config?.planType,
      community.featurePermissions
    );

    const monthlyLimitCheck =
      await magicLeadsUsageService.checkMonthlyLimit({
        communityObjectId,
        featurePermissionManager,
      });

    return monthlyLimitCheck;
  } catch (error) {
    logger.error('Failed to get monthly limit check', {
      error: error.message,
      communityId: communityObjectId,
    });

    // Return a default structure if there's an error
    return {
      allowed: false,
      limit: 0,
      currentUsage: 0,
      message: 'Failed to check monthly limit',
    };
  }
};

module.exports = {
  getLeadsResultLimit,
  checkLeadsPermissions,
  getFeaturePermissionManager,
  getMonthlyLimitCheck,
};
