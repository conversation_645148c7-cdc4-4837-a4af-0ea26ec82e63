const { ObjectId } = require('mongoose').Types;
const { MAGIC_LEADS_STATUS } = require('@constants/common');
const IcpLeadMatchesModel = require('@/src/models/magicLeads/icpLeadMatches.model');
const logger = require('../logger.service');

const CommunitySubscription = require('../../communitiesAPI/models/communitySubscriptions.model');
const ProgramParticipant = require('../../models/program/programParticipant.model');
const EventAttendee = require('../../communitiesAPI/models/eventAttendees.model');
const SessionAttendee = require('../../models/oneOnOneSessions/sessionAttendees.model');
const FolderViewer = require('../../models/product/folderViewers.model');
const {
  COMMUNITY_SUBSCRIPTION_STATUSES,
} = require('../../constants/common');
const { PRODUCT_TYPE } = require('../product/constants');
const { PARTICIPANT_PROGRAM_STATUS } = require('../program/constants');
const {
  COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES,
  COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES,
} = require('../../communitiesAPI/constants');
const { FOLDER_VIEWER_STATUS } = require('../folder/constants');

exports.getOutreachCountByLead = async ({
  communityObjectId,
  leadObjectId,
}) => {
  try {
    const result = await IcpLeadMatchesModel.aggregate([
      {
        $match: {
          communityObjectId: new ObjectId(communityObjectId),
          leadObjectId: new ObjectId(leadObjectId),
        },
      },
      {
        $group: {
          _id: null,
          totalOutreachCount: { $sum: '$outReachCount' },
          underReviewCount: {
            $sum: {
              $cond: [
                { $eq: ['$status', MAGIC_LEADS_STATUS.UNDER_REVIEW] },
                1,
                0,
              ],
            },
          },
        },
      },
    ]);

    return (
      (result[0]?.totalOutreachCount || 0) +
      (result[0]?.underReviewCount || 0)
    );
  } catch (error) {
    logger.error('Error getting outreach count by lead:', {
      error: error.message,
      communityObjectId,
      leadObjectId,
    });
    return 0;
  }
};

exports.getActiveSubscriptions = async ({ emails, communityCode }) => {
  try {
    if (!emails || emails.length === 0) {
      throw new Error('Must provide emails');
    }

    const existingMemberParams = {
      $or: [
        { status: COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT },
        {
          status: COMMUNITY_SUBSCRIPTION_STATUSES.CANCELLED,
          cancelledAt: { $gte: new Date() },
        },
      ],
    };

    const existingSubscriptions = await CommunitySubscription.find({
      communityCode,
      email: { $in: emails },
      ...existingMemberParams,
    }).lean();

    // For multiple emails, create a map of email -> subscription for quick lookup
    const subscriptionMap = {};
    existingSubscriptions.forEach((subscription) => {
      subscriptionMap[subscription.email] = subscription;
    });
    return subscriptionMap;
  } catch (error) {
    logger.error('Error getting active subscriptions:', {
      error: error.message,
      emails,
      communityCode,
    });
  }
  return {};
};

exports.checkProductPurchases = async ({
  productType,
  entityObjectId,
  learnerObjectIds,
}) => {
  try {
    if (!learnerObjectIds.length) {
      throw new Error('Must provide learnerObjectIds');
    }

    let purchases = [];

    switch (productType) {
      case PRODUCT_TYPE.CHALLENGE:
        purchases = await ProgramParticipant.find({
          programObjectId: entityObjectId,
          learnerObjectId: { $in: learnerObjectIds },
          status: PARTICIPANT_PROGRAM_STATUS.PARTICIPATED,
        }).lean();
        break;

      case PRODUCT_TYPE.EVENT:
        purchases = await EventAttendee.find({
          eventObjectId: entityObjectId,
          learnerObjectId: { $in: learnerObjectIds },
          status: COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING,
        }).lean();
        break;

      case PRODUCT_TYPE.SESSION:
        purchases = await SessionAttendee.find({
          sessionObjectId: entityObjectId,
          attendeeLearnerObjectId: { $in: learnerObjectIds },
          status: COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.BOOKED,
        }).lean();
        break;

      case PRODUCT_TYPE.COURSE:
      case PRODUCT_TYPE.DIGITAL_FILES:
        purchases = await FolderViewer.find({
          folderObjectId: entityObjectId,
          learnerObjectId: { $in: learnerObjectIds },
          status: {
            $in: [FOLDER_VIEWER_STATUS.PAID, FOLDER_VIEWER_STATUS.FREE],
          },
        }).lean();
        break;

      default:
        return {};
    }

    // For multiple learners, create a map of learnerObjectId -> purchase for quick lookup
    const purchaseMap = {};
    purchases.forEach((purchase) => {
      const learnerId =
        purchase.learnerObjectId || purchase.attendeeLearnerObjectId;
      purchaseMap[learnerId.toString()] = purchase;
    });
    return purchaseMap;
  } catch (error) {
    logger.error('Error checking product purchases:', {
      error: error.message,
      productType,
      entityObjectId,
      learnerObjectIds,
    });
    return {};
  }
};
