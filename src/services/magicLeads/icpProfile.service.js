const {
  createResponseCompletion,
  createEmbedding,
} = require('../../clients/openai.client');
const {
  SEARCH_FIELD_TYPE,
  ICP_PROFILE_STATUS,
  ALLOWED_COUNTRIES,
} = require('./constants');
const IcpProfilesModel = require('../../models/magicLeads/icpProfiles.model');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const { getProductData } = require('./productList.service');
const logger = require('../logger.service');
const { getNextMonthFirstDay } = require('./utils');
const { TEMPLATE_SOURCE_TYPE } = require('../../constants/common');

const TemplatePromptModel = require('../../models/ai/aiTemplatePrompts.model');
const { INPUT_ROLE_TYPE } = require('../aiCofounder/ai/constants');
const {
  checkLeadsPermissions,
} = require('./magicLeadsUsageCheck.service');
const promptService = require('../aiCofounder/ai/prompt');
const LearnerModel = require('../../models/learners.model');

// Language mapping for prompt templates
const LANGUAGE_MAP = {
  en: 'English (en)',
  'es-mx': 'Mexican Spanish (es-mx)',
  'pt-br': 'Brazilian Portuguese (pt-br)',
  ja: 'Japanese (ja)',
};

// Function call schema for generating complete ICP with multi-language support
const generateICPFunctionCall = (params) => ({
  type: 'function',
  name: `generate_ideal_customer_profile_in_${
    params?.targetLanguage ?? 'en'
  }`,
  description: `Generate an Ideal Customer Profile with search fields for lead generation. Root-level fields must be in ${
    LANGUAGE_MAP[params?.targetLanguage ?? 'en']
  }, en object fields must be in English.`,
  parameters: {
    type: 'object',
    properties: {
      idealCustomerProfile: {
        type: 'string',
        description: `MUST BE WRITTEN IN ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en']
        } ONLY. A **2-sentence, marketing-style insight** describing the ideal customer for this product. This will be shown directly to the creator, serving both as an analysis of the ICP and as context for why certain search fields (also generated by you) were chosen. Write this field entirely in ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en']
        }, not in English.`,
      },
      title: {
        type: 'string',
        description: `MUST BE WRITTEN IN ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en']
        } ONLY. A short UI label summarizing the ICP and search fields, used only for display. Write this field entirely in ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en']
        }, not in English.`,
      },
      icpVectorSearchContext: {
        type: 'string',
        description: `MUST BE WRITTEN IN ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en']
        } ONLY. A concise, information-dense description of what an ideal lead's profile looks like, based on the ICP and search fields. This field is used internally for vector search and relevance ranking. Write this field entirely in ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en']
        }, not in English.`,
      },
      countries: {
        type: 'array',
        items: {
          type: 'string',
          enum:
            ALLOWED_COUNTRIES[params?.targetLanguage ?? 'en'] ||
            ALLOWED_COUNTRIES.en,
        },
        description: `Array of country names where the ideal customers are likely to be located, in ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en']
        }. These should be the localized names corresponding to the same countries in the English version.`,
      },
      jobTitles: {
        type: 'array',
        items: {
          type: 'string',
        },
        description: `Array of real, searchable job titles from LinkedIn (e.g., "Marketing Manager", "Sales Executive") in ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en']
        }. Do not include lifestyle labels.`,
      },
      industries: {
        type: 'array',
        items: {
          type: 'string',
        },
        description: `Array of industry names where the ideal customers are likely to work, in ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en']
        }.`,
      },
      targetLanguage: {
        type: 'string',
        description: `Target language for the ICP.`,
        enum: [params?.targetLanguage ?? 'en'],
      },
      en: {
        type: 'object',
        description: 'English version of all fields',
        properties: {
          idealCustomerProfile: {
            type: 'string',
            description:
              'A **2-sentence, marketing-style insight** describing the ideal customer for this product. This will be shown directly to the creator, serving both as an analysis of the ICP and as context for why certain search fields (also generated by you) were chosen.',
          },
          title: {
            type: 'string',
            description:
              'A short UI label summarizing the ICP and search fields, used only for display.',
          },
          icpVectorSearchContext: {
            type: 'string',
            description:
              "A concise, information-dense description of what an ideal lead's profile looks like, based on the ICP and search fields. This field is used internally: once leads are found using the search fields generated, we use the `profileSummary` along with each lead's profile summary to perform vector search and relevance ranking. The creator will not see this.",
          },
          countries: {
            type: 'array',
            items: {
              type: 'string',
              enum: ALLOWED_COUNTRIES.en,
            },
            description: `Array of country names where the ideal customers are likely to be located.`,
          },
          jobTitles: {
            type: 'array',
            items: {
              type: 'string',
            },
            description:
              'Array of real, searchable job titles from LinkedIn (e.g., "Marketing Manager", "Sales Executive"). Do not include lifestyle labels.',
          },
          industries: {
            type: 'array',
            items: {
              type: 'string',
            },
            description:
              'Array of industry names where the ideal customers are likely to work.',
          },
        },
        required: [
          'idealCustomerProfile',
          'title',
          'icpVectorSearchContext',
          'countries',
          'jobTitles',
          'industries',
        ],
      },
    },
    required: [
      'idealCustomerProfile',
      'title',
      'icpVectorSearchContext',
      'countries',
      'jobTitles',
      'industries',
      'en',
    ],
    additionalProperties: false,
  },
});

// Function call schema for generating new ICP by CM (community manager flow)
const generateNewICPByCMFunctionCall = (params) => ({
  type: 'function',
  name: `generate_new_icp_by_cm_in_${params?.targetLanguage ?? 'en'}`,
  description: `Generate search fields, title, and vector search context for lead generation. Root-level fields must be in ${
    LANGUAGE_MAP[params.targetLanguage] || LANGUAGE_MAP['en']
  }, en object fields must be in English.`,
  parameters: {
    type: 'object',
    properties: {
      title: {
        type: 'string',
        description: `MUST BE WRITTEN IN ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en'] ||
          LANGUAGE_MAP['en']
        } ONLY. A short UI label summarizing the ICP and search fields, used only for display. Write this field entirely in ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en'] ||
          LANGUAGE_MAP['en']
        }, not in English.`,
      },
      icpVectorSearchContext: {
        type: 'string',
        description: `MUST BE WRITTEN IN ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en'] ||
          LANGUAGE_MAP['en']
        } ONLY. A concise, information-dense description of what an ideal lead's profile looks like, based on the ICP and search fields. This field is used internally for vector search and relevance ranking. Write this field entirely in ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en'] ||
          LANGUAGE_MAP['en']
        }, not in English.`,
      },
      countries: {
        type: 'array',
        items: {
          type: 'string',
          enum:
            ALLOWED_COUNTRIES[params?.targetLanguage ?? 'en'] ||
            ALLOWED_COUNTRIES.en,
        },
        description: `Array of country names where the ideal customers are likely to be located, in ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en'] ||
          LANGUAGE_MAP['en']
        }. These should be the localized names of the same countries provided in the English version.`,
      },
      jobTitles: {
        type: 'array',
        items: {
          type: 'string',
        },
        description: `Array of real, searchable job titles from LinkedIn (e.g., "Marketing Manager", "Sales Executive") in ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en'] ||
          LANGUAGE_MAP['en']
        }. Do not include lifestyle labels.`,
      },
      industries: {
        type: 'array',
        items: {
          type: 'string',
        },
        description: `Array of industry names where the ideal customers are likely to work, in ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en'] ||
          LANGUAGE_MAP['en']
        }.`,
      },
      en: {
        type: 'object',
        description: 'English version of all fields',
        properties: {
          title: {
            type: 'string',
            description:
              'A short UI label summarizing the ICP and search fields, used only for display.',
          },
          icpVectorSearchContext: {
            type: 'string',
            description:
              "A concise, information-dense description of what an ideal lead's profile looks like, based on the ICP and search fields. This field is used internally for vector search and relevance ranking.",
          },
          countries: {
            type: 'array',
            items: {
              type: 'string',
              enum: ALLOWED_COUNTRIES.en,
            },
            description: `Array of country names where the ideal customers are likely to be located.`,
          },
          jobTitles: {
            type: 'array',
            items: {
              type: 'string',
            },
            description:
              'Array of real, searchable job titles from LinkedIn (e.g., "Marketing Manager", "Sales Executive"). Do not include lifestyle labels.',
          },
          industries: {
            type: 'array',
            items: {
              type: 'string',
            },
            description:
              'Array of industry names where the ideal customers are likely to work.',
          },
        },
        required: [
          'title',
          'icpVectorSearchContext',
          'countries',
          'jobTitles',
          'industries',
        ],
      },
    },
    required: [
      'title',
      'icpVectorSearchContext',
      'countries',
      'jobTitles',
      'industries',
      'en',
    ],
    additionalProperties: false,
  },
});

// Function call schema for updating ICP from summary
const updateICPFromSummaryFunctionCall = (params) => ({
  type: 'function',
  name: `update_icp_from_summary_in_${params?.targetLanguage ?? 'en'}`,
  description: `Update ICP title, vector search context, and search fields based on new summary. Root-level fields must be in ${
    LANGUAGE_MAP[params?.targetLanguage ?? 'en']
  }, en object fields must be in English.`,
  parameters: {
    type: 'object',
    properties: {
      title: {
        type: 'string',
        description: `MUST BE WRITTEN IN ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en']
        } ONLY. A short UI label summarizing the updated ICP and search fields, used only for display. Write this field entirely in ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en']
        }, not in English.`,
      },
      icpVectorSearchContext: {
        type: 'string',
        description: `MUST BE WRITTEN IN ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en']
        } ONLY. A concise, information-dense description of what an ideal lead's profile looks like, based on the updated ICP and search fields. This field is used internally for vector search and relevance ranking. Write this field entirely in ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en']
        }, not in English.`,
      },
      countries: {
        type: 'array',
        items: {
          type: 'string',
          enum:
            ALLOWED_COUNTRIES[params?.targetLanguage ?? 'en'] ||
            ALLOWED_COUNTRIES.en,
        },
        description: `Array of country names where the ideal customers are likely to be located, in ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en']
        }. These should be the localized names corresponding to the same countries in the English version.`,
      },
      jobTitles: {
        type: 'array',
        items: {
          type: 'string',
        },
        description: `Array of real, searchable job titles from LinkedIn (e.g., "Marketing Manager", "Sales Executive") in ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en']
        }. Do not include lifestyle labels.`,
      },
      industries: {
        type: 'array',
        items: {
          type: 'string',
        },
        description: `Array of industry names where the ideal customers are likely to work, in ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en']
        }.`,
      },
      en: {
        type: 'object',
        description: 'English version of all fields',
        properties: {
          title: {
            type: 'string',
            description:
              'A short UI label summarizing the updated ICP and search fields, used only for display.',
          },
          icpVectorSearchContext: {
            type: 'string',
            description:
              "A concise, information-dense description of what an ideal lead's profile looks like, based on the updated ICP and search fields. This field is used internally for vector search and relevance ranking.",
          },
          countries: {
            type: 'array',
            items: {
              type: 'string',
              enum: ALLOWED_COUNTRIES.en,
            },
            description: `Array of country names where the ideal customers are likely to be located.`,
          },
          jobTitles: {
            type: 'array',
            items: {
              type: 'string',
            },
            description:
              'Array of real, searchable job titles from LinkedIn (e.g., "Marketing Manager", "Sales Executive"). Do not include lifestyle labels.',
          },
          industries: {
            type: 'array',
            items: {
              type: 'string',
            },
            description:
              'Array of industry names where the ideal customers are likely to work.',
          },
        },
        required: [
          'title',
          'icpVectorSearchContext',
          'countries',
          'jobTitles',
          'industries',
        ],
      },
    },
    required: [
      'title',
      'icpVectorSearchContext',
      'countries',
      'jobTitles',
      'industries',
      'en',
    ],
    additionalProperties: false,
  },
});

// Function call schema for updating ICP from search fields
const updateICPFromSearchFieldsFunctionCall = (params) => ({
  type: 'function',
  name: `update_icp_from_search_fields_in_${
    params?.targetLanguage ?? 'en'
  }`,
  description: `Update ICP title and vector search context based on updated search fields with detectedLanguage. Root-level fields must be in ${
    LANGUAGE_MAP[params?.targetLanguage ?? 'en']
  }, en object fields must be in English.`,
  parameters: {
    type: 'object',
    properties: {
      detectedLanguage: {
        type: 'string',
        description:
          'Detect the language of editedSearchFields and return it. Only accept: en, es-mx, pt-br, ja. If not one of these, return en.',
        enum: ['en', 'es-mx', 'pt-br', 'ja'],
      },
      title: {
        type: 'string',
        description: `MUST BE WRITTEN IN ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en']
        } ONLY. A short UI label summarizing the target audience implied by editedSearchFields and idealCustomerProfile. For display only. Write this field entirely in ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en']
        }, not in English.`,
      },
      icpVectorSearchContext: {
        type: 'string',
        description: `MUST BE WRITTEN IN ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en']
        } ONLY. A concise, information-dense description of what an ideal lead's profile looks like, based on editedSearchFields and idealCustomerProfile. This field is used internally for vector search and relevance ranking. Write this field entirely in ${
          LANGUAGE_MAP[params?.targetLanguage ?? 'en']
        }, not in English.`,
      },
      en: {
        type: 'object',
        description: 'English version of all fields',
        properties: {
          countries: {
            type: 'array',
            items: {
              type: 'string',
              enum: ALLOWED_COUNTRIES.en,
            },
            description:
              'Array of country names in English where the ideal customers are likely to be located.',
          },
          jobTitles: {
            type: 'array',
            items: {
              type: 'string',
            },
            description:
              'Array of real, searchable job titles from LinkedIn in English (e.g., "Marketing Manager", "Sales Executive"). Do not include lifestyle labels.',
          },
          industries: {
            type: 'array',
            items: {
              type: 'string',
            },
            description:
              'Array of industry names in English where the ideal customers are likely to work.',
          },
          title: {
            type: 'string',
            description:
              'A short UI label summarizing the target audience implied by editedSearchFields and idealCustomerProfile. For display only.',
          },
          icpVectorSearchContext: {
            type: 'string',
            description:
              "A concise, information-dense description of what an ideal lead's profile looks like, based on editedSearchFields and idealCustomerProfile. This field is used internally for vector search and relevance ranking.",
          },
        },
        required: [
          'countries',
          'jobTitles',
          'industries',
          'title',
          'icpVectorSearchContext',
        ],
      },
    },
    required: [
      'detectedLanguage',
      'title',
      'icpVectorSearchContext',
      'en',
    ],
    additionalProperties: false,
  },
});

// Helper: Clean originalAIGeneratedVersion for prompt usage (extract text from icpVectorSearchContext)
const cleanOriginalGeneratedVersionForPrompt = (
  originalAIGeneratedVersion
) => {
  if (!originalAIGeneratedVersion) return {};

  const cleaned = {};
  Object.keys(originalAIGeneratedVersion).forEach((lang) => {
    const versionData = originalAIGeneratedVersion[lang];
    cleaned[lang] = {
      ...versionData,
      // Extract only text from icpVectorSearchContext object
      icpVectorSearchContext:
        versionData.icpVectorSearchContext?.text ||
        versionData.icpVectorSearchContext,
    };
  });

  return cleaned;
};

// Helper: Extract function result from OpenAI response
const extractFunctionResult = (response) => {
  logger.info('Extracting function result from OpenAI response:', {
    hasOutput: !!response.output,
    outputLength: response.output?.length,
    firstOutputType: response.output?.[0]?.type,
    firstOutputStatus: response.output?.[0]?.status,
    firstOutputName: response.output?.[0]?.name,
  });

  // Check Responses API format
  if (response.output && response.output.length > 0) {
    const output = response.output[0];
    if (output.type === 'function_call') {
      if (output.status && output.status !== 'completed') {
        logger.warn(
          `Function call ${output.name} not completed. Status: ${output.status}`
        );
        throw new Error(
          `Function call not completed. Status: ${output.status}`
        );
      }

      if (output.arguments) {
        const result = JSON.parse(output.arguments);
        return result;
      }
    }
  }

  // Check Chat Completions format
  if (response.tool_calls && response.tool_calls.length > 0) {
    const toolCall = response.tool_calls[0];
    if (toolCall.function && toolCall.function.arguments) {
      return JSON.parse(toolCall.function.arguments);
    }
  }

  throw new Error(
    'Failed to extract function call result from OpenAI response'
  );
};

// Generate ICP for community manager flow (new flow)
const generateNewICPByCM = async (
  productData,
  icpSummary,
  targetLanguage = 'en'
) => {
  try {
    // Fetch the generate_new_icp_by_cm prompt template from database
    const templatePrompt = await TemplatePromptModel.findOne({
      type: 'generate_new_icp_by_cm',
      source: TEMPLATE_SOURCE_TYPE.MAGIC_LEADS,
      isActive: true,
    })
      .sort({ version: -1 })
      .lean();

    if (!templatePrompt) {
      throw new Error('No template found for generate_new_icp_by_cm');
    }

    // Replace {targetLanguage} placeholder with actual language if needed
    const languageName =
      LANGUAGE_MAP[targetLanguage] || LANGUAGE_MAP['en'];
    const prompt = promptService.replaceVariablesInPrompt({
      prompt: templatePrompt.prompt,
      variables: {
        targetLanguage: languageName,
      },
    });

    // Instruction message for ICP generation with language support
    const instruction = `You are an AI assistant helping a creator promote a digital product by identifying potential leads from our professional contacts database.
Your task is to generate search fields, title, and vector search context based on the provided ICP summary and product data.
Context & Considerations:
- Our lead database is primarily based on LinkedIn professional data (job titles, industries, company information, locations).
- The outputs you generate are meant to help the creator find leads who are both relevant to the product and likely to purchase it.
- All outputs must be designed to match leads in a LinkedIn-style professional database.
- CRITICAL COUNTRY RESTRICTION: You can ONLY use countries from our allowed list. Do NOT include China, Russia, or any other countries not in our allowed list, even if mentioned in the ICP summary. If the user mentions a disallowed country (like China), return an empty countries array instead of suggesting alternatives. Only include countries that are specifically relevant to the ICP, not the entire allowed list.
- CRITICAL LANGUAGE REQUIREMENT: You MUST generate the root-level fields (title, icpVectorSearchContext, countries, jobTitles, industries) in ${languageName} language ONLY. Do NOT write these fields in English.
- CRITICAL LANGUAGE REQUIREMENT: You MUST generate the 'en' object fields in English ONLY.
- If the target language is ${languageName}, write the main content in ${languageName}, never in English.
- Failure to follow the language requirements will result in an invalid response.`;

    // Create input with system prompts including the user's ICP summary
    const inputMessages = [
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: prompt,
      },
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: `Product Information:\n${JSON.stringify(productData)}`,
      },
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: `ICP Summary provided by user:\n${icpSummary}`,
      },
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: `ALLOWED COUNTRIES ONLY: You must ONLY use countries from this list: ${(
          ALLOWED_COUNTRIES[targetLanguage] || ALLOWED_COUNTRIES.en
        ).join(
          ', '
        )}. Do NOT use any other countries, including China, Russia, Japan, or any country not in this list. IMPORTANT: If the ICP mentions disallowed countries (like China, Japan), return an empty countries array []. Do NOT return the entire allowed countries list - only return specific countries that are relevant to the ICP requirements.`,
      },
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: `LANGUAGE INSTRUCTION: You are now operating in ${languageName} mode. All root-level fields (title, icpVectorSearchContext, countries, jobTitles, industries) must be written in ${languageName}. Only the 'en' object should contain English content. Think in ${languageName} and respond in ${languageName} for the main fields.

EXAMPLE FORMAT (if target language is Spanish):
{
  "title": "Emprendedores Tecnológicos en Latinoamérica",
  "icpVectorSearchContext": "Profesionales en startups que implementan sistemas de automatización en centros urbanos de Latinoamérica",
  "countries": ["México", "Colombia", "Argentina"],
  "jobTitles": ["Emprendedor", "Fundador", "Gerente de Producto"],
  "industries": ["Tecnología de la Información", "Software de Computación"],
  "en": {
    "title": "Tech Entrepreneurs in Latin America",
    "icpVectorSearchContext": "Startup professionals implementing automation systems in Latin American urban centers",
    "countries": ["Mexico", "Colombia", "Argentina"],
    "jobTitles": ["Entrepreneur", "Founder", "Product Manager"],
    "industries": ["Information Technology", "Computer Software"]
  }
}`,
      },
    ];

    const completionConfig = {
      instructions: instruction,
      input: inputMessages,
      temperature: 0.3,
      tools: [generateNewICPByCMFunctionCall({ targetLanguage })],
      tool_choice: 'required',
      truncation: 'auto',
    };

    const response = await createResponseCompletion(completionConfig);
    const result = extractFunctionResult(response);

    logger.info('New ICP generation by CM completed successfully');
    return result;
  } catch (error) {
    logger.error('Error generating new ICP by CM:', {
      error: error.message,
      targetLanguage,
    });
    throw error;
  }
};

// 1. Generate Multi-Language ICP: with instruction + prompt from DB + product data
const generateMultiLanguageICP = async (
  productData,
  targetLanguage = 'en'
) => {
  try {
    // Fetch the generate_icp prompt template from database
    const templatePrompt = await TemplatePromptModel.findOne({
      type: 'generate_icp',
      source: TEMPLATE_SOURCE_TYPE.MAGIC_LEADS,
      isActive: true,
    })
      .sort({ version: -1 })
      .lean();

    if (!templatePrompt) {
      throw new Error('No template found for generate_icp');
    }

    // Replace {targetLanguage} placeholder with actual language
    const languageName =
      LANGUAGE_MAP[targetLanguage] || LANGUAGE_MAP['en'];
    const prompt = promptService.replaceVariablesInPrompt({
      prompt: templatePrompt.prompt,
      variables: {
        targetLanguage: languageName,
      },
    });

    // Instruction message for multi-language ICP generation
    const instruction = `You are an AI assistant helping a creator promote a digital product by identifying potential leads from our professional contacts database.
Your task is to generate the fields required in a function call that supports lead search in multiple languages.
Context & Considerations:
- Our lead database is primarily based on LinkedIn professional data (job titles, industries, company information, locations).
- The outputs you generate are meant to help the creator find leads who are both relevant to the product and likely to purchase it.
- All outputs must be designed to match leads in a LinkedIn-style professional database.
- CRITICAL COUNTRY RESTRICTION: You can ONLY use countries from our allowed list. Do NOT include China, Russia, or any other countries not in our allowed list, even if the product or context suggests those countries. If the context mentions disallowed countries, return an empty countries array. Only include countries that are specifically relevant to the ICP, not the entire allowed list.
- CRITICAL LANGUAGE REQUIREMENT: You MUST generate the root-level fields (idealCustomerProfile, title, icpVectorSearchContext, countries, jobTitles, industries) in ${languageName} language ONLY. Do NOT write these fields in English.
- CRITICAL LANGUAGE REQUIREMENT: You MUST generate the 'en' object fields in English ONLY.
- If the target language is ${languageName}, write the main content in ${languageName}, never in English.
- Failure to follow the language requirements will result in an invalid response.`;

    // Create input with system prompts
    const inputMessages = [
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: prompt,
      },
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: `Product Information:\n${JSON.stringify(productData)}`,
      },
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: `ALLOWED COUNTRIES ONLY: You must ONLY use countries from this list: ${(
          ALLOWED_COUNTRIES[targetLanguage] || ALLOWED_COUNTRIES.en
        ).join(
          ', '
        )}. Do NOT use any other countries, including China, Russia, Japan, or any country not in this list. IMPORTANT: If the ICP mentions disallowed countries (like China, Japan), return an empty countries array []. Do NOT return the entire allowed countries list - only return specific countries that are relevant to the ICP requirements.`,
      },
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: `LANGUAGE INSTRUCTION: You are now operating in ${languageName} mode. All root-level fields (idealCustomerProfile, title, icpVectorSearchContext, countries, jobTitles, industries) must be written in ${languageName}. Only the 'en' object should contain English content. Think in ${languageName} and respond in ${languageName} for the main fields.

EXAMPLE FORMAT (if target language is Spanish):
{
  "idealCustomerProfile": "Los clientes ideales son emprendedores tecnológicos que buscan automatizar procesos. Valoran soluciones innovadoras y están dispuestos a invertir en herramientas de productividad.",
  "title": "Emprendedores Tecnológicos",
  "icpVectorSearchContext": "Profesionales en startups que implementan sistemas de automatización",
  "en": {
    "idealCustomerProfile": "Ideal customers are tech entrepreneurs seeking to automate processes. They value innovative solutions and are willing to invest in productivity tools.",
    "title": "Tech Entrepreneurs"
  }
}`,
      },
    ];

    logger.info('Generating multi-language ICP', {
      targetLanguage,
      languageName,
    });

    const completionConfig = {
      instructions: instruction,
      input: inputMessages,
      temperature: 0.3,
      tools: [generateICPFunctionCall({ targetLanguage })],
      tool_choice: 'required',
      truncation: 'auto',
    };

    const response = await createResponseCompletion(completionConfig);
    const result = extractFunctionResult(response);

    logger.info('Multi-language ICP generation completed successfully');
    return result;
  } catch (error) {
    logger.error('Error generating multi-language ICP:', {
      error: error.message,
      targetLanguage,
    });
    throw error;
  }
};

// 2. Update ICP summary: with prompt from DB + product data + new summary
const updateICPFromSummary = async (
  existingProfile,
  newIcpSummary,
  productData,
  targetLanguage = 'en'
) => {
  try {
    // Fetch the update_icp prompt template from database
    const templatePrompt = await TemplatePromptModel.findOne({
      type: 'update_icp',
      source: TEMPLATE_SOURCE_TYPE.MAGIC_LEADS,
      isActive: true,
    })
      .sort({ version: -1 })
      .lean();

    if (!templatePrompt) {
      throw new Error('No template found for update_icp');
    }

    // Replace {targetLanguage} placeholder with actual language if needed
    const languageName =
      LANGUAGE_MAP[targetLanguage] || LANGUAGE_MAP['en'];
    const prompt = promptService.replaceVariablesInPrompt({
      prompt: templatePrompt.prompt,
      variables: {
        updatedICP: newIcpSummary,
        originalGeneratedVersion: JSON.stringify(
          cleanOriginalGeneratedVersionForPrompt(
            existingProfile.originalAIGeneratedVersion
          )
        ),
        targetLanguage: languageName,
      },
    });

    const inputMessages = [
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: prompt,
      },
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: `Product Information:\n${JSON.stringify(productData)}`,
      },
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: `ALLOWED COUNTRIES ONLY: You must ONLY use countries from this list: ${(
          ALLOWED_COUNTRIES[targetLanguage] || ALLOWED_COUNTRIES.en
        ).join(
          ', '
        )}. Do NOT use any other countries, including China, Russia, Japan, or any country not in this list. IMPORTANT: If the ICP mentions disallowed countries (like China, Japan), return an empty countries array []. Do NOT return the entire allowed countries list - only return specific countries that are relevant to the ICP requirements.`,
      },
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: `LANGUAGE INSTRUCTION: You are now operating in ${languageName} mode. All root-level fields (title, icpVectorSearchContext, countries, jobTitles, industries) must be written in ${languageName}. Only the 'en' object should contain English content.`,
      },
    ];

    const completionConfig = {
      input: inputMessages,
      temperature: 0.3,
      tools: [updateICPFromSummaryFunctionCall({ targetLanguage })],
      tool_choice: 'required',
      truncation: 'auto',
    };

    const response = await createResponseCompletion(completionConfig);
    const result = extractFunctionResult(response);

    logger.info('ICP update from summary completed successfully');
    return result;
  } catch (error) {
    logger.error('Error updating ICP from summary:', {
      error: error.message,
    });
    throw error;
  }
};

// 3. Update search fields: with prompt from DB + product data + new search fields
const updateICPFromSearchFields = async (
  existingProfile,
  newSearchFields,
  productData,
  targetLanguage = 'en'
) => {
  try {
    // Fetch the update_icp_by_search_fields prompt template from database
    const templatePrompt = await TemplatePromptModel.findOne({
      type: 'update_icp_by_search_fields',
      source: TEMPLATE_SOURCE_TYPE.MAGIC_LEADS,
      isActive: true,
    })
      .sort({ version: -1 })
      .lean();

    if (!templatePrompt) {
      throw new Error('No template found for update_icp_by_search_fields');
    }

    // Replace {targetLanguage} placeholder with actual language if needed
    const languageName =
      LANGUAGE_MAP[targetLanguage] || LANGUAGE_MAP['en'];
    const prompt = promptService.replaceVariablesInPrompt({
      prompt: templatePrompt.prompt,
      variables: {
        editedSearchFields: JSON.stringify(newSearchFields),
        idealCustomerProfile: existingProfile.icpSummary,
        originalGeneratedVersion: JSON.stringify(
          cleanOriginalGeneratedVersionForPrompt(
            existingProfile.originalAIGeneratedVersion
          )
        ),
        targetLanguage: languageName,
      },
    });

    const inputMessages = [
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: prompt,
      },
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: `Product Information:\n${JSON.stringify(productData)}`,
      },
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: `ALLOWED COUNTRIES ONLY: You must ONLY use countries from this list: ${(
          ALLOWED_COUNTRIES[targetLanguage] || ALLOWED_COUNTRIES.en
        ).join(
          ', '
        )}. Do NOT use any other countries, including China, Russia, Japan, or any country not in this list. IMPORTANT: If the ICP mentions disallowed countries (like China, Japan), return an empty countries array []. Do NOT return the entire allowed countries list - only return specific countries that are relevant to the ICP requirements.`,
      },
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: `LANGUAGE INSTRUCTION: You are now operating in ${languageName} mode. All root-level fields (title, icpVectorSearchContext) must be written in ${languageName}. Only the 'en' object should contain English content.`,
      },
    ];

    logger.info('Updating ICP from search fields');

    const completionConfig = {
      input: inputMessages,
      temperature: 0.3,
      tools: [updateICPFromSearchFieldsFunctionCall({ targetLanguage })],
      tool_choice: 'required',
      truncation: 'auto',
    };

    const response = await createResponseCompletion(completionConfig);
    const result = extractFunctionResult(response);

    logger.info('ICP update from search fields completed successfully');
    return result;
  } catch (error) {
    logger.error('Error updating ICP from search fields:', {
      error: error.message,
    });
    throw error;
  }
};

// Helper: Validate search fields for CM flow (Flow B)
const validateSearchFields = (icpResponse) => {
  const validJobTitles = Array.isArray(icpResponse.jobTitles)
    ? icpResponse.jobTitles.filter(
        (title) =>
          title && typeof title === 'string' && title.trim().length > 0
      )
    : [];

  const validCountries = Array.isArray(icpResponse.countries)
    ? icpResponse.countries.filter(
        (country) =>
          country &&
          typeof country === 'string' &&
          country.trim().length > 0
      )
    : [];

  const validIndustries = Array.isArray(icpResponse.industries)
    ? icpResponse.industries.filter(
        (industry) =>
          industry &&
          typeof industry === 'string' &&
          industry.trim().length > 0
      )
    : [];

  logger.info(
    `Search fields validated: ${validJobTitles.length} job titles, ${validCountries.length} countries, ${validIndustries.length} industries`
  );

  // Transform to requirements format - always include all field types
  const searchFields = [
    {
      type: SEARCH_FIELD_TYPE.COUNTRY,
      values: validCountries,
    },
    {
      type: SEARCH_FIELD_TYPE.JOB_TITLE,
      values: validJobTitles,
    },
    {
      type: SEARCH_FIELD_TYPE.INDUSTRY,
      values: validIndustries,
    },
  ];

  return searchFields;
};

// Helper: Validate and transform ICP response format (reuses validateSearchFields)
const validateICP = (icp) => {
  // Check if this is the new multi-language format (from updateICPFromSummary)
  if (icp.en && typeof icp.en === 'object') {
    // Multi-language format - use target language data at root level
    const searchFields = validateSearchFields(icp);

    return {
      icpSummary: icp.idealCustomerProfile || '',
      title: icp.title || '',
      icpVectorSearchContext: icp.icpVectorSearchContext || '',
      searchFields,
      targetLanguage: icp.targetLanguage,
      localization: {
        en: {
          icpSummary: icp.en.idealCustomerProfile || '',
          title: icp.en.title || '',
          icpVectorSearchContext: icp.en.icpVectorSearchContext || '',
          searchFields: validateSearchFields(icp.en),
        },
      },
    };
  }
  // Legacy format - use the search fields validation logic
  const searchFields = validateSearchFields(icp);

  return {
    icpSummary: icp.idealCustomerProfile || '',
    title: icp.title || '',
    icpVectorSearchContext: icp.icpVectorSearchContext || '',
    searchFields,
    targetLanguage: icp.targetLanguage,
    localization: icp.localization,
  };
};

// Helper: Validate ICP response from updateICPFromSearchFields
const validateICPFromSearchFields = (icp) => {
  // Validate and transform target language search fields
  const targetLanguageSearchFields = validateSearchFields({
    countries: icp.countries || [],
    jobTitles: icp.jobTitles || [],
    industries: icp.industries || [],
  });

  // Validate English search fields from 'en' object
  const englishSearchFields = icp.en
    ? validateSearchFields({
        countries: icp.en.countries || [],
        jobTitles: icp.en.jobTitles || [],
        industries: icp.en.industries || [],
      })
    : [];

  return {
    title: icp.title || '',
    icpVectorSearchContext: icp.icpVectorSearchContext || '',
    searchFields: targetLanguageSearchFields,
    detectedLanguage: icp.detectedLanguage || 'en',
    localization: {
      en: {
        title: icp.en?.title || '',
        icpVectorSearchContext: icp.en?.icpVectorSearchContext || '',
        searchFields: englishSearchFields,
      },
    },
  };
};

// Helper: Generate vector embedding
const generateEmbedding = async (text) => {
  try {
    return await createEmbedding(text, 'text-embedding-3-small');
  } catch (error) {
    logger.error('Error generating embedding:', {
      error: error.message,
    });
    throw error;
  }
};

// Helper: Get community owner language preference
const getLanguagePreference = async (learnerObjectId) => {
  try {
    const learner = await LearnerModel.findById(learnerObjectId).lean();

    return learner?.languagePreference || 'en';
  } catch (error) {
    logger.warn('Failed to retrieve learner language preference:', {
      error: error.message,
      learnerObjectId,
    });
    return 'en'; // Fallback to English
  }
};

// Helper: Validate and transform multi-language ICP response
const validateMultiLanguageICP = (icp, targetLanguage) => {
  // Extract English data from the 'en' object
  const englishData = icp.en || {};

  // Target language data is at root level
  const targetData = {
    countries: icp.countries || [],
    jobTitles: icp.jobTitles || [],
    industries: icp.industries || [],
  };

  logger.info(
    `Validating multi-language ICP for target language: ${targetLanguage}`
  );

  // Transform search fields for target language - always include all field types
  const searchFields = [
    {
      type: SEARCH_FIELD_TYPE.COUNTRY,
      values: (targetData.countries || []).filter(
        (country) =>
          country &&
          typeof country === 'string' &&
          country.trim().length > 0
      ),
    },
    {
      type: SEARCH_FIELD_TYPE.JOB_TITLE,
      values: (targetData.jobTitles || []).filter(
        (title) =>
          title && typeof title === 'string' && title.trim().length > 0
      ),
    },
    {
      type: SEARCH_FIELD_TYPE.INDUSTRY,
      values: (targetData.industries || []).filter(
        (industry) =>
          industry &&
          typeof industry === 'string' &&
          industry.trim().length > 0
      ),
    },
  ];

  // Transform English search fields - always include all field types
  const englishSearchFields = [
    {
      type: SEARCH_FIELD_TYPE.COUNTRY,
      values: (englishData.countries || []).filter(
        (country) =>
          country &&
          typeof country === 'string' &&
          country.trim().length > 0
      ),
    },
    {
      type: SEARCH_FIELD_TYPE.JOB_TITLE,
      values: (englishData.jobTitles || []).filter(
        (title) =>
          title && typeof title === 'string' && title.trim().length > 0
      ),
    },
    {
      type: SEARCH_FIELD_TYPE.INDUSTRY,
      values: (englishData.industries || []).filter(
        (industry) =>
          industry &&
          typeof industry === 'string' &&
          industry.trim().length > 0
      ),
    },
  ];

  return {
    icpSummary: icp.idealCustomerProfile || '',
    title: icp.title || '',
    icpVectorSearchContext: icp.icpVectorSearchContext || '',
    searchFields,
    englishVersion: {
      icpSummary: englishData.idealCustomerProfile || '',
      title: englishData.title || '',
      icpVectorSearchContext: englishData.icpVectorSearchContext || '',
      searchFields: englishSearchFields,
    },
  };
};

// Main function: Generate ICP for product
const generateICPForProduct = async (
  communityObjectId,
  productType,
  entityObjectId,
  icpSummary = null,
  managerLearnerObjectId
) => {
  try {
    // Step 0: Get community code for language preference
    const community = await CommunityModel.findById(communityObjectId)
      .select('code')
      .lean();
    if (!community) {
      throw new Error('Community not found');
    }

    // Step 0.1: Get CM language preference
    let targetLanguage = await getLanguagePreference(
      managerLearnerObjectId
    );

    // Step 0.2: ICP Reuse Logic - Check for existing ICP

    // Step 1: Get comprehensive product data with community context
    const product = await getProductData(
      productType,
      entityObjectId,
      communityObjectId
    );
    if (!product) {
      throw new Error('Product not found');
    }

    // Step 2: Check permissions and limits
    const { monthlyLimitCheck } = await checkLeadsPermissions(
      communityObjectId
    );

    // Step 4: Generate ICP using OpenAI - Two flows based on icpSummary
    let icpResponse;
    let validatedICP;

    if (icpSummary) {
      // Find new profile with provided summary
      // Flow B: Community Manager flow - with ICP summary provided
      icpResponse = await generateNewICPByCM(
        product,
        icpSummary,
        targetLanguage
      );

      // For Flow B, we need to create a different validation since no idealCustomerProfile is generated
      // Now icpResponse has the same multi-language structure as generateMultiLanguageICP
      // But we need to add the icpSummary (idealCustomerProfile) to the structure
      const icpResponseWithSummary = {
        ...icpResponse,
        idealCustomerProfile: icpSummary, // Use provided summary as the idealCustomerProfile
        en: {
          ...icpResponse.en,
          idealCustomerProfile: icpSummary, // Use same summary for English version
        },
      };
      validatedICP = validateMultiLanguageICP(
        icpResponseWithSummary,
        targetLanguage
      );
    } else {
      // From find lead button in magic leads page
      let existingICP = await IcpProfilesModel.findOne({
        productType,
        entityObjectId,
      }).lean();

      if (existingICP && existingICP.originalAIGeneratedVersion) {
        targetLanguage = existingICP.targetLanguage;
        // Only overwrite if ICP profile status is not active
        // If status is not active, means this ICP is not used for leads generation yet
        // So we can overwrite all root-level fields with originalAIGeneratedVersion
        if (existingICP.status !== ICP_PROFILE_STATUS.ACTIVE) {
          // Restore root-level fields from originalAIGeneratedVersion
          const originalData =
            existingICP.originalAIGeneratedVersion[
              existingICP.targetLanguage
            ] || existingICP.originalAIGeneratedVersion['en'];

          const updateData = {
            title: originalData.title,
            searchFields: originalData.searchFields,
            icpSummary: originalData.idealCustomerProfile,
            icpVectorSearchContext: originalData.icpVectorSearchContext,
            localization: {
              en: existingICP.originalAIGeneratedVersion['en'],
            },
          };

          existingICP = await IcpProfilesModel.findByIdAndUpdate(
            existingICP._id,
            updateData,
            { new: true }
          ).lean();

          logger.info(
            `ICP reused and updated for ${productType} ${entityObjectId}`,
            {
              icpProfileId: existingICP._id,
              targetLanguage,
            }
          );
        }

        return {
          _id: existingICP._id,
          title: existingICP.title,
          icpSummary: existingICP.icpSummary,
          icpVectorSearchContext: {
            text: existingICP.icpVectorSearchContext?.text || '',
          },
          searchFields: existingICP.searchFields,
          allowedCountries:
            ALLOWED_COUNTRIES[targetLanguage] || ALLOWED_COUNTRIES.en,
          searchCountInfo: {
            limit: monthlyLimitCheck.monthlyLimit,
            count: monthlyLimitCheck.currentUsage,
            resetDateTime: getNextMonthFirstDay(),
          },
        };
      }

      // Flow A: Traditional Product-Based flow - generate multi-language ICP
      icpResponse = await generateMultiLanguageICP(
        product,
        targetLanguage
      );
      validatedICP = validateMultiLanguageICP(icpResponse, targetLanguage);
    }

    // Step 6: Generate vector embeddings for both languages
    const embedding = await generateEmbedding(
      validatedICP.icpVectorSearchContext
    );

    let englishEmbedding;
    if (
      validatedICP.englishVersion &&
      validatedICP.englishVersion.icpVectorSearchContext
    ) {
      englishEmbedding = await generateEmbedding(
        validatedICP.englishVersion.icpVectorSearchContext
      );
    }

    // Step 7: Prepare multi-language data structures
    const originalAIGeneratedVersion = {};
    const localization = {};

    // Always store English version
    if (validatedICP.englishVersion) {
      const icpInEn = {
        idealCustomerProfile: validatedICP.englishVersion.icpSummary,
        title: validatedICP.englishVersion.title,
        searchFields: validatedICP.englishVersion.searchFields,
        icpVectorSearchContext: {
          text: validatedICP.englishVersion.icpVectorSearchContext,
          embedding: englishEmbedding,
          generatedAt: new Date(),
          model: 'text-embedding-3-small',
          source: 'openai',
        },
      };
      originalAIGeneratedVersion['en'] = icpInEn;

      localization['en'] = icpInEn;
    }

    // Store target language version if different from English
    if (targetLanguage !== 'en') {
      originalAIGeneratedVersion[targetLanguage] = {
        idealCustomerProfile: validatedICP.icpSummary,
        title: validatedICP.title,
        searchFields: validatedICP.searchFields,
        icpVectorSearchContext: {
          text: validatedICP.icpVectorSearchContext,
          embedding,
          generatedAt: new Date(),
          model: 'text-embedding-3-small',
          source: 'openai',
        },
      };
    }

    // Step 8: Create and save ICP profile
    const icpProfile = new IcpProfilesModel({
      communityObjectId,
      entityObjectId,
      productType,
      title: validatedICP.title,
      searchFields: validatedICP.searchFields,
      icpSummary: validatedICP.icpSummary,
      icpVectorSearchContext: {
        text: validatedICP.icpVectorSearchContext,
        embedding,
        generatedAt: new Date(),
        model: 'text-embedding-3-small',
        source: 'openai',
      },
      targetLanguage,
      localization,
      originalAIGeneratedVersion,
      status: ICP_PROFILE_STATUS.DRAFT,
      managerLearnerObjectId,
    });

    const savedProfile = await icpProfile.save();

    logger.info(`ICP generated for ${productType} ${entityObjectId}`, {
      communityObjectId,
      icpProfileId: savedProfile._id,
    });

    return {
      _id: savedProfile._id,
      title: savedProfile.title,
      icpSummary: validatedICP.icpSummary,
      icpVectorSearchContext: {
        text: savedProfile.icpVectorSearchContext.text,
      }, // Return only text, no embedding
      searchFields: validatedICP.searchFields,
      allowedCountries:
        ALLOWED_COUNTRIES[targetLanguage] || ALLOWED_COUNTRIES.en,
      searchCountInfo: {
        limit: monthlyLimitCheck.monthlyLimit,
        count: monthlyLimitCheck.currentUsage,
        resetDateTime: getNextMonthFirstDay(),
      },
    };
  } catch (error) {
    logger.error('Error generating ICP for product:', {
      error: error.message,
      productType,
      entityObjectId,
    });
    throw error;
  }
};

// Main function: Update ICP profile content only (no status changes)
const updateICPProfile = async (
  icpProfileId,
  searchFields,
  icpSummary
) => {
  try {
    // Find the existing ICP profile
    const existingProfile = await IcpProfilesModel.findById(
      icpProfileId
    ).lean();
    if (!existingProfile) {
      throw new Error('ICP profile not found');
    }
    //Check permissions and limits
    const { monthlyLimitCheck } = await checkLeadsPermissions(
      existingProfile.communityObjectId
    );

    // Get the original product data - IMPORTANT: Always pass product data to AI
    const product = await getProductData(
      existingProfile.productType,
      existingProfile.entityObjectId,
      existingProfile.communityObjectId
    );
    if (!product) {
      throw new Error('Original product not found');
    }

    // Get target language from existing profile
    const targetLanguage = existingProfile.targetLanguage || 'en';

    let finalSearchFields = searchFields;
    let finalIcpSummary = icpSummary;
    let finalTitle = existingProfile.title;
    let finalIcpVectorSearchContext =
      existingProfile.icpVectorSearchContext?.text;
    let finalLocalization = existingProfile.localization;

    // Case 1: icpSummary updated - regenerate everything
    if (icpSummary) {
      logger.info('Updating ICP from summary with product data');
      const icpResponse = await updateICPFromSummary(
        existingProfile,
        icpSummary,
        product,
        targetLanguage
      );
      const validatedICP = validateICP(icpResponse);

      finalSearchFields = validatedICP.searchFields;
      finalTitle = validatedICP.title || finalTitle;
      finalIcpVectorSearchContext = validatedICP.icpVectorSearchContext;

      // Update localization if new data is available
      if (validatedICP.localization) {
        finalLocalization = {
          ...finalLocalization,
          ...validatedICP.localization,
        };
      }
    }
    // Case 2: searchFields updated - regenerate title and vector context
    else if (searchFields) {
      logger.info('Updating ICP from search fields with product data');
      finalIcpSummary = existingProfile.icpSummary;

      const icpResponse = await updateICPFromSearchFields(
        existingProfile,
        searchFields,
        product,
        targetLanguage
      );
      const validatedICP = validateICPFromSearchFields(icpResponse);

      // Don't override finalSearchFields since we want to keep the original input
      // but update the other fields based on the AI response
      finalTitle = validatedICP.title || finalTitle;
      finalIcpVectorSearchContext = validatedICP.icpVectorSearchContext;

      // Update localization with English version from AI response
      if (validatedICP.localization?.en) {
        finalLocalization = {
          ...finalLocalization,
          en: {
            ...finalLocalization?.en,
            title: validatedICP.localization.en.title,
            icpVectorSearchContext:
              validatedICP.localization.en.icpVectorSearchContext,
            searchFields: validatedICP.localization.en.searchFields,
          },
        };
      }
    }

    // Generate embedding for updated vector search context
    let icpVectorSearchContextWithEmbedding;
    if (finalIcpVectorSearchContext) {
      const embedding = await generateEmbedding(
        finalIcpVectorSearchContext
      );
      icpVectorSearchContextWithEmbedding = {
        text: finalIcpVectorSearchContext,
        embedding,
        generatedAt: new Date(),
        model: 'text-embedding-3-small',
        source: 'openai',
      };
    }

    // Generate embedding for localization.en.icpVectorSearchContext if it exists
    if (finalLocalization?.en?.icpVectorSearchContext) {
      const enEmbedding = await generateEmbedding(
        finalLocalization.en.icpVectorSearchContext
      );
      finalLocalization.en.icpVectorSearchContext = {
        text: finalLocalization.en.icpVectorSearchContext,
        embedding: enEmbedding,
        generatedAt: new Date(),
        model: 'text-embedding-3-small',
        source: 'openai',
      };
    }

    // Prepare update data
    const updateData = {};
    if (finalSearchFields) updateData.searchFields = finalSearchFields;
    if (finalIcpSummary) updateData.icpSummary = finalIcpSummary;
    if (finalTitle) updateData.title = finalTitle;
    if (finalLocalization) updateData.localization = finalLocalization;
    if (icpVectorSearchContextWithEmbedding) {
      updateData.icpVectorSearchContext =
        icpVectorSearchContextWithEmbedding;
    }

    const updatedProfile = await IcpProfilesModel.findByIdAndUpdate(
      icpProfileId,
      updateData,
      { new: true }
    ).lean();

    logger.info(`ICP profile ${icpProfileId} updated successfully`);

    return {
      _id: updatedProfile._id,
      title: updatedProfile.title,
      icpSummary: finalIcpSummary,
      icpVectorSearchContext: {
        text: updatedProfile.icpVectorSearchContext.text,
      }, // Return only text, no embedding
      searchFields: finalSearchFields,
      allowedCountries:
        ALLOWED_COUNTRIES[targetLanguage] || ALLOWED_COUNTRIES.en,
      searchCountInfo: {
        limit: monthlyLimitCheck.monthlyLimit,
        count: monthlyLimitCheck.currentUsage,
        resetDateTime: getNextMonthFirstDay(),
      },
    };
  } catch (error) {
    logger.error('Error updating ICP profile:', {
      error: error.message,
      icpProfileId,
    });
    throw error;
  }
};

module.exports = {
  generateICPFunctionCall,
  generateICPForProduct,
  updateICPProfile,
  updateICPFromSummary,
  updateICPFromSearchFields,
  validateICP,
  generateEmbedding,
};
