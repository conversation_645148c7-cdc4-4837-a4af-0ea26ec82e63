const { ObjectId } = require('mongoose').Types;
const EnrichedLeadsModel = require('../../models/magicLeads/enrichedLeads.model');
const IcpProfilesModel = require('../../models/magicLeads/icpProfiles.model');
const IcpLeadMatchesModel = require('../../models/magicLeads/icpLeadMatches.model');
const CommunityProduct = require('../../models/product/communityProduct.model');
const LearnerModel = require('../../models/learners.model');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const { getBasicProductData } = require('./productList.service');
const {
  getActiveSubscriptions,
  checkProductPurchases,
} = require('./common.service');
const logger = require('../logger.service');
const {
  EMAIL_VERIFICATION_STATUS,
  MAGIC_LEADS_STATUS,
} = require('../../constants/common');
const { PRODUCT_PRICE_TYPE } = require('../product/constants');
const {
  getFeaturePermissionManager,
} = require('./magicLeadsUsageCheck.service');
const magicLeadsUsageService = require('../featurePermissions/magicLeadsUsage.service');
const { getNextMonthFirstDay } = require('./utils');
const { ICP_PROFILE_STATUS } = require('./constants');
const commonService = require('./common.service');

exports.getProfileImageFromSocialProfiles = (socialProfiles) => {
  if (
    !socialProfiles ||
    !Array.isArray(socialProfiles) ||
    socialProfiles.length === 0
  ) {
    return null;
  }

  return socialProfiles[0].profileImageUrl || null;
};

const filterJoinedCommunityLeads = async ({
  enrichedLeads,
  communityCode,
  productType,
  entityObjectId,
}) => {
  if (enrichedLeads.length === 0) return [];

  // Get all learners by emails in one query
  const emails = enrichedLeads.map((lead) => lead.email);
  const learners = await LearnerModel.find({
    email: { $in: emails },
    isActive: true,
  })
    .select('_id email')
    .lean();

  if (learners.length === 0) return [];

  // Create email -> learner mapping
  const learnerMap = {};
  learners.forEach((learner) => {
    learnerMap[learner.email] = learner;
  });

  // Get subscriptions and purchases in bulk
  const learnerObjectIds = learners.map((learner) => learner._id);
  const [subscriptionMap, purchaseMap] = await Promise.all([
    getActiveSubscriptions({ emails, communityCode }),
    checkProductPurchases({
      productType,
      entityObjectId,
      learnerObjectIds,
    }),
  ]);

  // Filter leads that have subscriptions but haven't purchased
  const joinedLeads = [];
  for (const lead of enrichedLeads) {
    const learner = learnerMap[lead.email];
    if (!learner) continue;

    const subscription = subscriptionMap[lead.email];
    const hasPurchased = purchaseMap[learner._id.toString()];

    // Only add leads that have joined the community but haven't purchased the product
    if (subscription && !hasPurchased) {
      joinedLeads.push(lead._id);
    }
  }

  return joinedLeads;
};

const filterPurchasedProductLeads = async ({
  enrichedLeads,
  productType,
  entityObjectId,
}) => {
  if (enrichedLeads.length === 0) return [];

  // Get all learners by emails in one query
  const emails = enrichedLeads.map((lead) => lead.email);
  const learners = await LearnerModel.find({
    email: { $in: emails },
    isActive: true,
  })
    .select('_id email')
    .lean();

  if (learners.length === 0) return [];

  // Create email -> learner mapping
  const learnerMap = {};
  learners.forEach((learner) => {
    learnerMap[learner.email] = learner;
  });

  // Get purchases in bulk
  const learnerObjectIds = learners.map((learner) => learner._id);
  const purchaseMap = await checkProductPurchases({
    productType,
    entityObjectId,
    learnerObjectIds,
  });

  // Filter leads that have purchased the product
  const purchasedLeads = [];
  for (const lead of enrichedLeads) {
    const learner = learnerMap[lead.email];
    if (!learner) continue;

    const hasPurchased = purchaseMap[learner._id.toString()];
    if (hasPurchased) {
      purchasedLeads.push(lead._id);
    }
  }

  return purchasedLeads;
};

const handleStatusFiltering = async ({
  statusFilter,
  currentIcpProfile,
  productType,
  entityObjectId,
  communityCode,
}) => {
  try {
    const validLeadIds = [];

    // Get all leads for this ICP profile first
    const allIcpLeadMatches = await IcpLeadMatchesModel.find({
      icpProfileObjectId: currentIcpProfile._id,
    })
      .select('leadObjectId status')
      .lean();

    const allLeadIds = allIcpLeadMatches.map(
      (match) => match.leadObjectId
    );

    if (allLeadIds.length === 0) {
      return [];
    }

    // Get enriched leads with emails
    const enrichedLeads = await EnrichedLeadsModel.find({
      _id: { $in: allLeadIds },
    })
      .select('email')
      .lean();

    // Separate special status filters from regular ones
    const specialStatuses = [
      MAGIC_LEADS_STATUS.JOINED_COMMUNITY,
      MAGIC_LEADS_STATUS.PURCHASED_PRODUCT,
      MAGIC_LEADS_STATUS.ACCESS_GRANTED,
    ];

    const regularStatuses = statusFilter.filter(
      (status) => !specialStatuses.includes(status)
    );

    // Handle special status filters
    if (statusFilter.includes(MAGIC_LEADS_STATUS.JOINED_COMMUNITY)) {
      const joinedCommunityLeads = await filterJoinedCommunityLeads({
        enrichedLeads,
        communityCode,
        productType,
        entityObjectId,
      });
      validLeadIds.push(...joinedCommunityLeads);
    }

    if (
      statusFilter.includes(MAGIC_LEADS_STATUS.PURCHASED_PRODUCT) ||
      statusFilter.includes(MAGIC_LEADS_STATUS.ACCESS_GRANTED)
    ) {
      const purchasedLeads = await filterPurchasedProductLeads({
        enrichedLeads,
        productType,
        entityObjectId,
      });
      validLeadIds.push(...purchasedLeads);
    }

    // Handle regular status filters (like MAIL_SENT, NOT_REACHED_OUT, etc.)
    if (regularStatuses.length > 0) {
      const regularStatusLeads = allIcpLeadMatches
        .filter((match) => regularStatuses.includes(match.status))
        .map((match) => match.leadObjectId);
      validLeadIds.push(...regularStatusLeads);
    }

    // Remove duplicates and return
    return [...new Set(validLeadIds.map((id) => id.toString()))].map(
      (id) => new ObjectId(id)
    );
  } catch (error) {
    logger.error('Error in handleStatusFiltering:', {
      error: error.message,
      statusFilter,
    });
    return [];
  }
};

const enhanceLeadWithStatusProcessing = async ({
  lead,
  match,
  communityId,
  communityCode,
  productType,
  entityObjectId,
  leadEmail,
}) => {
  try {
    // Add default additional fields
    lead.outreachLimitHit = false;
    lead.outReachedForOtherProduct = false;
    lead.optOutFromEmail = false;

    // Get total outreach count
    const outreachCount = await commonService.getOutreachCountByLead({
      communityObjectId: communityId,
      leadObjectId: match.leadObjectId._id,
    });

    // Check if outreach count >= 2
    if (outreachCount >= 2) {
      lead.outreachLimitHit = true;
    }

    // Check if contacted for other products
    const otherProductOutreach = await IcpLeadMatchesModel.findOne({
      communityObjectId: communityId,
      icpProfileObjectId: { $ne: match.icpProfileObjectId },
      leadObjectId: match.leadObjectId._id,
      outReachCount: { $gt: 0 },
    }).lean();

    if (otherProductOutreach) {
      lead.outReachedForOtherProduct = true;
    }

    // Return optout info from leads
    const { reachInfo } = lead;
    if (
      reachInfo?.optOutFromEmail?.communityCodes?.includes(communityCode)
    ) {
      lead.optOutFromEmail = true;
    }
    delete lead.reachInfo;

    // Enhanced status check for all leads
    if (leadEmail && communityCode) {
      const learner = await LearnerModel.findOne({
        email: leadEmail,
        isActive: true,
      }).lean();

      if (learner) {
        // First Priority: Check if learner purchased the product
        const purchaseMap = await checkProductPurchases({
          productType,
          entityObjectId,
          learnerObjectIds: [learner._id],
        });

        if (purchaseMap[learner._id.toString()]) {
          // Get product details to determine final status
          const productDetails = await CommunityProduct.findOne({
            productType,
            entityObjectId,
          })
            .select('priceType')
            .lean();

          if (productDetails) {
            if (productDetails.priceType === PRODUCT_PRICE_TYPE.FREE) {
              lead.status = MAGIC_LEADS_STATUS.ACCESS_GRANTED;
            } else if (
              [
                PRODUCT_PRICE_TYPE.FIXED,
                PRODUCT_PRICE_TYPE.FLEXIBLE,
              ].includes(productDetails.priceType)
            ) {
              lead.status = MAGIC_LEADS_STATUS.PURCHASED_PRODUCT;
            }
          }
        } else {
          // Second Priority: Check for active subscription
          const subscriptionMap = await getActiveSubscriptions({
            emails: [leadEmail],
            communityCode,
          });

          if (subscriptionMap[leadEmail]) {
            lead.status = MAGIC_LEADS_STATUS.JOINED_COMMUNITY;
          }
        }
      }
    }
  } catch (error) {
    logger.error('Error enhancing lead with status processing:', {
      error: error.message,
      leadId: lead.leadObjectId,
    });
  }
};

exports.getICPProfilesAndLeads = async (
  communityId,
  productType,
  entityObjectId,
  icpProfileId = null,
  pageNo = 1,
  pageSize = 15,
  searchName = null,
  statusFilter = null
) => {
  try {
    let currentIcpProfile;

    // Get all active ICP profiles for this product
    const icpProfiles = await IcpProfilesModel.find({
      communityObjectId: communityId,
      productType,
      entityObjectId,
      status: ICP_PROFILE_STATUS.ACTIVE,
    })
      .sort({ createdAt: -1 })
      .lean();

    if (icpProfiles.length === 0) {
      throw new Error('No ICP profiles found for this product');
    }

    if (icpProfileId) {
      // If icpProfileId is provided, get that specific active ICP profile
      currentIcpProfile = await IcpProfilesModel.findOne({
        _id: icpProfileId,
        status: ICP_PROFILE_STATUS.ACTIVE,
      }).lean();

      if (!currentIcpProfile) {
        throw new Error('Active ICP profile not found');
      }
    } else {
      // Use the latest (first) ICP profile
      currentIcpProfile = icpProfiles[0];
    }

    const idealCustomerProfiles = await Promise.all(
      icpProfiles.map(async (icp) => {
        // Get leads count for this ICP profile
        const leadsCount = await IcpLeadMatchesModel.countDocuments({
          icpProfileObjectId: icp._id,
        });

        const result = {
          _id: icp._id,
          title: icp.title,
          icpSummary: icp.icpSummary,
          searchFields: icp.searchFields,
          leadsCount,
        };
        if (result._id.toString() === currentIcpProfile._id.toString()) {
          currentIcpProfile = result;
        }
        return result;
      })
    );

    // Get basic product data (title only - used in multiple places)
    const product = await getBasicProductData(productType, entityObjectId);

    // Get community details for subscription checks
    const community = await CommunityModel.findOne({ _id: communityId })
      .select('code')
      .lean();
    const communityCode = community?.code;

    // Calculate pagination
    const skip = (pageNo - 1) * pageSize;

    let filteredLeadIds = [];

    // Optimized Status Filtering (before search)
    if (statusFilter) {
      const statusFilterLeadIds = await handleStatusFiltering({
        statusFilter,
        currentIcpProfile,
        productType,
        entityObjectId,
        communityCode,
      });

      filteredLeadIds = statusFilterLeadIds;
    }

    // Step 1: If searchName is provided, use Atlas Search to find leads by name
    if (searchName) {
      // Use filtered lead IDs if status filtering was applied
      let icpLeadIds = [];

      if (filteredLeadIds.length > 0) {
        // If status filtering was applied, use those filtered lead IDs
        icpLeadIds = filteredLeadIds;
      } else {
        // Get all lead IDs for this ICP profile
        const icpLeadQuery = { icpProfileObjectId: currentIcpProfile._id };

        const allIcpLeadMatches = await IcpLeadMatchesModel.find(
          icpLeadQuery
        )
          .select('leadObjectId')
          .lean();

        icpLeadIds = allIcpLeadMatches.map((match) => match.leadObjectId);
      }

      // Use Atlas Search to find leads by name within the filtered ICP leads
      if (icpLeadIds.length > 0) {
        const searchPipeline = [
          {
            $search: {
              index: 'enriched_leads_search',
              compound: {
                should: [
                  {
                    phrase: {
                      path: 'fullName',
                      query: searchName,
                      slop: 0,
                      score: {
                        boost: { value: 10 },
                      },
                    },
                  },
                  // Text search - normal priority
                  {
                    text: {
                      path: 'fullName',
                      query: searchName,
                      score: {
                        boost: { value: 1.0 },
                      },
                    },
                  },
                ],
                minimumShouldMatch: 1,
                must: [
                  {
                    equals: {
                      path: 'isActive',
                      value: true,
                    },
                  },
                ],
                filter: {
                  in: {
                    path: '_id',
                    value: icpLeadIds,
                  },
                },
              },
            },
          },
          {
            $project: { _id: 1 },
          },
        ];

        const matchingLeads = await EnrichedLeadsModel.aggregate(
          searchPipeline
        );
        filteredLeadIds = matchingLeads.map((lead) => lead._id);
      }

      // If no leads found by name, return empty results
      if (!filteredLeadIds || filteredLeadIds.length === 0) {
        return {
          product: {
            _id: entityObjectId,
            productType,
            title: product.title,
            thumbnail: product.coverImg,
          },
          currentIcpProfile,
          idealCustomerProfiles,
          leads: [],
          searchCountInfo: {
            limit: 0,
            count: 0,
            resetDateTime: getNextMonthFirstDay(),
          },
          metadata: {
            total: 0,
            limit: pageSize,
            page: pageNo,
            pages: 0,
          },
        };
      }
    }

    // Step 2: Build the final query for IcpLeadMatches
    const matchQuery = {
      icpProfileObjectId: currentIcpProfile._id,
    };

    // If name search or status filter was performed, use the filtered lead IDs from search
    if (statusFilter || searchName) {
      matchQuery.leadObjectId = { $in: filteredLeadIds };
    }

    // Get total count for pagination metadata
    const totalLeads = await IcpLeadMatchesModel.countDocuments(
      matchQuery
    );

    // Get paginated lead matches
    // Sort by vectorScore in descending order
    // Similarity Score: The vectorSearchScore is a similarity score (typically between 0 and 1) that indicates how closely the lead's profile matches the ICP's
    // characteristics
    // Higher Score = Better Match: A higher vectorScore means the lead is more similar to the ideal customer profile, making them a better potential match
    const leadMatches = await IcpLeadMatchesModel.find(matchQuery)
      .populate('leadObjectId')
      .sort({ vectorSimilarity: -1, _id: 1 })
      .skip(skip)
      .limit(pageSize)
      .lean();

    // Get feature permission manager for monthly limit check
    const featurePermissionManager = await getFeaturePermissionManager(
      communityId
    );

    const monthlyLimitCheck =
      await magicLeadsUsageService.checkMonthlyLimit({
        communityObjectId: communityId,
        featurePermissionManager,
      });

    // Enhanced leads data formatting with status processing
    const leads = await Promise.all(
      leadMatches.map(async (match) => {
        const baseLeadData = {
          _id: match._id,
          leadObjectId: match.leadObjectId._id,
          fullName: match.leadObjectId.fullName,
          profileImage: this.getProfileImageFromSocialProfiles(
            match.leadObjectId.socialProfiles
          ),
          jobTitle: match.leadObjectId.jobTitle,
          emailVerified:
            match.leadObjectId.verification?.email?.status ===
            EMAIL_VERIFICATION_STATUS.VALID,
          city: match.leadObjectId.location?.city || null,
          country: match.leadObjectId.location?.countryName,
          companyName: match.leadObjectId.company?.name,
          companyWebsite: match.leadObjectId.company?.website,
          socialProfiles:
            match.leadObjectId.socialProfiles?.length > 0
              ? match.leadObjectId.socialProfiles.map((profile) => ({
                  platform: profile.platform,
                  url: profile.url,
                }))
              : [],
          aiSummary: match.aiSummary,
          hasOutreach: match.outReachCount > 0,
          status: match.status,
          vectorScore: match.vectorSimilarity,
          reachInfo: match.leadObjectId.reachInfo,
          outreachEmailTemplates: match.outreachEmailTemplates,
        };

        // Enhanced status processing and additional fields
        await enhanceLeadWithStatusProcessing({
          lead: baseLeadData,
          match,
          communityId,
          communityCode,
          productType,
          entityObjectId,
          leadEmail: match.leadObjectId.email,
        });

        return baseLeadData;
      })
    );

    // Sort by vectorScore (higher scores first) with _id as tiebreaker for consistent pagination
    leads.sort((a, b) => {
      if (b.vectorScore !== a.vectorScore) {
        return b.vectorScore - a.vectorScore; // Higher score first
      }
      // Use _id for consistent ordering when scores are equal
      return a._id.toString().localeCompare(b._id.toString());
    });

    return {
      product: {
        _id: entityObjectId,
        productType,
        title: product.title,
        thumbnail: product.coverImg,
      },
      currentIcpProfile,
      idealCustomerProfiles,
      leads,
      searchCountInfo: {
        limit: monthlyLimitCheck.monthlyLimit,
        count: monthlyLimitCheck.currentUsage,
        resetDateTime: getNextMonthFirstDay(),
      },
      metadata: {
        total: totalLeads,
        limit: pageSize,
        page: pageNo,
        pages: Math.ceil(totalLeads / pageSize),
      },
    };
  } catch (error) {
    logger.error('Error getting ICP profiles and leads:', {
      error: error.message,
    });
    throw error;
  }
};

exports.getLeadsByBatchIds = async (icpLeadsMatchObjectIds) => {
  try {
    const leadMatches = await IcpLeadMatchesModel.find({
      _id: { $in: icpLeadsMatchObjectIds },
    })
      .select('_id aiSummary')
      .lean();

    return leadMatches.map((match) => ({
      _id: match._id,
      ...(match.aiSummary && { aiSummary: match.aiSummary }),
    }));
  } catch (error) {
    logger.error('Error getting leads by batch IDs:', {
      error: error.message,
      icpLeadsMatchObjectIds,
    });
    throw error;
  }
};
