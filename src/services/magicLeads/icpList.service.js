const IcpProfilesModel = require('../../models/magicLeads/icpProfiles.model');
const IcpLeadMatchesModel = require('../../models/magicLeads/icpLeadMatches.model');
const EnrichedLeadsModel = require('../../models/magicLeads/enrichedLeads.model');
const CommunityProduct = require('../../models/product/communityProduct.model');
const LearnerModel = require('../../models/learners.model');
const { buildAtlasSearchQuery } = require('./leadsGeneration.service');
const {
  getActiveSubscriptions,
  checkProductPurchases,
} = require('./common.service');
const { ICP_PROFILE_STATUS } = require('./constants');
const { PRODUCT_PRICE_TYPE } = require('../product/constants');
const logger = require('../logger.service');
const { retrieveActiveCommunity } = require('../common/common.service');

const getTotalAvailableLeadsCount = async (
  icpProfile,
  excludeLeadIds = []
) => {
  try {
    const searchQuery = buildAtlasSearchQuery(
      icpProfile,
      excludeLeadIds,
      true
    );

    const pipeline = [
      {
        $search: { index: 'enriched_leads_search', ...searchQuery },
      },
      {
        $count: 'totalAvailable',
      },
    ];

    const result = await EnrichedLeadsModel.aggregate(pipeline);
    return result[0]?.totalAvailable || 0;
  } catch (error) {
    logger.error('Error getting total available leads count:', {
      error: error.message,
      icpProfileId: icpProfile._id,
    });
    return 0;
  }
};

const calculateStatusDistribution = async (
  icpProfileId,
  productType,
  entityObjectId,
  communityCode
) => {
  try {
    // 1. Get all leads for this ICP profile with populated lead data
    const allLeads = await IcpLeadMatchesModel.find({
      icpProfileObjectId: icpProfileId,
    })
      .populate('leadObjectId', 'email')
      .lean();

    if (allLeads.length === 0) return {};

    const statusCounts = {};

    // 2. Get all learner ids by leads email
    const emails = allLeads
      .map((lead) => lead.leadObjectId?.email)
      .filter(Boolean);
    const learners = await LearnerModel.find({
      email: { $in: emails },
      isActive: true,
    })
      .select('_id email')
      .lean();

    const learnerMap = {}; // email -> learner
    learners.forEach((learner) => {
      learnerMap[learner.email] = learner;
    });

    let remainingLeads = [...allLeads];
    const learnerObjectIds = learners.map((learner) => learner._id);

    // Get product details for determining final status type
    const productDetails = await CommunityProduct.findOne({
      productType,
      entityObjectId,
    })
      .select('priceType')
      .lean();

    // 3. Check product purchases -> get purchaseMap [learnerId: purchase]
    const purchaseMap = await checkProductPurchases({
      productType,
      entityObjectId,
      learnerObjectIds,
    });

    // 4. Count purchased leads and remove from remaining leads
    const finalPurchaseStatus =
      productDetails?.priceType === PRODUCT_PRICE_TYPE.FREE
        ? 'ACCESS_GRANTED'
        : 'PURCHASED_PRODUCT';

    let purchasedCount = 0;
    remainingLeads = remainingLeads.filter((lead) => {
      const leadEmail = lead.leadObjectId?.email;
      if (leadEmail && learnerMap[leadEmail]) {
        const learnerId = learnerMap[leadEmail]._id.toString();
        if (purchaseMap[learnerId]) {
          purchasedCount++;
          return false; // Remove from remaining leads
        }
      }
      return true; // Keep in remaining leads
    });

    if (purchasedCount > 0) {
      statusCounts[finalPurchaseStatus] = purchasedCount;
    }

    // 5. Get active subscriptions -> get subscriptionMap [email: subscription]
    const remainingEmails = remainingLeads
      .map((lead) => lead.leadObjectId?.email)
      .filter(Boolean);

    const subscriptionMap = await getActiveSubscriptions({
      emails: remainingEmails,
      communityCode,
    });

    // 6. Count joined community leads and remove from remaining leads
    let joinedCommunityCount = 0;
    remainingLeads = remainingLeads.filter((lead) => {
      const leadEmail = lead.leadObjectId?.email;
      if (leadEmail && subscriptionMap[leadEmail]) {
        joinedCommunityCount++;
        return false; // Remove from remaining leads
      }
      return true; // Keep in remaining leads
    });

    if (joinedCommunityCount > 0) {
      statusCounts['JOINED_COMMUNITY'] = joinedCommunityCount;
    }

    // 7. For rest of leads, count by their original statuses
    remainingLeads.forEach((lead) => {
      const status = lead.status;
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });

    return statusCounts;
  } catch (error) {
    logger.error('Error calculating status distribution:', {
      error: error.message,
      icpProfileId,
      productType,
      entityObjectId,
    });
    return {};
  }
};

exports.getICPList = async (
  communityId,
  productType,
  entityObjectId,
  icpProfileObjectId
) => {
  try {
    const community = await retrieveActiveCommunity(communityId);

    // Get all active ICP profiles for this product
    let query = {
      communityObjectId: communityId,
      productType,
      entityObjectId,
      status: ICP_PROFILE_STATUS.ACTIVE,
    };
    if (icpProfileObjectId) {
      query = { _id: icpProfileObjectId };
    }
    const icpProfiles = await IcpProfilesModel.find(query)
      .sort({ createdAt: -1 })
      .lean();

    if (icpProfiles.length === 0) {
      return { icpList: [] };
    }

    // Process each ICP profile to get enhanced data
    const icpList = await Promise.all(
      icpProfiles.map(async (icp) => {
        // Get existing matched lead IDs for moreLeads calculation
        const existingMatches = await IcpLeadMatchesModel.find({
          icpProfileObjectId: icp._id,
        })
          .select('leadObjectId')
          .lean();
        const currentLeadsCount = existingMatches.length;

        const existingLeadIds = existingMatches.map(
          (match) => match.leadObjectId
        );

        // Check if more leads are available
        const totalAvailableCount = await getTotalAvailableLeadsCount(
          icp,
          existingLeadIds
        );
        const moreLeads = totalAvailableCount > 0;

        // Calculate status distribution
        const statusDistribution = await calculateStatusDistribution(
          icp._id,
          productType,
          entityObjectId,
          community.code
        );

        // Convert status distribution to required format
        const statusDistributionArray = Object.entries(
          statusDistribution
        ).map(([status, leadsCount]) => ({
          status,
          leadsCount,
        }));

        return {
          _id: icp._id,
          title: icp.title,
          icpSummary: icp.icpSummary,
          searchFields: icp.searchFields,
          currentLeadsCount,
          moreLeads,
          statusDistribution: statusDistributionArray,
        };
      })
    );

    return { icpList };
  } catch (error) {
    logger.error('Error getting ICP list:', {
      error: error.message,
      communityId,
      productType,
      entityObjectId,
    });
    throw error;
  }
};
