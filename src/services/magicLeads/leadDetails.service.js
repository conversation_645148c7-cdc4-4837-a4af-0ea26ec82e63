const EnrichedLeadsModel = require('../../models/magicLeads/enrichedLeads.model');
const getActivityLogService = require('../membership/getActivityLog.service');
const logger = require('../logger.service');
const { EMAIL_VERIFICATION_STATUS } = require('../../constants/common');
const {
  getProfileImageFromSocialProfiles,
} = require('./leadsRetrieval.service');

// getMergedActivityLogs, formatLeadActivityLogs, and getEntityTitle have been moved to getActivityLog.service.js

/**
 * Get lead details by leadObjectId with activity logs
 */
const getLeadDetails = async (communityId, leadObjectId) => {
  try {
    // Step 1: Get lead details from enriched_leads table
    const lead = await EnrichedLeadsModel.findById(leadObjectId).lean();

    if (!lead) {
      throw new Error('Lead not found');
    }

    // Step 2: Format lead data according to the requirements
    const leadDetails = {
      _id: lead._id,
      fullName: lead.fullName,
      profileImage: getProfileImageFromSocialProfiles(lead.socialProfiles),
      jobTitle: lead.jobTitle,
      emailVerified:
        lead.verification?.email?.status ===
        EMAIL_VERIFICATION_STATUS.VALID,
      city: lead.location?.city || null,
      country: lead.location?.countryName,
      companyName: lead.company?.name,
      companyWebsite: lead.company?.website,
      socialProfiles:
        lead.socialProfiles?.length > 0
          ? lead.socialProfiles.map((profile) => ({
              platform: profile.platform,
              url: profile.url,
            }))
          : [],
    };

    // Step 3: Get activity logs (lead + member)
    const activities = await getActivityLogService.getMergedActivityLogs(
      communityId,
      lead.email,
      leadObjectId
    );

    return {
      leadDetails,
      ...activities,
    };
  } catch (error) {
    logger.error('Error getting lead details:', {
      error: error.message,
      communityId,
      leadObjectId,
    });
    throw error;
  }
};

module.exports = {
  getLeadDetails,
};
