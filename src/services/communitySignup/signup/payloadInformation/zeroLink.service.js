const ZeroLinkModel = require('../../../../models/zerolink/zerolink.model');
const { ResourceNotFoundError } = require('../../../../utils/error.util');
const {
  getAddonPriceInLocalCurrency,
} = require('../../../../communitiesAPI/services/common/communityAddonPrice.service');
const CurrencyService = require('./currency.service');
const { PricingService } = require('../../common');
const {
  PURCHASE_TYPE,
  PRICE_TYPE,
} = require('../../../../constants/common');

exports.retrieveZeroLinkInfo = async ({
  item,
  ip,
  community,
  existingSubscription,
  memberInfo,
  paymentProvider,
  paymentBackendRpc,
}) => {
  const { entityId: zeroLinkObjectId, selectedAmount } = item;
  const { _id: communityObjectId, code: communityCode } = community;

  const [entityInfo] = await Promise.all([
    ZeroLinkModel.findById(zeroLinkObjectId).lean(),
  ]);

  if (!entityInfo) {
    throw new ResourceNotFoundError('Entity not found');
  }

  if (entityInfo.amount == null) {
    entityInfo.amount = 0;
  }

  if (
    entityInfo.pricingConfig?.priceType === PRICE_TYPE.FLEXIBLE &&
    entityInfo.pricingConfig?.suggestedAmount == null
  ) {
    entityInfo.pricingConfig.suggestedAmount = entityInfo.amount;
  }

  if (!entityInfo.title) {
    // set a default title when creating payment on our payment gateway
    entityInfo.title = `${communityCode} Zero link`;
  }

  const { paymentMethodCountryCode } = memberInfo;

  const priceDetail = await getAddonPriceInLocalCurrency({
    ip,
    addon: entityInfo,
    communityObjectId,
    selectedAmount,
    purchaseType: PURCHASE_TYPE.ZERO_LINK,
    paymentMethodCountryCode,
    paymentProvider,
  });

  const amountInUsd = await CurrencyService.convertAmountToUsd(
    priceDetail.originalCheckoutAmount,
    priceDetail.checkoutCurrency,
    paymentBackendRpc
  );

  const discountedCurrency = priceDetail.checkoutCurrency;
  const discountedAmount = priceDetail.checkoutAmount;
  const discountedAmountInUsd = await CurrencyService.convertAmountToUsd(
    priceDetail.checkoutAmount,
    priceDetail.checkoutCurrency,
    paymentBackendRpc
  );

  const isFlexiblePricing = PricingService.isFlexiblePricing(entityInfo);

  delete entityInfo.__v;

  return {
    ...item,
    metadata: {
      entityInfo,
      priceDetail,
      amountInUsd,
      discountedAmount,
      discountedCurrency,
      discountedAmountInUsd,
      freeFolder: false,
      isFree: false,
      isFlexiblePricing,
      requirePayment: true,
      existingSubscription,
    },
  };
};
