const { PURCHASE_TYPE } = require('../../../../constants/common');
const logger = require('../../../logger.service');
const { ParamError } = require('../../../../utils/error.util');
const { ZERO_LINK_STATUS } = require('../../../zeroLink/constants');

function retrieveZeroLink(items) {
  const filteredZeroLinks = items.filter(
    ({ type }) => type === PURCHASE_TYPE.ZERO_LINK
  );

  if (filteredZeroLinks.length > 1) {
    logger.error(`retrieveZeroLink: ${JSON.stringify(filteredZeroLinks)}`);

    throw new ParamError(
      'Only one zerolink is allowed in the items payload'
    );
  }

  return filteredZeroLinks?.[0];
}

exports.validateZeroLink = async (
  community,
  items,
  learner,
  decodedSignupToken
) => {
  const { _id: communityObjectId } = community;

  const zeroLink = retrieveZeroLink(items);

  if (!zeroLink) {
    return;
  }

  if (!decodedSignupToken) {
    return;
  }

  const { metadata } = zeroLink;
  if (metadata.entityInfo.status !== ZERO_LINK_STATUS.ACTIVE) {
    throw new ParamError('Zero link is inactive now');
  }

  if (metadata.isFlexiblePricing && metadata.priceDetail.amount === 0) {
    throw new ParamError('Amount cannot be 0 for zero link checkout');
  }
};
