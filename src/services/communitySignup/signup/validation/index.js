const { DateTime } = require('luxon');
const CommunityService = require('./community.service');
const SubscriptionService = require('./subscription.service');
const EventService = require('./event.service');
const FolderService = require('./folder.service');
const SessionService = require('./session.service');
const SignupTokenService = require('./signupToken.service');
const LearnerService = require('./learner.service');
const PaymentProviderService = require('./paymentProvider.service');
const CommunityRoleService = require('./communityRole.service');
const ChallengeService = require('./challenges.service');
const ZeroLinkService = require('./zeroLink.service');
const membershipUsageService = require('../../../featurePermissions/membershipUsage.service');
const {
  ParamError,
  ToUserError,
} = require('../../../../utils/error.util');
const { FEATURE_PERMISSION_ERROR } = require('@/src/constants/errorCode');

const validateItemDetails = (items) => {
  items.forEach((item) => {
    if (
      item.communityReferralCode &&
      item.referralDetails?.planObjectId?.toString() !==
        item.planObjectId.toString()
    ) {
      throw new ParamError(
        'Items planObjectId does not match the referral plan'
      );
    }

    if ('isChangeTier' in item.metadata) {
      if (!item.metadata.isChangeTier) {
        throw new ParamError(
          'isChangeTier must be true or undefined. Use change plan api for plan interval changes.'
        );
      }
      if (!item.metadata.existingPlanOrder) {
        throw new ParamError(
          'Cannot change tier if there is no existing plan order'
        );
      }
      if (
        item.metadata.existingPlanOrder.localCurrency !==
        item.metadata.price.currency
      ) {
        throw new ParamError(
          'Change tier flow should use same currency as existing plan order'
        );
      }
      const now = DateTime.utc();
      const restrictedDate = DateTime.fromJSDate(
        item.metadata.existingPlanOrder.nextBillingDate
      ).minus({
        days: 1,
      });
      // This is to consider scenarios asynchronous process between renewal and change tier events
      if (now.toISO() > restrictedDate.toISO()) {
        throw new ParamError(
          'Cannot change tier 1 day before nextBillingDate'
        );
      }
    }
  });
};

exports.validatePayload = async (payloadInformation) => {
  const {
    memberInfo,
    community,
    items,
    learner,
    paymentProvider,
    existingSubscription,
    decodedSignupToken,
  } = payloadInformation;

  if (items.length === 0 || items.length > 2) {
    throw new ParamError(
      'Items payload cannot be empty or more than 2 items'
    );
  }

  CommunityService.validateCommunity(community, items);
  const limitCheck = await membershipUsageService.checkAddMemberLimit(
    community._id,
    1,
    true
  );
  if (!existingSubscription && !limitCheck.allowed) {
    throw new ToUserError(
      limitCheck.message,
      FEATURE_PERMISSION_ERROR[limitCheck.error]
    );
  }
  await Promise.all([
    LearnerService.validateLearner({
      community,
      memberInfo,
      learner,
      decodedSignupToken,
    }),
    SubscriptionService.validateSubscription(
      community,
      items,
      learner,
      decodedSignupToken
    ),
    EventService.validateEvent(
      community,
      items,
      learner,
      decodedSignupToken
    ),
    FolderService.validateFolder(community, items, learner),
    SessionService.validateSession(
      community,
      items,
      learner,
      decodedSignupToken
    ),
    ChallengeService.validateChallenge(community, items, learner),
    ZeroLinkService.validateZeroLink(
      community,
      items,
      learner,
      decodedSignupToken
    ),
    SignupTokenService.validateDecodedSignupToken({
      decodedSignupToken,
      learner,
      community,
      items,
    }),
    PaymentProviderService.validatePaymentProvider({
      community,
      paymentProvider,
      items,
    }),
    CommunityRoleService.validateMember(community, existingSubscription),
  ]);
};

exports.validatePayloadForPlan = async (payloadInformation) => {
  const {
    community,
    items,
    learner,
    existingSubscription,
    decodedSignupToken,
  } = payloadInformation;

  if (items.length === 0 || items.length > 1) {
    throw new ParamError(
      'Items payload cannot be empty or more than 1 items'
    );
  }

  CommunityService.validateCommunity(community, items, true);
  validateItemDetails(items);
  await Promise.all([
    SignupTokenService.validateDecodedSignupTokenForPlan({
      decodedSignupToken,
      learner,
      community,
      items,
    }),
    CommunityRoleService.validateIsManager(
      community,
      existingSubscription
    ),
  ]);
};
