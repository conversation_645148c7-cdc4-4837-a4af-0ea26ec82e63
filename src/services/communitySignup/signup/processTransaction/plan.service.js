const paymentMetadataService = require('./paymentMetadata');
const planOrderService = require('../../../plan/planOrder.service');
const planService = require('../../../plan/plan.service');
const pricingService = require('../../../plan/pricing.service');
const paymentProviderUtils = require('../../../../utils/paymentProvider.util');
const planCreditService = require('../../../plan/credit');

const { ParamError } = require('../../../../utils/error.util');

async function handleAutoEnrollment({
  ip,
  userAgent,
  community,
  plan,
  planOrder,
  isSubscribed,
  paymentProvider,
  memberInfo,
}) {
  const { _id: planOrderObjectId, localCurrency } = planOrder;

  const purchased = false;

  const { requirePayment } = plan.metadata;

  if (!requirePayment) {
    throw new ParamError('Require payment is false');
  }

  const paymentMetadata =
    paymentMetadataService.generatePlanPaymentMetadata({
      community,
      planOrder,
      ip,
      userAgent,
      paymentProvider,
      memberInfo,
    });

  return {
    alreadyPurchased: isSubscribed,
    purchased,
    requirePayment,
    currency: localCurrency,
    planOrderObjectId,
    paymentMetadata,
  };
}

exports.handlePlan = async ({
  ip,
  userAgent,
  community,
  learner,
  countryInfo,
  timezone,
  requestor,
  trackingData,
  plan,
  decodedSignupToken,
  paymentProvider,
  memberInfo,
  session,
}) => {
  const { isSubscribed, requirePayment, existingPlanOrder } =
    plan.metadata;

  if (isSubscribed) {
    return {
      alreadyPurchased: isSubscribed,
      purchased: isSubscribed,
      planStatus: existingPlanOrder.status,
      requirePayment,
    };
  }

  const planOrder = await planOrderService.createOrUpdatePlanOrder({
    community,
    learner,
    countryInfo,
    timezone,
    requestor,
    trackingData,
    plan,
    decodedSignupToken,
    paymentProvider,
    session,
  });

  const paymentStatus = await handleAutoEnrollment({
    ip,
    userAgent,
    community,
    plan,
    planOrder,
    isSubscribed,
    paymentProvider,
    memberInfo,
  });

  return paymentStatus;
};

async function getFirstBillingCreditHistory({
  originalAmount,
  paidCurrency,
  communityCreditBalance,
  prorationDetails,
  paymentBackendRpc,
}) {
  const creditWallet = { ...communityCreditBalance };
  let isProratedAmountIncluded = false;
  if (prorationDetails?.creditAmount) {
    // Account for creditable proration amount in final calculation
    const existingAmount =
      creditWallet[prorationDetails.creditCurrency] ?? 0;
    // eslint-disable-next-line no-param-reassign
    creditWallet[prorationDetails.creditCurrency] =
      existingAmount + prorationDetails?.creditAmount;
    isProratedAmountIncluded = true;
  }

  const creditHistory = [];
  const firstBilling = await planCreditService.getBillingCycleCredit({
    billingCycle: 1,
    isProratedAmountIncluded,
    originalAmount,
    paidCurrency,
    creditWallet,
    paymentBackendRpc,
  });

  if (firstBilling) {
    creditHistory.push(firstBilling);
  }
  // const secondBilling = await getBillingCycleCredit({
  //   billingCycle: 2,
  //   isProratedAmountIncluded,
  //   originalAmount,
  //   paidCurrency,
  //   creditWallet,
  //   paymentBackendRpc,
  // });

  // if (secondBilling) {
  //   creditHistory.push(secondBilling);
  // }
  return creditHistory;
}

async function retrievePlanWithCreditBalanceHandling({
  paymentBackendRpc,
  countryInfo,
  communityCreditBalance,
  community,
  plan,
  session,
}) {
  const { priceId, metadata } = plan;

  const { price, entityInfo, isTierUpgrade, prorationDetails } = metadata;

  const localAmount = price.unitAmount;
  const paidCurrency = price.currency;
  const recurring = price.recurring;
  const interval = recurring.interval;
  const intervalCount = recurring.interval_count;

  if (!isTierUpgrade) {
    // Credit balance can only be used for tier upgrades
    return plan;
  }

  const currencies = Object.keys(communityCreditBalance);
  if (!currencies.length && !prorationDetails?.creditAmount) {
    return plan;
  }

  const creditHistory = await getFirstBillingCreditHistory({
    originalAmount: localAmount,
    paidCurrency,
    communityCreditBalance,
    prorationDetails,
    paymentBackendRpc,
  });

  const creditInfoForBilling = creditHistory.find(
    (credit) => credit.billingCycle === 1
  );

  if (!creditInfoForBilling) {
    return plan;
  }
  const { paidAmount } = creditInfoForBilling;
  const { payment_methods: paymentMethods } = community;

  const paymentProviderForPriceId =
    paymentProviderUtils.retrievePaymentProviderForPlan(
      paymentMethods,
      paidCurrency
    );

  // Either a zero Price plan that points to originalAmount plan
  // Or a partial Price plan that points to originalAmount plan
  const { plan: newPlan, priceId: newPriceId } =
    await planService.createCustomiseCreditPlan({
      title: `${entityInfo.type}_CREDIT_APPLIED - ${community.code}-${creditInfoForBilling.billingCycle}`,
      entityType: entityInfo.entityType,
      interval,
      intervalCount,
      defaultAmount: paidAmount,
      defaultCurrency: paidCurrency,
      originalPriceId: priceId,
      originalAmount: localAmount,
      originalPlan: entityInfo,
      nextBillingPlan: entityInfo,
      paymentBackendRpc,
      paymentProvider: paymentProviderForPriceId,
      session,
    });

  const pricing = await pricingService.retrievePrice({
    plan: newPlan,
    priceId: newPriceId,
    community,
    countryInfo,
    paymentBackendRpc,
    localCurrencyEnforced: paidCurrency,
    retrieveLocalCurrencyFromNextBillingPlan: true,
    nextBillingPlan: entityInfo,
  });

  const finalPlanData = { ...plan };
  finalPlanData.metadata.creditDetails = {
    creditHistory,
    creditPrice: {
      paymentProviderForPriceId,
      priceId: newPriceId,
      active: pricing.active,
      currency: pricing.currency.toUpperCase(),
      product: pricing.product,
      recurring: pricing.recurring,
      unitAmount: pricing.amount,
    },
    creditPlan: newPlan,
  };
  return finalPlanData;
}

async function handleTierChangeAutoEnrollment({
  ip,
  userAgent,
  community,
  plan,
  planOrder,
  paymentProvider,
  memberInfo,
}) {
  const {
    _id: planOrderObjectId,
    localCurrency,
    amountInLocalCurrency,
  } = planOrder;

  const purchased = false;

  const {
    isChangeTier,
    isTierUpgrade,
    prorationDetails,
    creditDetails = {},
  } = plan.metadata;
  const { creditHistory = [] } = creditDetails;
  const firstBillingCredit = creditHistory.find(
    (credit) => credit.billingCycle === 1
  );

  const creditBreakdown = {};
  const charge = {
    currency: localCurrency,
    originalAmount: amountInLocalCurrency || 0,
    creditAmount: 0,
    amount: amountInLocalCurrency,
  };

  if (prorationDetails) {
    creditBreakdown.prorate = {
      creditAmount: prorationDetails.creditAmount,
      creditCurrency: prorationDetails.creditCurrency,
    };
  }

  if (firstBillingCredit) {
    charge.amount = firstBillingCredit.paidAmount;
    charge.creditAmount = firstBillingCredit.paidCreditAmount;
    creditBreakdown.chargeCreditAmount = charge.creditAmount;
  }

  const requirePayment = plan.metadata.requirePayment ?? charge.amount > 0;

  const paymentMetadata =
    paymentMetadataService.generatePlanPaymentMetadata({
      community,
      planOrder,
      charge,
      ip,
      userAgent,
      paymentProvider,
      memberInfo,
    });

  return {
    charge,
    purchased,
    requirePayment,
    currency: localCurrency,
    planOrderObjectId,
    paymentMetadata,
    isTierUpgrade,
    isChangeTier,
    creditBreakdown,
  };
}

exports.handleTierChange = async ({
  ip,
  userAgent,
  community,
  learner,
  countryInfo,
  timezone,
  requestor,
  trackingData,
  plan,
  communityCreditBalance,
  decodedSignupToken,
  paymentProvider,
  paymentBackendRpc,
  memberInfo,
  session,
}) => {
  const { isChangeTier } = plan.metadata;

  if (!isChangeTier) {
    throw new ParamError('isChangeTier must be true for this flow');
  }

  const reformattedPlan = await retrievePlanWithCreditBalanceHandling({
    paymentBackendRpc,
    countryInfo,
    communityCreditBalance,
    community,
    plan,
    session,
  });

  const planOrder = await planOrderService.createOrUpdatePlanOrder({
    community,
    learner,
    countryInfo,
    timezone,
    requestor,
    trackingData,
    plan: reformattedPlan,
    decodedSignupToken,
    paymentProvider,
    session,
  });

  const paymentStatus = await handleTierChangeAutoEnrollment({
    ip,
    userAgent,
    community,
    plan,
    planOrder,
    paymentProvider,
    memberInfo,
  });

  return paymentStatus;
};
