const ObjectId = require('mongoose').Types.ObjectId;

const EnrichedLeadsModel = require('../../models/magicLeads/enrichedLeads.model');
const IcpLeadActivityLogsModel = require('../../models/magicLeads/icpLeadActivityLogs.model');
const MembershipModel = require('../../models/membership/membership.model');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const MagicReachEmailRecipientModel = require('../../models/magicReach/communityMagicReachEmailRecipients.model');
const Learner = require('../../models/learners.model');

const logger = require('../logger.service');
const { ParamError } = require('../../utils/error.util');

const { MEMBERSHIP_ACTIVITY_KEYS } = require('../../constants/common');

const {
  getBasicProductData,
} = require('../magicLeads/productList.service');
const { ACTIVITY_TYPES } = require('../magicLeads/constants');
const { PRODUCT_TYPE } = require('../product/constants');
const { MembershipEntryProcessor } = require('./get.service');
const { retrieveActiveCommunity } = require('../common/common.service');

// Note: MembershipEntryProcessor will be imported later to avoid circular dependency

/**
 * Get entity title based on product type and entity object ID
 */
const getEntityTitle = async (
  productType,
  entityObjectId,
  communityId
) => {
  try {
    if (productType === PRODUCT_TYPE.MEMBERSHIP) {
      // For MEMBERSHIP, use community title
      const community = await CommunityModel.findById(communityId)
        .select('title')
        .lean();
      return community?.title || 'Community';
    }

    // For other product types, get title from product data
    const productData = await getBasicProductData(
      productType,
      entityObjectId
    );
    return productData.title || 'Product';
  } catch (error) {
    logger.warn('Error getting entity title:', {
      error: error.message,
      productType,
      entityObjectId,
    });
    return 'Product';
  }
};

/**
 * Format lead activity logs to match membership activity format
 */
const formatLeadActivityLogs = async (leadActivityLogs, communityId) => {
  // Process all logs in parallel to avoid await-in-loop
  const logPromises = leadActivityLogs.map(async (log) => {
    try {
      const entityTitle = await getEntityTitle(
        log.metadata?.productType,
        log.metadata?.entityObjectId,
        communityId
      );

      const baseActivity = {
        _id: log._id,
        entityType: log.metadata?.productType || 'UNKNOWN',
        entityObjectId: log.metadata?.entityObjectId || log.sourceEntityId,
        from: 'magicLeads',
        date: log.createdAt,
        entityTitle,
      };

      switch (log.activityType) {
        case ACTIVITY_TYPES.LEAD_FOUND:
          return {
            id: MEMBERSHIP_ACTIVITY_KEYS.MAGIC_LEADS_LEAD_FOUND,
            ...baseActivity,
            label: `Via Magic Leads: ${entityTitle}`,
          };

        case ACTIVITY_TYPES.LEAD_OUTREACH_MAIL_SENT:
          return {
            id: MEMBERSHIP_ACTIVITY_KEYS.MAGIC_LEADS_OUTREACH_SENT,
            ...baseActivity,
            mailObjectId: log.sourceEntityId, // sourceEntityId is mail ID for outreach
            label: `Outreach email sent for ${entityTitle}`,
          };

        case ACTIVITY_TYPES.LEAD_EMAIL_OPENED:
          return {
            id: MEMBERSHIP_ACTIVITY_KEYS.MAGIC_LEADS_EMAIL_OPENED,
            ...baseActivity,
            mailObjectId: log.sourceEntityId, // sourceEntityId is mail ID for email events
            label: `Email opened for ${entityTitle}`,
          };

        case ACTIVITY_TYPES.LEAD_EMAIL_CLICKED: {
          const emailRecipient =
            await MagicReachEmailRecipientModel.findOne(
              {
                draftId: log.sourceEntityId,
              },
              { clickInfo: 1 }
            ).lean();

          return {
            id: MEMBERSHIP_ACTIVITY_KEYS.MAGIC_LEADS_EMAIL_CLICKED,
            ...baseActivity,
            mailObjectId: log.sourceEntityId, // sourceEntityId is mail ID for email events
            linkClicked: emailRecipient?.clickInfo?.[0]?.linkClicked,
            label: `Email clicked for ${entityTitle}`,
          };
        }
        default:
          logger.warn('Unknown activity type:', log.activityType);
          return null;
      }
    } catch (error) {
      logger.error('Error formatting lead activity log:', {
        error: error.message,
        logId: log._id,
      });
      return null;
    }
  });

  // Wait for all promises to resolve and filter out null values
  const results = await Promise.all(logPromises);
  return results.filter((log) => log !== null);
};

/**
 * Get community member activity from membership get service
 * This function is extracted from membership/get.service.js to prevent circular imports
 */
const getCommunityMemberActivity = async ({
  managerLearnerObjectId,
  communityId,
  membershipId,
}) => {
  const community = await retrieveActiveCommunity(communityId);

  const [member, managerLearner] = await Promise.all([
    MembershipModel.findOne(
      {
        _id: new ObjectId(membershipId),
        communityObjectId: community._id,
      },
      {
        lastChangeStreamEventIds: 0,
      }
    ).lean(),
    Learner.findById(managerLearnerObjectId, {
      languagePreference: 1,
    }).lean(),
  ]);

  if (!member) {
    throw new ParamError('Membership not found');
  }

  let learnerObjectId = member.learnerObjectId;
  if (!learnerObjectId) {
    const learner = await Learner.findOne({
      email: member.email,
      isActive: true,
    })
      .select('_id')
      .lean();
    learnerObjectId = learner?._id;
  }

  // Import MembershipEntryProcessor dynamically to avoid circular dependency
  // eslint-disable-next-line import/no-dynamic-require

  const memberWithDetails = new MembershipEntryProcessor({
    member,
  });

  await memberWithDetails.extractSupportingDataForActivity({
    languagePreference: (managerLearner?.languagePreference ?? 'en')
      ?.replace(/-/g, '_')
      .toLowerCase(),
    communityCode: community.code,
  });

  await memberWithDetails.updateEntitiesCacheFromActivities();

  return { activities: memberWithDetails.processForActivityPage() };
};

/**
 * Common function to merge lead and member activity logs
 * This merges activity logs before and after they join community
 */
exports.getMergedActivityLogs = async (
  communityId,
  email,
  leadObjectId
) => {
  try {
    // Get lead activity logs
    if (!leadObjectId) {
      const lead = await EnrichedLeadsModel.findOne({
        email,
        isActive: true,
      })
        .select('_id')
        .lean();
      // eslint-disable-next-line no-param-reassign
      leadObjectId = lead?._id;
    }
    let formattedLeadLogs = [];
    if (leadObjectId) {
      const leadActivityLogs = await IcpLeadActivityLogsModel.find({
        leadObjectId,
        communityObjectId: communityId,
      })
        .sort({ createdAt: -1 })
        .lean();

      // Format lead activity logs to match membership activity format
      formattedLeadLogs = await formatLeadActivityLogs(
        leadActivityLogs,
        communityId
      );
    }

    // Try to get member activity logs if the lead is a member
    let memberActivities = [];
    try {
      // Find membership by community and email
      const membership = await MembershipModel.findOne({
        communityObjectId: communityId,
        email,
      })
        .select('_id')
        .lean();

      if (membership) {
        // Get member activity logs
        const memberActivityResult = await getCommunityMemberActivity({
          communityId,
          membershipId: membership._id.toString(),
        });

        memberActivities = memberActivityResult.activities || [];
      }
    } catch (memberError) {
      logger.info('Member activity logs not found or error occurred:', {
        error: memberError.message,
        email,
        communityId,
      });
      throw memberError;
    }

    // Merge lead activities into member activities while preserving member order
    const mergedActivities = [];

    // Sort lead activities by date (newest first)
    const sortedLeadLogs = formattedLeadLogs.sort(
      (a, b) => new Date(b.date) - new Date(a.date)
    );

    // If there are no member activities, just return sorted lead activities
    if (memberActivities.length === 0) {
      return { activities: sortedLeadLogs };
    }

    // If there are no lead activities, just return member activities
    if (sortedLeadLogs.length === 0) {
      return { activities: memberActivities };
    }

    // Step 1: Add all lead activities that occurred before the first member activity
    const firstMemberDate = new Date(memberActivities[0].date);
    for (const leadActivity of sortedLeadLogs) {
      if (new Date(leadActivity.date) > firstMemberDate) {
        mergedActivities.push(leadActivity);
      }
    }

    // Step 2: Process each member activity and insert relevant lead activities
    for (let i = 0; i < memberActivities.length; i++) {
      const currentMember = memberActivities[i];
      const currentMemberDate = new Date(currentMember.date);

      // Add the current member activity
      mergedActivities.push(currentMember);

      // Check if there's a next member activity to determine the time range
      if (i < memberActivities.length - 1) {
        const nextMember = memberActivities[i + 1];
        const nextMemberDate = new Date(nextMember.date);

        // Add lead activities that occurred between current and next member activity
        for (const leadActivity of sortedLeadLogs) {
          const leadDate = new Date(leadActivity.date);
          if (leadDate <= currentMemberDate && leadDate > nextMemberDate) {
            mergedActivities.push(leadActivity);
          }
        }
      } else {
        // This is the last member activity, add all remaining lead activities that occurred after or at this time
        for (const leadActivity of sortedLeadLogs) {
          const leadDate = new Date(leadActivity.date);
          if (leadDate <= currentMemberDate) {
            mergedActivities.push(leadActivity);
          }
        }
      }
    }

    return { activities: mergedActivities };
  } catch (error) {
    logger.error('Error getting merged activity logs:', {
      error: error.message,
      leadObjectId,
      email,
      communityId,
    });
    throw error;
  }
};
