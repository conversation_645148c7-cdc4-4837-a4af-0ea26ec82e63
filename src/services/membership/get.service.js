/* eslint-disable no-param-reassign */
const ObjectId = require('mongoose').Types.ObjectId;
const { DateTime } = require('luxon');
const { Readable } = require('stream');

const EventAttendees = require('../../communitiesAPI/models/eventAttendees.model');
const MembershipActionEvent = require('../../models/actionEvent/membershipActionEvents.model');
const AddonActionEvent = require('../../models/actionEvent/addonActionEvents.model');

const AbandonedCartsModel = require('../../models/abandonedCarts/abandonedCarts.model');
const FolderModel = require('../../communitiesAPI/models/communityFolders.model');
const EventModel = require('../../communitiesAPI/models/communityEvents.model');
const ProgramModel = require('../../models/program/program.model');

const Learner = require('../../models/learners.model');
const Membership = require('../../models/membership/membership.model');
const MembershipSegment = require('../../models/membership/segment.model');
const Community = require('../../communitiesAPI/models/community.model');
const CommunitySubscription = require('../../communitiesAPI/models/communitySubscriptions.model');
const CommunityApplication = require('../../communitiesAPI/models/communityApplications.model');
const CommunityPurchaseTransaction = require('../../communitiesAPI/models/communityPurchaseTransactions.model');
const WhatsappParticipant = require('../../communitiesAPI/models/whatsappParticipants.model');
const RevenueTransaction = require('../../models/revenueTransaction.model');
const Localization = require('../../models/platform/localization.model');
const RawTransactions = require('../../models/rawTransaction.model');
const CommunityRole = require('../../communitiesAPI/models/communityRole.model');

const logger = require('../logger.service');
const membershipSearchUtils = require('./utils/membershipSearch.utils');
const membershipExportUtils = require('./utils/exportV2.utils');
const membershipConfigUtils = require('./utils/config.utils');
const membershipCountService = require('./count.service');
const commonService = require('../zeroLink/common.service');

const objectUtils = require('../../utils/object.utils');

const communityUIConfigService = require('../../communitiesAPI/services/web/communityUIConfig.service');

const { getMongoServerTimeoutConfig } = require('../config.service');

const { ParamError, DBError } = require('../../utils/error.util');

const {
  MEMBERSHIP_STATUS,
  MEMBERSHIP_PAYMENT_STATUS,
  MEMBERSHIP_TYPE,
  MEMBERSHIP_COMMUNITY_ROLE,
  FILTERING_FIELDS,
} = require('./constants');

const {
  ABANDONED_CARTS_STATUS,
  MEMBERSHIP_ACTION_EVENT_TYPES,
  ADDON_ACTION_EVENT_TYPES,
  MEMBERSHIP_ACTIVITY_KEYS,
  TRANSACTION_TYPE,
  PURCHASE_TYPE,
  COMMUNITY_SUBSCRIPTION_STATUSES,
} = require('../../constants/common');

const {
  COMMUNITY_ONE_TIME_PAYMENT_ENTITIES,
  MEMBERSHIP_PLAN_INTERVAL,
  COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES,
} = require('../../communitiesAPI/constants');
const ZeroLinkModel = require('../../models/zerolink/zerolink.model');

class MembershipEntryProcessor {
  constructor({ member, entitiesCache }) {
    this.member = member;
    this.activities = [];
    this.entitiesCache = entitiesCache ?? new Map();
  }

  getMember() {
    return this.member;
  }

  getActivityLabel(id, variablesData = {}) {
    const text = this.activitiesLocaleKeyMap.get(id) ?? '';
    let processedText = text;
    for (const key of Object.keys(variablesData)) {
      const regex = new RegExp(`{${key}}`, 'g');
      processedText = processedText.replace(regex, variablesData[key]);
    }
    return processedText;
  }

  async extractApplicationData({ applicationMap = {} }) {
    await this.getApplication(applicationMap);
    return this.member;
  }

  async extractSupportingData({
    applicationMap = {},
    subscriptionMap = {},
    purchaseTransactionMap = {},
    learnerMap = {},
    whatsappParticipantMap = {},
    revenueTransactionMap = {},
    needRawTransaction = true,
    needRawTransactionsCount = false,
  }) {
    await this.getApplication(applicationMap);
    await this.getSubscriptionAndPurchaseTransaction({
      subscriptionMap,
      purchaseTransactionMap,
      revenueTransactionMap,
    });
    await this.getLearner(learnerMap);
    await this.getWhatsappParticipant(whatsappParticipantMap);
    if (needRawTransaction) {
      await this.getRawTransactions();
    }
    if (needRawTransactionsCount) {
      await this.countRawTransactions();
    }
    return this.member;
  }

  async extractSupportingDataForActivity({
    languagePreference,
    communityCode,
  }) {
    await this.setupActivitiesLocaleMap(languagePreference);
    await Promise.all([
      // this.addLeadToActivityList(communityCode),
      this.addMembershipAbandonedCartToActivityList(communityCode),
      this.addMembershipEventsToActivityList(),
      this.addAddonEventsToActivityList(),
      this.addRefundToActivityList(),
      this.addEventAttendeeActionsToActivityList(),
      this.addAbandonedCheckoutsToActivityList(),
    ]);
  }

  async setEntitiesCache(
    Model,
    entityType,
    arrayOfObjectIds = [],
    projection
  ) {
    const LIMIT = 100;
    const rounds = Math.ceil(arrayOfObjectIds.length / LIMIT);
    const queries = [];
    for (let i = 0; i < rounds; i++) {
      const start = i * LIMIT;
      let end = start + LIMIT;
      if (end > arrayOfObjectIds.length) end = arrayOfObjectIds.length;
      queries.push({ start, end });
    }
    await Promise.all(
      queries.map(async ({ start, end }) => {
        const queryArray = arrayOfObjectIds.slice(start, end);
        const entities = await Model.find(
          { _id: { $in: queryArray } },
          projection
        ).lean();

        entities.forEach((entity) => {
          this.entitiesCache.set(
            `${entityType}-${entity._id.toString()}`,
            entity
          );
        });
      })
    );
  }

  async updateEntitiesCacheFromActivities() {
    const folderObjectIds = []; // inclusive of sessions and digital products
    const eventObjectIds = [];
    const challengeObjectIds = [];
    const zeroLinkObjectIds = [];
    this.activities.forEach((activity) => {
      if (activity.entityType) {
        switch (activity.entityType) {
          case PURCHASE_TYPE.SESSION:
          case PURCHASE_TYPE.FOLDER:
            folderObjectIds.push(activity.entityObjectId);
            return;
          case PURCHASE_TYPE.EVENT:
            eventObjectIds.push(activity.entityObjectId);
            break;
          case PURCHASE_TYPE.CHALLENGE:
            challengeObjectIds.push(activity.entityObjectId);
            break;
          case PURCHASE_TYPE.ZERO_LINK:
            zeroLinkObjectIds.push(activity.entityObjectId);
            break;
          default:
            break;
        }
      } else {
        switch (activity.entityCollection) {
          case COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.FOLDER:
            folderObjectIds.push(activity.entityObjectId);
            return;
          case COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.EVENT:
            eventObjectIds.push(activity.entityObjectId);
            break;
          case COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.CHALLENGE:
            challengeObjectIds.push(activity.entityObjectId);
            break;
          case COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.ZERO_LINK:
            zeroLinkObjectIds.push(activity.entityObjectId);
            break;
          default:
            break;
        }
      }
    });

    const defaultProjection = {
      title: 1,
    };
    await Promise.all([
      this.setEntitiesCache(
        FolderModel,
        PURCHASE_TYPE.FOLDER,
        folderObjectIds,
        defaultProjection
      ),
      this.setEntitiesCache(
        EventModel,
        PURCHASE_TYPE.EVENT,
        eventObjectIds,
        defaultProjection
      ),
      this.setEntitiesCache(
        ProgramModel,
        PURCHASE_TYPE.CHALLENGE,
        challengeObjectIds,
        defaultProjection
      ),
      this.setEntitiesCache(
        ZeroLinkModel,
        PURCHASE_TYPE.ZERO_LINK,
        zeroLinkObjectIds,
        { ...defaultProjection, amount: 1, currency: 1, pricingConfig: 1 }
      ),
    ]);
  }

  getEntityFromCache(activity) {
    if (activity.entityType === PURCHASE_TYPE.SESSION) {
      return this.entitiesCache.get(
        `${PURCHASE_TYPE.FOLDER}-${activity.entityObjectId.toString()}`
      );
    }
    if (activity.entityType) {
      return this.entitiesCache.get(
        `${activity.entityType}-${activity.entityObjectId.toString()}`
      );
    }
    switch (activity.entityCollection) {
      case COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.CHALLENGE:
        return this.entitiesCache.get(
          `${
            PURCHASE_TYPE.CHALLENGE
          }-${activity.entityObjectId.toString()}`
        );
      case COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.EVENT:
        return this.entitiesCache.get(
          `${PURCHASE_TYPE.EVENT}-${activity.entityObjectId.toString()}`
        );

      case COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.FOLDER: {
        return this.entitiesCache.get(
          `${PURCHASE_TYPE.FOLDER}-${activity.entityObjectId.toString()}`
        );
      }
      case COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.ZERO_LINK: {
        return this.entitiesCache.get(
          `${
            PURCHASE_TYPE.ZERO_LINK
          }-${activity.entityObjectId.toString()}`
        );
      }
      default:
        break;
    }
  }

  sortActivities() {
    this.activities.sort((a, b) => {
      if (!b.date && b.id === MEMBERSHIP_ACTIVITY_KEYS.LEAD_CREATED) {
        return -1;
      }
      if (b.date - a.date === 0) {
        return a.id === MEMBERSHIP_ACTIVITY_KEYS.LEAD_CREATED ? 1 : -1;
      }
      return b.date - a.date;
    });
    return this.activities;
  }

  processForActivityPage() {
    this.activities = this.activities.map((activity) => {
      if (!activity.entityTitle) {
        const entity = this.getEntityFromCache(activity);
        activity.entityTitle = entity?.title;
        if (
          activity.entityType === PURCHASE_TYPE.ZERO_LINK ||
          activity.entityCollection ===
            COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.ZERO_LINK
        ) {
          if (entity && (!entity?.title || entity?.title === '')) {
            activity.entityTitle = commonService.formatSearchTitle(
              entity.title,
              entity,
              this.activitiesLocaleKeyMap
            );
          }
        }
      }
      if (!activity.label) {
        const labelData = activity.labelData ?? activity;
        labelData.entityTitle = activity?.entityTitle;
        activity.label = this.getActivityLabel(activity.id, labelData);
      }
      delete activity.entityObjectId;
      delete activity.entityCollection;
      delete activity.labelData;
      delete activity.purchaseType;
      return activity;
    });
    return this.sortActivities();
  }

  processForDetailPage({ community }) {
    this.fillApplicationData({ community });
    this.fillSubscriptionData();
    this.fillPurchaseTransactionData();
    this.fillRevenueTransactionData();
    this.fillLearnerData();
    this.fillWhatsappParticipantData();
    this.updateFieldsByStatus();
    this.hideFields();
    this.cleanUp();
    return this.member;
  }

  processForDownload() {
    this.fillApplicationData();
    this.fillSubscriptionData();
    this.fillPurchaseTransactionData();
    this.fillRevenueTransactionData();
    this.fillRawTransactionsData();
    this.fillLearnerData();
    this.fillWhatsappParticipantData();
    this.updateFieldsByStatus();
    this.updateAbandonedCheckoutInfo();
    this.hideFields();
    this.cleanUp();
    return this.member;
  }

  processForListPage({ community, withApplication = false }) {
    if (withApplication) {
      this.fillApplicationData({ community });
    }

    this.updateAbandonedCheckoutInfo();
    this.updateFieldsByStatus();
    this.hideFields();
    return this.member;
  }

  cleanUp() {
    delete this.member.subscription;
    delete this.member.application;
    delete this.member.revenueTransaction;
    delete this.member.purchaseTransaction;
    delete this.member.revenueTransaction;
    delete this.member.learner;
    delete this.member.whatsappParticipant;
  }

  fillApplicationData({ community } = {}) {
    if (this.application) {
      const { applicationConfigDataFields = [] } = community ?? {};
      applicationConfigDataFields.forEach((dataField) => {
        if (!dataField.isVisible) {
          delete this.application[dataField.fieldName];
        }
      });

      this.member.communityApplicationData = this.application;
    }
    return this.member;
  }

  fillSubscriptionData() {
    if (!this.subscription) {
      return this.member;
    }
    if (!this.member.subscriptionInfo) {
      logger.error('Subscription info not found');
      return this.member;
    }

    if (this.subscription.stripePrice) {
      let backupPrice;
      let backupCurrency;
      if (this.subscription.stripePriceId) {
        const priceComponents = this.subscription.stripePriceId.split('_');
        if (priceComponents.length === 2) {
          backupCurrency = priceComponents[0];
          backupPrice = priceComponents[1];
        }
      }
      if (this.member.subscriptionInfo.paymentInfo) {
        if (!this.member.subscriptionInfo.paymentInfo.localPrice) {
          this.member.subscriptionInfo.paymentInfo.localPrice =
            this.subscription.stripePrice || backupPrice;
        }
        if (!this.member.subscriptionInfo.paymentInfo.localCurrency) {
          this.member.subscriptionInfo.paymentInfo.localCurrency =
            this.subscription.stripeCurrency || backupCurrency;
        }
      }
    } else if (this.member.subscriptionInfo?.paymentInfo) {
      delete this.member.subscriptionInfo.paymentInfo.paymentStatus;
    }

    this.member.revewerNotes = this.subscription.revewerNotes;
    this.member.subscriptionId = this.subscription.subscriptionId;
    let subscriptionStart = this.member.subscriptionInfo.signUpDate;
    if (subscriptionStart) {
      subscriptionStart = DateTime.fromJSDate(subscriptionStart).toUTC();
      let subscriptionEnd =
        this.member.subscriptionInfo.unsubscribedInfo?.subscriptionEndDate;
      if (
        !subscriptionEnd ||
        this.member.status === MEMBERSHIP_STATUS.SUBSCRIBED
      ) {
        subscriptionEnd = DateTime.utc();
      } else {
        subscriptionEnd = DateTime.fromJSDate(subscriptionEnd).toUTC();
        if (subscriptionEnd > DateTime.utc()) {
          subscriptionEnd = DateTime.utc();
        }
      }
      this.member.subscriptionInfo.subscriptionDuration = Math.ceil(
        subscriptionEnd.diff(subscriptionStart, 'days').toObject().days
      );
      this.member.subscriptionInfo.subscriptionDurationUnit = 'day(s)';
    } else {
      this.member.subscriptionInfo.subscriptionDuration = 0;
    }
    if (this.rawTransactions) {
      this.member.subscriptionInfo.noOfPaidTransactions =
        this.rawTransactions.length ?? 0;
    }
    return this.member;
  }

  fillPurchaseTransactionData() {
    if (!this.purchaseTransaction) {
      return this.member;
    }
    this.member.purchaseTransactionData = this.purchaseTransaction;
    return this.member;
  }

  fillRevenueTransactionData() {
    if (!this.revenueTransaction) {
      return this.member;
    }
    if (!this.member.subscriptionInfo?.paymentInfo) {
      logger.error('Subscription payment info not found');
      return this.member;
    }
    this.member.subscriptionInfo.paymentInfo.paymentMethod =
      this.revenueTransaction.paymentMethod;
    return this.member;
  }

  fillRawTransactionsData() {
    if (this.rawTransactionsCount) {
      this.member.noOfPaidTransactions = this.rawTransactionsCount;
    }
    return this.member;
  }

  fillLearnerData() {
    if (!this.learner) {
      return this.member;
    }
    this.member.bio = this.learner.description;
    this.member.learnerId = this.learner.learnerId;
    return this.member;
  }

  fillWhatsappParticipantData() {
    if (!this.whatsappParticipant) {
      return this.member;
    }
    if (!this.member.whatsappInfo) {
      logger.error('Whatsapp info not found');
      return this.member;
    }
    let lastAction;
    let lastActionDate;
    if (this.whatsappParticipant.lastReacted) {
      lastAction = 'Reacted to a message';
      lastActionDate = this.whatsappParticipant.lastReacted;
    }
    if (this.whatsappParticipant.lastMessageSent) {
      lastAction = 'Sent a message in chat';
      lastActionDate = this.whatsappParticipant.lastMessageSent;
    }
    this.member.whatsappInfo.lastAction = lastAction;
    this.member.whatsappInfo.lastActionDate = lastActionDate;
    return this.member;
  }

  updateFieldsByStatus() {
    switch (this.member.status) {
      case MEMBERSHIP_STATUS.UNSUBSCRIBED:
        delete this.member.subscriptionInfo?.paymentInfo?.nextBillingDate;
        if (
          this.member.subscriptionInfo?.unsubscribedInfo
            ?.subscriptionEndDate > DateTime.utc()
        ) {
          this.member.status = MEMBERSHIP_STATUS.SUBSCRIBED;
        }
        break;
      case MEMBERSHIP_STATUS.REMOVED:
        delete this.member.subscriptionInfo?.paymentInfo?.nextBillingDate;
        break;
      case MEMBERSHIP_STATUS.REJECTED:
        delete this.member.subscriptionInfo?.paymentInfo?.nextBillingDate;
        break;
      default:
        break;
    }
    return this.member;
  }

  updateAbandonedCheckoutInfo() {
    const entities =
      this.member.abandonedCheckoutInfo?.productsNotPurchased ?? [];
    const firstAbandonedCheckout =
      this.member.abandonedCheckoutInfo?.firstAbandonedCheckout;
    if (!firstAbandonedCheckout?.entityObjectId) {
      return;
    }

    const entityDetails =
      this.entitiesCache.get(
        `${
          firstAbandonedCheckout.entityType === PURCHASE_TYPE.SESSION
            ? PURCHASE_TYPE.FOLDER
            : firstAbandonedCheckout.entityType
        }-${firstAbandonedCheckout.entityObjectId.toString()}`
      ) ?? {};

    if (firstAbandonedCheckout.entityType === PURCHASE_TYPE.SUBSCRIPTION) {
      entityDetails.title = 'Membership';
    }

    this.member.abandonedCheckoutInfo = {
      firstAbandonedCheckout: {
        ...firstAbandonedCheckout,
        title: entityDetails.title,
      },
      noOfAbandonedCheckouts: entities.length,
    };
    return this.member;
  }

  hideFields() {
    if (
      this.member.subscriptionInfo?.paymentInfo?.paymentStatus ===
      MEMBERSHIP_PAYMENT_STATUS.INCOMPLETE
    ) {
      delete this.member.subscriptionInfo.paymentInfo.paymentStatus;
    } else if (
      this.member.status === MEMBERSHIP_STATUS.SUBSCRIBED &&
      this.member.subscriptionInfo
    ) {
      delete this.member.subscriptionInfo.removalInfo;
      delete this.member.subscriptionInfo.unsubscribedInfo;
    }

    if (
      this.member.subscriptionInfo?.memberType === MEMBERSHIP_TYPE.FREE
    ) {
      const is100PercentDiscount =
        this.member.subscriptionInfo?.paymentInfo
          ?.discountCodeUsedValue === 100;

      if (is100PercentDiscount) {
        const discountCode =
          this.member.subscriptionInfo.paymentInfo.discountCodeUsed;

        delete this.member.subscriptionInfo.paymentInfo;

        this.member.subscriptionInfo.paymentInfo = {
          discountCodeUsed: discountCode,
        };
      }
    }
    return this.member;
  }

  async getApplication(applicationMap) {
    const applicationId = this.member.applicationInfo?.applicationObjectId;
    if (applicationId) {
      this.application = applicationMap[applicationId];
      if (!this.application) {
        this.application = await CommunityApplication.findOne({
          _id: applicationId,
        }).lean();
      }
      if (
        this.member.subscriptionInfo?.purchaseTransactionObjectId &&
        this.application?.communitySignupId?.toString() !==
          this.member.subscriptionInfo.purchaseTransactionObjectId.toString()
      ) {
        await this.getApplicationByPurchaseTransactionObjectId();
      }
    } else if (
      this.member.applicationInfo &&
      this.member.subscriptionObjectId
    ) {
      await this.getApplicationBySubscriptionObjectId();
      if (!this.application) {
        await this.getApplicationByPurchaseTransactionObjectId();
      }
    }
  }

  async getApplicationBySubscriptionObjectId() {
    this.application = await CommunityApplication.findOne({
      subscriptionObjectId: this.member.subscriptionObjectId,
    }).lean();
  }

  async getApplicationByPurchaseTransactionObjectId() {
    if (this.member.subscriptionInfo?.purchaseTransactionObjectId) {
      this.application = await CommunityApplication.findOne({
        communitySignupId:
          this.member.subscriptionInfo.purchaseTransactionObjectId,
      }).lean();
    }
  }

  async getSubscriptionAndPurchaseTransaction({
    subscriptionMap,
    purchaseTransactionMap,
    revenueTransactionMap,
  }) {
    const subscriptionObjectId = this.member.subscriptionObjectId;
    if (subscriptionObjectId) {
      this.subscription = subscriptionMap[subscriptionObjectId];
      if (!this.subscription) {
        this.subscription = await CommunitySubscription.findOne({
          _id: subscriptionObjectId,
        }).lean();
      }
      if (this.subscription) {
        const purchaseTransactionObjectId =
          this.subscription.communitySignupId;
        if (purchaseTransactionObjectId) {
          this.purchaseTransaction =
            purchaseTransactionMap[purchaseTransactionObjectId];
          if (!this.purchaseTransaction) {
            this.purchaseTransaction =
              await MembershipEntryProcessor.getPurchaseTransactionDetails(
                purchaseTransactionObjectId
              );
          }
          if (this.purchaseTransaction.local_amount > 0) {
            this.revenueTransaction =
              revenueTransactionMap[purchaseTransactionObjectId];
            if (!this.revenueTransaction) {
              this.revenueTransaction = await RevenueTransaction.findOne({
                purchasedId: new ObjectId(purchaseTransactionObjectId),
              });
            }
          }
        }
      }
    }
  }

  async countRawTransactions() {
    const learnerObjectId = this.member.learnerObjectId;
    const communityObjectId = this.member.communityObjectId;
    if (learnerObjectId && communityObjectId) {
      if (!this.rawTransactions) {
        const [
          oneTimeRawTransactionsCount,
          refundedOnetimeTransactionsCount,
          subscriptionRawTransaction,
        ] = await Promise.all([
          RawTransactions.countDocuments({
            learnerObjectId,
            communityObjectId,
            transactionType: TRANSACTION_TYPE.INBOUND,
            purchaseType: { $ne: PURCHASE_TYPE.SUBSCRIPTION },
            status: 'Success',
          }),
          RawTransactions.countDocuments({
            learnerObjectId,
            communityObjectId,
            transactionType: TRANSACTION_TYPE.OUTBOUND,
            purchaseType: { $ne: PURCHASE_TYPE.SUBSCRIPTION },
            status: 'Success',
          }),
          RawTransactions.findOne({
            learnerObjectId,
            communityObjectId,
            transactionType: TRANSACTION_TYPE.INBOUND,
            purchaseType: PURCHASE_TYPE.SUBSCRIPTION,
            status: 'Success',
          })
            .sort({ transactionCreatedAt: -1 })
            .lean(),
        ]);

        if (subscriptionRawTransaction) {
          this.rawTransactionsCount =
            oneTimeRawTransactionsCount -
            refundedOnetimeTransactionsCount +
            1;
        } else {
          this.rawTransactionsCount =
            oneTimeRawTransactionsCount - refundedOnetimeTransactionsCount;
        }
      }
    }
  }

  async getRawTransactions() {
    const learnerObjectId = this.member.learnerObjectId;
    const communityObjectId = this.member.communityObjectId;
    if (learnerObjectId && communityObjectId) {
      if (!this.rawTransactions) {
        const [
          oneTimeRawTransactions,
          refundedOnetimeTransactions,
          subscriptionRawTransaction,
        ] = await Promise.all([
          RawTransactions.find(
            {
              learnerObjectId,
              communityObjectId,
              transactionType: TRANSACTION_TYPE.INBOUND,
              purchaseType: { $ne: PURCHASE_TYPE.SUBSCRIPTION },
              status: 'Success',
            },
            { purchaseType: 1, purchasedId: 1 }
          ).lean(),
          RawTransactions.find(
            {
              learnerObjectId,
              communityObjectId,
              transactionType: TRANSACTION_TYPE.OUTBOUND,
              purchaseType: { $ne: PURCHASE_TYPE.SUBSCRIPTION },
              status: 'Success',
            },
            { purchaseType: 1, purchasedId: 1 }
          ).lean(),
          RawTransactions.findOne(
            {
              learnerObjectId,
              communityObjectId,
              transactionType: TRANSACTION_TYPE.INBOUND,
              purchaseType: PURCHASE_TYPE.SUBSCRIPTION,
              status: 'Success',
            },
            { purchaseType: 1, purchasedId: 1 }
          )
            .sort({ transactionCreatedAt: -1 })
            .lean(),
        ]);

        const onetimeRawTransactionsWithoutRefunds =
          oneTimeRawTransactions.filter(
            (onetimeRawTransaction) =>
              !refundedOnetimeTransactions.find(
                (refundedOnetimeRawTransaction) =>
                  refundedOnetimeRawTransaction.purchasedId.toString() ===
                  onetimeRawTransaction.purchasedId.toString()
              )
          );

        if (subscriptionRawTransaction) {
          this.rawTransactions = [
            ...onetimeRawTransactionsWithoutRefunds,
            subscriptionRawTransaction,
          ];
        } else {
          this.rawTransactions = onetimeRawTransactionsWithoutRefunds;
        }
      }
    }
  }

  async getLearner(learnerMap) {
    const learnerObjectId = this.member.learnerObjectId;
    if (learnerObjectId) {
      this.learner = learnerMap[learnerObjectId];
      if (!this.learner) {
        this.learner = await Learner.findOne(
          {
            _id: learnerObjectId,
          },
          { description: 1, learnerId: 1 }
        ).lean();
      }
    }
  }

  async getWhatsappParticipant(whatsappParticipantMap) {
    const whatsappParticipantId =
      this.member.whatsappInfo?.whatsappParticipantObjectId;
    if (whatsappParticipantId) {
      this.whatsappParticipant =
        whatsappParticipantMap[whatsappParticipantId];
      if (!this.whatsappParticipant) {
        this.whatsappParticipant = await WhatsappParticipant.findOne(
          {
            _id: whatsappParticipantId,
          },
          {
            lastReacted: 1,
            lastMessageSent: 1,
          }
        ).lean();
      }
    } else if (
      this.member.whatsappInfo &&
      this.member.subscriptionObjectId
    ) {
      this.whatsappParticipant = await WhatsappParticipant.findOne(
        {
          subscriptionObjectId: this.member.subscriptionObjectId,
        },
        {
          lastReacted: 1,
          lastMessageSent: 1,
        }
      ).lean();
    }
  }

  static async getPurchaseTransactionDetails(purchaseTransactionId) {
    const purchaseTransaction =
      await CommunityPurchaseTransaction.aggregate([
        {
          $match: {
            _id: new ObjectId(purchaseTransactionId),
          },
        },
        {
          $lookup: {
            from: 'community_discount_transactions',
            localField: '_id',
            foreignField: 'purchaseTransactionObjectId',
            as: 'communityDiscountTransactionsData',
          },
        },
        {
          $unwind: {
            path: '$communityDiscountTransactionsData',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'community_discount',
            localField:
              'communityDiscountTransactionsData.communityDiscountObjectId',
            foreignField: '_id',
            as: 'communityDiscountTransactionsData.communityDiscountData',
          },
        },
        {
          $unwind: {
            path: '$communityDiscountTransactionsData.communityDiscountData',
            preserveNullAndEmptyArrays: true,
          },
        },
      ]).option({ lean: true });
    return purchaseTransaction?.[0];
  }

  async setupActivitiesLocaleMap(languagePreference) {
    const locale = await Localization.find({
      key: { $regex: MEMBERSHIP_ACTIVITY_KEYS.REGEX_IDENTIFIER },
    }).lean();

    const localeKeyMap = new Map();

    if (locale.length > 0) {
      locale.forEach((lang) => {
        const value = lang[languagePreference] || lang['en'];
        localeKeyMap.set(lang.key, value);
      });
    }
    this.activitiesLocaleKeyMap = localeKeyMap;
  }

  async addLeadToActivityList(communityCode) {
    let leadCreatedDate = this.member?.leadInfo?.leadCreatedDate;
    if (!this.member?.leadInfo?.leadCreatedDate) {
      const purchaseTransaction =
        await CommunityPurchaseTransaction.findOne({
          community_code: communityCode,
          email: this.member.email,
        })
          .sort({ createdAt: 1 })
          .lean();
      leadCreatedDate = purchaseTransaction?.createdAt;
    }
    this.activities.push({
      id: MEMBERSHIP_ACTIVITY_KEYS.LEAD_CREATED,
      label: this.getActivityLabel(MEMBERSHIP_ACTIVITY_KEYS.LEAD_CREATED),
      date: leadCreatedDate,
    });
  }

  async addMembershipAbandonedCartToActivityList(communityCode) {
    const purchaseTransaction = await CommunityPurchaseTransaction.findOne(
      {
        community_code: communityCode,
        email: this.member.email,
        $or: [{ amount: { $gt: 0 } }, { applyDiscount: true }],
      }
    )
      .sort({ createdAt: 1 })
      .lean();

    if (!purchaseTransaction) {
      return;
    }

    if (this.member.subscriptionInfo?.signUpDate) {
      const oneHourLater = DateTime.fromJSDate(
        purchaseTransaction.createdAt
      ).plus({
        hours: 1,
      });
      const signUpDate = DateTime.fromJSDate(
        this.member.subscriptionInfo?.signUpDate
      );
      if (signUpDate < oneHourLater) {
        return;
      }
    }

    const launchDate = DateTime.utc(2024, 10, 25);
    const purchaseTransactionDate = DateTime.fromJSDate(
      purchaseTransaction.createdAt
    );
    if (purchaseTransactionDate < launchDate) {
      const productsNotPurchased =
        this.member.abandonedCheckoutInfo?.productsNotPurchased ?? [];
      const subscription = productsNotPurchased.filter(
        (i) => i.entityType === PURCHASE_TYPE.SUBSCRIPTION
      );
      if (!subscription) {
        return;
      }
    }

    this.activities.push({
      id: MEMBERSHIP_ACTIVITY_KEYS.INTERESTED_IN_PAID_MEMBERSHIP,
      label: this.getActivityLabel(
        MEMBERSHIP_ACTIVITY_KEYS.INTERESTED_IN_PAID_MEMBERSHIP
      ),
      date: purchaseTransaction.createdAt,
    });
  }

  async addMembershipEventsToActivityList() {
    const membershipActionEvents = await MembershipActionEvent.find(
      {
        actionEventType: {
          $in: [
            MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_PENDING_APPROVAL,
            MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_APPROVED,
            MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_REJECTED,
            MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_RENEWAL,
            MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_UNSUBSCRIBE,
            MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_SUCCESS,
            MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_CANCELLED,
          ],
        },
        appliedToLearnerObjectId: this.member.learnerObjectId,
        communityObjectId: this.member.communityObjectId,
      },
      {
        from: 'membershipActionEvent',
        actionEventType: 1,
        date: '$actionEventCreatedAt',
        currency: '$data.amountBreakdownInLocalCurrency.currency',
        amount: '$data.amountBreakdownInLocalCurrency.paidAmount',
        discountCode: '$data.purchaseTransaction.promoCodeStripeId',
        isFree: '$data.isFree',
        isFullDiscount: '$data.isFullDiscount',
        interval: '$data.interval',
        intervalCount: '$data.intervalCount',
        reason: '$data.subscription.cancellationReason',
      }
    ).lean();

    membershipActionEvents.forEach((item) => {
      const { actionEventType, interval, intervalCount } = item;
      let id;
      switch (actionEventType) {
        case MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_APPROVED:
          id = MEMBERSHIP_ACTIVITY_KEYS.MEMBERSHIP_APPROVED;
          break;
        case MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_SUCCESS:
          id = MEMBERSHIP_ACTIVITY_KEYS.MEMBERSHIP_JOINED;
          break;
        case MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_PENDING_APPROVAL:
          id = MEMBERSHIP_ACTIVITY_KEYS.MEMBERSHIP_APPLIED;
          break;
        case MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_REJECTED:
          id = MEMBERSHIP_ACTIVITY_KEYS.MEMBERSHIP_REJECTED;
          break;
        case MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_RENEWAL:
          id = MEMBERSHIP_ACTIVITY_KEYS.MEMBERSHIP_RENEWED;
          break;
        case MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_UNSUBSCRIBE:
          id = MEMBERSHIP_ACTIVITY_KEYS.MEMBERSHIP_UNSUBSCRIBED;
          break;
        case MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_CANCELLED:
          id = MEMBERSHIP_ACTIVITY_KEYS.MEMBERSHIP_UNSUBSCRIBED;
          break;
        default:
          break;
      }

      let intervalId = MEMBERSHIP_ACTIVITY_KEYS.INTERVAL_MONTHLY;
      if (interval === MEMBERSHIP_PLAN_INTERVAL.YEAR) {
        intervalId = MEMBERSHIP_ACTIVITY_KEYS.INTERVAL_ANNUAL;
      } else if (interval === MEMBERSHIP_PLAN_INTERVAL.MONTH) {
        if (intervalCount === 3) {
          intervalId = MEMBERSHIP_ACTIVITY_KEYS.INTERVAL_QUANTERLY;
        } else if (intervalCount === 6) {
          intervalId = MEMBERSHIP_ACTIVITY_KEYS.INTERVAL_SEMI_ANNUAL;
        }
      }

      const translationData = {
        interval: this.getActivityLabel(intervalId),
      };

      delete item._id;
      delete item.actionEventType;
      this.activities.push({
        id,
        labelData: translationData,
        ...item,
      });

      if (id === MEMBERSHIP_ACTIVITY_KEYS.MEMBERSHIP_APPROVED) {
        this.activities.push({
          id: MEMBERSHIP_ACTIVITY_KEYS.MEMBERSHIP_JOINED,
          labelData: translationData,
          ...item,
        });
      }
    });
  }

  async addAddonEventsToActivityList() {
    const addonActionEvents = await AddonActionEvent.aggregate([
      {
        $match: {
          actionEventType: {
            $in: [
              ADDON_ACTION_EVENT_TYPES.ZERO_LINK_SUCCESS,
              ADDON_ACTION_EVENT_TYPES.CHALLENGE_SIGNUP,
              ADDON_ACTION_EVENT_TYPES.SESSION_SIGNUP,
              ADDON_ACTION_EVENT_TYPES.FOLDER_SIGNUP,
            ],
          },
          appliedToLearnerObjectId: this.member.learnerObjectId,
          communityObjectId: this.member.communityObjectId,
        },
      },
      {
        $project: {
          from: 'addonActionEvent',
          entityObjectId: '$data.entityObjectId',
          entityCollection: '$data.entityCollection',
          discountCode: '$data.discountCode',
          isFree: '$data.isFree',
          isFullDiscount: '$data.isFullDiscount',
          actionEventType: 1,
          date: '$actionEventCreatedAt',
          currency: '$data.amountBreakdownInLocalCurrency.currency',
          amount: '$data.amountBreakdownInLocalCurrency.paidAmount',
        },
      },
    ]);

    addonActionEvents.forEach((item) => {
      let id;
      switch (item.actionEventType) {
        case ADDON_ACTION_EVENT_TYPES.ZERO_LINK_SUCCESS:
          id = MEMBERSHIP_ACTIVITY_KEYS.ZERO_LINK_PURCHASED;
          break;
        case ADDON_ACTION_EVENT_TYPES.FOLDER_SIGNUP:
          id = MEMBERSHIP_ACTIVITY_KEYS.ADDON_PRODUCT_PURCHASED;
          break;
        case ADDON_ACTION_EVENT_TYPES.SESSION_SIGNUP:
          id = MEMBERSHIP_ACTIVITY_KEYS.ADDON_SESSION_BOOKED;
          break;
        case ADDON_ACTION_EVENT_TYPES.CHALLENGE_SIGNUP:
          id = MEMBERSHIP_ACTIVITY_KEYS.ADDON_CHALLENGE_JOINED;
          break;
        default:
          break;
      }
      delete item.actionEventType;
      this.activities.push({
        id,
        ...item,
      });
    });
  }

  async addRefundToActivityList() {
    const refundRevenueTransactions = await RevenueTransaction.aggregate([
      {
        $match: {
          transactionType: 'OUTBOUND',
          learnerObjectId: this.member.learnerObjectId,
          communityObjectId: this.member.communityObjectId,
        },
      },
      {
        $lookup: {
          from: 'community_addon_transactions',
          localField: 'purchasedId',
          foreignField: '_id',
          as: 'transaction',
        },
      },
      {
        $unwind: {
          path: '$transaction',
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $project: {
          from: 'revenueTransaction',
          entityObjectId: '$transaction.entityObjectId',
          entityCollection: '$transaction.entityCollection',
          date: '$transactionCreatedAt',
          purchaseType: 1,
          currency: '$amountBreakdownInLocalCurrency.currency',
          amount: '$amountBreakdownInLocalCurrency.paidAmount',
          discountCode: 1,
        },
      },
    ]);

    refundRevenueTransactions.forEach((item) => {
      let id;
      switch (item.purchaseType) {
        case PURCHASE_TYPE.FOLDER:
          id = MEMBERSHIP_ACTIVITY_KEYS.ADDON_PRODUCT_PURCHASED;
          break;
        case PURCHASE_TYPE.CHALLENGE:
          id = MEMBERSHIP_ACTIVITY_KEYS.ADDON_CHALLENGE_JOINED;
          break;
        case PURCHASE_TYPE.EVENT:
          id = MEMBERSHIP_ACTIVITY_KEYS.ADDON_EVENT_REGISTERED;
          break;
        case PURCHASE_TYPE.SESSION:
          id = MEMBERSHIP_ACTIVITY_KEYS.ADDON_SESSION_BOOKED;
          break;
        case PURCHASE_TYPE.ZERO_LINK:
          id = MEMBERSHIP_ACTIVITY_KEYS.ZERO_LINK_PURCHASED;
          break;
        default:
          break;
      }
      delete item.purchaseType;
      this.activities.push({
        id,
        refund: true,
        ...item,
      });
    });
  }

  getEventAttendeeActivityDetails({
    renewedStatus,
    history,
    item,
    byLearner = false,
  }) {
    const date = history.updatedTimestamp;
    let id;
    switch (history.status) {
      case COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING: {
        if (
          renewedStatus === COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING &&
          !byLearner
        ) {
          id = MEMBERSHIP_ACTIVITY_KEYS.ADDON_EVENT_APPROVED;
        } else if (
          renewedStatus ===
            COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.REJECTED &&
          !byLearner
        ) {
          id = MEMBERSHIP_ACTIVITY_KEYS.ADDON_EVENT_REJECTED;
        }
        break;
      }
      case COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.NOT_GOING: {
        if (
          renewedStatus === COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING
        ) {
          id = MEMBERSHIP_ACTIVITY_KEYS.ADDON_EVENT_REGISTERED;
        } else if (
          renewedStatus === COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING
        ) {
          id = MEMBERSHIP_ACTIVITY_KEYS.ADDON_EVENT_APPLIED;
        }
        break;
      }
      case COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING: {
        if (
          renewedStatus === COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING
        ) {
          id = MEMBERSHIP_ACTIVITY_KEYS.ADDON_EVENT_APPLIED;
        }
        break;
      }
      default: {
        if (
          renewedStatus === COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING
        ) {
          id = MEMBERSHIP_ACTIVITY_KEYS.ADDON_EVENT_REGISTERED;
        } else if (
          renewedStatus === COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING
        ) {
          id = MEMBERSHIP_ACTIVITY_KEYS.ADDON_EVENT_APPLIED;
        }
        break;
      }
    }

    return {
      id,
      label: this.getActivityLabel(id, item),
      date,
    };
  }

  async addEventAttendeeActionsToActivityList() {
    const eventObjectIds = [];
    const eventCheckoutIds = [];
    const eventAttendees = await EventAttendees.find({
      learnerObjectId: this.member.learnerObjectId,
      communityObjectId: this.member.communityObjectId,
    })
      .select(
        'quantity learnerObjectId statusHistory status createdAt eventObjectId eventCheckoutId'
      )
      .lean();
    eventAttendees.forEach((eventAttendee) => {
      eventObjectIds.push(eventAttendee.eventObjectId);
      if (eventAttendee.eventCheckoutId) {
        eventCheckoutIds.push(eventAttendee.eventCheckoutId.toString());
      }
    });

    const [events, actionEvents] = await Promise.all([
      EventModel.find({ _id: { $in: eventObjectIds } })
        .select('title')
        .lean(),
      AddonActionEvent.find({
        'data.purchaseObjectId': { $in: eventCheckoutIds },
        actionEventType: ADDON_ACTION_EVENT_TYPES.EVENT_SIGNUP,
      })
        .select('data')
        .lean(),
    ]);

    const eventCache = new Map();
    events.forEach((event) =>
      eventCache.set(event._id.toString(), { entityTitle: event.title })
    );
    const actionEventCache = new Map();
    actionEvents.forEach((actionEvent) => {
      if (!actionEvent.data) {
        return;
      }
      actionEventCache.set(actionEvent.data.purchaseObjectId.toString(), {
        discountCode: actionEvent.data.discountCode,
        currency:
          actionEvent.data.amountBreakdownInLocalCurrency?.currency,
        amount:
          actionEvent.data.amountBreakdownInLocalCurrency?.paidAmount,
        isFree: actionEvent.data.isFree,
        isFullDiscount: actionEvent.data.isFullDiscount,
      });
    });

    eventAttendees.forEach((item) => {
      const eventInfo =
        eventCache.get(item.eventObjectId.toString()) ?? {};
      const actionEventInfo =
        actionEventCache.get(item.eventCheckoutId?.toString()) ?? {};
      let renewedStatus = item.status;
      const statusHistory = item.statusHistory ?? [];
      item.entityTitle = eventInfo.entityTitle;
      const info = {
        ...item,
        ...actionEventInfo,
        from: 'eventAttendees',
        isFree: actionEventInfo.isFree ?? true,
        isFullDiscount: actionEventInfo.isFullDiscount ?? false,
      };
      delete info._id;
      delete info.statusHistory;
      delete info.createdAt;
      delete info.learnerObjectId;
      delete info.status;
      delete info.eventObjectId;
      delete info.eventCheckoutId;

      if (statusHistory && statusHistory.length > 0) {
        statusHistory.sort(
          (a, b) => b.updatedTimestamp - a.updatedTimestamp
        );
        statusHistory.forEach((history) => {
          const byLearner =
            history.updatedByLearnerObjectId.toString() ===
            item.learnerObjectId.toString();
          const details = this.getEventAttendeeActivityDetails({
            renewedStatus,
            history,
            item,
            byLearner,
          });

          this.activities.push({
            ...info,
            ...details,
          });
          renewedStatus = history.status;
        });
      } else {
        const details = this.getEventAttendeeActivityDetails({
          renewedStatus: item.status,
          history: {
            updatedTimestamp: item.createdAt,
            status: null,
          },
          item,
        });
        this.activities.push({
          ...info,
          ...details,
        });
      }
    });
  }

  async addAbandonedCheckoutsToActivityList() {
    const abandonedCheckouts = await AbandonedCartsModel.aggregate([
      {
        $match: {
          learnerObjectId: this.member.learnerObjectId,
          communityObjectId: this.member.communityObjectId,
        },
      },
      {
        $project: {
          from: 'abandonedCarts',
          entityType: 1,
          entityObjectId: 1,
          date: '$createdAt',
          updatedAt: 1,
          status: 1,
        },
      },
    ]);

    const id = MEMBERSHIP_ACTIVITY_KEYS.INTERESTED_IN;
    abandonedCheckouts.forEach((item) => {
      if (item.status === ABANDONED_CARTS_STATUS.PURCHASED) {
        const oneHourLater = DateTime.fromJSDate(item.date).plus({
          hours: 1,
        });
        const purchasedDate = DateTime.fromJSDate(item.updatedAt);
        if (purchasedDate < oneHourLater) {
          return;
        }
      }
      delete item.updatedAt;
      delete item.status;
      this.activities.push({
        id,
        ...item,
      });
    });
  }
}

const getEntityCache = async (entityObjectIds = []) => {
  const entitiesCache = new Map();
  if (entityObjectIds.length === 0) {
    return entitiesCache;
  }
  const query = { _id: entityObjectIds };

  const [folders, events, challenges] = await Promise.all([
    FolderModel.find(query).select('title type').lean(),
    EventModel.find(query).select('title').lean(),
    ProgramModel.find(query).select('title').lean(),
  ]);

  folders.forEach((entity) => {
    const key = `${PURCHASE_TYPE.FOLDER}-${entity._id.toString()}`;
    const value = { title: entity.title };
    entitiesCache.set(key, value);
  });
  events.forEach((entity) => {
    const key = `${PURCHASE_TYPE.EVENT}-${entity._id.toString()}`;
    const value = { title: entity.title };
    entitiesCache.set(key, value);
  });
  challenges.forEach((entity) => {
    const key = `${PURCHASE_TYPE.CHALLENGE}-${entity._id.toString()}`;
    const value = { title: entity.title };
    entitiesCache.set(key, value);
  });
  return entitiesCache;
};

const getEntitiesCacheFromAbandonedCarts = async ({
  otherFilters,
  communityId,
  extraFilters = {},
}) => {
  const processedKey =
    FILTERING_FIELDS.ABANDONED_CHECKOUT_PRODUCTS_NOT_PURCHASED_ENTITY_OBJECTID.replace(
      /_/g,
      '.'
    );

  if (otherFilters && !otherFilters[processedKey]) {
    return new Map();
  }

  const entityObjectIds = await AbandonedCartsModel.find({
    communityObjectId: new ObjectId(communityId),
    ...extraFilters,
  }).distinct('entityObjectId');

  const entitiesCache = await getEntityCache(entityObjectIds);
  return entitiesCache;
};

const getCommunityMembersBySearchBasic = async ({
  community,
  searchString,
  searchIn,
  communityRole,
  status,
  reachFilters,
  otherFilters,
  projection,
  sortBy,
  sortOrder,
  skip,
  limit,
  extraConditions,
}) => {
  const searchPipeline = membershipSearchUtils.buildSearchPipeline({
    community,
    searchString,
    searchIn,
    communityRole,
    status,
    reachFilters,
    otherFilters,
    projection,
    sortBy,
    sortOrder,
    skip,
    limit,
    returnStoredSource: true,
    extraConditions,
  });
  const maxTimeMS = getMongoServerTimeoutConfig({
    collectionName: 'membership',
    operation: 'aggregate',
  });
  return Membership.aggregate(searchPipeline)
    .option({ maxTimeMS })
    .read('sp');
};

const countCommunityMembersBySearchBasic = async ({
  community,
  searchString,
  searchIn,
  communityRole,
  status,
  reachFilters,
  otherFilters,
}) => {
  const searchMetaPipeline = membershipSearchUtils.buildSearchMetaPipeline(
    {
      community,
      searchString,
      searchIn,
      communityRole,
      status,
      reachFilters,
      otherFilters,
    }
  );
  const maxTimeMS = getMongoServerTimeoutConfig({
    collectionName: 'membership',
    operation: 'aggregate',
  });
  const meta = await Membership.aggregate(searchMetaPipeline)
    .option({ maxTimeMS })
    .read('sp');
  return meta?.[0]?.count?.total ?? 0;
};

const getCommunityMemberBySearch = async ({
  communityId,
  searchString,
  searchIn,
  communityRole,
  status,
  reachFilters,
  otherFilters,
  projection,
  sortBy,
  sortOrder,
  skip,
  limit,
  withApplication = false,
  startObjectId = null,
  endObjectId = null,
}) => {
  const community = await Community.findOne({ _id: communityId }).lean();
  if (!community) {
    throw new ParamError('Community not found');
  }
  const searchPipeline = membershipSearchUtils.buildSearchPipeline({
    community,
    searchString,
    searchIn,
    communityRole,
    status,
    reachFilters,
    otherFilters,
    projection,
    sortBy,
    sortOrder,
    skip,
    limit,
    returnStoredSource: true,
    startObjectId,
    endObjectId,
  });

  const searchMetaPipeline = membershipSearchUtils.buildSearchMetaPipeline(
    {
      community,
      searchString,
      searchIn,
      communityRole,
      status,
      reachFilters,
      otherFilters,
      startObjectId,
      endObjectId,
    }
  );

  const maxTimeMS = getMongoServerTimeoutConfig({
    collectionName: 'membership',
    operation: 'aggregate',
  });

  const [members, meta] = await Promise.all([
    Membership.aggregate(searchPipeline).option({ maxTimeMS }).read('sp'),
    Membership.aggregate(searchMetaPipeline)
      .option({ maxTimeMS })
      .read('sp'),
  ]);

  if (!meta?.length) {
    logger.error('Search members meta invalid');
    return {
      members: [],
      meta: {
        total: 0,
        limit,
        page: skip / limit + 1,
        pages: 0,
      },
    };
  }
  const entitiesCache = await getEntitiesCacheFromAbandonedCarts({
    otherFilters,
    communityId,
    extraFilters: { status: ABANDONED_CARTS_STATUS.LEAD },
  });

  const membersToReturn = [];
  for await (const member of members) {
    const memberToReturn = new MembershipEntryProcessor({
      member,
      entitiesCache,
    });

    if (withApplication) {
      await memberToReturn.extractApplicationData({});
    }

    membersToReturn.push(
      memberToReturn.processForListPage({
        community,
        withApplication,
      })
    );
  }

  return {
    members: membersToReturn,
    meta: {
      total: meta[0].count.total,
      limit,
      page: skip / limit + 1,
      pages: Math.ceil(meta[0].count.total / limit),
    },
  };
};

const getCommunityMembers = async ({
  communityId,
  searchString,
  communityRole,
  status,
  reachFilters,
  otherFilters,
  projection,
  sortBy,
  sortOrder,
  skip,
  limit,
  withApplication,
  startObjectId = null,
  endObjectId = null,
}) => {
  const results = await getCommunityMemberBySearch({
    communityId,
    searchString,
    communityRole,
    status,
    reachFilters,
    otherFilters,
    projection,
    sortBy,
    sortOrder,
    skip,
    limit,
    withApplication,
    startObjectId,
    endObjectId,
  });
  return results;
};

const buildPipelineForValidSortBy = ({
  community,
  searchString,
  searchIn,
  communityRole,
  status,
  reachFilters,
  otherFilters,
  projection,
  sortBy,
  skip,
  limit,
  returnStoredSource,
  sortByAccountCompleteness = false,
  extraConditionsParam = {},
}) => {
  const extraConditions = {
    filter: [
      {
        exists: {
          path: sortBy, // Ensure the field exists
        },
      },
      {
        range: {
          path: sortBy,
          gt: '',
        },
      },
    ],
    ...extraConditionsParam,
  };

  return membershipSearchUtils.buildSearchPipeline({
    extraConditions,
    community,
    searchString,
    searchIn,
    communityRole,
    status,
    reachFilters,
    otherFilters,
    projection,
    sortBy,
    skip,
    limit,
    returnStoredSource,
    sortByAccountCompleteness,
  });
};

const getMembersWithInvalidSortBy = async ({
  community,
  searchString,
  searchIn,
  status,
  otherFilters,
  projection,
  sortBy,
  skip,
  limit,
  getBufferData,
  maxTimeMS,
  totalMembers,
}) => {
  const extraConditions = {
    mustNot: [
      {
        range: {
          path: sortBy,
          gt: '',
        },
      },
    ],
  };

  const searchMetaPipeline = membershipSearchUtils.buildSearchMetaPipeline(
    {
      extraConditions,
      community,
      searchString,
      searchIn,
      status,
      otherFilters,
    }
  );

  const meta = await Membership.aggregate(searchMetaPipeline)
    .option({ maxTimeMS })
    .read('sp');

  if (!meta?.length) {
    throw DBError('Search members meta invalid');
  }

  const totalMembersWithValidSortByField =
    totalMembers - meta[0].count.total;
  let skipForNull = 0;
  if (skip - totalMembersWithValidSortByField > 0) {
    skipForNull = skip - totalMembersWithValidSortByField;
  }

  const pipeline = membershipSearchUtils.buildSearchPipeline({
    extraConditions,
    community,
    searchString,
    searchIn,
    status,
    otherFilters,
    projection,
    sortBy,
    skip: skipForNull,
    limit: getBufferData ? limit + 1 : limit,
    returnStoredSource: true,
  });

  const results = await Membership.aggregate(pipeline)
    .option({ maxTimeMS })
    .read('sp');

  return results;
};

const getCommunityMembersForLandingPage = async ({
  communityId,
  searchString,
  searchIn,
  otherFilters,
  skip,
  limit,
  status,
  projection,
  getBufferData = false,
  extraConditionsParam,
}) => {
  const community = await Community.findOne({ _id: communityId }).lean();
  if (!community) {
    throw new ParamError('Community not found');
  }

  if (!community.isActive) {
    throw new ParamError('Community is not active');
  }

  if (community.config?.hideMemberTab) {
    throw new ParamError('It is disabled');
  }

  const sortBy = 'name';

  const searchPipelineForValidSortBy = buildPipelineForValidSortBy({
    community,
    searchString,
    searchIn,
    status,
    otherFilters,
    projection,
    sortBy,
    skip,
    limit,
    returnStoredSource: true,
    sortByAccountCompleteness: true,
    extraConditionsParam,
  });

  const searchMetaPipelineForAll =
    membershipSearchUtils.buildSearchMetaPipeline({
      community,
      searchString,
      searchIn,
      status,
      otherFilters,
      extraConditions: extraConditionsParam,
    });

  const maxTimeMS = getMongoServerTimeoutConfig({
    collectionName: 'membership',
    operation: 'aggregate',
  });

  const [membersWithValidSortBy, meta] = await Promise.all([
    Membership.aggregate(searchPipelineForValidSortBy)
      .option({ maxTimeMS })
      .read('sp'),
    Membership.aggregate(searchMetaPipelineForAll)
      .option({ maxTimeMS })
      .read('sp'),
  ]);

  if (!meta?.length) {
    throw new DBError('Search members meta invalid');
  }

  const totalMembers = meta[0].count.total;
  const standardMeta = {
    total: totalMembers,
    limit,
    page: skip / limit + 1,
    pages: Math.ceil(totalMembers / limit),
  };

  if (membersWithValidSortBy.length >= standardMeta.limit) {
    return {
      members: membersWithValidSortBy,
      meta: standardMeta,
    };
  }

  // Get the member without name setup
  // Reason: Sorting asc prioritises null values
  const membersWithInvalidSortBy = await getMembersWithInvalidSortBy({
    community,
    searchString,
    searchIn,
    status,
    otherFilters,
    projection,
    sortBy,
    skip,
    limit,
    getBufferData,
    maxTimeMS,
    totalMembers,
  });

  return {
    members: [...membersWithValidSortBy, ...membersWithInvalidSortBy],
    meta: standardMeta,
  };
};

const getCommunityMemberForLandingPage = async ({
  communityObjectId,
  learnerObjectId,
  projection,
}) => {
  const community = await Community.findOne({
    _id: communityObjectId,
  }).lean();
  if (!community) {
    throw new ParamError('Community not found');
  }

  const member = await Membership.findOne(
    { learnerObjectId, communityObjectId },
    projection
  ).lean();

  return member;
};

const getCommunityMember = async ({ communityId, membershipId }) => {
  const community = await Community.findOne({
    _id: new ObjectId(communityId),
  }).lean();

  if (!community) {
    throw new ParamError('Community not found');
  }

  const member = await Membership.findOne(
    {
      _id: new ObjectId(membershipId),
      communityObjectId: community._id,
    },
    {
      lastChangeStreamEventIds: 0,
    }
  ).lean();

  if (!member) {
    throw new ParamError('Membership not found');
  }

  const memberWithDetails = new MembershipEntryProcessor({
    member,
  });

  await memberWithDetails.extractSupportingData({});

  return memberWithDetails.processForDetailPage({ community });
};

async function getDetailMaps(members) {
  const subscriptionIds = members.map(
    (member) => member.subscriptionObjectId
  );
  const subscriptions = await CommunitySubscription.find(
    {
      _id: { $in: subscriptionIds },
    },
    {
      revewerNotes: 1,
      communitySignupId: 1,
      stripePrice: 1,
      stripeCurrency: 1,
      subscriptionId: 1,
    }
  ).lean();
  const subscriptionMap = subscriptions.reduce((acc, s) => {
    acc[s._id] = s;
    return acc;
  }, {});

  const purchaseTransactionIds = subscriptions.map(
    (s) => s.communitySignupId
  );
  const purchaseTransactions = await CommunityPurchaseTransaction.find({
    _id: { $in: purchaseTransactionIds },
  }).lean();
  const purchaseTransactionMap = purchaseTransactions.reduce((acc, p) => {
    acc[p._id] = p;
    return acc;
  }, {});

  const revenueTransactions = await RevenueTransaction.find({
    purchasedId: { $in: purchaseTransactionIds },
  });
  const revenueTransactionMap = revenueTransactions.reduce((acc, r) => {
    acc[r.purchasedId] = r;
    return acc;
  }, {});

  const applicationIds = members.map(
    (member) => member.applicationInfo?.applicationObjectId
  );
  const applications = await CommunityApplication.find({
    _id: { $in: applicationIds },
  }).lean();
  const applicationMap = applications.reduce((acc, a) => {
    acc[a._id] = a;
    return acc;
  }, {});

  const learnerIds = members.map((member) => member.learnerObjectId);
  const learners = await Learner.find(
    {
      _id: { $in: learnerIds },
    },
    {
      description: 1,
      learnerId: 1,
    }
  ).lean();
  const learnerMap = learners.reduce((acc, l) => {
    acc[l._id] = l;
    return acc;
  }, {});

  const whatsappParticipantIds = members.map(
    (member) => member.whatsappInfo?.whatsappParticipantObjectId
  );
  const whatsappParticipants = await WhatsappParticipant.find(
    {
      _id: { $in: whatsappParticipantIds },
    },
    {
      lastReacted: 1,
      lastMessageSent: 1,
    }
  ).lean();
  const whatsappParticipantMap = whatsappParticipants.reduce((acc, w) => {
    acc[w._id] = w;
    return acc;
  }, {});

  return {
    subscriptionMap,
    purchaseTransactionMap,
    applicationMap,
    learnerMap,
    whatsappParticipantMap,
    revenueTransactionMap,
  };
}

const passChecks = (field) => {
  return field
    ? `"${field.toString().replace(/"/g, "'").replace(/\n/g, '')}"`
    : '-';
};

const generateCommunityMembersCsvStream = async ({
  communityId,
  searchString,
  communityRole,
  status,
  otherFilters,
  sortBy,
  sortOrder,
}) => {
  const community = await Community.findOne({ _id: communityId }).lean();
  if (!community) {
    throw new ParamError('Community not found');
  }
  const csvStream = new Readable({
    objectMode: true,
    read() {},
  });

  const exportConfig =
    await communityUIConfigService.getCommunityExportConfig(community);
  const csvHeaders = membershipExportUtils.getMemberCsvHeaders({
    community,
    exportConfig,
  });

  csvStream.push(
    `${csvHeaders.map((entry) => passChecks(entry.label)).join(',')}\n`
  );

  let skip = 0;
  const limit = 500;
  let hasNextPage = true;

  const getNextPage = async () => {
    const searchPipeline = membershipSearchUtils.buildSearchPipeline({
      community,
      searchString,
      communityRole,
      status,
      otherFilters,
      skip,
      limit,
      sortBy,
      sortOrder,
    });
    const maxTimeMS = getMongoServerTimeoutConfig({
      collectionName: 'membership',
      operation: 'aggregate',
    });
    const members = await Membership.aggregate(searchPipeline)
      .option({
        maxTimeMS,
        lean: true,
      })
      .read('sp');
    const {
      subscriptionMap,
      applicationMap,
      purchaseTransactionMap,
      learnerMap,
      whatsappParticipantMap,
      revenueTransactionMap,
    } = await getDetailMaps(members);

    const entitiesCache = await getEntitiesCacheFromAbandonedCarts({
      otherFilters,
      communityId,
      extraFilters: { status: ABANDONED_CARTS_STATUS.LEAD },
    });
    for (const member of members) {
      try {
        const memberWithDetails = new MembershipEntryProcessor({
          member,
          entitiesCache,
        });
        // eslint-disable-next-line no-await-in-loop
        await memberWithDetails.extractSupportingData({
          applicationMap,
          subscriptionMap,
          purchaseTransactionMap,
          whatsappParticipantMap,
          learnerMap,
          revenueTransactionMap,
          needRawTransaction: false,
          needRawTransactionsCount: true,
        });
        const formatedData = membershipExportUtils.formatMemberCsvEntry({
          exportConfig,
          member: memberWithDetails.processForDownload(),
          applicationConfigDataFields:
            community.applicationConfigDataFields,
        });
        const csvValues = [];
        for (const header of csvHeaders) {
          const value = objectUtils.accessNestedObjectValue(
            formatedData,
            header.key
          );
          if (!value) {
            csvValues.push('');
          } else {
            csvValues.push(passChecks(value));
          }
        }
        csvStream.push(`${csvValues.join(',')}\n`);
      } catch (error) {
        logger.error(
          `Error processing member ${member._id} for export`,
          error,
          error.stack
        );
        csvStream.push(
          `${member.name},${member.phoneNumber},${member.countryInfo?.name},${member.email}\n`
        );
      }
    }
    skip += limit;
    if (!members?.length || members.length < limit) {
      hasNextPage = false;
    }
  };

  let isFetchingData = false;

  csvStream._read = () => {
    if (isFetchingData) {
      return;
    }

    isFetchingData = true;

    getNextPage().finally(() => {
      isFetchingData = false;

      if (!hasNextPage) {
        csvStream.push(null);
      }
    });
  };

  return csvStream;
};

async function getCommunityMembershipSegments({
  bucketOnly = false,
  communityId,
  languagePreference,
}) {
  const community = await Community.findOne({ _id: communityId });
  if (!community) {
    throw new ParamError('Community not found');
  }
  let defaultSegmentQuery = {
    communityCode: 'ALL',
    isActive: { $ne: false },
  };
  let communitySegmentQuery = {
    communityCode: community.code,
  };
  if (bucketOnly) {
    defaultSegmentQuery = {
      communityCode: 'ALL',
      isBucket: true,
    };
    communitySegmentQuery = {
      communityCode: community.code,
      isBucket: true,
    };
  }

  const defaultSegments = await MembershipSegment.find(defaultSegmentQuery)
    .sort({ displayIndex: 1 })
    .lean();
  const communitySegments = await MembershipSegment.find(
    communitySegmentQuery
  ).lean();
  const availableSegments = [];
  const communitySegmentMap = communitySegments.reduce((acc, segment) => {
    acc[segment.name] = segment;
    return acc;
  }, {});

  const GENERAL_LOCALE_IDENTIFIER = 'membership-segment';
  const localeKeyMap = new Map();
  const locale = await Localization.find({
    key: { $regex: GENERAL_LOCALE_IDENTIFIER },
  }).lean();
  locale.forEach((lang) => {
    const value = lang[languagePreference] || lang['en'];
    localeKeyMap.set(lang.key, value);
  });

  for (const segment of defaultSegments) {
    if (communitySegmentMap[segment.name]) {
      availableSegments.push(communitySegmentMap[segment.name]);
    } else {
      availableSegments.push({
        ...segment,
        displayName:
          localeKeyMap.get(
            `${GENERAL_LOCALE_IDENTIFIER}-${segment.name.replace(
              /_/g,
              '-'
            )}-display-name`
          ) || segment.displayName,
        description:
          localeKeyMap.get(
            `${GENERAL_LOCALE_IDENTIFIER}-${segment.name.replace(
              /_/g,
              '-'
            )}-description`
          ) || segment.description,
      });
    }
  }

  const segmentCountMap = {};
  await Promise.all(
    availableSegments.map(async (segment) => {
      let reachFilters = {};
      if (bucketOnly) {
        reachFilters = {
          optOutFromEmail: false,
          optOutFromWhatsapp: false,
        };
      }
      const { queryParams, otherFilters } =
        await membershipSearchUtils.processGetMembersQueryParams({
          segment: segment.name,
        });

      const count =
        await membershipCountService.countCommunityMembersWithFilters({
          communityId: community._id,
          otherFilters,
          reachFilters,
          communityRole: queryParams.role || [],
          status: queryParams.status || [],
          searchString: queryParams.searchString,
        });

      segmentCountMap[segment.name] = count;
      for (const [key, value] of Object.entries(segment.filters)) {
        if (value instanceof Array) {
          // eslint-disable-next-line no-param-reassign
          segment.filters[key] = value.join(',');
        }
      }
    })
  );

  const toReturnSegments = [];
  const communityTypes =
    membershipConfigUtils.getCommunityTypes(community);
  for (const segment of availableSegments) {
    const toReturnSegment = segment;
    toReturnSegment.count = segmentCountMap[segment.name];
    if (toReturnSegment.count > 0) {
      toReturnSegments.push(toReturnSegment);
    } else {
      const hideIfEmpty = toReturnSegment.hideIfEmpty;
      if (!hideIfEmpty) {
        const hideForCommunity = toReturnSegment.hideForCommunity;
        if (hideForCommunity) {
          if (
            !communityTypes.some((type) => hideForCommunity.includes(type))
          ) {
            toReturnSegments.push(toReturnSegment);
          }
        } else {
          toReturnSegments.push(toReturnSegment);
        }
      }
    }
  }

  return toReturnSegments;
}

const getCommunityMembershipSegment = async ({ segmentName }) => {
  const segment = await MembershipSegment.findOne({ name: segmentName });
  return segment;
};

async function getActiveSubscriptionFromEmail({ email, communityCode }) {
  const existingMemberParams = {
    $or: [
      { status: COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT },
      {
        status: COMMUNITY_SUBSCRIPTION_STATUSES.CANCELLED,
        cancelledAt: { $gte: new Date() },
      },
    ],
  };
  const existingSubscription = await CommunitySubscription.findOne({
    communityCode,
    email,
    ...existingMemberParams,
  });

  return existingSubscription;
}

const getCommunityMemberRoleFromSource = async ({
  email,
  communityCode,
}) => {
  const [nonMemberRoles, activeSubscription] = await Promise.all([
    CommunityRole.findOne(
      {
        communityCode,
        email,
      },
      { role: 1 }
    ),
    getActiveSubscriptionFromEmail({
      email,
      communityCode,
    }),
  ]);
  if (!activeSubscription) {
    return [];
  }
  if (!nonMemberRoles) {
    return [MEMBERSHIP_COMMUNITY_ROLE.MEMBER];
  }
  return nonMemberRoles.role;
};

const isCommunityManager = async ({ email, communityCode }) => {
  const roles = await getCommunityMemberRoleFromSource({
    email,
    communityCode,
  });
  if (
    roles.includes(MEMBERSHIP_COMMUNITY_ROLE.MANAGER) ||
    roles.includes(MEMBERSHIP_COMMUNITY_ROLE.ADMIN) ||
    roles.includes(MEMBERSHIP_COMMUNITY_ROLE.OWNER)
  ) {
    return true;
  }
  return false;
};

// getCommunityMemberActivity has been moved to getActivityLog.service.js to prevent circular imports

module.exports = {
  getCommunityMembershipSegments,
  getCommunityMembers,
  getCommunityMember,
  generateCommunityMembersCsvStream,
  getCommunityMembershipSegment,
  getCommunityMembersBySearchBasic,
  countCommunityMembersBySearchBasic,
  getCommunityMemberRoleFromSource,
  isCommunityManager,
  getCommunityMembersForLandingPage,
  getCommunityMemberForLandingPage,
  getActiveSubscriptionFromEmail,
  MembershipEntryProcessor,
};
