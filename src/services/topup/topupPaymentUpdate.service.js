const CommunityTopupOrderModel = require('../../models/order/communityTopupOrder.model');
const {
  submitCampaignForReview,
} = require('../magicAudience/magicAudienceCampaign.service');
const { PAYMENT_STATUSES } = require('../../communitiesAPI/constants');
const { PURCHASE_TYPE } = require('../../constants/common');
const PrimaryMongooseConnection = require('../../rpc/primaryMongooseConnection');
const {
  resetWalletBalanceByTransaction,
} = require('../wallet/wallet.service');

exports.topupOrderPaymentUpdates = async ({
  topupOrderId,
  topupOrder,
  status,
  failureCode,
  failureMessage,
  eventTime,
  session,
}) => {
  if (status === PAYMENT_STATUSES.SUCCESS) {
    await CommunityTopupOrderModel.updateOne(
      {
        _id: topupOrderId,
        'paymentDetails.status': {
          $in: [
            PAYMENT_STATUSES.PENDING,
            PAYMENT_STATUSES.INCOMPLETE,
            PAYMENT_STATUSES.FAILED,
          ],
        },
      },
      {
        'paymentDetails.status': PAYMENT_STATUSES.SUCCESS,
        'paymentDetails.latestUpdatedTime': eventTime,
        status: PAYMENT_STATUSES.SUCCESS,
        'paymentDetails.failureCode': 'N/A',
        'paymentDetails.failureMessage': 'N/A',
      },
      { session }
    );

    if (!topupOrder) {
      topupOrder = await CommunityTopupOrderModel.findById(
        topupOrderId
      ).lean();
    }

    // Update campaign status
    if (topupOrder.topupType === PURCHASE_TYPE.ADS_CAMPAIGN_TOPUP) {
      await submitCampaignForReview({
        campaignId: topupOrder.entityObjectId,
        session,
        topupOrderId,
      });
    }
  } else if (status === PAYMENT_STATUSES.FAILED) {
    topupOrder = await CommunityTopupOrderModel.findOneAndUpdate(
      {
        _id: topupOrderId,
        'paymentDetails.status': PAYMENT_STATUSES.PENDING,
      },
      {
        'paymentDetails.status': PAYMENT_STATUSES.FAILED,
        'paymentDetails.failureCode': failureCode,
        'paymentDetails.failureMessage': failureMessage,
        'paymentDetails.latestUpdatedTime': eventTime,
        status: PAYMENT_STATUSES.FAILED,
      },
      { session }
    );

    // If the payment is failed, reset the outgoing amount
    if (topupOrder) {
      await resetWalletBalanceByTransaction(
        topupOrderId,
        PURCHASE_TYPE.ADS_AVAILABLE_TO_OUTGOING,
        session
      );
    }
  }

  return topupOrder;
};

exports.topupOrderPaymentUpdatesWithSession = async ({
  topupOrderId,
  status,
  failureCode,
  failureMessage,
  eventTime,
}) => {
  let topupOrder;

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    topupOrder = await this.topupOrderPaymentUpdates({
      topupOrderId,
      status,
      failureCode,
      failureMessage,
      eventTime,
      session,
    });
    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }

  return topupOrder;
};
