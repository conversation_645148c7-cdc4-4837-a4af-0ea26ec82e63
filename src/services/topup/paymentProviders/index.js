const { ToUserError } = require('@utils/error.util');
const {
  COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES,
} = require('../../../communitiesAPI/constants');
const CommunityTopupOrderModel = require('../../../models/order/communityTopupOrder.model');
const {
  PAYMENT_PROVIDER,
  PURCHASE_TYPE,
  DEFAULT_CURRENCY,
} = require('../../../constants/common');
const { ParamError } = require('../../../utils/error.util');
const { WalletService } = require('../../wallet');
const {
  getCommunityOwnerInfo,
} = require('../../common/communityOwner.service');
const StripeService = require('./stripe.service');
const {
  handleAutoEnrollment,
} = require('../topupOrderHandler/autoEnrolmentForMetaAdsCampaign');

const retrieveAndManageTopupOrderLock = async (
  topupOrderId,
  lock = true
) => {
  const filter = {
    _id: topupOrderId,
  };

  let paymentStatus =
    COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES.INCOMPLETE;

  // Change payment detail status to pending to lock it (incomplete to pending)
  // Change payment detail status to incomplete to unlock it if error occur (to incomplete)
  if (lock) {
    filter['paymentDetails.status'] = {
      $nin: [
        COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES.PENDING,
        COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES.SUCCESS,
      ],
    };
    paymentStatus = COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES.PENDING;
  }

  const updatedTopupOrder =
    await CommunityTopupOrderModel.findOneAndUpdate(
      filter,
      {
        'paymentDetails.status': paymentStatus,
        status: paymentStatus,
      },
      {
        new: true,
      }
    ).lean();

  if (!updatedTopupOrder) {
    throw new ParamError('Topup order cannot update');
  }

  return updatedTopupOrder;
};

const updateTopupOrderPaymentToFailed = async ({
  topupOrderId,
  failureReason,
}) => {
  await CommunityTopupOrderModel.updateOne(
    { _id: topupOrderId },
    {
      'paymentDetails.status':
        COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES.FAILED,
      'paymentDetails.failureReason': failureReason,
      status: COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES.FAILED,
    }
  );
};

const updateTopupOrderPayment = async ({ topupOrderId, paymentId }) => {
  await CommunityTopupOrderModel.updateOne(
    { _id: topupOrderId },
    {
      'paymentDetails.paymentId': paymentId,
    }
  );
};

const lockWalletBalance = async ({ topupOrder }) => {
  if (topupOrder.priceDetailsInUsd.deductibleWalletBalance <= 0) {
    return;
  }
  const deductibleWalletBalance =
    topupOrder.priceDetailsInUsd.deductibleWalletBalance;

  const owner = await getCommunityOwnerInfo(topupOrder.communityObjectId);
  const walletOwnerLearnerObjectId = owner.learner._id;

  const amountBreakdownInUsd = {
    expectedPaidAmount: deductibleWalletBalance,
    netAmount: deductibleWalletBalance,
  };
  const result = await WalletService.updateWalletBalance({
    communityObjectId: topupOrder.communityObjectId,
    learnerObjectId: walletOwnerLearnerObjectId,
    transactionId: topupOrder._id,
    transactionType: PURCHASE_TYPE.ADS_AVAILABLE_TO_OUTGOING,
    paymentProvider: PAYMENT_PROVIDER.NAS,
    amountBreakdownInBaseCurrency: {
      ...amountBreakdownInUsd,
      currency: DEFAULT_CURRENCY,
    },
    amountBreakdownInUsd,
    transactionCreatedAt: new Date(),
  });
};

exports.handleTopupTransaction = async ({
  email,
  metadata,
  customerId,
  topupOrderId,
  paymentProvider,
  paymentMethodId,
  paymentMetadata,
  paymentStatus,
  ip,
}) => {
  if (!topupOrderId) {
    throw new ParamError(
      'Topup order object id does not exists in signup token'
    );
  }

  const topupOrder = await retrieveAndManageTopupOrderLock(
    topupOrderId,
    true
  );

  const { requirePayment } = paymentStatus;
  if (!requirePayment) {
    // When the wallet balance can fully cover the campaign cost
    await handleAutoEnrollment({
      email,
      requirePayment,
      topupOrder,
    });
    return paymentStatus;
  }

  let result;

  try {
    // Lock wallet balance
    await lockWalletBalance({ topupOrder });

    if (
      [PAYMENT_PROVIDER.STRIPE, PAYMENT_PROVIDER.STRIPE_US].includes(
        paymentProvider
      )
    ) {
      result = await StripeService.createPaymentIntent({
        email,
        topupOrder,
        paymentMethodId,
        customerId,
        paymentMetadata,
        paymentProvider,
        confirmMetadata: metadata,
      });
    } else {
      throw new ToUserError('No payment provider in decoded token');
    }
  } catch (err) {
    if (err?.response?.status === 418 || err.statusCode === 418) {
      await updateTopupOrderPaymentToFailed({
        topupOrderId,
        failureReason: err.message,
      });
    }
    throw err;
  }

  await updateTopupOrderPayment({
    topupOrderId,
    paymentId: result?.paymentId,
  });

  return result;
};
