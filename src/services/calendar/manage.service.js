const mongoose = require('mongoose');

const ObjectId = mongoose.Types.ObjectId;
const { DateTime } = require('luxon');
const logger = require('../logger.service');

const { CALENDAR_PLATFORMS } = require('./constants');
const GoogleService = require('./google/index');
const Users = require('../../models/users.model');
const Calendars = require('../../models/calendar/calendars.model');
const CalendarAccounts = require('../../models/calendar/calendarAccounts.model');

const { ParamError } = require('../../utils/error.util');

exports.getCalendars = async (user) => {
  if (!user.calendarAccounts || user.calendarAccounts?.length === 0) {
    return [];
  }
  const calendarforWritingEvents =
    user.calendarforWritingEvents?.toString();
  const calendarforDeconflicting = [];
  const calendarAccountObjectIds = user.calendarAccounts.map((account) => {
    account.calendarforDeconflicting.forEach((id) =>
      calendarforDeconflicting.push(id.toString())
    );
    return new ObjectId(account.calendarAccountObjectId);
  });

  const calendarAccountWithCalendars = await Calendars.aggregate([
    {
      $match: {
        calendarAccountObjectId: { $in: calendarAccountObjectIds },
      },
    },
    {
      $sort: { isDefault: -1 },
    },
    {
      $group: {
        _id: '$calendarAccountObjectId',
        calendars: {
          $push: {
            _id: '$_id',
            title: '$title',
            isDefault: '$isDefault',
            userCanWrite: '$userCanWrite',
          },
        },
      },
    },
    {
      $lookup: {
        from: 'calendar_accounts',
        localField: '_id',
        foreignField: '_id',
        as: 'calendarAccount',
      },
    },
    {
      $unwind: {
        path: '$calendarAccount',
        preserveNullAndEmptyArrays: true,
      },
    },
  ]);
  const accounts = calendarAccountWithCalendars.map((account) => {
    const calendars = account.calendars;
    const writableCalendars = [];
    const readableCalendars = calendars.map((calendar) => {
      const formattedCalendar = {
        _id: calendar._id,
        title: calendar.title,
        isDefault: calendar.isDefault,
        isSelected: calendarforDeconflicting.includes(
          calendar._id.toString()
        ),
      };
      if (calendar.userCanWrite)
        writableCalendars.push({
          ...formattedCalendar,
          isSelected: calendarforWritingEvents === calendar._id.toString(),
        });

      return formattedCalendar;
    });

    return {
      platform: account.calendarAccount?.platform,
      email: account.calendarAccount?.email,
      calendarAccountObjectId: account._id,
      writeCalendars: writableCalendars,
      readCalendars: readableCalendars,
    };
  });
  return accounts;
};

exports.setCalendarForWriteEvents = async (user, calendarObjectId) => {
  if (!user.calendarAccounts || user.calendarAccounts?.legth === 0) {
    throw new ParamError(`No calendar is connected`);
  }
  if (
    (user.calendarforWritingEvents &&
      user.calendarforWritingEvents.toString() === calendarObjectId) ||
    (!user.calendarforWritingEvents && !calendarObjectId)
  ) {
    logger.info(`No change needed for calendarforWritingEvents`);
    return;
  }

  if (calendarObjectId) {
    const calendar = await Calendars.findById(calendarObjectId)
      .select('_id')
      .lean();

    if (!calendar) {
      throw new ParamError(`${calendarObjectId} calendar is not found`);
    }
  }

  const calendarforWritingEvents = calendarObjectId
    ? new ObjectId(calendarObjectId)
    : null;
  const updatedUser = await Users.findByIdAndUpdate(
    user._id,
    {
      $set: {
        calendarforWritingEvents,
      },
    },
    {
      new: true,
      populate: { path: 'calendarAccounts.calendarAccountObjectId' },
    }
  ).lean();

  const calendarAccounts = updatedUser.calendarAccounts.map((account) => {
    return {
      platform: account.platform,
      email: account.calendarAccountObjectId?.email,
      calendarforDeconflicting: account.calendarforDeconflicting,
    };
  });
  return {
    calendarAccounts,
    calendarforWritingEvents: updatedUser.calendarforWritingEvents,
  };
};

exports.setCalendarsForDeconflicting = async (
  user,
  calendarAccountObjectId,
  calendarObjectIds
) => {
  let accessToUpdate;
  (user.calendarAccounts ?? []).forEach((account) => {
    if (
      account.calendarAccountObjectId.toString() ===
      calendarAccountObjectId.toString()
    ) {
      accessToUpdate = account;
    }
  });

  if (!accessToUpdate) {
    throw new ParamError(
      `${calendarAccountObjectId} calendar access is not found`
    );
  }
  let calendarIdsForDeconflicting;
  switch (accessToUpdate.platform) {
    case CALENDAR_PLATFORMS.GOOGLE:
      await GoogleService.initEnvVariables();
      calendarIdsForDeconflicting =
        await GoogleService.updateNotificationChannelForDeconflictingCalendars(
          user._id,
          accessToUpdate,
          calendarObjectIds
        );
      break;
    default:
      break;
  }

  const updatedUser = await Users.findByIdAndUpdate(
    user._id,
    {
      $set: {
        'calendarAccounts.$[element].calendarforDeconflicting':
          calendarIdsForDeconflicting,
      },
    },
    {
      arrayFilters: [
        {
          'element.calendarAccountObjectId':
            accessToUpdate.calendarAccountObjectId,
        },
      ],
      new: true,
      populate: { path: 'calendarAccounts.calendarAccountObjectId' },
    }
  ).lean();

  const calendarAccounts = updatedUser.calendarAccounts.map((account) => {
    return {
      platform: account.platform,
      email: account.calendarAccountObjectId.email,
      calendarforDeconflicting: account.calendarforDeconflicting,
    };
  });
  return {
    calendarAccounts,
    calendarforWritingEvents: updatedUser.calendarforWritingEvents,
  };
};

exports.getHostOccupiedCalendarSlotMap = async (
  hostLearnerObjectId,
  timezone
) => {
  const user = await Users.findOne({
    learner: new ObjectId(hostLearnerObjectId),
    isActive: true,
  })
    .select('calendarAccounts')
    .lean();
  const calendarAccounts = user.calendarAccounts ?? [];
  const calendarObjectIds = [];
  if (calendarAccounts.length > 0) {
    for await (const access of calendarAccounts) {
      if (access.calendarforDeconflicting.length > 0) {
        calendarObjectIds.push(...access.calendarforDeconflicting);
      }
    }
  }
  const calendars = await Calendars.find({
    _id: { $in: calendarObjectIds },
  }).lean();

  const formattedSlotsMap = new Map();

  await Promise.all(
    calendars.map(async (calendar) => {
      (calendar.occupiedSlots ?? []).forEach((slots) => {
        const startDate = DateTime.fromISO(
          slots.startDate.toISOString()
        ).setZone(timezone);
        const endDate = DateTime.fromISO(
          slots.endDate.toISOString()
        ).setZone(timezone);
        const formattedStartDateStr = startDate.toFormat('dd/MM/yyyy');
        const formattedEndDateStr = endDate.toFormat('dd/MM/yyyy');

        if (formattedStartDateStr === formattedEndDateStr) {
          const startDateIntervals =
            formattedSlotsMap.get(formattedStartDateStr) ?? [];
          startDateIntervals.push({
            from: startDate.toFormat('HH:mm'),
            to: endDate.toFormat('HH:mm'),
          });
          formattedSlotsMap.set(formattedStartDateStr, startDateIntervals);
        } else {
          const days = endDate.diff(startDate).as('days');

          const arr = new Array(Math.ceil(days));

          [...arr].forEach((item, index) => {
            const date = startDate.plus({ days: index });
            const dateStr = date.toFormat('dd/MM/yyyy');
            const intervals = formattedSlotsMap.get(dateStr) ?? [];

            let from = `00:00`;
            let to = `23:59`;
            if (dateStr === formattedStartDateStr) {
              from = startDate.toFormat('HH:mm');
            } else if (dateStr === formattedEndDateStr) {
              to = endDate.toFormat('HH:mm');
            }
            intervals.push({ from, to });
            formattedSlotsMap.set(dateStr, intervals);
          });
        }
      });
    })
  );
  return formattedSlotsMap;
};

const isTimeRangeOccupied = (
  occupiedSlots = [],
  sessionStartTime,
  sessionEndTime
) => {
  let isOccupied = false;
  occupiedSlots.forEach((slots) => {
    if (isOccupied) return;
    const startDate = DateTime.fromISO(slots.startDate).toUTC();
    const endDate = DateTime.fromISO(slots.endDate).toUTC();
    const sessionStartDate = DateTime.fromISO(sessionStartTime).toUTC();
    const sessionEndDate = DateTime.fromISO(sessionEndTime).toUTC();
    if (startDate === sessionStartDate) {
      logger.info(
        `Occupied on calendar: [${startDate}-${endDate}], Session=[${sessionStartDate}-${sessionEndDate}]`
      );
      isOccupied = true;
    }
    //SSD 10:00:00 -  //SED 10:30:00
    if (startDate < sessionStartDate) {
      //SD 09:00:00 - ED 10:15:00
      if (endDate > sessionStartDate) {
        logger.info(
          `Occupied on calendar: [${startDate}-${endDate}], Session=[${sessionStartDate}-${sessionEndDate}]`
        );
        isOccupied = true;
      }
      //Cases that can be ignored:
      //SD 09:00:00 - ED 10:00:00 endDate === sessionStartDate
      //SD 09:00:00 - ED 09:30:00 endDate < sessionStartDate
    }
    //SD 10:00:00
    if (startDate.toMillis() === sessionStartDate.toMillis()) {
      logger.info(
        `Occupied on calendar: [${startDate}-${endDate}], Session=[${sessionStartDate}-${sessionEndDate}]`
      );
      isOccupied = true;
    }
    if (startDate > sessionStartDate) {
      //SD 10:15:00 //ED 10:30:00
      if (startDate < sessionEndDate) {
        logger.info(
          `Occupied on calendar: [${startDate}-${endDate}], Session=[${sessionStartDate}-${sessionEndDate}]`
        );
        isOccupied = true;
      }
      //Cases that can be ignored:
      //SD 10:30:00 - ED 11:00:00
      //SD 10:45:00 - ED 11:00:00
    }
  });
  return isOccupied;
};

exports.validateSlotIsNotBooked = async (
  hostLearnerObjectId,
  sessionStartTime,
  sessionEndTime
) => {
  const user = await Users.findOne({
    learner: new ObjectId(hostLearnerObjectId),
    isActive: true,
  })
    .select('calendarAccounts')
    .lean();
  const userCalendarAccounts = user.calendarAccounts ?? [];
  if (userCalendarAccounts.length === 0) {
    return;
  }

  const calendarObjectIds = [];
  const calendarAccountObjectIds = [];

  for await (const account of userCalendarAccounts) {
    if (account.calendarforDeconflicting.length > 0) {
      calendarObjectIds.push(...account.calendarforDeconflicting);
      calendarAccountObjectIds.push(account.calendarAccountObjectId);
    }
  }
  const [calendars, calendarAccounts] = await Promise.all([
    Calendars.find({
      _id: { $in: calendarObjectIds },
    }).lean(),
    CalendarAccounts.find({
      _id: { $in: calendarAccountObjectIds },
    }).lean(),
  ]);

  const calendarAccountsMap = new Map();
  calendarAccounts.forEach((account) => {
    calendarAccountsMap.set(account._id.toString(), account);
  });

  const promise = calendars.map(async (calendar) => {
    switch (calendar.platform) {
      case CALENDAR_PLATFORMS.GOOGLE: {
        await GoogleService.initEnvVariables();
        const calendarAccount = calendarAccountsMap.get(
          calendar.calendarAccountObjectId.toString()
        );
        const googleClient =
          GoogleService.initGoogleClient(calendarAccount);

        const lowerBoundEndTime = DateTime.fromISO(sessionStartTime)
          .toUTC()
          .startOf('day');
        const upperBoundStartTime = lowerBoundEndTime.plus({ days: 1 });
        const updateDetails =
          await GoogleService.getEventsWithinGivenTimeRange(
            googleClient,
            calendar.calendarId,
            lowerBoundEndTime.toISO(),
            upperBoundStartTime.toISO()
          );
        return isTimeRangeOccupied(
          updateDetails.occupiedSlots,
          sessionStartTime,
          sessionEndTime
        );
      }
      default:
        break;
    }
  });

  const results = await Promise.all(promise);
  const filter = results.filter((i) => !!i);
  return !!(filter.length > 0);
};

exports.addEventsInCalendar = async (
  calendar,
  calendarAccount,
  data,
  attendeeLearnerInfo
) => {
  let updateDetails = {};
  switch (calendar.platform) {
    case CALENDAR_PLATFORMS.GOOGLE:
      {
        await GoogleService.initEnvVariables();
        const googleClient =
          GoogleService.initGoogleClient(calendarAccount);

        updateDetails = await GoogleService.addEvent(
          googleClient,
          calendar.calendarId,
          data,
          attendeeLearnerInfo
        );
      }
      break;
    default:
      break;
  }

  return {
    ...updateDetails,
    calendarObjectId: calendar._id,
  };
};

exports.deleteEventsInCalendar = async (
  calendarAccount,
  calendarEvent
) => {
  let updateDetails = {};
  switch (calendarAccount.platform) {
    case CALENDAR_PLATFORMS.GOOGLE:
      {
        await GoogleService.initEnvVariables();
        const googleClient =
          GoogleService.initGoogleClient(calendarAccount);
        updateDetails = await GoogleService.deleteEvent(
          googleClient,
          calendarEvent.calendarId,
          calendarEvent.eventId
        );
      }
      break;
    default:
      break;
  }
};
