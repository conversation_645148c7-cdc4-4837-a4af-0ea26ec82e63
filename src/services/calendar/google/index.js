const mongoose = require('mongoose');

const ObjectId = mongoose.Types.ObjectId;
const uuid = require('uuid');
const { DateTime } = require('luxon');

const {
  GOOGLE_CALENDAR_SCOPE,
  GOOGLE_DEFAULT_OAUTH_REDIRECT_URI,
  GOOGLE_CALENDAR_API_DEFAULT_KEYWORDS,
  WEBHOOK_NOTIFICATIONS,
  CALENDAR_PLATFORMS,
} = require('../constants');

const { CONFIG_TYPES } = require('../../../constants/common');
const { getConfigByType } = require('../../config.service');
const logger = require('../../logger.service');

const authService = require('./authApi.service');
const calendarService = require('./calendarApi.service');
const googleUtils = require('./util');
const GoogleClient = require('./client');

const CalendarModel = require('../../../models/calendar/calendars.model');
const CalendarAccountModel = require('../../../models/calendar/calendarAccounts.model');
const UserModel = require('../../../models/users.model');
const {
  InternalError,
  ToUserError,
} = require('../../../utils/error.util');
const { CALENDAR_ERROR } = require('../../../constants/errorCode');

let clientId = process.env.GOOGLE_CALENDAR_CLIENT_ID;
let clientSecret = process.env.GOOGLE_CALENDAR_CLIENT_SECRET;
let jwtSecretKey = process.env.CALENDAR_JWT_SECRET_KEY;
let googleRedirectUri = process.env.GOOGLE_OAUTH_REDIRECT_URI;

exports.initEnvVariables = async () => {
  logger.info('Initing googgle auth client');
  if (
    !jwtSecretKey ||
    !clientId ||
    !clientSecret ||
    (!clientId && !clientSecret) ||
    !googleRedirectUri
  ) {
    logger.info(
      'No clientSecret and clientId found. Searching from the config collection...'
    );
    const { envVarData = null } = await getConfigByType(
      CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
    );
    if (envVarData && Object.keys(envVarData).length) {
      logger.info('Setting the env var found in config...');
      clientId = envVarData?.GOOGLE_CALENDAR_CLIENT_ID;
      clientSecret = envVarData?.GOOGLE_CALENDAR_CLIENT_SECRET;
      jwtSecretKey = envVarData?.CALENDAR_JWT_SECRET_KEY;
      googleRedirectUri =
        envVarData?.GOOGLE_OAUTH_REDIRECT_URI ||
        GOOGLE_DEFAULT_OAUTH_REDIRECT_URI;
      console.log(`DEBUG: ${googleRedirectUri}`);
    }
  }
  if (
    !jwtSecretKey ||
    !clientId ||
    !clientSecret ||
    (!clientId && !clientSecret)
  ) {
    logger.error('Error when instantiating google oauth client ');
  }
};

/**
 * Get connection details from Google Auth
 * @param {String} code // authCode from Google oAuth
 * @returns {Object} details // fields you need to init calendar account
 */
exports.initConnectionFromAuthCode = async (code, origin) => {
  console.log(
    `DEBUG: init origin ${origin} default: ${googleRedirectUri}`
  );

  const data = await authService.getAccessTokenFromAuthCode(
    clientId,
    clientSecret,
    origin ?? googleRedirectUri,
    code
  );
  if (!data.scope.includes(GOOGLE_CALENDAR_SCOPE)) {
    throw new ToUserError(
      'Connection failed due to permission not granted. Please connect again.',
      CALENDAR_ERROR.GOOGLE_CONNECTION_FAILED
    );
  }
  const details = await googleUtils.formatConnectionDetails(
    data,
    jwtSecretKey
  );
  return details;
};

/**
 * Initialise calendar account from given connection details
 * @param {Object} connection
 * @returns {Object} { calendarAccount, isExisting: boolean to determine if account already exists }
 */
exports.initCalendarAccount = async (connection) => {
  const existingAccount = await CalendarAccountModel.findOne({
    platform: connection.platform,
    accountId: connection.accountId,
  }).lean();

  let calendarAccount;
  if (existingAccount) {
    calendarAccount = await CalendarAccountModel.findByIdAndUpdate(
      existingAccount._id,
      { ...connection, disconnectionEmailSent: false }
    );
  } else {
    calendarAccount = await CalendarAccountModel.create(connection);
  }

  return { calendarAccount, isExisting: !!existingAccount };
};

/**
 * Initialise google client with the specified calendar account
 * @param {Object} calendarAccount
 * @returns GoogleClient
 */
exports.initGoogleClient = (calendarAccount) => {
  const googleClient = new GoogleClient(calendarAccount, {
    clientId,
    clientSecret,
    jwtSecretKey,
  });
  googleClient.init();
  return googleClient;
};

/**
 * Initialise calendars for the specified calendar account
 * @param {GoogleClient} googleClient
 * @param {Boolean} isExisting
 * @returns default Calendar.
 */
exports.initCalendarsForGivenAccount = async (
  googleClient,
  isExisting
) => {
  const accountId = googleClient.accountId;
  logger.info(
    `Setting up google calendars after connection with accountId ${accountId}`
  );

  if (isExisting) {
    // find primary calendar
    const defaultCalendar = await CalendarModel.findOne({
      calendarAccountObjectId: googleClient.calendarAccountObjectId,
      isDefault: true,
    }).lean();

    if (defaultCalendar) {
      logger.info(
        `Default calendar already exists in collection for ${googleClient.calendarAccountObjectId}. Skipping calendar setup.`
      );
      return defaultCalendar;
    }
    const calendars = await CalendarModel.find({
      calendarAccountObjectId: googleClient.calendarAccountObjectId,
    }).lean();
    await this.syncCalendarAccount(googleClient, calendars);
  } else {
    await this.syncCalendarAccount(googleClient);
  }

  const defaultCalendar = await CalendarModel.findOne({
    calendarAccountObjectId: googleClient.calendarAccountObjectId,
    isDefault: true,
  }).lean();
  return defaultCalendar;
};

exports.syncCalendar = async (
  googleClient,
  calendar,
  returnDetailsWithoutUpdate = false
) => {
  const lowerBoundEndTime = DateTime.utc();
  const upperBoundStartTime = lowerBoundEndTime.plus({ days: 60 });
  const updateDetails = await this.getEventsWithinGivenTimeRange(
    googleClient,
    calendar.calendarId,
    lowerBoundEndTime.toISO(),
    upperBoundStartTime.toISO()
  );
  if (returnDetailsWithoutUpdate) {
    return updateDetails;
  }
  await CalendarModel.findByIdAndUpdate(calendar._id, updateDetails);
};

exports.syncCalendarAccount = async (
  googleClient,
  existingCalendars = [],
  params = {}
) => {
  const lastSyncAt = DateTime.utc().toISO();
  let updatedSyncToken;
  let hasNextPage = true;
  let pageToken = '';
  const pipeline = [];
  const existingCalendarsMap = new Map();
  existingCalendars.forEach((value) => {
    existingCalendarsMap.set(value.calendarId, {
      _id: value._id,
      title: value.title,
      isDefault: value.isDefault,
      notificationChannel: value.notificationChannel,
    });
  });

  while (hasNextPage) {
    // eslint-disable-next-line no-await-in-loop
    const response = await calendarService.listCalendarList(
      googleClient,
      `pageToken=${pageToken}`
    );
    const { nextPageToken, nextSyncToken, items } = response;
    if (nextSyncToken) {
      updatedSyncToken = nextSyncToken;
    }
    if (nextPageToken) {
      pageToken = nextPageToken;
    } else {
      hasNextPage = false;
    }
    // eslint-disable-next-line no-await-in-loop
    await Promise.all(
      items.map(async (calendar) => {
        const calendarDetails =
          googleUtils.formatDefaultCalendarDetails(calendar);
        if (!existingCalendarsMap.has(calendarDetails.calendarId)) {
          if (!calendarDetails.calendarId.includes('#')) {
            pipeline.push({
              insertOne: {
                document: {
                  calendarAccountObjectId:
                    googleClient.calendarAccountObjectId,
                  accountId: googleClient.accountId,
                  lastSyncAt,
                  ...calendarDetails,
                },
              },
            });
          }
        } else {
          const existingCalendar = existingCalendarsMap.get(
            calendarDetails.calendarId
          );
          if (
            calendarDetails.title !== existingCalendar.title ||
            calendarDetails.isDefault !== existingCalendar.isDefault
          ) {
            pipeline.push({
              updateOne: {
                filter: {
                  _id: new ObjectId(
                    existingCalendarsMap.get(
                      calendarDetails.calendarId
                    )._id
                  ),
                },
                update: {
                  $set: {
                    lastSyncAt,
                    ...calendarDetails,
                  },
                },
              },
            });
          }
          existingCalendarsMap.delete(calendarDetails.calendarId);
        }
      })
    );
  }

  const missingCalendarObjectIds = [];
  const missingCalendarWithNotification = [];
  // eslint-disable-next-line no-unused-vars
  existingCalendarsMap.forEach((value, key) => {
    missingCalendarObjectIds.push(new ObjectId(value._id));
    if (value.notificationChannel)
      missingCalendarWithNotification.push(value.notificationChannel);
  });

  await Promise.all(
    missingCalendarWithNotification.map(async (notificationChannel) => {
      await calendarService.stopChannel(
        googleClient,
        notificationChannel.id,
        notificationChannel.resourceId
      );
    })
  );

  if (missingCalendarObjectIds.length > 0) {
    pipeline.push({
      deleteMany: {
        filter: {
          _id: { $in: missingCalendarObjectIds },
        },
      },
    });
    await UserModel.updateMany(
      { calendarforWritingEvents: { $in: missingCalendarObjectIds } },
      { calendarforWritingEvents: null }
    );
    await UserModel.updateMany(
      {
        'calendarAccounts.platform': CALENDAR_PLATFORMS.GOOGLE,
        'calendarAccounts.calendarforDeconflicting': {
          $in: missingCalendarObjectIds,
        },
      },
      {
        $pull: {
          'calendarAccounts.$[element].calendarforDeconflicting': {
            $in: missingCalendarObjectIds,
          },
        },
      },
      { arrayFilters: [{ 'element.platform': CALENDAR_PLATFORMS.GOOGLE }] }
    );
  }

  const accountUpdateDetails = {
    lastSyncAt,
    metadata: { nextSyncToken: updatedSyncToken },
  };
  if (params.messageNumber) {
    accountUpdateDetails.metadata.messageNumber = params.messageNumber;
  }
  await Promise.all([
    CalendarModel.bulkWrite(pipeline),
    CalendarAccountModel.findByIdAndUpdate(
      googleClient.calendarAccountObjectId,
      accountUpdateDetails
    ),
  ]);
};

exports.getNotificationChannelFromWatchingCalendarEvents = async (
  googleClient,
  calendar
) => {
  const calendarObjectIdStr = calendar._id.toString();
  const calendarAccountObjectIdStr =
    googleClient.calendarAccountObjectId.toString();

  const id = uuid.v4();
  const token = `calendarAccountObjectId=${calendarAccountObjectIdStr}&calendarId=${calendar.calendarId}&calendarObjectId=${calendarObjectIdStr}`;
  const queryParams =
    GOOGLE_CALENDAR_API_DEFAULT_KEYWORDS.EVENT_TYPES_QUERY_PARAMS;
  const ttlInSeconds = 60 * 60 * 24 * 30; // 30 days
  const response = await calendarService.watchCalendarEvents(
    googleClient,
    calendar.calendarId,
    queryParams,
    {
      id,
      token,
      type: GOOGLE_CALENDAR_API_DEFAULT_KEYWORDS.WEBHOOK_TYPE,
      address: WEBHOOK_NOTIFICATIONS,
      params: {
        ttl: ttlInSeconds,
      },
    }
  );
  const expirationInMilliseconds = parseInt(response.expiration, 10);
  return {
    ...response,
    expiresAt: DateTime.fromMillis(expirationInMilliseconds),
  };
};

exports.getEventsWithinGivenTimeRange = async (
  googleClient,
  calendarId,
  lowerBoundEndTime,
  upperBoundStartTime
) => {
  // timeMax: Upper bound (exclusive) for an event's start time to filter by. Must be greater than timeMin
  // timeMin: Lower bound (exclusive) for an event's end time to filter by.
  const queryParams =
    GOOGLE_CALENDAR_API_DEFAULT_KEYWORDS.EVENT_TYPES_QUERY_PARAMS +
    `&timeMin=${lowerBoundEndTime}&timeMax=${upperBoundStartTime}` +
    `&orderBy=startTime&singleEvents=true`;
  let updatedSyncToken;
  let hasNextPage = true;
  let pageToken = '';
  const occupiedSlots = [];

  logger.info(
    `getLast30DaysEvents for calendar ${calendarId} |` +
      `queryParams=${queryParams}`
  );

  while (hasNextPage) {
    // eslint-disable-next-line no-await-in-loop
    const response = await calendarService.listCalendarEvents(
      googleClient,
      calendarId,
      `${queryParams}&pageToken=${pageToken}`
    );
    const { nextPageToken, nextSyncToken, items, timeZone } = response;
    if (nextSyncToken) {
      updatedSyncToken = nextSyncToken;
    }
    if (nextPageToken) {
      pageToken = nextPageToken;
    } else {
      hasNextPage = false;
    }
    items.forEach((item) => {
      const startDate = googleUtils.formatDateFromEvent(
        item.start,
        timeZone
      );
      const endDate = googleUtils.formatDateFromEvent(item.end, timeZone);

      const attendees = item.attendees ?? [];
      const self = attendees.filter((i) => i.self);
      if (
        (self &&
          self.responseStatus ===
            GOOGLE_CALENDAR_API_DEFAULT_KEYWORDS.RESPONSE_STATUS_DECLINED) ||
        (item.transparency &&
          item.transparency ===
            GOOGLE_CALENDAR_API_DEFAULT_KEYWORDS.TRANSPARENCY_TRANSPARENT)
      ) {
        return;
      }

      occupiedSlots.push({
        id: item.id,
        startDate,
        endDate,
        metadata: {
          etag: item.etag,
          eventType: item.eventType,
          status: item.status,
        },
        updatedAt: DateTime.fromISO(item.updated).toUTC().toISO(),
      });
    });
  }

  return {
    occupiedSlots,
    lastSyncAt: lowerBoundEndTime,
    metadata: { nextSyncToken: updatedSyncToken },
  };
};

/**
 * Setup watch notifications for calendar account
 * @param {GoogleClient} googleClient
 * @returns nothing. Only watch calendar account
 */
exports.setupCalendarAccountNotification = async (googleClient) => {
  if (!googleClient.notificationChannel) {
    const calendarAccountObjectIdStr =
      googleClient.calendarAccountObjectId.toString();

    const id = uuid.v4();
    const token = `calendarAccountObjectId=${calendarAccountObjectIdStr}`;
    const ttlInSeconds = 60 * 60 * 24 * 30; // 30 days

    const response = await calendarService.watchCalendarList(
      googleClient,
      {
        id,
        token,
        type: GOOGLE_CALENDAR_API_DEFAULT_KEYWORDS.WEBHOOK_TYPE,
        address: WEBHOOK_NOTIFICATIONS,
        params: {
          ttl: ttlInSeconds,
        },
      }
    );
    const expirationInMilliseconds = parseInt(response.expiration, 10);
    const notificationChannel = {
      ...response,
      expiresAt: DateTime.fromMillis(expirationInMilliseconds),
    };
    try {
      const newAccount = await CalendarAccountModel.findByIdAndUpdate(
        googleClient.calendarAccountObjectId,
        { notificationChannel },
        { upsert: true, new: true }
      ).lean();
      googleClient.setNotificationChannel(newAccount.notificationChannel);
    } catch (err) {
      logger.error(
        `Failed to update calendar accounts. Stopping channel` +
          `| id=${notificationChannel.id}` +
          `| resourceId=${notificationChannel.resourceId}`
      );
      await calendarService.stopChannel(
        googleClient,
        notificationChannel.id,
        notificationChannel.resourceId
      );
      throw err;
    }
  }
};

/**
 * Setup watch notifications for calendar
 * @param {GoogleClient} googleClient
 * @param {String} calendarId // if nell, set to default calendar
 * @returns nothing. Only watch calendar
 */
exports.setupCalendarNotification = async (googleClient, calendar) => {
  if (!calendar) {
    throw new InternalError(`Calendar not provided`);
  }

  if (!calendar.notificationChannel) {
    await this.syncCalendar(googleClient, calendar);
    const notificationChannel =
      await this.getNotificationChannelFromWatchingCalendarEvents(
        googleClient,
        calendar
      );

    try {
      await CalendarModel.findByIdAndUpdate(calendar._id, {
        notificationChannel,
        forDeconflicting: true,
      });
    } catch (err) {
      logger.error(
        `Failed to update calendar. Stopping channel` +
          `| id=${notificationChannel.id}` +
          `| resourceId=${notificationChannel.resourceId}`
      );
      await calendarService.stopChannel(
        googleClient,
        notificationChannel.id,
        notificationChannel.resourceId
      );
      throw err;
    }
  }
};

exports.stopChannelForDeconflictingCalendars = async (
  googleClient,
  calendarObjectIdsToUnwatch
) => {
  const calendarsToUnwatch = await CalendarModel.find({
    _id: { $in: calendarObjectIdsToUnwatch },
  }).lean();

  await Promise.all(
    calendarsToUnwatch.map(async (calendar) => {
      if (calendar.notificationChannel) {
        await calendarService.stopChannel(
          googleClient,
          calendar.notificationChannel.id,
          calendar.notificationChannel.resourceId
        );
      }
    })
  );
  await CalendarModel.updateMany(
    { _id: { $in: calendarObjectIdsToUnwatch } },
    {
      $set: {
        forDeconflicting: false,
      },
      $unset: {
        notificationChannel: 1,
      },
    }
  ).lean();
};

/**
 * Stop watch notifications for calendar
 * @param {ObjectId} userObjectId // if nell, set to default calendar
 * @param {Object} accessToDisconnect
 * @returns nothing.
 */
exports.disconnect = async (userObjectId, accessToDisconnect) => {
  const calendarAccount = await CalendarAccountModel.findById(
    accessToDisconnect.calendarAccountObjectId
  ).lean();
  if (!calendarAccount) {
    throw new InternalError(
      `Missing calendar account!!` +
        `| calendarAccountObjectId=${accessToDisconnect.calendarAccountObjectId}`
    );
  }

  const googleClient = this.initGoogleClient(calendarAccount);

  const users = await UserModel.find({
    _id: { $ne: userObjectId },
    'calendarAccounts.calendarAccountObjectId': new ObjectId(
      googleClient.calendarAccountObjectId
    ),
  })
    .select('calendarAccounts')
    .lean();

  const otherUsersDeconflictingCalendarSet =
    await googleUtils.getOtherUsersDeconflictingCalendarSets(users);

  const calendarObjectIdsToUnwatch =
    accessToDisconnect.calendarforDeconflicting.filter(
      (calendarObjectId) =>
        !otherUsersDeconflictingCalendarSet.has(
          calendarObjectId.toString()
        )
    );
  try {
    await this.stopChannelForDeconflictingCalendars(
      googleClient,
      calendarObjectIdsToUnwatch
    );

    if (users.length === 0) {
      if (calendarAccount.notificationChannel) {
        await calendarService.stopChannel(
          googleClient,
          calendarAccount.notificationChannel.id,
          calendarAccount.notificationChannel.resourceId
        );
        await CalendarAccountModel.findByIdAndUpdate(calendarAccount._id, {
          $unset: {
            notificationChannel: 1,
          },
        });
      }
    }
  } catch (error) {
    if (error.name !== CALENDAR_ERROR.GOOGLE_DISCONNECTED.name) {
      throw error;
    }
    await Promise.all([
      CalendarModel.updateMany(
        { _id: { $in: calendarObjectIdsToUnwatch } },
        {
          $set: {
            forDeconflicting: false,
          },
          $unset: {
            notificationChannel: 1,
          },
        }
      ),
      CalendarAccountModel.findByIdAndUpdate(calendarAccount._id, {
        $unset: {
          notificationChannel: 1,
        },
      }),
    ]);
  }
};

/**
 *
 * @param {*} accessToken
 * @param {*} minAccessRole 'writer' returns calendars with writer and owner access
 */
exports.getCalendars = async (accessToken, minAccessRole = null) => {
  const queryParams = `${
    minAccessRole ? `minAccessRole=${minAccessRole}` : ''
  }`;

  let hasNextPage = true;
  let pageToken = '';
  const results = [];

  while (hasNextPage) {
    // eslint-disable-next-line no-await-in-loop
    const response = await calendarService.listCalendarList(
      accessToken,
      `${queryParams}&pageToken=${pageToken}`
    );
    const { nextPageToken, items } = response;
    if (nextPageToken) {
      pageToken = nextPageToken;
    } else {
      hasNextPage = false;
    }
    items.forEach((item) => {
      results.push({
        id: item.id,
        title: item.summary,
      });
    });
  }
  return results;
};

/**
 * Setup watch channels for selected calendars AND
 * Stop watch channels for calendars that are not selected
 * @param {*} userObjectId
 * @param {*} userCalendarAccount
 * @param {*} calendarIdsForDeconflicting
 */
exports.updateNotificationChannelForDeconflictingCalendars = async (
  userObjectId,
  userCalendarAccount,
  calendarIdsForDeconflicting = []
) => {
  const calendarAccount = await CalendarAccountModel.findById(
    userCalendarAccount.calendarAccountObjectId
  ).lean();
  if (!calendarAccount) {
    throw new InternalError(
      `Missing calendar account!!` +
        `| calendarAccountObjectId=${userCalendarAccount.calendarAccountObjectId}`
    );
  }

  const googleClient = this.initGoogleClient(calendarAccount);

  const [calendars, users] = await Promise.all([
    CalendarModel.find({
      calendarAccountObjectId: userCalendarAccount.calendarAccountObjectId,
    })
      .select('_id calendarId notificationChannel')
      .lean(),
    UserModel.find({
      _id: { $ne: new ObjectId(userObjectId) },
      'calendarAccounts.calendarAccountObjectId': new ObjectId(
        userCalendarAccount.calendarAccountObjectId
      ),
    })
      .select('calendarAccounts')
      .lean(),
  ]);

  const otherUsersDeconflictingCalendarSet =
    await googleUtils.getOtherUsersDeconflictingCalendarSets(users);

  const pipeline = [];
  const calendarObjectIdsToUnwatch = [];
  const finalCalendarIdsForDeconflicting = [];

  const promise = calendars.map(async (calendar) => {
    const calendarObjectIdStr = calendar._id.toString();
    if (calendarIdsForDeconflicting.includes(calendarObjectIdStr)) {
      finalCalendarIdsForDeconflicting.push(
        new ObjectId(calendarObjectIdStr)
      );
      if (calendar.notificationChannel?.id) {
        return;
      }
      const details = await this.syncCalendar(
        googleClient,
        calendar,
        true
      );
      // Handle for newly selected calendars whose channel is not setup
      const notificationChannel =
        await this.getNotificationChannelFromWatchingCalendarEvents(
          googleClient,
          calendar
        );
      pipeline.push({
        updateOne: {
          filter: { _id: calendar._id },
          update: {
            $set: {
              ...details,
              notificationChannel,
              forDeconflicting: true,
            },
          },
        },
      });
    } else if (
      !calendarIdsForDeconflicting.includes(calendarObjectIdStr) &&
      !otherUsersDeconflictingCalendarSet.has(calendarObjectIdStr) &&
      calendar.notificationChannel?.id
    ) {
      // Handle for unselected calendars whose channel needs to be stopped since no other users have selected it as well.
      calendarObjectIdsToUnwatch.push(calendar._id);
    }
  });

  await Promise.all(promise);
  await this.stopChannelForDeconflictingCalendars(
    googleClient,
    calendarObjectIdsToUnwatch
  );

  try {
    await CalendarModel.bulkWrite(pipeline);
  } catch (err) {
    logger.error(
      `Failed to update calendar accounts. Stopping channels specified in pipeline` +
        `| pipeline=${JSON.stringify(pipeline)}`
    );
    await Promise.all(
      pipeline.map(async (item) => {
        const { notificationChannel } = item.updateOne?.update['$set'];
        if (notificationChannel) {
          await calendarService.stopChannel(
            googleClient,
            notificationChannel.id,
            notificationChannel.resourceId
          );
        }
      })
    );

    throw err;
  }
  return finalCalendarIdsForDeconflicting;
};

/**
 * Add event to calendar
 * @param {GoogleClient} googleClient
 * @param {String} calendarId
 * @param {Object} data
 * @param {Object} attendeeLearnerInfo
 * @returns Object of event details
 */
exports.addEvent = async (
  googleClient,
  calendarId,
  data,
  attendeeLearnerInfo
) => {
  const bodyParams = googleUtils.formatInsertEventBodyParams(
    data,
    attendeeLearnerInfo
  );
  const response = await calendarService.insertCalendarEvent(
    googleClient,
    calendarId,
    '',
    bodyParams
  );
  return {
    platform: CALENDAR_PLATFORMS.GOOGLE,
    calendarId,
    eventId: response.id,
    metadata: {
      sequence: response.sequence,
      etag: response.etag,
      iCalUID: response.iCalUID,
      eventType: response.eventType,
    },
  };
};

/**
 * Delete event to calendar
 * @param {GoogleClient} googleClient
 * @param {String} calendarId
 * @param {String} eventId
 * @returns nothing
 */
exports.deleteEvent = async (googleClient, calendarId, eventId) => {
  await calendarService.deleteOneCalendarEvent(
    googleClient,
    calendarId,
    eventId
  );
};

exports.validateWebhookInfo = async (
  params,
  calendarAccount,
  calendar
) => {
  const {
    channelToken,
    channelId,
    resourceId,
    resourceUri,
    messageNumber,
  } = params;

  if (!calendarAccount) {
    throw new InternalError('Missing calendar Account');
  }

  let notificationChannel = calendarAccount.notificationChannel;
  if (!calendar) {
    if (calendarAccount.metadata?.messageNumber > messageNumber) {
      throw new InternalError(
        'Ignore webhook event as messageNumber is smaller than updated account messageNumber'
      );
    }
  } else {
    if (calendar.metadata?.messageNumber > messageNumber) {
      throw new InternalError(
        'Ignore webhook event as messageNumber is smaller than updated calendar messageNumber'
      );
    }
    notificationChannel = calendar.notificationChannel;
  }

  if (
    channelId !== notificationChannel.id ||
    resourceId !== notificationChannel.resourceId ||
    resourceUri !== notificationChannel.resourceUri ||
    channelToken !== notificationChannel.token
  ) {
    throw new InternalError(
      'Mismatched in notificationChannel information!' +
        `calendarAccount=${calendarAccount._id}` +
        `calendar=${calendar._id}` +
        `params=${JSON.stringify(params)}`
    );
  }
};
