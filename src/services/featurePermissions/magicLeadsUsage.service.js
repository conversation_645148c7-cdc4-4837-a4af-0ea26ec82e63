const MagicLeadsUsageModel = require('../../models/magicLeads/magicLeadsUsage.model');
const { FEATURE_LIST_ID } = require('../../constants/common');

/**
 * Get start of current month
 */
const getStartOfCurrentMonth = () => {
  const now = new Date();
  return new Date(now.getFullYear(), now.getMonth(), 1);
};

/**
 * Retrieve monthly magic leads usage count for a community
 */
const retrieveMagicLeadsUsage = async ({
  communityObjectId,
  startDate = null,
}) => {
  const start = startDate || getStartOfCurrentMonth();
  const end = new Date();

  const usageCount = await MagicLeadsUsageModel.countDocuments({
    communityObjectId,
    createdAt: { $gte: start, $lte: end },
  });

  return usageCount;
};

/**
 * Record successful magic leads usage
 */
const recordMagicLeadsUsage = async (
  {
    communityObjectId,
    icpProfileObjectId,
    generatedLeadsCount,
    requestedCount,
    executionTime,
  },
  options = {}
) => {
  const usage = new MagicLeadsUsageModel({
    communityObjectId,
    icpProfileObjectId,
    generatedLeadsCount,
    requestedCount,
    executionTime,
  });

  // Support session for transactions
  await usage.save(options);
  return usage;
};

/**
 * Check if community can perform lead generation within monthly limit
 */
const checkMonthlyLimit = async ({
  communityObjectId,
  featurePermissionManager,
}) => {
  const currentUsage = await retrieveMagicLeadsUsage({
    communityObjectId,
  });

  const monthlyLimit = featurePermissionManager.getFeatureLimit(
    FEATURE_LIST_ID.MAGIC_LEADS_SEARCH_LIMIT
  );

  const remainingLimit = monthlyLimit - currentUsage;

  return {
    allowed: currentUsage < monthlyLimit,
    currentUsage,
    monthlyLimit,
    remainingLimit,
    message:
      currentUsage >= monthlyLimit
        ? `Monthly limit of ${monthlyLimit} lead generations exceeded. Used: ${currentUsage}/${monthlyLimit}`
        : `${remainingLimit} lead generations remaining this month`,
  };
};

module.exports = {
  retrieveMagicLeadsUsage,
  recordMagicLeadsUsage,
  checkMonthlyLimit,
  getStartOfCurrentMonth,
};
