const CommunityModel = require('@/src/communitiesAPI/models/community.model');
const { ParamError } = require('@/src/utils/error.util');
const FeaturePermissionManager = require('../common/featurePermissionManager.service');
const { FEATURE_LIST_ID_TO_NAME } = require('@/src/constants/common');

exports.getCommunityPermissions = async (communityId) => {
  const community = await CommunityModel.findById(communityId, {
    config: 1,
    featurePermissions: 1,
    currentStorageUsageInBytes: 1,
  }).lean();

  if (!community) {
    throw new ParamError('Community not found');
  }
  const planType = community.config?.planType;
  const featurePermissions = community.featurePermissions || [];

  const featurePermissionManager = new FeaturePermissionManager(
    planType,
    featurePermissions
  );

  const featurePermissionSettings =
    featurePermissionManager.getAllFeaturesDetailed();
  const features = featurePermissionSettings;

  return {
    featurePermissionType: featurePermissionManager.getFeaturePermType(),
    planType: community.config?.planType ?? 'FREE',
    features,
  };
};

exports.getFeaturePlanPermissions = async () => {
  const featurePermissionSettings =
    FeaturePermissionManager.retrieveFeaturePermissionDefaultConfigs();
  Object.keys(featurePermissionSettings).forEach(
    (featurePermissionType) => {
      const finalConfigurations = {};
      const configurations =
        featurePermissionSettings[featurePermissionType];

      Object.keys(configurations).forEach((featureId) => {
        const featureName = FEATURE_LIST_ID_TO_NAME[featureId];
        finalConfigurations[featureName] = {
          featureName,
          ...configurations[featureId],
        };
      });

      featurePermissionSettings[featurePermissionType] =
        finalConfigurations;
    }
  );

  return featurePermissionSettings;
};
