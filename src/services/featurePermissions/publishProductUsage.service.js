const { DateTime } = require('luxon');
const mongoose = require('mongoose');

const { FEATURE_PERMISSION_ERROR } = require('@constants/errorCode');
const {
  FEATURE_LIST_ID,
  CONFIG_TYPES,
  PURCHASE_TYPE,
} = require('@constants/common');

const {
  ResourceNotFoundError,
  ToUserError,
} = require('@utils/error.util');

const FeaturePermissionManager = require('@services/common/featurePermissionManager.service');
const { getConfigByTypeFromCache } = require('@services/config.service');
const { PRODUCT_STATUS } = require('@services/product/constants');

const CommunityModel = require('@/src/communitiesAPI/models/community.model');
const CommunityProducts = require('@/src/models/product/communityProduct.model');

class PublishProductUsageService {
  /**
   * Retrieve final community data based on projection
   * @param {*} communityObjectId
   * @param {*} projection
   * @param {*} communityData
   * @returns
   */
  static async _retrieveCommunity(
    communityObjectId,
    projection = {},
    communityData = {}
  ) {
    const requiredFields = Object.keys(projection);
    const hasAllFields = requiredFields.every(
      (field) => field in (communityData || {})
    );

    let community;
    if (!hasAllFields) {
      community = await CommunityModel.findById(
        communityObjectId,
        projection
      ).lean();
    } else {
      community = {};
      let data = communityData;
      if (communityData instanceof mongoose.Document) {
        data = communityData.toObject();
      }

      requiredFields.forEach((field) => {
        community[field] = data[field];
      });
    }

    if (!community) {
      throw new ResourceNotFoundError('Community not found');
    }
    return community;
  }

  /**
   * Get the launch date for the publish product limit feature
   * @returns {Date} Launch date as a JavaScript Date object
   */
  static async _getLimitLaunchJSDate() {
    const { envVarData = null } = await getConfigByTypeFromCache(
      CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
    );
    const limitLaunchDateStr =
      envVarData?.PUBLISH_PRODUCT_LIMIT_LAUNCH_DATE ??
      '2025-07-28T00:00:00Z';

    const date = DateTime.fromISO(limitLaunchDateStr, {
      zone: 'utc',
    }).toJSDate();

    return date;
  }

  /**
   * Get publish poduct usage for a community
   * @param {ObjectId} communityObjectId community._id
   * @param {Object} communityData community // Optional
   * @returns {number} Final usage count
   * @throws {ResourceNotFoundError} if community not found
   */
  static async getCommunityPublishProductUsage(
    communityObjectId,
    communityData = {}
  ) {
    const community = await this._retrieveCommunity(
      communityObjectId,
      { _id: 1, isPaidCommunity: 1 },
      communityData
    );

    // TODO: Commenting this out first cause requirement is not clear yet.
    // if (
    //   community.currentPublishProductUsage !== null &&
    //   community.currentPublishProductUsage !== undefined
    // ) {
    //   return community.currentPublishProductUsage;
    // }

    const calculatedUsage = await this.calculatePublishProductUsage(
      community._id,
      community.isPaidCommunity
    );

    return calculatedUsage;
  }

  /**
   * Calculate the publish poduct usage based on CommunityProducts collection
   * @param {ObjectId} communityObjectId community._id
   * @param {boolean} isPaidCommunity community.isPaidCommunity
   * @returns {number} Final usage count
   * @throws {ResourceNotFoundError} if community not found
   */
  static async calculatePublishProductUsage(
    communityObjectId,
    isPaidCommunity
  ) {
    let usage = 0;
    if (isPaidCommunity) {
      usage += 1;
    }

    const limitLaunchJSDate = await this._getLimitLaunchJSDate();
    const publishedProducts = await CommunityProducts.countDocuments(
      {
        communityObjectId,
        status: PRODUCT_STATUS.PUBLISHED,
        createdAt: { $gte: limitLaunchJSDate },
      },
      { _id: 1 }
    ).lean();

    usage += publishedProducts || 0;

    return usage;
  }

  /**
   * Returns boolean indicating if the community has maxed out the publish product limit
   * @param {*} communityObjectId
   * @param {*} communityData
   * @returns Boolean indicating if the community has maxed out the publish product limit
   * @throws {ResourceNotFoundError} if community not found
   */
  static async hasMaxedPublishProductLimit(
    communityObjectId,
    communityData = {}
  ) {
    const community = await this._retrieveCommunity(
      communityObjectId,
      {
        _id: 1,
        isPaidCommunity: 1,
        baseCurrency: 1,
        config: 1,
        featurePermissions: 1,
      },
      communityData
    );

    const featureId = FEATURE_LIST_ID.PRODUCT_PUBLISH;
    const featurePermissionManager = new FeaturePermissionManager(
      community.config?.planType,
      community.featurePermissions
    );

    if (
      !featurePermissionManager.isFeatureAllowed(
        featureId,
        community.baseCurrency
      )
    ) {
      return true;
    }

    if (featurePermissionManager.isFeatureUnlimited(featureId)) {
      return false;
    }

    const currentUsage = await this.getCommunityPublishProductUsage(
      communityObjectId,
      community
    );

    return featurePermissionManager.hasMaxedFeatureLimit(
      featureId,
      currentUsage
    );
  }

  /**
   * Returns boolean if publishing selected Product is Allowed based on createdAt date
   * @param {ObjectId} communityObjectId community._id
   * @param {JSDate} productCreatedAt product.createdAt // Needed
   * @param {Object} communityData community // Optional
   * @returns publishSelectedProductAllowed boolean field
   */
  static async publishSelectedProductAllowed(
    communityObjectId,
    productCreatedAt,
    communityData = {}
  ) {
    const limitLaunchJSDate = await this._getLimitLaunchJSDate();
    if (productCreatedAt.toISOString() < limitLaunchJSDate.toISOString()) {
      return true;
    }
    const hasMaxedPublishProductLimit =
      await this.hasMaxedPublishProductLimit(
        communityObjectId,
        communityData
      );

    return !hasMaxedPublishProductLimit;
  }

  /**
   * Returns boolean if publishing paid membership is Allowed based on createdAt date
   * @param {ObjectId} communityObjectId community._id
   * @param {Object} communityData community // Optional
   * @returns publishSelectedProductAllowed boolean field // selectProduct refers to paid memebrship
   */
  static async publishPaidMembershipAllowed(
    communityObjectId,
    communityData = {}
  ) {
    const community = await this._retrieveCommunity(
      communityObjectId,
      {
        _id: 1,
        isPaidCommunity: 1,
        baseCurrency: 1,
        config: 1,
        featurePermissions: 1,
      },
      communityData
    );

    // If the community is already a paid community, they should be able to change the paid membership prices
    if (community.isPaidCommunity) {
      return true;
    }

    const hasMaxedPublishProductLimit =
      await this.hasMaxedPublishProductLimit(communityObjectId, community);

    return !hasMaxedPublishProductLimit;
  }

  /**
   * Function to check if publishing a product will overshoot the publish product limit
   * @param {*} communityObjectId
   * @param {String} productType  // Needed
   * @param {JSDate} productCreatedAt product.createdAt // Needed
   * @param {*} communityData
   * @returns {Boolean} whether the product can be published without exceeding the limit
   *
   * @throws {ToUserError} if the publish product limit is exceeded or if feature is not allowed
   * @throws {ResourceNotFoundError} if community not found
   */
  static async checkPublishProductLimit(
    communityObjectId,
    productType,
    productCreatedAt,
    communityData = {}
  ) {
    const limitLaunchJSDate = await this._getLimitLaunchJSDate();
    if (
      productCreatedAt.toISOString() < limitLaunchJSDate.toISOString() &&
      productType !== PURCHASE_TYPE.SUBSCRIPTION
    ) {
      return true;
    }
    const community = await this._retrieveCommunity(
      communityObjectId,
      {
        _id: 1,
        isPaidCommunity: 1,
        baseCurrency: 1,
        config: 1,
        featurePermissions: 1,
      },
      communityData
    );

    if (
      productType === PURCHASE_TYPE.SUBSCRIPTION &&
      community.isPaidCommunity
    ) {
      return true;
    }

    const featureId = FEATURE_LIST_ID.PRODUCT_PUBLISH;
    const featurePermissionManager = new FeaturePermissionManager(
      community.config?.planType,
      community.featurePermissions
    );

    if (
      !featurePermissionManager.isFeatureAllowed(
        featureId,
        community.baseCurrency
      )
    ) {
      throw new ToUserError(
        'You’re on the Trial Plan. Upgrade to publish your product',
        FEATURE_PERMISSION_ERROR.PRODUCT_PUBLISH_NOT_ALLOWED
      );
    }

    if (!featurePermissionManager.isFeatureUnlimited(featureId)) {
      const currentUsage = await this.getCommunityPublishProductUsage(
        communityObjectId,
        community
      );

      const additionalProductNo = 1;
      const hasOvershotFeatureLimit =
        featurePermissionManager.hasOvershotFeatureLimit(
          featureId,
          currentUsage + additionalProductNo
        );

      if (hasOvershotFeatureLimit) {
        throw new ToUserError(
          'You’ve reached your publish product limit. Upgrade your plan to increase your limit.',
          FEATURE_PERMISSION_ERROR.PRODUCT_PUBLISH_LIMIT_EXCEEDED
        );
      }
    }
    return true;
  }

  // TODO: Commenting this out first cause requirement is not clear yet.
  // static async incrementUsage(communityObjectId, additionalProductNo) {
  //   if (!additionalProductNo || additionalProductNo <= 0) {
  //     return;
  //   }

  //   await CommunityModel.updateOne(
  //     { _id: communityObjectId },
  //     { $set: { currentPublishProductUsage: additionalProductNo } }
  //   );
  // }

  // TODO: Commenting this out first cause requirement is not clear yet.
  // static async decrementUsage(communityObjectId, additionalProductNo) {
  //   if (!additionalProductNo || additionalProductNo <= 0) {
  //     return;
  //   }

  //   await CommunityModel.updateOne(
  //     { _id: communityObjectId },
  //     { $set: { currentPublishProductUsage: -additionalProductNo } }
  //   );
  // }
}

module.exports = PublishProductUsageService;
