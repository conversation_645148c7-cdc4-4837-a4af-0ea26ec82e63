const mongoose = require('mongoose');

const { FEATURE_PERMISSION_ERROR } = require('@constants/errorCode');
const {
  FEATURE_LIST_ID,
  TEMPLATE_SOURCE_TYPE,
} = require('@constants/common');

const {
  ResourceNotFoundError,
  ToUserError,
} = require('@utils/error.util');

const FeaturePermissionManager = require('@services/common/featurePermissionManager.service');

const CommunityModel = require('@/src/communitiesAPI/models/community.model');
const TemplateLibraryModel = require('@/src/models/getInspired/templateLibrary.model');

class AiCofounderProductCreationService {
  /**
   * Retrieve final community data based on projection
   * @param {*} communityObjectId
   * @param {*} projection
   * @param {*} communityData
   * @returns
   */
  static async _retrieveCommunity(
    communityObjectId,
    projection = {},
    communityData = {}
  ) {
    const requiredFields = Object.keys(projection);
    const hasAllFields = requiredFields.every(
      (field) => field in (communityData || {})
    );

    let community;
    if (!hasAllFields) {
      community = await CommunityModel.findById(
        communityObjectId,
        projection
      ).lean();
    } else {
      community = {};
      let data = communityData;
      if (communityData instanceof mongoose.Document) {
        data = communityData.toObject();
      }

      requiredFields.forEach((field) => {
        community[field] = data[field];
      });
    }

    if (!community) {
      throw new ResourceNotFoundError('Community not found');
    }
    return community;
  }

  /**
   * Function to check if product can be created via ai cofounder
   * @param {ObjectId} communityObjectId
   * @param {ObjectId} templateLibraryId  // Needed
   * @param {*} communityData
   * @returns {Boolean} whether the product can be created via ai cofounder
   *
   * @throws {ToUserError} if the product cannot be created via ai cofounder
   * @throws {ResourceNotFoundError} if community not found
   */
  static async checkProductCreationEligibility(
    communityObjectId,
    templateLibraryId,
    communityData = {}
  ) {
    if (!templateLibraryId) {
      // If no templateLibraryId is provided, allow product creation since that means it's not an AI Cofounder / get inspired product
      return true;
    }

    const template = await TemplateLibraryModel.findById(
      templateLibraryId,
      { source: 1 }
    ).lean();

    if (template?.source !== TEMPLATE_SOURCE_TYPE.AI_COFOUNDER) {
      return true;
    }

    const community = await this._retrieveCommunity(
      communityObjectId,
      {
        _id: 1,
        isPaidCommunity: 1,
        baseCurrency: 1,
        config: 1,
        featurePermissions: 1,
      },
      communityData
    );

    const featureId = FEATURE_LIST_ID.AI_COFOUNDER_PRODUCT_CREATION;
    const featurePermissionManager = new FeaturePermissionManager(
      community.config?.planType,
      community.featurePermissions
    );

    if (
      !featurePermissionManager.isFeatureAllowed(
        featureId,
        community.baseCurrency
      )
    ) {
      throw new ToUserError(
        'Feature not allowed for current plan',
        FEATURE_PERMISSION_ERROR.FEATURE_NOT_ALLOWED
      );
    }

    return true;
  }
}

module.exports = AiCofounderProductCreationService;
