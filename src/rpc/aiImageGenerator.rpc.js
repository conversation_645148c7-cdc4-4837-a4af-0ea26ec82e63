const httpContext = require('express-http-context');

const logger = require('../services/logger.service');
const { sendMessageToSQSQueue } = require('../handlers/sqs.handler');
const { AI_IMAGE_GENERATION_QUEUE_URL } = require('../config');

exports.sendEventToQueue = async ({
  templateObjectId,
  communityObjectId,
  chatObjectId,
  index,
}) => {
  if (
    !templateObjectId ||
    !communityObjectId ||
    !chatObjectId ||
    index == null
  ) {
    throw new Error('Invalid parameters');
  }

  const requestId = httpContext.get('reqId');

  const message = {
    data: {
      templateObjectId,
      communityObjectId,
      chatObjectId,
      index,
    },
    requestor: 'Learning Portal Backend',
    requestId,
  };

  try {
    await sendMessageToSQSQueue({
      queueUrl: AI_IMAGE_GENERATION_QUEUE_URL,
      messageBody: message,
    });
  } catch (err) {
    logger.error(`sendEventToQueue: error: ${err.response?.data ?? err}`);
    throw err;
  }
};
