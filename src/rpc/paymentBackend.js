const { PAYMENT_BACKEND_URL, PAYMENT_BACKEND_AUTH_KEY } = process.env;
const { DateTime } = require('luxon');
const { getConfigByTypeFromCache } = require('../services/config.service');
const {
  CONFIG_TYPES,
  PAYMENT_PROVIDER,
  EBANX_SUPPORTED_CURRENCY,
} = require('../constants/common');
const logger = require('../services/logger.service');
const axios = require('../clients/axios.client');
const conversionRateModel = require('../models/conversionRate.model');
const { ToUserError, ParamError } = require('../utils/error.util');
const { GENERIC_ERROR } = require('../constants/errorCode');

class PaymentBackendRpc {
  constructor() {
    this.host = PAYMENT_BACKEND_URL;
    this.authKey = PAYMENT_BACKEND_AUTH_KEY;
  }

  async init() {
    if (this.host && this.authKey) {
      return;
    }

    try {
      const config = await getConfigByTypeFromCache(
        CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
      );
      this.host = config?.envVarData?.PAYMENT_BACKEND_URL;
      this.authKey = config?.envVarData?.PAYMENT_BACKEND_AUTH_KEY;
    } catch (err) {
      logger.info('Failed to get config from cache/db: ', err);
      throw err;
    }
  }

  async getConversionRate(sellCurrency, buyCurrency) {
    const date = DateTime.utc()
      .plus({ hours: -23, minutes: -59 })
      .toFormat('yyyy-LL-dd');
    const conversionRecord = await conversionRateModel.findOne({
      date,
      buyCurrency,
      sellCurrency,
    });

    if (conversionRecord) {
      return conversionRecord;
    }
    const axiosConfig = {
      method: 'GET',
      url: `${this.host}/api/v1/currency-conversion/get?sellCurrency=${sellCurrency}&buyCurrency=${buyCurrency}`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
    };
    const axiosResponse = await axios(axiosConfig);
    const result = axiosResponse?.data;
    logger.info(
      'Getting result from payment backend for conversion rate: ',
      result
    );
    return result?.data;
  }

  async getStripePrice({
    priceId,
    paymentProvider,
    withOtherCurrencies = false,
    localCurrency = null,
  }) {
    const axiosConfig = {
      method: 'GET',
      url: `${this.host}/api/v1/stripe/prices/${priceId}?paymentProvider=${paymentProvider}&withOtherCurrencies=${withOtherCurrencies}&localCurrency=${localCurrency}`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`getStripePrice: ${JSON.stringify(result)}`);
    return result;
  }

  async getStripeIndiaPrice(priceId) {
    const axiosConfig = {
      method: 'GET',
      url: `${this.host}/api/v1/stripe-india/prices/${priceId}`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`getStripeIndiaPrice: ${JSON.stringify(result)}`);
    return result;
  }

  async updateStripeProduct(
    stripeProductId,
    productName,
    paymentProvider
  ) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe/update-product`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        stripeProductId,
        productName,
        paymentProvider,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`updateStripeProduct: ${JSON.stringify(result)}`);
    return result;
  }

  async createStripeProduct({
    name,
    description,
    prices,
    paymentProvider = PAYMENT_PROVIDER.STRIPE,
  }) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe/create-product`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        name,
        description,
        prices,
        paymentProvider,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`createStripeProduct: ${JSON.stringify(result)}`);
    return result;
  }

  async updateStripeProductPricing({
    stripeProductId,
    prices,
    baseCurrency,
    paymentProvider = PAYMENT_PROVIDER.STRIPE,
    archiveExistingDefaultPrice = false,
  }) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe/update-product-pricing`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        stripeProductId,
        prices,
        baseCurrency,
        paymentProvider,
        archiveExistingDefaultPrice,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`updateStripeProductPricing: ${JSON.stringify(result)}`);
    return result;
  }

  async archiveStripeProductPricing({
    stripeProductId = undefined,
    archiveAllPrice = false,
    stripePriceIds = [],
    paymentProvider = PAYMENT_PROVIDER.STRIPE,
  }) {
    if (archiveAllPrice && !stripeProductId) {
      throw new ParamError('stripeProductId is required');
    }

    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe/archive-price`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        stripeProductId,
        paymentProvider,
        archiveAllPrice,
        stripePriceIds,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`archiveStripeProductPricing: ${JSON.stringify(result)}`);
    return result;
  }

  async listAllStripeProductPricing({
    stripeProductId,
    paymentProvider = PAYMENT_PROVIDER.STRIPE,
    localCurrency,
    baseCurrency,
    allCurrency = false,
    onlySelectedCurrency = false,
  }) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe/list-all-price`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        stripeProductId,
        paymentProvider,
        localCurrency,
        baseCurrency,
        allCurrency,
        onlySelectedCurrency,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`listAllStripeProductPricing: ${JSON.stringify(result)}`);
    return result;
  }

  async createStripeDiscount({
    type,
    value,
    code,
    stripeProductId,
    maxRedemptions,
    communityCode,
    duration,
    durationInMonths,
    isActive,
    paymentProvider = PAYMENT_PROVIDER.STRIPE,
  }) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe/create-discount`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        type,
        value,
        code,
        stripeProductId,
        maxRedemptions,
        communityCode,
        duration,
        durationInMonths,
        isActive,
        paymentProvider,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`createStripeDiscount: ${JSON.stringify(result)}`);
    return result;
  }

  async updateStripeDiscount({
    stripePromotionCodeId,
    isActive,
    paymentProvider = PAYMENT_PROVIDER.STRIPE,
  }) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe/update-discount`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        stripePromotionCodeId,
        isActive,
        paymentProvider,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`updateStripeDiscount: ${JSON.stringify(result)}`);
    return result;
  }

  async updateStripeIndiaProduct(stripeProductId, productName) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe-india/update-product`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        stripeProductId,
        productName,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`updateStripeIndiaProduct: ${JSON.stringify(result)}`);
    return result;
  }

  async retrieveStripeCustomerAndPaymentMethods(
    email,
    currency,
    paymentProvider
  ) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe/customers`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        email,
        currency,
        paymentProvider,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(
      `retrieveStripeCustomerAndPaymentMethods: ${JSON.stringify(result)}`
    );
    return result;
  }

  async retrieveStripeIndiaCustomerAndPaymentMethods(email) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe-india/customers`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        email,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(
      `retrieveStripeIndiaCustomerAndPaymentMethods: ${JSON.stringify(
        result
      )}`
    );
    return result;
  }

  async createStripePaymentIntent(
    email,
    customerId,
    paymentMethodId,
    amount,
    currency,
    description,
    metadata,
    paymentProvider = PAYMENT_PROVIDER.STRIPE
  ) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe/paymentIntents`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        email,
        customerId,
        paymentMethodId,
        amount,
        currency,
        description,
        metadata,
        paymentProvider,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`createStripePaymentIntent: ${JSON.stringify(result)}`);
    return result;
  }

  async createStripeIndiaPaymentIntent(
    email,
    customerId,
    paymentMethodId,
    amount,
    currency,
    description,
    metadata,
    isUpi = false
  ) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe-india/paymentIntents`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        email,
        customerId,
        paymentMethodId,
        amount,
        currency,
        description,
        metadata,
        isUpi,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(
      `createStripeIndiaPaymentIntent: ${JSON.stringify(result)}`
    );
    return result;
  }

  async createXenditPaymentIntent(
    referenceID,
    currency,
    amount,
    checkoutMethod,
    channelCode,
    channelProperties,
    metadata
  ) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/xendit/charge-ewallet`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        referenceID,
        currency,
        amount,
        checkoutMethod,
        channelCode,
        channelProperties,
        metadata,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`createXenditPaymentIntent: ${JSON.stringify(result)}`);
    return result;
  }

  async refundStripePaymentIntent(
    paymentIntentId,
    paymentProvider = PAYMENT_PROVIDER.STRIPE
  ) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe/create-refund`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        paymentIntentId,
        paymentProvider,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`refundStripePaymentIntent: ${JSON.stringify(result)}`);
    return result;
  }

  async refundStripeIndiaPaymentIntent(paymentIntentId) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe-india/create-refund`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        paymentIntentId,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(
      `refundStripeIndiaPaymentIntent: ${JSON.stringify(result)}`
    );
    return result;
  }

  async refundXenditPaymentIntent(
    referenceId,
    country,
    amount,
    reason,
    ewalletChargeId
  ) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/xendit/refund-ewallet`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        referenceId,
        country,
        amount,
        reason,
        ewalletChargeId,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`refundXenditPaymentIntent: ${JSON.stringify(result)}`);
    return result;
  }

  async refundVoltPayment(paymentId) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/volt/create-refund`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        paymentId,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`refundVoltPayment: ${JSON.stringify(result)}`);
    return result;
  }

  async refundRazorpayPayment(paymentId) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/razorpay/create-refund`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        paymentId,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`refundRazorpayPayment: ${JSON.stringify(result)}`);
    return result;
  }

  async refundEbanxPayment(
    paymentId,
    amount,
    description,
    purchaseId,
    purchaseType,
    ebanxKeyTag
  ) {
    try {
      const axiosConfig = {
        method: 'POST',
        url: `${this.host}/api/v1/ebanx/create-refund`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
        },
        data: {
          paymentId,
          amount,
          description,
          purchaseId,
          purchaseType,
          ebanxKeyTag,
        },
      };
      const result = await axios(axiosConfig).then((res) => res.data.data);
      logger.info(`refundEbanxPayment: ${JSON.stringify(result)}`);
      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
      }
      throw err;
    }
  }

  async getStripeSubscription(
    stripeSubscriptionId,
    paymentProvider = PAYMENT_PROVIDER.STRIPE
  ) {
    const axiosConfig = {
      method: 'GET',
      url: `${this.host}/api/v1/stripe/subscriptions?stripeSubscriptionId=${stripeSubscriptionId}&paymentProvider=${paymentProvider}`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`getStripeSubscription: ${JSON.stringify(result)}`);
    return result;
  }

  async cancelStripeSubscription(
    stripeSubscriptionId,
    cancellationReason,
    paymentProvider,
    issueRefund,
    cancelledAtNextBillingDate
  ) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe/cancel-subscription`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        stripeSubscriptionId,
        cancellationReason,
        paymentProvider,
        issueRefund,
        cancelledAtNextBillingDate,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`cancelStripeSubscription: ${JSON.stringify(result)}`);
    return result;
  }

  async reactivateStripeSubscription({ subscriptionId, paymentProvider }) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe/revoke-cancellation`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        stripeSubscriptionId: subscriptionId,
        paymentProvider,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info('reactivateStripeSubscription result:', result);
    return result;
  }

  async changeStripePlan({
    stripeSubscriptionId,
    purchaseTransactionObjectId = null,
    priceId,
    promotionCode = null,
    paymentProvider = PAYMENT_PROVIDER.STRIPE,
    priceInfo = null,
  }) {
    const data = {
      stripeSubscriptionId,
      purchaseTransactionObjectId,
      priceId,
      paymentProvider,
    };

    if (promotionCode) {
      data.promotionCode = promotionCode;
    }

    if (priceInfo) {
      data.priceInfo = priceInfo;
    }

    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe/change-plan`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data,
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`changeStripePlan: ${JSON.stringify(result)}`);
    return result;
  }

  async changeStripeIndiaPlan({
    stripeSubscriptionId,
    purchaseTransactionObjectId = null,
    priceId,
    promotionCode = null,
    priceInfo = null,
  }) {
    const data = {
      stripeSubscriptionId,
      purchaseTransactionObjectId,
      priceId,
    };

    if (promotionCode) {
      data.promotionCode = promotionCode;
    }

    if (priceInfo) {
      data.priceInfo = priceInfo;
    }

    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe-india/change-plan`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data,
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`changeStripeIndiaPlan: ${JSON.stringify(result)}`);
    return result;
  }

  async updateStripePaymentMethod(
    stripeSubscriptionId,
    paymentMethodId,
    paymentProvider = PAYMENT_PROVIDER.STRIPE
  ) {
    const axiosConfig = {
      method: 'PATCH',
      url: `${this.host}/api/v1/stripe/payment-methods`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        stripeSubscriptionId,
        paymentMethodId,
        paymentProvider,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`updateStripePaymentMethod: ${JSON.stringify(result)}`);
    return result;
  }

  async updateStripeIndiaPaymentMethod(
    stripeSubscriptionId,
    paymentMethodId
  ) {
    const axiosConfig = {
      method: 'PATCH',
      url: `${this.host}/api/v1/stripe-india/payment-methods`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        stripeSubscriptionId,
        paymentMethodId,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(
      `updateStripeIndiaPaymentMethod: ${JSON.stringify(result)}`
    );
    return result;
  }

  async createStripeSubscription({
    customerId,
    priceId,
    paymentMethodId,
    promotionCode = null,
    trialPeriodDays = null,
    cancelledAt = null,
    metadata,
    paymentProvider,
    selectedCurrency = null,
    priceInfo = null,
  }) {
    const data = {
      customerId,
      priceId,
      paymentMethodId,
      metadata,
      paymentProvider,
    };

    if (promotionCode) {
      data.promotionCode = promotionCode;
    }

    if (trialPeriodDays) {
      data.trialPeriodDays = trialPeriodDays;
    }

    if (cancelledAt) {
      data.cancelledAt = cancelledAt;
    }

    if (selectedCurrency) {
      data.selectedCurrency = selectedCurrency;
    }

    if (priceInfo) {
      data.priceInfo = priceInfo;
    }

    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe/subscriptions`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data,
    };

    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`createStripeSubscription: ${JSON.stringify(result)}`);
    return result;
  }

  async getStripePaymentMethod(
    paymentMethodId,
    paymentProvider = PAYMENT_PROVIDER.STRIPE
  ) {
    const axiosConfig = {
      method: 'GET',
      url: `${this.host}/api/v1/stripe/paymentMethods/${paymentMethodId}?paymentProvider=${paymentProvider}`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`getStripePaymentMethod: ${JSON.stringify(result)}`);
    return result;
  }

  async getStripeIndiaSubscription(stripeSubscriptionId) {
    const axiosConfig = {
      method: 'GET',
      url: `${this.host}/api/v1/stripe-india/subscriptions?stripeSubscriptionId=${stripeSubscriptionId}`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`getStripeIndiaSubscription: ${JSON.stringify(result)}`);
    return result;
  }

  async createStripeIndiaSubscription({
    customerId,
    priceId,
    paymentMethodId,
    promotionCode = null,
    trialPeriodDays = null,
    metadata,
    priceInfo = null,
  }) {
    const data = {
      customerId,
      priceId,
      paymentMethodId,
      metadata,
    };

    if (promotionCode) {
      data.promotionCode = promotionCode;
    }

    if (trialPeriodDays) {
      data.trialPeriodDays = trialPeriodDays;
    }

    if (priceInfo) {
      data.priceInfo = priceInfo;
    }

    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe-india/subscriptions`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data,
    };

    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(
      `createStripeIndiaSubscription: ${JSON.stringify(result)}`
    );

    return result;
  }

  async getStripeIndiaPaymentMethod(paymentMethodId) {
    const axiosConfig = {
      method: 'GET',
      url: `${this.host}/api/v1/stripe-india/paymentMethods/${paymentMethodId}`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`getStripeIndiaPaymentMethod: ${JSON.stringify(result)}`);
    return result;
  }

  async getStripeIndiaPaymentIntent(paymentIntentId) {
    const axiosConfig = {
      method: 'GET',
      url: `${this.host}/api/v1/stripe-india/paymentIntents/${paymentIntentId}`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`getStripeIndiaPaymentIntent: ${JSON.stringify(result)}`);
    return result;
  }

  async createStripeConnectAccount({
    email,
    communityName,
    communityCountryCode,
    communityLink,
    defaultCurrencyCode,
    refreshUrl,
    returnUrl,
    paymentProvider,
    metadata,
  }) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe/accounts`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        email,
        communityName,
        communityCountryCode,
        communityLink,
        defaultCurrencyCode,
        refreshUrl,
        returnUrl,
        paymentProvider,
        metadata,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`createStripeConnectAccount: ${JSON.stringify(result)}`);
    return result;
  }

  async getStripeConnectAccountLink({
    accountId,
    refreshUrl,
    returnUrl,
    paymentProvider,
  }) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe/accounts/link`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        accountId,
        refreshUrl,
        returnUrl,
        paymentProvider,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`getStripeConnectAccountLink: ${JSON.stringify(result)}`);
    return result;
  }

  async rejectStripeConnectAccount({ accountId, paymentProvider }) {
    const axiosConfig = {
      method: 'DELETE',
      url: `${this.host}/api/v1/stripe/accounts`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        accountId,
        paymentProvider,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`rejectStripeConnectAccount: ${JSON.stringify(result)}`);
    return result;
  }

  async createStripeAccountSession({ accountId, paymentProvider }) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe/accounts/session`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        accountId,
        paymentProvider,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`createStripeAccountSession: ${JSON.stringify(result)}`);
    return result;
  }

  async createStripeFXQuote({ accountId, toCurrency, paymentProvider }) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe/accounts/getFXQuote`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        accountId,
        toCurrency,
        paymentProvider,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`createStripeFXQuote: ${JSON.stringify(result)}`);
    return result;
  }

  async createStripeTransfer({
    accountId,
    transferCurrency,
    transferAmount,
    fxQuoteId,
    paymentProvider,
  }) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/stripe/accounts/transfer`,
      headers: {
        Authorization: `Bearer ${this.authKey}`,
      },
      data: {
        accountId,
        transferCurrency,
        transferAmount,
        fxQuoteId,
        paymentProvider,
      },
    };
    const result = await axios(axiosConfig).then((res) => res.data.data);
    logger.info(`createStripeTransfer: ${JSON.stringify(result)}`);
    return result;
  }

  async createStripePayout({
    accountId,
    payoutCurrency,
    payoutAmount,
    metadata,
    paymentProvider,
  }) {
    try {
      const axiosConfig = {
        method: 'POST',
        url: `${this.host}/api/v1/stripe/accounts/payout`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
        },
        data: {
          accountId,
          payoutCurrency,
          payoutAmount,
          metadata,
          paymentProvider,
        },
      };
      const result = await axios(axiosConfig).then((res) => res.data.data);
      logger.info(`createStripePayout: ${JSON.stringify(result)}`);
      return result;
    } catch (err) {
      const { errorDetails, message } = err.response?.data ?? {};
      // Because after transfer, the balance wont be able to payout immediately,
      // so we might encounter this error if we do the payout right after transfer
      if (errorDetails?.code === 'balance_insufficient') {
        throw new ToUserError(message);
      }
      throw err;
    }
  }

  async createVoltPayment(
    uniqueReference,
    currency,
    amount,
    email,
    cpfId,
    name,
    learnerId,
    metadata,
    ip
  ) {
    try {
      const axiosConfig = {
        method: 'POST',
        url: `${this.host}/api/v1/volt/create-payment`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
          'x-forwarded-for': ip,
        },
        data: {
          uniqueReference,
          currency,
          amount,
          email,
          cpfId,
          name,
          learnerId,
          metadata,
        },
      };
      const result = await axios(axiosConfig).then((res) => res.data.data);
      logger.info(`createVoltPayment: ${JSON.stringify(result)}`);
      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
      }
      throw err;
    }
  }

  async createRazorpayOrder(currency, amount, receipt, metadata, ip) {
    try {
      const axiosConfig = {
        method: 'POST',
        url: `${this.host}/api/v1/razorpay/orders`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
          'x-forwarded-for': ip,
        },
        data: {
          currency,
          amount,
          receipt,
          notes: metadata,
        },
      };
      const result = await axios(axiosConfig).then((res) => res.data.data);
      logger.info(`createRazorpayOrder: ${JSON.stringify(result)}`);
      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
      }
      throw err;
    }
  }

  async getRazorpaySubscription(razorpaySubscriptionId) {
    try {
      const axiosConfig = {
        method: 'GET',
        url: `${this.host}/api/v1/razorpay/subscriptions`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
        },
        params: {
          subscriptionId: razorpaySubscriptionId,
        },
      };

      const result = await axios(axiosConfig).then((res) => res.data.data);

      logger.info(`getRazorpaySubscription: ${JSON.stringify(result)}`);

      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
      }
      throw err;
    }
  }

  async changeRazorpayPlan(
    razorpaySubscriptionId,
    communityCode,
    amount,
    interval,
    intervalCount,
    metadata,
    promotionCode
  ) {
    try {
      const axiosConfig = {
        method: 'POST',
        url: `${this.host}/api/v1/razorpay/change-plan`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
        },
        data: {
          subscriptionId: razorpaySubscriptionId,
          communityCode,
          amount,
          interval,
          intervalCount,
          promotionCode,
          metadata,
        },
      };

      const result = await axios(axiosConfig).then((res) => res.data.data);

      logger.info(`changeRazorpayPlan: ${JSON.stringify(result)}`);

      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
      }
      throw err;
    }
  }

  async createRazorpayPlanAndSubscription(
    communityCode,
    amount,
    interval,
    intervalCount,
    metadata,
    promotionCode,
    trialPeriodDays
  ) {
    try {
      const axiosConfig = {
        method: 'POST',
        url: `${this.host}/api/v1/razorpay/subscriptions`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
        },
        data: {
          communityCode,
          amount,
          interval,
          intervalCount,
          promotionCode,
          trialPeriodDays,
          metadata,
        },
      };

      const result = await axios(axiosConfig).then((res) => res.data.data);

      logger.info(
        `createRazorpayPlanAndSubscription: ${JSON.stringify(result)}`
      );

      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
      }
      throw err;
    }
  }

  async cancelRazorpaySubscription(subscriptionId, cancelAtCycleEnd) {
    try {
      const axiosConfig = {
        method: 'DELETE',
        url: `${this.host}/api/v1/razorpay/subscriptions`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
        },
        data: {
          subscriptionId,
          cancelAtCycleEnd,
        },
      };

      const result = await axios(axiosConfig).then((res) => res.data.data);

      logger.info(`cancelRazorpaySubscription: ${JSON.stringify(result)}`);

      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
      }
      throw err;
    }
  }

  async updateRazorpaySubscription(
    subscriptionId,
    subscriptionStartDateTime
  ) {
    try {
      const axiosConfig = {
        method: 'PATCH',
        url: `${this.host}/api/v1/razorpay/subscriptions`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
        },
        data: {
          subscriptionId,
          subscriptionStartDateTime,
        },
      };

      const result = await axios(axiosConfig).then((res) => res.data.data);

      logger.info(`updateRazorpaySubscription: ${JSON.stringify(result)}`);

      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
      }
      throw err;
    }
  }

  async fetchRazorpaySubscriptionAndUpdate(subscriptionId) {
    try {
      const axiosConfig = {
        method: 'POST',
        url: `${this.host}/api/v1/razorpay/check-subscription-and-update`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
        },
        data: {
          subscriptionId,
        },
      };

      const result = await axios(axiosConfig).then((res) => res.data.data);

      logger.info(
        `fetchRazorpaySubscriptionAndUpdate: ${JSON.stringify(result)}`
      );

      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
      }
      throw err;
    }
  }

  async getRazorpayInvoiceBySubscription(subscriptionId) {
    try {
      const axiosConfig = {
        method: 'GET',
        url: `${this.host}/api/v1/razorpay/invoice?subscriptionId=${subscriptionId}`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
        },
      };

      const result = await axios(axiosConfig).then((res) => res.data.data);

      logger.info(
        `getRazorpayInvoiceBySubscription: ${JSON.stringify(result)}`
      );

      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
      }
      throw err;
    }
  }

  async getRazorpayPayment(paymentId) {
    try {
      const axiosConfig = {
        method: 'GET',
        url: `${this.host}/api/v1/razorpay/payment?paymentId=${paymentId}`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
        },
      };

      const result = await axios(axiosConfig).then((res) => res.data.data);

      logger.info(`getRazorpayPayment: ${JSON.stringify(result)}`);

      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
      }
      throw err;
    }
  }

  async createEbanxPaymentPage(
    currency,
    amount,
    email,
    name,
    metadata,
    country,
    redirectUrl,
    instalments
  ) {
    try {
      const axiosConfig = {
        method: 'POST',
        url: `${this.host}/api/v1/ebanx/create-payment`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
        },
        data: {
          currency,
          amount,
          email,
          name,
          metadata,
          country,
          redirectUrl,
          instalments,
        },
      };
      const result = await axios(axiosConfig).then((res) => res.data.data);
      logger.info(`createEbanxPaymentPage: ${JSON.stringify(result)}`);
      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
      }
      throw err;
    }
  }

  async ebanxDirectCharge(
    amount,
    currency,
    name,
    email,
    country,
    metadata,
    paymentToken,
    paymentType,
    cpfId,
    taxPayerId,
    redirectUrl,
    enforce3DS
  ) {
    if (currency === EBANX_SUPPORTED_CURRENCY.BRL && !cpfId) {
      throw new ParamError(`CPF ID is required for Brazil payment`);
    } else if (currency === EBANX_SUPPORTED_CURRENCY.ARS && !taxPayerId) {
      throw new ParamError(
        `Tax payer ID is required for Argentina payment`
      );
    }
    try {
      const axiosConfig = {
        method: 'POST',
        url: `${this.host}/api/v1/ebanx/direct-charge`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
        },
        data: {
          amount,
          currency,
          name,
          email,
          country,
          metadata,
          paymentToken,
          paymentType,
          cpfId,
          taxPayerId,
          redirectUrl,
          enforce3DS,
        },
      };
      const result = await axios(axiosConfig).then((res) => res.data.data);
      logger.info(`ebanxDirectCharge: ${JSON.stringify(result)}`);
      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
        const { errorDetails } = err.response?.data ?? {};
        if (
          // Invalid document id,
          ['BP-DR-23', 'BP-DOC-01'].includes(errorDetails?.status_code)
        ) {
          throw new ToUserError(err.message);
        } else if (errorDetails?.status_code === 'BP-DR-6') {
          throw new ToUserError(
            'Selected amount is below the minimum allowed. Please choose a higher amount.',
            GENERIC_ERROR.SELECTED_AMOUNT_LESS_THAN_MIN_AMOUNT
          );
        } else if (
          ['BP-DR-18', 'BP-DR-103'].includes(errorDetails?.status_code)
        ) {
          // Customer is disabled on ebanx
          throw new ToUserError(
            'Your payment is rejected by the gateway. Please try to pay by choosing international card.',
            GENERIC_ERROR.PAYMENT_FAILURE_GATEWAY_REJECT
          );
        } else if (errorDetails?.status_code === 'BP-DR-67') {
          throw new ToUserError('card_due_date is invalid.');
        }
      }
      throw err;
    }
  }

  async ebanxFetchAndUpdatePaymentStatus(
    hashCodes,
    notificationType = 'update',
    planOrderObjectId = null
  ) {
    try {
      const axiosConfig = {
        method: 'POST',
        url: `${this.host}/api/v1/ebanx/fetch-and-update-payment-status`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
        },
        data: `operation=payment_status_change&notification_type=${notificationType}&hash_codes=${hashCodes}`,
      };

      if (planOrderObjectId) {
        axiosConfig.data += `&planOrderObjectId=${planOrderObjectId}`;
      }
      const result = await axios(axiosConfig).then((res) => res.data.data);
      logger.info(
        `ebanxFetchAndUpdatePaymentStatus: ${JSON.stringify(result)}`
      );
      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
      }
      throw err;
    }
  }

  async createPaypalOrder(
    amount,
    currency,
    referenceId,
    itemName,
    email,
    learnerObjectId
  ) {
    try {
      const axiosConfig = {
        method: 'POST',
        url: `${this.host}/api/v1/paypal/create-order`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
        },
        data: {
          amount,
          currency,
          referenceId,
          itemName,
          email,
          learnerObjectId,
        },
      };
      const result = await axios(axiosConfig).then((res) => res.data.data);
      logger.info(`createPaypalOrder: ${JSON.stringify(result)}`);
      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
      }
      throw err;
    }
  }

  async refundPaypalOrder(
    amount,
    currency,
    paymentTransactionReferenceId,
    notes = '',
    clientIdName = ''
  ) {
    try {
      const axiosConfig = {
        method: 'POST',
        url: `${this.host}/api/v1/paypal/create-refund`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
        },
        data: {
          amount,
          currency,
          paymentTransactionReferenceId,
          notes,
          clientIdName,
        },
      };
      const result = await axios(axiosConfig).then((res) => res.data.data);
      logger.info(`refundPaypalOrder: ${JSON.stringify(result)}`);
      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
      }
      throw err;
    }
  }

  async paypalFetchPaymentAndUpdate(
    purchaseType,
    purchaseId,
    paypalOrderId
  ) {
    try {
      const axiosConfig = {
        method: 'POST',
        url: `${this.host}/api/v1/paypal/fetch-payment-and-update`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
        },
        data: {
          purchaseType,
          purchaseId,
          paypalOrderId,
        },
      };
      const result = await axios(axiosConfig).then((res) => res.data.data);
      logger.info(
        `paypalFetchPaymentAndUpdate: ${JSON.stringify(result)}`
      );
      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
      }
      throw err;
    }
  }

  async createPaypalProduct(communityCode) {
    try {
      const axiosConfig = {
        method: 'POST',
        url: `${this.host}/api/v1/paypal/create-product`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
        },
        data: {
          communityCode,
        },
      };
      const result = await axios(axiosConfig).then((res) => res.data.data);
      logger.info(`createPaypalProduct: ${JSON.stringify(result)}`);
      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
      }
      throw err;
    }
  }

  async createPaypalSubscription(productId, description, billingCycles) {
    try {
      const axiosConfig = {
        method: 'POST',
        url: `${this.host}/api/v1/paypal/subscription`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
        },
        data: {
          productId,
          description,
          billingCycles,
        },
      };
      const result = await axios(axiosConfig).then((res) => res.data.data);
      logger.info(`createPaypalSubscription: ${JSON.stringify(result)}`);
      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
      }
      throw err;
    }
  }

  async getPaypalSubscription(subscriptionId, clientIdName = '') {
    try {
      const axiosConfig = {
        method: 'GET',
        url: `${this.host}/api/v1/paypal/subscription?subscriptionId=${subscriptionId}&clientIdName=${clientIdName}`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
        },
      };
      const result = await axios(axiosConfig).then((res) => res.data.data);
      logger.info(`getPaypalSubscription: ${JSON.stringify(result)}`);
      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
      }
      throw err;
    }
  }

  async cancelPaypalSubscription(
    paypalSubscriptionId,
    cancellationReason,
    clientIdName = ''
  ) {
    try {
      const axiosConfig = {
        method: 'POST',
        url: `${this.host}/api/v1/paypal/cancel-subscription`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
        },
        data: {
          paypalSubscriptionId,
          cancellationReason,
          clientIdName,
        },
      };
      const result = await axios(axiosConfig).then((res) => res.data.data);
      logger.info(`cancelPaypalSubscription: ${JSON.stringify(result)}`);
      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
      }
      throw err;
    }
  }

  async changePaypalSubscriptionPlan(
    productId,
    subscriptionId,
    description,
    billingCycles,
    redirectUrl
  ) {
    try {
      const axiosConfig = {
        method: 'POST',
        url: `${this.host}/api/v1/paypal/change-plan`,
        headers: {
          Authorization: `Bearer ${this.authKey}`,
        },
        data: {
          productId,
          subscriptionId,
          description,
          billingCycles,
          redirectUrl,
        },
      };
      const result = await axios(axiosConfig).then((res) => res.data.data);
      logger.info(
        `changePaypalSubscriptionPlan: ${JSON.stringify(result)}`
      );
      return result;
    } catch (err) {
      if (err?.response?.data?.errorMessage) {
        err.message = err?.response?.data?.errorMessage;
      }
      throw err;
    }
  }
}

module.exports = PaymentBackendRpc;
