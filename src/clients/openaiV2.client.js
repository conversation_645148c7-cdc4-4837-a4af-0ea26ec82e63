const { OpenAI } = require('openai');
const { OPENAI_API_KEY } = require('../config');

const {
  IMPORTANT_SERVICE_STATUS,
  IMPORTANT_SERVICE_SOURCE,
  IMPORTANT_SERVICE_SERVICE_NAME,
} = require('@/src/monitoring/constants');
const importantServiceMetrics = require('@/src/monitoring/importantServiceMetrics');
const logger = require('@/src/services/logger.service');

const client = new OpenAI({ apiKey: OPENAI_API_KEY });

async function createResponse(payload, options) {
  const method = 'responses.create';
  const startTime = Date.now();

  try {
    const result = await client.responses.create(payload, options);

    // Emit success if it's not a stream
    if (!payload.stream) {
      importantServiceMetrics.incrementProcessedCounter({
        source: IMPORTANT_SERVICE_SOURCE.LPBE,
        serviceName: IMPORTANT_SERVICE_SERVICE_NAME.OPENAI,
        method,
        status: IMPORTANT_SERVICE_STATUS.SUCCESS,
      });
      importantServiceMetrics.recordLatency({
        source: IMPORTANT_SERVICE_SOURCE.LPBE,
        serviceName: IMPORTANT_SERVICE_SERVICE_NAME.OPENAI,
        method,
        status: IMPORTANT_SERVICE_STATUS.SUCCESS,
        startTime,
      });
    }

    // If streaming response, wrap async iterator to catch mid-stream errors
    if (
      payload.stream === true &&
      result &&
      typeof result[Symbol.asyncIterator] === 'function'
    ) {
      const originalIterator = result[Symbol.asyncIterator].bind(result);

      result[Symbol.asyncIterator] = () => {
        const iterator = originalIterator();
        return {
          async next() {
            try {
              const value = await iterator.next();

              // Emit success if it's not a stream
              if (value.done) {
                importantServiceMetrics.incrementProcessedCounter({
                  source: IMPORTANT_SERVICE_SOURCE.LPBE,
                  serviceName: IMPORTANT_SERVICE_SERVICE_NAME.OPENAI,
                  method,
                  status: IMPORTANT_SERVICE_STATUS.SUCCESS,
                });
                importantServiceMetrics.recordLatency({
                  source: IMPORTANT_SERVICE_SOURCE.LPBE,
                  serviceName: IMPORTANT_SERVICE_SERVICE_NAME.OPENAI,
                  method,
                  status: IMPORTANT_SERVICE_STATUS.SUCCESS,
                  startTime,
                });
              }

              return value;
            } catch (err) {
              importantServiceMetrics.incrementProcessedCounter({
                source: IMPORTANT_SERVICE_SOURCE.LPBE,
                serviceName: IMPORTANT_SERVICE_SERVICE_NAME.OPENAI,
                method,
                status: IMPORTANT_SERVICE_STATUS.FAILURE,
              });
              importantServiceMetrics.recordLatency({
                source: IMPORTANT_SERVICE_SOURCE.LPBE,
                serviceName: IMPORTANT_SERVICE_SERVICE_NAME.OPENAI,
                method,
                status: IMPORTANT_SERVICE_STATUS.FAILURE,
                startTime,
              });

              logger.error(
                `[openai] Error in stream response: ${err.message}`
              );

              throw err;
            }
          },
          async return() {
            // Support for early stream termination if needed
            if (typeof iterator.return === 'function') {
              return iterator.return();
            }
            return { done: true };
          },
          async throw(error) {
            if (typeof iterator.throw === 'function') {
              return iterator.throw(error);
            }
            throw error;
          },
        };
      };
    }

    return result;
  } catch (error) {
    importantServiceMetrics.incrementProcessedCounter({
      source: IMPORTANT_SERVICE_SOURCE.LPBE,
      serviceName: IMPORTANT_SERVICE_SERVICE_NAME.OPENAI,
      method,
      status: IMPORTANT_SERVICE_STATUS.FAILURE,
    });
    importantServiceMetrics.recordLatency({
      source: IMPORTANT_SERVICE_SOURCE.LPBE,
      serviceName: IMPORTANT_SERVICE_SERVICE_NAME.OPENAI,
      method,
      status: IMPORTANT_SERVICE_STATUS.FAILURE,
      startTime,
    });

    logger.error(`[openai] Error in response: ${error.message}`);

    throw error;
  }
}

// Preserve `this` context for other methods in `responses`
const wrappedResponses = Object.create(client.responses);
wrappedResponses.create = createResponse;

const wrappedClient = {
  ...client,
  responses: wrappedResponses,
};

module.exports = wrappedClient;
