const { OpenAI } = require('openai');
const path = require('path');
const fs = require('fs');
const { ObjectId } = require('mongoose').Types;
const {
  OPENAI_API_KEY,
  OPEN_AI_ASSISTANT_ID,
  OPEN_AI_ASSISTANT_ES_MX,
  OPEN_AI_PRODUCT_FEEDBACK_ASSISTANT_ID,
} = require('../config');
const applicationLeadsModel = require('../models/staticBackend/applicationLeads.model');
const CommunityModel = require('../communitiesAPI/models/community.model');
const communityAIAssistantModel = require('../communitiesAPI/models/communityAIAssistant.model');

const logger = require('../services/logger.service');
const {
  getCommunityInfo,
  getUserInfo,
  getProductInfo,
  getUserNotifications,
  // getPaymentInformation,
  getSubscriptionInformation,
  getPaymentInformation,
} = require('./assistantHandlers/productFeedbackBot.handler');

const MAX_WORD_COUNT = 10000;

const openai = new OpenAI({
  apiKey: OPENAI_API_KEY,
});

function getTrimmedMessages(messages) {
  let totalWordCount = 0;
  const trimmedMessages = [];

  for (const message of messages) {
    const messageWordCount = message.content?.split(/\s+/).length;

    if (totalWordCount + messageWordCount > MAX_WORD_COUNT) {
      const remainingWords = MAX_WORD_COUNT - totalWordCount;
      const trimmedMessage = message.content
        .split(/\s+/)
        .slice(0, remainingWords)
        .join(' ');
      trimmedMessages.push({
        role: message.role,
        content: trimmedMessage,
      });
    } else {
      trimmedMessages.push(message);
    }
    totalWordCount += messageWordCount;
  }

  return trimmedMessages;
}

const createChatComletion = async (messages) => {
  const trimmedMessages = getTrimmedMessages(messages);
  const response = await openai.chat.completions.create({
    model: 'gpt-4.1-mini',
    messages: trimmedMessages,
  });
  return response;
};

const updateOrCreateConversation = async ({
  communityId,
  productId,
  productType,
  threadIdInfo,
  learnerId,
  isFAQ,
  sessionId,
  countryId,
}) => {
  const filter = {
    communityId,
    productId,
    productType,
    sessionId,
    isFAQ: isFAQ || false,
  };

  const updateData = {
    $inc: { messagesCount: 1 },
    $set: {
      sessionId,
      threadId: threadIdInfo,
      countryId,
      ...(learnerId ? { learnerId } : {}), // Ensure learnerId is included only if it exists
    },
  };

  try {
    const updateResp = await communityAIAssistantModel.updateOne(
      filter,
      updateData,
      { upsert: true }
    );

    return updateResp;
  } catch (error) {
    // retry without upsert
    if (error.code === 11000) {
      const updateResp = await communityAIAssistantModel.updateOne(
        filter,
        updateData
      );

      return updateResp;
    }

    throw error;
  }
};

const fetchAssistantAndAskAQuestion = async ({
  messageContent,
  name,
  email,
  currentLocale,
}) => {
  let GPT_ASSISTANT_ID;
  switch (currentLocale) {
    case 'en':
      GPT_ASSISTANT_ID = OPEN_AI_ASSISTANT_ID;
      break;
    case 'es-mx':
      GPT_ASSISTANT_ID = OPEN_AI_ASSISTANT_ES_MX;
      break;
    default:
      GPT_ASSISTANT_ID = OPEN_AI_ASSISTANT_ID;
  }
  const assistant = await openai.beta.assistants.retrieve(
    GPT_ASSISTANT_ID
  );

  const thread = await openai.beta.threads.create();

  await openai.beta.threads.messages.create(thread.id, {
    role: 'user',
    content: messageContent,
  });

  // this is a non streaming code
  const run = await openai.beta.threads.runs.createAndPoll(thread.id, {
    assistant_id: assistant.id,
  });
  let messageResponse = '';

  if (run.status === 'completed') {
    const messages = await openai.beta.threads.messages.list(
      run.thread_id
    );
    for (const message of messages.data.reverse()) {
      if (message.role === 'assistant') {
        const { content } = message;
        messageResponse += content?.[0]?.text?.value;
      }
    }
  } else {
    messageResponse = 'Sorry, I am not able to process your request';
  }

  const userMessageAndResponse = {
    userMessage: messageContent,
    assistantMessage: messageResponse,
    name,
    email,
  };

  await applicationLeadsModel.findOneAndUpdate(
    {
      email,
      source: 'challengeGPT',
      courseCode: 'CHALLENGE_GPT',
    },
    {
      $push: {
        gptResponse: userMessageAndResponse,
      },
    },
    {
      upsert: true,
    }
  );

  return messageResponse;
};

const fetchSalesAgentAndGetFaqs = async (params) => {
  const {
    messageContent,
    threadId,
    communityId,
    productType,
    productId,
    learnerId,
    sessionId,
    countryId,
  } = params;

  let threadIdInfo = threadId;

  const [community] = await Promise.all([
    CommunityModel.findOne({
      _id: new ObjectId(communityId),
    }),
  ]);

  const { aiAssistantInfo } = community;

  if (!aiAssistantInfo) {
    const error = new Error('Assistant info not found');
    error.errorCode = 400;
    throw error;
  }

  // front end will also check this condition
  const {
    isEnabledForChallenges,
    isEnabledForEvents,
    isEnabledForCourse,
    isEnabledFor1on1,
    assistantId,
  } = aiAssistantInfo;

  // Define a mapping for product types and their corresponding flags and error messages
  const productTypeConfig = {
    CHALLENGE: {
      isEnabled: isEnabledForChallenges,
      errorMessage: 'Assistant is not enabled for challenges',
    },
    EVENT: {
      isEnabled: isEnabledForEvents,
      errorMessage: 'Assistant is not enabled for events',
    },
    COURSE: {
      isEnabled: isEnabledForCourse,
      errorMessage: 'Assistant is not enabled for courses',
    },
    '1ON1': {
      isEnabled: isEnabledFor1on1,
      errorMessage: 'Assistant is not enabled for 1-on-1',
    },
  };

  // Check if the product type exists in the mapping
  const productConfig = productTypeConfig[productType];

  if (!productConfig) {
    const error = new Error('No assistant found for the type of product');
    error.errorCode = 403;
    throw error;
  }

  // Validate if the assistant is enabled for the given product type
  if (!productConfig.isEnabled) {
    const error = new Error(productConfig.errorMessage);
    error.errorCode = 403;
    throw error;
  }

  if (threadIdInfo) {
    try {
      // fetch all the runs
      const runs = await openai.beta.threads.runs.list(threadIdInfo);

      if (runs.data.length > 0) {
        await Promise.all(
          runs.data?.map(async (run) => {
            console.log('stopping the run');
            if (run.status === 'in_progress' || run.status === 'queued')
              await openai.beta.threads.runs.cancel(run.id, {
                thread_id: threadId,
              });
          })
        );
      }
    } catch (error) {
      logger.log('no thread running for the user');
    }
  }

  // Retrieve the assistant
  const assistant = await openai.beta.assistants.retrieve(assistantId);

  // Create a new thread
  if (!threadId) {
    const thread = await openai.beta.threads.create();
    threadIdInfo = thread.id;
  }

  await openai.beta.threads.messages.create(threadIdInfo, {
    role: 'user',
    content: messageContent,
  });

  // Get the assistant's response at once
  const run = await openai.beta.threads.runs.createAndPoll(threadIdInfo, {
    assistant_id: assistant.id,
  });

  const status = run?.status;

  if (status === 'completed') {
    threadIdInfo = run.thread_id;

    // fetch msgs in thread
    const messages = await openai.beta.threads.messages.list(threadIdInfo);

    const latestMessage = messages.data[0];
    const assistantResponse = latestMessage.content[0].text.value;

    // return statusResponse.data.content; // Full assistant response
    await updateOrCreateConversation({
      communityId,
      productId,
      productType,
      threadIdInfo,
      learnerId,
      countryId,
      isFAQ: true,
      sessionId,
    });

    return {
      threadId: threadIdInfo,
      assistantResponse,
    };
  }

  logger.error(
    `[FAIQ][faq-run]|status=${status}|last_error=${run.last_error}|threadId=${run.thread_id}`
  );
  const error = new Error(
    'Assistant response not completed. Status: ' + status
  );
  error.errorCode = 500;
  throw error;
};

const fetchSalesAgentAndAskAQuestion = async ({
  messageContent,
  threadId,
  communityId,
  productType,
  productId,
  learnerId,
  response,
  countryId,
  isFAQ,
  sessionId,
}) => {
  try {
    let threadIdInfo = threadId;

    const [community] = await Promise.all([
      CommunityModel.findOne({
        _id: new ObjectId(communityId),
      }),
    ]);

    const { aiAssistantInfo } = community;

    if (!aiAssistantInfo) {
      return response
        .status(400)
        .json({ error: 'Assistant info not found' });
    }
    // front end will also check this condition
    const {
      isEnabledForChallenges,
      isEnabledForEvents,
      isEnabledForCourse,
      isEnabledFor1on1,
      assistantId,
    } = aiAssistantInfo;

    response.setHeader('Content-Type', 'text/plain');

    // Define a mapping for product types and their corresponding flags and error messages
    const productTypeConfig = {
      CHALLENGE: {
        isEnabled: isEnabledForChallenges,
        errorMessage: 'Assistant is not enabled for challenges',
      },
      EVENT: {
        isEnabled: isEnabledForEvents,
        errorMessage: 'Assistant is not enabled for events',
      },
      COURSE: {
        isEnabled: isEnabledForCourse,
        errorMessage: 'Assistant is not enabled for courses',
      },
      '1ON1': {
        isEnabled: isEnabledFor1on1,
        errorMessage: 'Assistant is not enabled for 1-on-1',
      },
    };

    // Check if the product type exists in the mapping
    const productConfig = productTypeConfig[productType];

    if (!productConfig) {
      return response
        .status(403)
        .json({ error: 'No assistant found for the type of product' });
    }

    if (threadIdInfo) {
      try {
        // fetch all the runs
        const runs = await openai.beta.threads.runs.list(threadIdInfo);

        if (runs.data.length > 0) {
          await Promise.all(
            runs.data?.map(async (run) => {
              console.log('stopping the run');
              if (run.status === 'in_progress' || run.status === 'queued')
                await openai.beta.threads.runs.cancel(run.id, {
                  thread_id: threadId,
                });
            })
          );
        }
      } catch (error) {
        logger.log('no thread running for the user');
      }
    }
    // Validate if the assistant is enabled for the given product type
    if (!productConfig.isEnabled) {
      return response
        .status(403)
        .json({ error: productConfig.errorMessage });
    }

    // Retrieve the assistant
    const assistant = await openai.beta.assistants.retrieve(assistantId);

    // Create a new thread
    if (!threadId) {
      const thread = await openai.beta.threads.create();
      threadIdInfo = thread.id;
    }

    response.write('threadId:' + threadIdInfo);
    await openai.beta.threads.messages.create(threadIdInfo, {
      role: 'user',
      content: messageContent,
    });

    const run = openai.beta.threads.runs.stream(threadIdInfo, {
      assistant_id: assistant.id,
    });

    run
      .on('textCreated', () => {
        // response.write(threadIdInfo + '\n');
      })
      .on('textDelta', (textDelta) => {
        response.write(textDelta.value); // Stream the token to the frontend
      })
      .on('end', async () => {
        try {
          await updateOrCreateConversation({
            communityId,
            productId,
            productType,
            threadIdInfo,
            learnerId,
            countryId,
            isFAQ,
            sessionId,
          });
        } catch (error) {
          logger.error(
            '[Faiq][Ask]Error while updating the assistant conversation',
            error,
            error.stack
          );
        }
        // TODO: store the conversation on S3
        response.end(); // Close the response once the stream is finished
      })
      .on('error', (error) => {
        console.error('Streaming error:', error);
        response.status(500).end('Error during stream');
      })
      .on('close', () => {
        console.error('Streaming closed');
      });
  } catch (error) {
    // stop the stream and send the error message
    response.end();
    response.status(500).json({ error: error.message });
  }
};

/**
 * Description
 * @param {any} {question}
 * @returns {Promise<any>}
 */
const askAQuestionToProductFeedbackBot = async ({ question }) => {
  const factualGuidancePrefix =
    "Please provide only factual information based on what you know with certainty. If you are unsure about any aspect or don't have enough information, please acknowledge that and advise the user to contact the technical team for accurate information. and please try to provide all the admin js links (if you recieve them) \n\nQuestion: ";
  const questionWithGuidance = factualGuidancePrefix + question;
  const assistant = await openai.beta.assistants.retrieve(
    OPEN_AI_PRODUCT_FEEDBACK_ASSISTANT_ID
  );

  // const END_TEMPLATE =
  //   '---------\n make sure that you provide all the admin js link you receive in the response';
  const thread = await openai.beta.threads.create();
  await openai.beta.threads.messages.create(thread.id, {
    role: 'user',
    content: questionWithGuidance,
  });
  // Create and poll run
  let run = await openai.beta.threads.runs.createAndPoll(thread.id, {
    assistant_id: assistant.id,
  });

  const handleRunStatus = async () => {
    // Check if the run is completed
    if (run.status === 'completed') {
      const messages = await openai.beta.threads.messages.list(thread.id);
      return messages.data;
    }
    if (run.status === 'requires_action') {
      // eslint-disable-next-line no-return-await, no-use-before-define
      return await handleRequiresAction();
    }
    logger.error('Run did not complete:', run);
  };

  const handleRequiresAction = async () => {
    // Check if there are tools that require outputs
    if (
      run.required_action &&
      run.required_action.submit_tool_outputs &&
      run.required_action.submit_tool_outputs.tool_calls
    ) {
      // Loop through each tool in the required action section
      const toolOutputs = await Promise.all(
        run.required_action.submit_tool_outputs.tool_calls.map(
          async (tool) => {
            if (
              tool.function.name === 'fetch_community_info_' ||
              tool.function.name === 'fetch_community_info'
            ) {
              const communityInfo = await getCommunityInfo(tool);
              return communityInfo;
            }
            if (tool.function.name === 'fetch_user_info') {
              const userInfo = await getUserInfo(tool);

              return userInfo;
            }
            if (tool.function.name === 'fetch_product_info') {
              const productInfo = await getProductInfo(tool);
              return productInfo;
            }
            if (tool.function.name === 'fetch_user_notifications') {
              const userNotifications = await getUserNotifications(tool);
              return userNotifications;
            }
            if (tool.function.name === 'fetch_payment_information') {
              const paymentInformation = await getPaymentInformation(tool);
              return paymentInformation;
            }
            if (tool.function.name === 'fetch_subscription_info') {
              const subscriptionInformation =
                await getSubscriptionInformation(tool);
              return subscriptionInformation;
            }
          }
        )
      );

      // Submit all tool outputs at once after collecting them in a list
      if (toolOutputs.length > 0) {
        run = await openai.beta.threads.runs.submitToolOutputsAndPoll(
          run.id,
          { thread_id: thread.id, tool_outputs: toolOutputs }
        );
      }

      // Check status after submitting tool outputs
      return handleRunStatus();
    }
  };

  return handleRunStatus();
};

const ingestDocDataToProductFeedbackBot = async ({ data }) => {
  const assistant = await openai.beta.assistants.retrieve(
    OPEN_AI_PRODUCT_FEEDBACK_ASSISTANT_ID
  );

  const existingFileIds = [
    'file-6wNzFaRKHWnomgJTwigHXn',
    'file-9P6gCTtKEMX1CtpkqaCAds',
    'file-F5EU3H2DgorfRJ6VEnGH8w',
    'file-9yq9awvrugDAJF7KQZSmrG',
    'file-YFqjGpqp8RFptYoWDV2JfW',
  ];

  const vectorStoreId =
    assistant?.tool_resources?.file_search?.vector_store_ids?.[0];

  if (!vectorStoreId) {
    return;
  }

  const productFeedbackBotTextFile = path.join(
    '/tmp',
    'temp_helpArticles.txt'
  );

  fs.writeFileSync(productFeedbackBotTextFile, data);

  const files = [
    { path: productFeedbackBotTextFile, purpose: 'assistants' },
  ];

  const newFileIds = [];

  for await (const file of files) {
    const uploadedFile = await openai.files.create({
      file: fs.createReadStream(file.path),
      purpose: file.purpose,
    });
    newFileIds.push(uploadedFile.id);
  }

  // retrieve all the existing files and delete them
  const vectorStoreFiles = await openai.vectorStores.files.list(
    vectorStoreId
  );

  // deleting the files that are already connected
  for await (const file of vectorStoreFiles.data) {
    try {
      if (!existingFileIds.includes(file.id)) {
        await openai.vectorStores.files.delete(file.id, {
          vector_store_id: vectorStoreId,
        });
        await openai.files.delete(file.id);
      }
    } catch (error) {
      logger.info(`error deleting file ${file.id}`);
    }
  }

  //  Upload new files to the vector store
  await openai.vectorStores.fileBatches.create(vectorStoreId, {
    file_ids: newFileIds,
  });

  // delete the temp file
  fs.unlinkSync(productFeedbackBotTextFile);

  return true;
};

const moderateText = async (text) => {
  try {
    const moderation = await openai.moderations.create({
      model: 'omni-moderation-latest',
      input: text,
    });

    return moderation;
  } catch (error) {
    return null;
  }
};

const createEmbedding = async (text, model = 'text-embedding-3-small') => {
  try {
    const response = await openai.embeddings.create({
      model,
      input: text,
      encoding_format: 'float',
    });

    return response.data[0].embedding;
  } catch (error) {
    logger.error('Error creating embedding:', error);
    throw error;
  }
};

const createResponseCompletion = async (config) => {
  try {
    const response = await openai.responses.create({
      model: 'gpt-4.1-mini',
      ...config,
    });
    return response;
  } catch (error) {
    logger.error('Error creating response completion:', error);
    throw error;
  }
};

module.exports = {
  createChatComletion,
  createResponseCompletion,
  fetchAssistantAndAskAQuestion,
  fetchSalesAgentAndAskAQuestion,
  fetchSalesAgentAndGetFaqs,
  askAQuestionToProductFeedbackBot,
  ingestDocDataToProductFeedbackBot,
  moderateText,
  createEmbedding,
};
