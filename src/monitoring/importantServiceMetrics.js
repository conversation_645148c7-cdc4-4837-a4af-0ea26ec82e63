const {
  IMPORTANT_SERVICE_METRICS,
  IMPORTANT_SERVICE_DIMENSIONS,
} = require('./constants');
const { getMetric } = require('./metricRegistry');
const { env } = require('../config');

exports.recordLatency = ({
  source,
  serviceName,
  method,
  status,
  startTime,
}) => {
  const durationMs = Date.now() - startTime;

  const histogram = getMetric(
    IMPORTANT_SERVICE_METRICS.REQUEST_LATENCY_HISTOGRAM
  );

  histogram.observe(
    {
      [IMPORTANT_SERVICE_DIMENSIONS.SOURCE]: source,
      [IMPORTANT_SERVICE_DIMENSIONS.SERVICE_NAME]: serviceName,
      [IMPORTANT_SERVICE_DIMENSIONS.METHOD]: method,
      [IMPORTANT_SERVICE_DIMENSIONS.STATUS]: status,
      [IMPORTANT_SERVICE_DIMENSIONS.ENV]: env,
    },
    durationMs
  );
};

exports.incrementProcessedCounter = ({
  source,
  serviceName,
  method,
  status,
}) => {
  const metric = getMetric(IMPORTANT_SERVICE_METRICS.PROCESSED_COUNTER);

  metric
    .labels({
      [IMPORTANT_SERVICE_DIMENSIONS.SOURCE]: source,
      [IMPORTANT_SERVICE_DIMENSIONS.SERVICE_NAME]: serviceName,
      [IMPORTANT_SERVICE_DIMENSIONS.METHOD]: method,
      [IMPORTANT_SERVICE_DIMENSIONS.STATUS]: status,
      [IMPORTANT_SERVICE_DIMENSIONS.ENV]: env,
    })
    .inc();
};
