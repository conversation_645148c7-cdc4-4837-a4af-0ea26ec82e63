const axios = require('axios');
const crypto = require('crypto');
const logger = require('../services/logger.service');
const { getConfigByTypeFromCache } = require('../services/config.service');
const { CONFIG_TYPES } = require('../constants/common');

// Utility: SHA256 Hash Email (Facebook requires hashing for user_data)
function hashSHA256(value) {
  return crypto
    .createHash('sha256')
    .update(value.toLowerCase().trim())
    .digest('hex');
}

function generateUserData({ learner = {}, trackingData = {} }) {
  const { learnerId, email, firstName, lastName, phoneNumber } = learner;

  const userData = {
    em: email ? hashSHA256(email) : undefined,
    ph: phoneNumber ? hashSHA256(phoneNumber) : undefined,
    fn: firstName ? hashSHA256(firstName) : undefined,
    ln: lastName ? hashSHA256(lastName) : undefined,
    external_id: learnerId,
    country: trackingData.country
      ? hashSHA256(trackingData.country)
      : undefined,
  };

  const trackingDataKeyToFbKey = {
    ip: 'client_ip_address',
    userAgent: 'client_user_agent',
    _fbc: 'fbc',
    _fbp: 'fbp',
  };

  // set userData.client_ip_address, userData.client_user_agent, etc. if exists in trackingData.ip, trackingData.userAgent, etc.
  Object.keys(trackingDataKeyToFbKey).forEach((key) => {
    if (trackingData[key]) {
      userData[trackingDataKeyToFbKey[key]] = trackingData[key];
    }
  });

  return userData;
}

async function trackFacebookEvent({
  learner,
  eventName,
  eventId,
  trackingData = {},
  customData = {},
}) {
  try {
    const { envVarData = null } = await getConfigByTypeFromCache(
      CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
    );
    const pixelId = envVarData?.NAS_FB_PIXEL_ID;
    const accessToken = envVarData?.NAS_FB_ACCESS_TOKEN;

    if (!pixelId || !accessToken) {
      logger.error('FB Pixel ID or Access Token not found', { eventName });
      return;
    }

    const path = `https://graph.facebook.com/v23.0/${pixelId}/events`;

    const payload = {
      data: [
        {
          event_name: eventName,
          event_time: Math.floor(Date.now() / 1000),
          event_id: eventId,
          user_data: generateUserData({ learner, trackingData }),
          custom_data: customData,
          action_source: 'system_generated',
        },
      ],
      access_token: accessToken,
    };

    const res = await axios.post(path, payload);
    logger.info(
      `FB Pixel event sent. eventName=${eventName}. Payload data=`,
      { payloadData: payload.data, responseData: res.data }
    );
    return res;
  } catch (err) {
    logger.error(
      `FB Pixel event failed. eventName=${eventName}. `,
      err.response?.data || err.message
    );
    return err;
  }
}

module.exports = {
  trackFacebookEvent,
};
