const { ObjectId } = require('mongoose').Types;

const PrimaryMongooseConnection = require('../rpc/primaryMongooseConnection');
const logger = require('../services/logger.service');

exports.toObjectId = (id) => {
  if (id instanceof ObjectId) {
    return id;
  }

  return new ObjectId(id);
};

exports.lookupAndUnwind = (
  from,
  localField,
  foreignField,
  as,
  preserveNullAndEmptyArrays = true
) => {
  const lookupQuery = [
    {
      $lookup: {
        from,
        localField,
        foreignField,
        as,
      },
    },
    {
      $unwind: {
        path: `$${as}`,
        preserveNullAndEmptyArrays,
      },
    },
  ];

  return lookupQuery;
};

exports.conditionalLookupAndUnwind = (
  from,
  fieldAssignment,
  pipeline,
  as,
  preserveNullAndEmptyArrays = true
) => {
  const lookupQuery = [
    {
      $lookup: {
        from,
        let: fieldAssignment,
        pipeline,
        as,
      },
    },
    {
      $unwind: {
        path: `$${as}`,
        preserveNullAndEmptyArrays,
      },
    },
  ];

  return lookupQuery;
};

// Utility function to check if the error is a write conflict
function isWriteConflictError(error) {
  return error && error.code === 112; // 112 is the MongoDB WriteConflict error code
}

exports.isWriteConflictError = isWriteConflictError;

// Retry wrapper for Mongoose transactions
exports.withTransactionRetry = async (
  transactionFn,
  retries = 3,
  ignoreErrorCodeList = []
) => {
  let attempts = 0;

  /* eslint-disable no-await-in-loop */
  while (attempts < retries) {
    const primaryMongooseConnection =
      await PrimaryMongooseConnection.connect();

    const session = await primaryMongooseConnection.startSession();
    session.startTransaction();
    try {
      // Execute the transactional function
      const result = await transactionFn(session);
      // Commit the transaction
      await session.commitTransaction();
      return result;
    } catch (error) {
      await session.abortTransaction();
      // If it's a write conflict, retry the transaction
      if (isWriteConflictError(error)) {
        attempts++;
        logger.warn(
          `Write conflict detected. Retrying transaction... Attempt ${attempts} of ${retries}`
        );
      } else if (ignoreErrorCodeList.includes(error?.code)) {
        logger.warn(`Ignoring error code: ${error?.code}`);
        return;
      } else {
        // If it's not a write conflict, rethrow the error
        throw error;
      }
      if (attempts >= retries) {
        throw new Error('Transaction failed after maximum retries');
      }
    } finally {
      await session.endSession();
    }
  }
  /* eslint-enable no-await-in-loop */
};

exports.withTransaction = async (transactionFn) => {
  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();
  const session = await primaryMongooseConnection.startSession();

  session.startTransaction();

  try {
    const result = await transactionFn(session);
    await session.commitTransaction();
    return result;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};
