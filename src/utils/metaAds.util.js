const { ObjectId } = require('mongoose').Types;
const { META_ADS_STATUS } = require('../services/magicAudience/constants');
const MetaAdsCampaigns = require('../models/magicAudience/metaAdsCampaigns.model');

/**
 * Check if there is an active campaign for a community or specific product
 * Throws an error if an active campaign is found
 * @param {string} communityId - Community ObjectId
 * @param {string} productId - Optional product ObjectId
 * @returns {boolean} - Returns true if no active campaign found
 */
exports.checkActiveCampaignForSlugChange = async (
  communityId,
  productId
) => {
  const filter = {
    communityObjectId: new ObjectId(communityId),
    status: { $in: [META_ADS_STATUS.ACTIVE, META_ADS_STATUS.PAUSED] },
  };
  if (productId) {
    filter.entityObjectId = new ObjectId(productId);
  }
  const activeCampaign = await MetaAdsCampaigns.findOne(filter).lean();
  // if there is an active campaign, throw an error that the link cannot be changed because there is an active campaign
  if (activeCampaign) {
    throw new Error(
      'Cannot change the link because there is an active campaign for this product.'
    );
  }

  return true;
};
