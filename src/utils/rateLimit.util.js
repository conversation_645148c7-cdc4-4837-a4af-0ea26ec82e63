const rateLimit = require('express-rate-limit');
const httpContext = require('express-http-context');
const OperationCounter = require('../models/platform/operationCounter.model');

const { CONFIG_TYPES } = require('../constants/common');
const { getConfigByTypeFromCache } = require('../services/config.service');
const { RateLimitError } = require('./error.util');

const RATE_LIMIT_MESSAGE = 'Too many requests';

const logger = require('../services/logger.service');

const MODULE_TYPE = {
  GET_INSPIRED: 'GET_INSPIRED',
  PUBLIC: 'PUBLIC',
  CHAT: 'CHAT',
  COMMUNITY_ONBOARDING: 'COMMUNITY_ONBOARDING',
  COMMUNITY_UPDATE: 'COMMUNITY_UPDATE',
  COMMUNITY_SIGNUP: 'COMMUNITY_SIGNUP',
  EVENT_DUPLICATION: 'EVENT_DUPLICATION',
  MAGIC_LEADS: 'MAGIC_LEADS',
};

const OPERATION = {
  CREATE_EVENT_DUPLICATES: 'create_event_duplicates',
  CREATE_COMMUNITY: 'create_community',
};

const IDENTIFIER_TYPE = {
  IP: (req) => req.ip,
  COMMUNITY_ID: (req) => req.params.communityId,
  USER_ID: (req) => req.user._id.toString(),
};

const rateLimiters = {};

async function getRateLimitConfig({ module, key }) {
  const config = await getConfigByTypeFromCache(
    CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
  );
  const configName = `${module.toUpperCase()}_RATE_LIMIT`;
  const rateLimitConfig = config?.envVarData?.[configName];

  if (!rateLimitConfig) {
    return {
      windowMs: 1000,
      max: 5,
      message: RATE_LIMIT_MESSAGE,
    };
  }

  const selectedRateLimitConfig =
    rateLimitConfig[key] ?? rateLimitConfig['default'];

  if (!selectedRateLimitConfig) {
    return {
      windowMs: 1000,
      max: 5,
      message: RATE_LIMIT_MESSAGE,
    };
  }

  if (selectedRateLimitConfig && !selectedRateLimitConfig.message) {
    selectedRateLimitConfig.message = RATE_LIMIT_MESSAGE;
  }

  return selectedRateLimitConfig;
}

async function getRateLimitForEndpoint({ module, method, endpoint }) {
  const key = `[${method.toUpperCase()}]${endpoint}`;
  if (!rateLimiters[key]) {
    const rateLimitConfig = await getRateLimitConfig({
      module,
      key,
    });
    if (!rateLimitConfig) {
      return;
    }
    rateLimiters[key] = rateLimit(rateLimitConfig);
  }
  return rateLimiters[key];
}

const rateLimitMiddleware =
  ({ module }) =>
  async (req, res, next) => {
    const endpoint = httpContext.get('apiRoute');
    const method = req.method;
    try {
      const rateLimiter = await getRateLimitForEndpoint({
        module,
        method,
        endpoint,
      });

      if (rateLimiter) {
        rateLimiter(req, res, next);
      } else {
        next();
      }
    } catch (error) {
      logger.error('rateLimitMiddleware error', error, error.stack);
      next(error);
    }
  };

const atomicRateLimit = async ({
  req,
  module,
  operation,
  identifierFn = IDENTIFIER_TYPE.IP,
}) => {
  const endpoint = httpContext.get('apiRoute');
  const method = req.method;
  const key = `[${method.toUpperCase()}]${endpoint}`;

  // Get rate limit config
  const rateLimiter = await getRateLimitConfig({
    module,
    key,
  });

  if (!rateLimiter) {
    return false;
  }

  const limit = rateLimiter.max;

  // Atomic update and check
  const operationCounter = await OperationCounter.findOneAndUpdate(
    {
      operation,
      identifier: identifierFn(req),
    },
    {
      $inc: { count: 1 },
      $setOnInsert: { createdAt: new Date() },
    },
    {
      new: true, // Return updated document
      upsert: true, // Create if doesn't exist
    }
  );

  const operationCount = operationCounter.count;
  return operationCount > limit;
};

const mongoRateLimitMiddleware = ({ module, operation, identifierFn }) => {
  return async (req, _, next) => {
    try {
      const isLimitExceeded = await atomicRateLimit({
        req,
        module,
        operation,
        identifierFn,
      });

      if (isLimitExceeded) {
        throw new RateLimitError(RATE_LIMIT_MESSAGE);
      }

      // Continue with route handler
      next();
    } catch (error) {
      logger.error('mongoRateLimitMiddleware error', error, error.stack);
      next(error);
    }
  };
};

module.exports = {
  MODULE_TYPE,
  OPERATION,
  IDENTIFIER_TYPE,
  rateLimitMiddleware,
  mongoRateLimitMiddleware,
};
