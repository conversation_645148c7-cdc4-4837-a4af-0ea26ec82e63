const { Router } = require('express');
const express = require('express');

// routes
const ReferralRouter = require('../api/v1/referral/router');
const courseRoutes = require('./courses');
const getStreamRoutes = require('./getstream');
const {
  communitiesRouter,
  communitiesMobileRouter,
} = require('../communitiesAPI/routers');
const mobileGenericRouter = require('./mobile');

const uploadRouter = require('../api/v1/upload/router');
const mpChatRouter = require('../api/v1/chat/mp/router');
const abandonedCheckoutRouter = require('../api/v1/abandonedCheckouts/router');
const advertisementRouter = require('../api/v1/advertisement/router');
const campaignRouter = require('../api/v1/campaign/router');
const calendarRouter = require('../api/v1/calendar/router');
const communityReferralRouter = require('../api/v1/communityReferral/router');
const magicReachRouter = require('../api/v1/magicReach/router');
const magicLeadsRouter = require('../api/v1/magicLeads/router');
const userRouter = require('../api/v1/user/router');
const membershipRouter = require('../api/v1/membership/router');
const cmpHomeRouter = require('../api/v1/cmpHome/router');
const uiRouter = require('../api/v1/ui/router');
const internalRouter = require('../api/v1/internal/router');
const oneOnOneSessionRouter = require('../api/v1/oneOnOneSessions/router');
const getStartedRouter = require('../api/v1/getStartedSuperCharge/router');
const countriesRouter = require('../api/v1/countries/router');
const imageRouter = require('../api/v1/uploadImage/router');
const videoRouter = require('../api/v1/uploadVideo/router');
const documentRouter = require('../api/v1/uploadDocument/router');
const assetRouter = require('../api/v1/uploadAssets/router');
const notificationRouter = require('../api/v1/notification/router');
const challengeRouter = require('../api/v1/challenge');
const communityLandingPageRouter = require('../api/v1/communityLandingPage/router');
const walletRouter = require('../api/v1/wallet/router');
const tokenRouter = require('../api/v1/token/router');
const subscriptionRouter = require('../api/v1/subscriptions/router');
const affiliateRouter = require('../api/v1/affiliate');
const platformRouter = require('../api/v1/platform/router');
const faiqRouter = require('../api/v1/faiq/router');
const fraudRouter = require('../api/v1/fraud/router');
const redisCacheRouter = require('../api/v1/redisCache/router');
const supportingTicketRouter = require('../api/v1/supportingTicket/router');
const topProductsRouter = require('../api/v1/topProducts/router');
const getInspiredRouter = require('../api/v1/getInspired/router');
const receiptGenerationRouter = require('../api/v1/receipt/router');
const zeroLinkRouter = require('../api/v1/zeroLink/router');
const unsplashRouter = require('../api/v1/unsplash/router');
const magicAudienceRouter = require('../api/v1/magicAudience/router');
const topupRouter = require('../api/v1/topup/router');
const productRouter = require('../api/v1/product/router');
const featurePermissionRouter = require('../api/v1/permissions/router');
const ebanxWhitelistRouter = require('../api/v1/ebanx-whitelist/router');

// services
const { upload } = require('../services/photo_upload.service');

// validators and middleware

const oauthTokenMiddleware = require('../middleware/oauthToken.middleware');
const oauthRefreshTokenMiddleware = require('../middleware/oauthRefreshToken.middleware');
const tokenValidator = require('../validations/token.validation');
const userValidation = require('../validations/user.validation');
const apiKeyValidator = require('../validations/apiKey.validation');
const {
  userUpdateValidator,
} = require('../validations/userUpdate.validation');
const studentProfileTokenValidator = require('../validations/studentProfileToken.validation');
const {
  basicProfileUpdateValidator,
} = require('../validations/basicProfileUpdate.validation');
const {
  contentItemSchema,
  contentIdSchema,
} = require('../validations/userContent.validation');
const { validator } = require('../middleware/validator.middleware');
const {
  uploadCustomImage,
} = require('../middleware/uploadCustomImage.middleware');
const { handlerWrapper } = require('../utils/request.util');
const {
  MODULE_TYPE,
  rateLimitMiddleware,
} = require('../utils/rateLimit.util');

// controllers
const {
  sendgridEventListener,
} = require('../controllers/sendgrid/eventWebhook.controller');
const signUpController = require('../controllers/authentication/signup.controller');
const logInController = require('../controllers/authentication/login.controller');
const forgetPasswordController = require('../controllers/authentication/forget-password.controller');
const {
  resetPasswordAuthenticatedController,
  resetPasswordUnauthenticatedController,
} = require('../controllers/authentication/reset-password.controller');
const userProfileController = require('../controllers/user_profile.controller');
const {
  getUnitOffersByCourseId,
  getFullUnitOfferInfoByUnitId,
} = require('../controllers/get_unit_offers.controller');
const uploadPhoto = require('../controllers/upload_photo.controller');
const updateProfile = require('../controllers/update_profile.controller');
const {
  updateLearnerWalletController,
  updateLearnerWalletWebhookController,
} = require('../controllers/update_learner_wallet_controller');

const {
  sendEmailToRsvpUsers,
  registerForEventV2,
} = require('../communitiesAPI/controllers/web/events');

const uploadFeedbackController = require('../controllers/upload_feedback.controller');
const {
  getFeedbackByOfferId,
  getTrainerFeedbackById,
} = require('../controllers/get_feedback_form.controller');
const {
  getTrainerInfoById,
} = require('../controllers/get_trainer_page.controller');
const {
  getFeedbackAnswerByLearnerSurveyId,
} = require('../controllers/get_feedback_answers.controller');
const {
  getContent,
  editContent,
  addContent,
  deleteContent,
  uploadUserFormData,
  uploadUserTextFormData,
  setPhotoQuery,
} = require('../controllers/user_content.controller');
const {
  getLearnerPublicController,
} = require('../controllers/get_learner_public.controller');
const {
  getWalletController,
} = require('../controllers/referralCode/wallet.controller');
const {
  verifyLearnerJwtTokenController,
  verifyLearnerEmailController,
} = require('../controllers/verify_learner.controller');
const {
  getLearnerPrivateController,
} = require('../controllers/get_learner_private.controller');
const {
  getTrainerCoursesByTrainerId,
} = require('../controllers/get_trainer_courses.controller');
const {
  getStudentCoursesByLearnerId,
} = require('../controllers/get_student_courses.controller');
const {
  getCompletedVideosCountController,
} = require('../controllers/get_completed_videos_count.controller');
const getAssignmentDataController = require('../controllers/assignment/getAssignment.controller');
const submitAssignmentController = require('../controllers/assignment/submitAssignment.controller');
const submitFeedbackController = require('../controllers/assignment/submitFeedback.controller');
const {
  getStarRatingBySurveyId,
} = require('../controllers/feedback/getStarRating.controller');
const {
  createStarRatingBySurveyId,
} = require('../controllers/feedback/submitStarRating.controller');
const getCertificateDataController = require('../controllers/certificate/getCertificate.controller');

const getTokenController = require('../controllers/authentication/get-token.controller');
const updateBasicProfileController = require('../controllers/profile/updateBasicProfile.controller');

const discordLoginController = require('../controllers/authentication/discordLogin.controller');

const {
  getPromoCodeController,
} = require('../controllers/referralCode/getReferralCode.controller');

const { creatorValidator } = require('../validations/creator.validation');
const {
  getCreatorOverviewStatsController,
  getCreatorB2bEnrolmentStatsController,
} = require('../controllers/creator/getCreatorOverviewStats.controller');
const {
  getCreatorCourses,
} = require('../controllers/creator/getCreatorCourses.controller');
const {
  getCreatorRevenueController,
} = require('../controllers/creator/getCreatorRevenue.controller');
const {
  getCreatorStudentCountriesController,
} = require('../controllers/creator/getCreatorStudentCountries.controller');
const {
  getCourseFeedbackListController,
} = require('../controllers/creator/getCourseFeedbackList.controller');
const {
  getCourseDetailedRatingsController,
} = require('../controllers/creator/getCourseDetailedRatings.controller');
const {
  getCourseCompletionRateController,
} = require('../controllers/creator/getCourseCompletionRate.controller');
const {
  getCreatorStudentsController,
} = require('../controllers/creator/getCreatorStudents.controller');
const {
  getCreatorEmailGroupsController,
} = require('../controllers/creator/getCreatorEmailGroups.controller');
const {
  getCreatorEmailGroupByIdController,
} = require('../controllers/creator/getCreatorEmailGroupById.controller');
const {
  sendCreatorEmailController,
} = require('../controllers/creator/sendCreatorEmail.controller');
const {
  sentCreatorEmailsController,
} = require('../controllers/creator/sentCreatorEmails.controller');
const {
  freeTrialSignupController,
} = require('../controllers/onDemandFreeTrial/freeTrialSignup.controller');
const {
  dismissModalController,
} = require('../controllers/onDemandFreeTrial/dismissModal.controller');
const signedupInfoController = require('../controllers/get_signed_up_info.controller');
const imageUrlResponseController = require('../controllers/imageUrlResponse');
const {
  postRoutePreHandlerMiddleware,
} = require('../middleware/request.middleware');

const {
  getNft,
  getNftV2,
  nftCertificateRecordWebHook,
  getNftRecord,
  getNftRecords,
} = require('../controllers/nft/nft.controller');

const {
  memberInfoData,
  registerOauthClient,
  updateUserConsent,
  returnOauthUrl,
  findCommunityInfo,
  accessToken,
  refreshToken,
} = require('../controllers/oauth/oauth.controller');

const { checkUserSignup } = require('../controllers/userCheckController');

const {
  getContractController,
} = require('../controllers/web3Contract/web3Contract.controller');

const {
  generateAllCommunityShortUrls,
  generateSpecificCommunityShortUrls,
  generateSpecificTypeShortUrl,
} = require('../communitiesAPI/controllers/web/shortUrls');

const {
  getLastInteractedCommunityController,
} = require('../controllers/getLastInteracted.controller');

const {
  saveStats,
  getStats,
  getDiscordUserInfo,
} = require('../controllers/socialChatAnalytics.controller');

const {
  communityEventsController,
  communityFolderController,
  whatsappController,
  signupFlowController,
  communityPostsController,
} = require('../communitiesAPI/controllers/web');

const {
  mobileWhatsappController,
} = require('../communitiesAPI/controllers/mobile');

const {
  createDemoCommunity,
} = require('../controllers/productDemo.controller');
const {
  confirmPhoneNumberByUser,
} = require('../controllers/learner/learner.controller');
const {
  communityPostAccessValidator,
} = require('../communitiesAPI/validations/community.validation');
const userValidationWithoutError = require('../validations/userValidationWithoutError.validation');
// const accessTokenValidationWithoutError = require('../validations/tokenNoError.validation');
const optionalAccessTokenValidationWithoutError = require('../validations/optionalToken.validation');
const optOutOfNotifications = require('../controllers/optOutOfNotifications');
const { ACCESS_TOKEN_TYPE } = require('../constants/common');

const router = Router({ mergeParams: true });

router.get(
  '/server-time',
  postRoutePreHandlerMiddleware,
  rateLimitMiddleware({
    module: MODULE_TYPE.PUBLIC,
  }),
  (req, res) => {
    const timestamp = Date.now();
    const datetime = new Date(timestamp).toISOString();
    res.json({ timestamp, datetime });
  }
);

router.post(
  '/photo-upload',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  upload.single('photo'),
  uploadPhoto
);
router.use(express.json());

/* Public routes */
router.get(
  '/community-manager/signup-check',
  postRoutePreHandlerMiddleware,
  checkUserSignup
);
router.post('/sign-up', postRoutePreHandlerMiddleware, signUpController);
router.post('/log-in', postRoutePreHandlerMiddleware, logInController);
router.post(
  '/forget-password',
  postRoutePreHandlerMiddleware,
  forgetPasswordController
);
router.post(
  '/reset-password/:resetPasswordToken',
  postRoutePreHandlerMiddleware,
  resetPasswordUnauthenticatedController
);
router.post(
  '/discord-login',
  postRoutePreHandlerMiddleware,
  discordLoginController
);
router.get(
  '/event/:communityLink/:slug',
  postRoutePreHandlerMiddleware,
  communityEventsController.getEventBySlug
);

router.get(
  '/resource/:communityLink/:slug',
  postRoutePreHandlerMiddleware,
  optionalAccessTokenValidationWithoutError,
  userValidationWithoutError,
  communityFolderController.getResourceBySlug
);

router.get(
  '/posts/:communityLink/:slug',
  postRoutePreHandlerMiddleware,
  optionalAccessTokenValidationWithoutError,
  communityPostAccessValidator,
  communityPostsController.getAnnouncementPostBySlug
);

// Create Product Demo Controller

router.post(
  '/product-demo',
  postRoutePreHandlerMiddleware,
  createDemoCommunity
);

// get-token API is used to refresh access token as well as for social logins (apple, fb and google)
router.post(
  '/get-token',
  postRoutePreHandlerMiddleware,
  getTokenController
);
router.post(
  '/free-trial-signup',
  postRoutePreHandlerMiddleware,
  freeTrialSignupController
);

router.get(
  '/trainer-info',
  postRoutePreHandlerMiddleware,
  apiKeyValidator,
  getTrainerInfoById
);

router.get(
  '/student-profile',
  postRoutePreHandlerMiddleware,
  getLearnerPublicController,
  studentProfileTokenValidator,
  getLearnerPrivateController
);

router.get(
  '/communities-shorturls',
  postRoutePreHandlerMiddleware,
  apiKeyValidator,
  generateAllCommunityShortUrls
);

router.get(
  '/communities-shorturls/:communityId',
  postRoutePreHandlerMiddleware,
  apiKeyValidator,
  generateSpecificCommunityShortUrls
);

router.post(
  '/type-shorturls',
  postRoutePreHandlerMiddleware,
  apiKeyValidator,
  generateSpecificTypeShortUrl
);

// Unprotected signup flow related routes
router
  .route('/check-phone-number')
  .get(
    postRoutePreHandlerMiddleware,
    signupFlowController.phoneNumberCheck
  );

// This is required by whatsapp and configured in the whatsapp dashboard
router
  .route('/whatsapp-webhook')
  .get(
    postRoutePreHandlerMiddleware,
    whatsappController.whatsappWebhookGetListener
  )
  .post(
    postRoutePreHandlerMiddleware,
    whatsappController.whatsappWebhookPostListener
  );

router
  .route('/mobile/whatsapp/:communityCode/membersCount')
  .get(
    postRoutePreHandlerMiddleware,
    mobileWhatsappController.getWhatsappMembersCount
  );

router
  .route('/whatsapp/:communityCode/membersCount')
  .get(
    postRoutePreHandlerMiddleware,
    whatsappController.getWhatsappMembersCount
  );

router
  .route('/mobile/whatsapp/:communityId/status')
  .get(
    postRoutePreHandlerMiddleware,
    mobileWhatsappController.getWhatsappStatus
  );

router
  .route('/whatsapp/:communityId/status')
  .get(
    postRoutePreHandlerMiddleware,
    whatsappController.getWhatsappStatus
  );

router
  .route('/whatsapp-analytics/:communityId')
  .get(
    postRoutePreHandlerMiddleware,
    whatsappController.getWhatsappAnalytics
  );

router.get(
  '/promo-code',
  postRoutePreHandlerMiddleware,
  getPromoCodeController
);
// router.post('/create-new-transaction', createReferralTransactionController);

router.post(
  '/nft-certificate-webhook',
  postRoutePreHandlerMiddleware,
  nftCertificateRecordWebHook
);

// FB Oauth Routes Start
router.get(
  '/oauth/member-info',
  postRoutePreHandlerMiddleware,
  oauthTokenMiddleware,
  memberInfoData
);

router.post(
  '/oauth/register-client',
  postRoutePreHandlerMiddleware,
  apiKeyValidator,
  registerOauthClient
);

router.get(
  '/oauth/authorize',
  postRoutePreHandlerMiddleware,
  returnOauthUrl
);

router.post(
  '/oauth/access-token',
  postRoutePreHandlerMiddleware,
  accessToken
);

router.get(
  '/oauth/refresh-token',
  postRoutePreHandlerMiddleware,
  oauthRefreshTokenMiddleware,
  refreshToken
);
// FB Oauth Routes end

// Verify learner email
router.get(
  '/learner/verify-email',
  postRoutePreHandlerMiddleware,
  apiKeyValidator,
  verifyLearnerEmailController
);

// Update learner wallet address webhook
router.post(
  '/learner/wallet-address/webhook',
  postRoutePreHandlerMiddleware,
  apiKeyValidator,
  updateLearnerWalletWebhookController
);

router.get(
  '/save-bot-stats',
  postRoutePreHandlerMiddleware,
  apiKeyValidator,
  saveStats
);
// router.get('/bot-stats', getStats);

// remind all users who have RSVP'd
router
  .route('/rsvp-reminder')
  .post(
    postRoutePreHandlerMiddleware,
    apiKeyValidator,
    sendEmailToRsvpUsers
  );
router
  .route('/sign-up-for-event')
  .post(
    postRoutePreHandlerMiddleware,
    apiKeyValidator,
    registerForEventV2
  );

router.post(
  '/sendgrid-event-webhook',
  postRoutePreHandlerMiddleware,
  handlerWrapper({
    handler: sendgridEventListener,
  })
);

/***
 *
 * Private Routes
 *
 */
// Signup
router.put(
  '/learner',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  signupFlowController.updateLearnerPhoneNumber
);

router.post(
  '/update-basic-profile',
  postRoutePreHandlerMiddleware,
  tokenValidator([ACCESS_TOKEN_TYPE.EMAIL], [ACCESS_TOKEN_TYPE.EMAIL]),
  basicProfileUpdateValidator,
  updateBasicProfileController
);

// Ouath User consent
router.get(
  '/event/private/:communityLink/:slug',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  communityEventsController.getEventBySlug
);

router.post(
  '/oauth/user-consent',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  updateUserConsent
);

router.get(
  '/oauth/community-info',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  findCommunityInfo
);

// Bot Stats Route

router.get(
  '/bot-stats',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  getStats
);

router.post(
  '/discord-user-info',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  getDiscordUserInfo
);

// Course routes
router.use('/', courseRoutes);

router.post(
  '/auth/reset-password',
  postRoutePreHandlerMiddleware,
  tokenValidator([ACCESS_TOKEN_TYPE.EMAIL], [ACCESS_TOKEN_TYPE.EMAIL]),
  resetPasswordAuthenticatedController
);

router.get(
  '/trainer-page',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  getTrainerInfoById
);

router.get(
  '/trainer-courses',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  getTrainerCoursesByTrainerId
);

router.get(
  '/student-courses',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  getStudentCoursesByLearnerId
);

router.put(
  '/confirm-phoneNumber',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  confirmPhoneNumberByUser
);
router.put(
  '/update-profile',
  postRoutePreHandlerMiddleware,
  rateLimitMiddleware({
    module: 'ACCOUNT',
  }),
  tokenValidator(),
  userValidation,
  userUpdateValidator,
  updateProfile
);

router.put(
  '/wallet-address',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  updateLearnerWalletController
);

router.get(
  '/unit-offers',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  getUnitOffersByCourseId
);

router.get(
  '/user-profile',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  userProfileController
);

router.get(
  '/user-profile/contents',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  getContent
);

router
  .route('/user-profile/contents/:contentType')
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    uploadUserFormData,
    uploadUserTextFormData,
    validator(contentItemSchema),
    editContent
  )
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    setPhotoQuery,
    uploadUserFormData,
    validator(contentItemSchema),
    addContent
  );

router.patch(
  '/user-profile/contents/:contentType',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  validator(contentIdSchema),
  deleteContent
);

router.get(
  '/opt-out-notifications',
  postRoutePreHandlerMiddleware,
  optOutOfNotifications
);
router.get(
  '/unit-offer-full-info',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  getFullUnitOfferInfoByUnitId
);

// feedback routes
router.post(
  '/submit-feedback',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  uploadFeedbackController
);
router.get(
  '/feedback',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  getFeedbackByOfferId
);
router.get(
  '/feedback-answers',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  getFeedbackAnswerByLearnerSurveyId
);
router.get(
  '/trainer-feedback/:id',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  getTrainerFeedbackById
);

// wallet routes
router.get(
  '/wallet',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  getWalletController
);

// upload image
router.post(
  '/uploadImage',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  uploadCustomImage.single('image'),
  imageUrlResponseController.imageUrlResponseController
);

// assignment routes
router.get(
  '/assignment',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  getAssignmentDataController
);
router.post(
  '/submit-assignment',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  submitAssignmentController
);
router.post(
  '/submit-assignment-feedback',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  submitFeedbackController
);

// ratings route
router.get(
  '/rating',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  getStarRatingBySurveyId
);
router.post(
  '/rating',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  createStarRatingBySurveyId
);

// certificate route
router.get(
  '/certificate',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  getCertificateDataController
);

// creator-portal routes
router.get(
  '/creator-overview-stats',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  creatorValidator,
  getCreatorOverviewStatsController
);
router.get(
  '/creator-b2b-stats',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  creatorValidator,
  getCreatorB2bEnrolmentStatsController
);

router.get(
  '/creator-courses',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  creatorValidator,
  getCreatorCourses
);
router.get(
  '/creator-analytics-revenue',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  creatorValidator,
  getCreatorRevenueController
);
router.get(
  '/creator-student-countries',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  creatorValidator,
  getCreatorStudentCountriesController
);
router.get(
  '/course-feedback-list',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  creatorValidator,
  getCourseFeedbackListController
);
router.get(
  '/course-detailed-ratings',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  creatorValidator,
  getCourseDetailedRatingsController
);
router.get(
  '/completion-rate',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  creatorValidator,
  getCourseCompletionRateController
);
router.get(
  '/creator-student-list',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  creatorValidator,
  getCreatorStudentsController
);
router.get(
  '/creator-email-groups',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  creatorValidator,
  getCreatorEmailGroupsController
);
router.get(
  '/creator-email-group/:groupId',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  creatorValidator,
  getCreatorEmailGroupByIdController
);
router.post(
  '/creator-send-email',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  creatorValidator,
  sendCreatorEmailController
);
router.get(
  '/creator-sent-emails',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  creatorValidator,
  sentCreatorEmailsController
);

// getstream comment routes
router.use('/', getStreamRoutes);

// ondemand free-trial routes
router.patch(
  '/dismiss-modal',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  dismissModalController
);

// get signed up user info
router.get(
  '/signed-up-info',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  signedupInfoController
);

// added for testing purposes
// router.post('/create-feedback', createFeedbackFormController);

// NFT Certificate Single Record
router.get(
  '/nft-record',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  getNftRecord
);

// NFT Certificate Records
router.get(
  '/nft-records',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  getNftRecords
);

router.post(
  '/nft-certificate',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  getNft
);

router.post(
  '/nft-certificate-air-drop',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  getNftV2
);

router.use('/referral', ReferralRouter);
router.use('/members/affiliates', affiliateRouter.mpRouter);

// Communities API
router.use('/communities', communitiesRouter);

// Communties API for mobile
router.use('/mobile/communities', communitiesMobileRouter);

// Generic mobile APIs
router.use('/mobile', mobileGenericRouter);

router.use('/redisCache', redisCacheRouter);

router.use('/chats', mpChatRouter);

router.use('/supporting-tickets', supportingTicketRouter);

router.use('/uploads', uploadRouter);

router.use('/unsplash', unsplashRouter);

// Web3 Contract
router.get(
  '/web3-contract',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  getContractController
);

// Verify jwt token webhook for metaphi
router.post(
  '/learner/verify-jwt',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  verifyLearnerJwtTokenController
);

router.get(
  '/completed-videos-count',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  getCompletedVideosCountController
);

router.get(
  '/last-interacted-community',
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  getLastInteractedCommunityController
);

abandonedCheckoutRouter.setupRouter(router);
advertisementRouter.setupRouter(router);
campaignRouter.setupRouter(router);
calendarRouter.setupRouter(router);
communityReferralRouter.setupRouter(router);
magicReachRouter.setupRouter(router);
magicLeadsRouter.setupRouter(router);
userRouter.setupRouter(router);
membershipRouter.setupRouter(router);
cmpHomeRouter.setupRouter(router);
uiRouter.setupRouter(router);
internalRouter.setupRouter(router);
getStartedRouter.setupRouter(router);
countriesRouter.setupRouter(router);
imageRouter.setupRouter(router);
documentRouter.setupRouter(router);
assetRouter.setupRouter(router);
notificationRouter.setupRouter(router);
challengeRouter.setupRouter(router);
oneOnOneSessionRouter.setupRouter(router);
videoRouter.setupRouter(router);
communityLandingPageRouter.setupRouter(router);
walletRouter.setupRouter(router);
tokenRouter.setupRouter(router);
subscriptionRouter.setupRouter(router);
platformRouter.setupRouter(router);
faiqRouter.setupRouter(router);
fraudRouter.setupRouter(router);
topProductsRouter.setupRouter(router);
getInspiredRouter.setupRouter(router);
receiptGenerationRouter.setupRouter(router);
zeroLinkRouter.setupRouter(router);
magicAudienceRouter.setupRouter(router);
topupRouter.setupRouter(router);
productRouter.setupRouter(router);
featurePermissionRouter.setupRouter(router);
ebanxWhitelistRouter.setupRouter(router);

module.exports = router;
