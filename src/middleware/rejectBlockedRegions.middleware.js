const BlockedRegions = require('../models/fraud/blockedRegions.model');
const { BLOCKED_REGION_TYPES } = require('../constants/blockedRegions');
const logger = require('../services/logger.service');
const { ToUserError } = require('../utils/error.util');
const { GENERIC_ERROR } = require('../constants/errorCode');

const rejectBlockedRegions = async (req, res, next) => {
  try {
    const orConditions = [];

    // Check timezone from request body
    const timezoneId = req.body?.community?.timezone?.toLowerCase();
    if (timezoneId) {
      orConditions.push({
        type: BLOCKED_REGION_TYPES.TIMEZONE,
        value: timezoneId,
      });
    }

    // Check country from userCountry middleware
    const countryName = req.userCountry?.name?.toLowerCase();
    if (countryName) {
      orConditions.push({
        type: BLOCKED_REGION_TYPES.COUNTRY,
        value: countryName,
      });
    }

    if (!orConditions.length) {
      return next();
    }

    const blockedRegion = await BlockedRegions.findOne({
      $or: orConditions,
    }).lean();

    if (blockedRegion) {
      logger.info('Rejected request from blocked region', {
        blockedRegion,
        userId: req.user?._id,
        userCountry: req.userCountry,
      });

      const err = new ToUserError(
        'We currently do not support services in your region.',
        GENERIC_ERROR.BLOCKED_REGION
      );
      return next(err);
    }

    next();
  } catch (err) {
    logger.error('Error in blocked regions check:', err);
    next(err);
  }
};

module.exports = rejectBlockedRegions;
