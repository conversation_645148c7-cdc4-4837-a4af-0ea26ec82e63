const {
  getCountryFromIP,
} = require('@services/countryFromIP/countryFromIP.service');
const {
  getCountryInfoByCodeFromDBorCache,
} = require('@services/countryInfoMapping/countryInfoMapping.service');
const { getUserIP } = require('@utils/headers.util');
const logger = require('@services/logger.service');

/**
 * Middleware to detect user's country from IP using existing countryFromIP service
 * Adds country information to request object for currency mapping
 */
const ipCountryDetectionMiddleware = async (req, res, next) => {
  try {
    // Get user IP address
    const userIP = getUserIP(req);

    // Use existing service that handles CloudFront + API fallback
    const countryName = await getCountryFromIP({ ip: userIP });

    if (countryName) {
      // Get country code from country name
      const countryInfo = await getCountryInfoByCodeFromDBorCache(
        null,
        countryName
      );
      const countryCode = countryInfo?.countryCode;

      if (countryCode) {
        req.userCountry = {
          code: countryCode.toUpperCase(),
          name: countryName,
          detected: true,
          source: 'countryFromIP-service',
        };

        logger.debug('Country detected from IP', {
          userId: req.user?._id,
          countryCode: countryCode.toUpperCase(),
          countryName,
          ip: userIP,
          source: 'countryFromIP-service',
        });
      } else {
        // Country name found but no code mapping
        req.userCountry = {
          code: 'US',
          name: countryName,
          detected: false,
          source: 'no-code-mapping',
        };

        logger.warn('Country name found but no code mapping', {
          userId: req.user?._id,
          countryName,
          ip: userIP,
        });
      }
    } else {
      // No country detected
      req.userCountry = {
        code: 'US',
        name: 'Unknown',
        detected: false,
        source: 'fallback',
      };

      logger.warn('No country detected from IP, using fallback', {
        userId: req.user?._id,
        ip: userIP,
      });
    }

    next();
  } catch (error) {
    logger.error('IP country detection middleware error', {
      error: error.message,
      stack: error.stack,
      userId: req.user?._id,
      ip: req.ip,
    });

    // Set fallback on error and continue
    req.userCountry = {
      code: 'US',
      name: 'Unknown',
      detected: false,
      source: 'error-fallback',
    };

    next();
  }
};

module.exports = ipCountryDetectionMiddleware;
