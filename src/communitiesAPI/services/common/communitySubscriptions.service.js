const { ObjectId } = require('mongoose').Types;

// models
const jwt = require('jsonwebtoken');
const axios = require('../../../clients/axios.client');
const CommunitySubscription = require('../../models/communitySubscriptions.model');
const Community = require('../../models/community.model');
const CommunityRoles = require('../../models/communityRole.model');
const PurchaseTransactionModel = require('../../models/communityPurchaseTransactions.model');
const UserModel = require('../../../models/users.model');
const Learner = require('../../../models/learners.model');
const membersLimitNotificationService = require('../../../services/common/membersLimitNotification.service');

const {
  JWT_SECRET_KEY,
  MAIN_PAYMENT_BACKEND_URL,
  REROUTE_MEMBER_LINK,
} = require('../../../config');
const {
  communityEnrolmentStatuses,
  INTERNAL_EMAIL_DOMAINS,
  COMMUNITY_MEMBER_TYPES,
  DEFAULT_COMMUNITY_HOST_PROFILE_IMAGE,
  DEFAULT_COMMUNITY_PROFILE_IMAGE,
  DEFAULT_CURRENCY,
  CONFIG_TYPES,
  PAYMENT_PROVIDER,
  PURCHASE_TYPE,
  MEMBERSHIP_ACTION_EVENT_TYPES,
  BATCH_METADATA_MODEL_TYPE,
  VERIFY_PAYMENT_ACCESS_TYPE,
} = require('../../../constants/common');
const { aclRoles, PAYMENT_STATUSES } = require('../../constants');
const {
  endsWithItemsInStringList,
} = require('../../../utils/string_handling');

const {
  CURRENCY_WITH_NON_DECIMAL_POINTS,
} = require('../../../constants/common');
const { CENTS_PER_DOLLAR } = require('../../constants');

// services
const { sendEmail } = require('../../../services/notification/index');
const logger = require('../../../services/logger.service');

const CountryCurrencyMappingModel = require('../../../models/countryInfoMapping.model');
const PaymentBackendRpc = require('../../../rpc/paymentBackend');
const AuthServiceRpc = require('../../../rpc/authService.rpc');
const membershipService = require('../../../services/membership');
const PaymentProviderUtils = require('../../../utils/paymentProvider.util');
const {
  ParamError,
  ResourceNotFoundError,
} = require('../../../utils/error.util');
const { getConfigByType } = require('../../../services/config.service');
const { sleepForSeconds } = require('../../utils/timeOutRoutes');
const stripeService = require('../../../clients/stripe.client');
const {
  cancelSubscriptionService,
} = require('../../../services/communitySubscription');
const planOrderService = require('../../../services/plan/planOrder.service');
const actionEventService = require('../../../services/actionEvent');
const batchMetadataService = require('../../../services/batchMetadata');
const { mapFailureCodeToPaymentError } = require('./paymentErrorUtils');
const communityNotificationService = require('../../../services/communityNotification');
const {
  LEGENDS_COMMUNITY_CODE,
  COUNTRY_CREATED,
  latamCountriesArray,
  INDIAUPI_COMMUNITY_CODE,
  LATAM_COMMUNITY_CODE,
} = require('../../constants');

function getAmountInDollarOrCents(amount, currency) {
  const canDivideBy100 =
    !CURRENCY_WITH_NON_DECIMAL_POINTS.includes(currency);

  return canDivideBy100 ? amount / CENTS_PER_DOLLAR : amount;
}

const existingMemberFilter = () => ({
  $or: [
    { status: communityEnrolmentStatuses.CURRENT },
    {
      $and: [
        { status: communityEnrolmentStatuses.CANCELLED },

        { cancelledAt: { $gte: new Date() } },
      ],
    },
  ],
});

const getMemberListByCommunityCode = async (
  activeCommunityCode,
  excludeInternalEmails = false,
  excludeManagerEmails = false,
  filter = {}
) => {
  let emailsToExclude = [];
  const emailFilter = {};
  if (excludeInternalEmails) {
    emailFilter['memberType'] = { $ne: COMMUNITY_MEMBER_TYPES.NASACADEMY };
  }
  if (excludeManagerEmails) {
    const managerEmails = await CommunityRoles.aggregate([
      {
        $match: {
          communityCode: activeCommunityCode,
          role: aclRoles.MANAGER,
        },
      },
      {
        $group: {
          _id: '$communityObjectId',
          emails: {
            $push: '$email',
          },
        },
      },
    ]);

    emailsToExclude = [
      ...emailsToExclude,
      ...(managerEmails?.[0]?.emails ?? []),
    ];
  }
  emailFilter['email'] = { $nin: emailsToExclude };

  try {
    const result = await CommunitySubscription.find({
      communityCode: activeCommunityCode,
      ...filter,
      ...emailFilter,
      ...existingMemberFilter(),
    });
    return result;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

const getMemberCountByCommunityCode = async (
  activeCommunityCode,
  excludeInternalEmails = false,
  excludeManagerEmails = false,
  filter = {}
) => {
  try {
    let emailsToExclude = [];
    const emailFilter = {};
    if (excludeInternalEmails) {
      emailFilter['memberType'] = {
        $ne: COMMUNITY_MEMBER_TYPES.NASACADEMY,
      };
    }
    if (excludeManagerEmails) {
      const managerEmails = await CommunityRoles.aggregate([
        {
          $match: {
            communityCode: activeCommunityCode,
            role: aclRoles.MANAGER,
          },
        },
        {
          $group: {
            _id: '$communityObjectId',
            emails: {
              $push: '$email',
            },
          },
        },
      ]);

      emailsToExclude = [
        ...emailsToExclude,
        ...(managerEmails?.[0]?.emails ?? []),
      ];
    }
    emailFilter['email'] = { $nin: emailsToExclude };

    const result =
      (await CommunitySubscription.countDocuments({
        communityCode: activeCommunityCode,
        ...filter,
        ...emailFilter,
        ...existingMemberFilter(),
      })) || 0;
    return result;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

const getMemberCountByCommunityId = async (communityObjectId) => {
  logger.info(
    `Getting community members for communityID: ${communityObjectId}`
  );
  try {
    const community = await Community.findById(communityObjectId);
    if (!community) {
      throw new Error(
        `Community with objectId ${communityObjectId} cannot be found`
      );
    }
    // const memberCount = await getMemberCountByCommunityCode(
    //   community.code,
    //   excludeInternalEmails,
    //   excludeManagerEmails,
    //   filter
    // );
    const memberCountInfo =
      await membershipService.countService.countCommunityMembers({
        communityCode: community?.code,
      });
    return memberCountInfo.summary.memberCount;
  } catch (err) {
    logger.error(err.message, err.stack);
    throw err;
  }
};

const isLearnerSubscribedToCommunity = async ({
  learnerId,
  activeCommunityCode,
}) => {
  try {
    const result = await CommunitySubscription.find({
      learnerId,
      communityCode: activeCommunityCode,
      status: {
        $in: [
          communityEnrolmentStatuses.CURRENT,
          communityEnrolmentStatuses.CANCELLED,
        ],
      },
    });
    return result?.length > 0;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

const getCurrentSubscription = async ({
  learnerObjectId,
  activeCommunityCode,
}) => {
  try {
    const result = await CommunitySubscription.findOne({
      learnerObjectId,
      communityCode: activeCommunityCode,
      status: {
        $in: [communityEnrolmentStatuses.CURRENT],
      },
    }).lean();

    return result;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

const getUserSubscriptionWithProjection = async ({
  learnerId = null,
  communityObjectId,
  projectionParams = null,
}) => {
  try {
    const { code = null } = await Community.findById(
      communityObjectId,
      'code'
    );
    let subscription = null;
    const filters = {
      learnerId,
      communityCode: code,
      status: communityEnrolmentStatuses.CURRENT,
    };
    logger.info('Subscription filter params: ', filters);
    logger.info('Projection params: ', projectionParams);
    if (projectionParams) {
      subscription = await CommunitySubscription.findOne(
        filters,
        projectionParams
      );
    } else {
      subscription = await CommunitySubscription.findOne(filters);
    }
    logger.info('Fetched subscription as: ', subscription);
    return subscription;
  } catch (err) {
    logger.error('Unable to get user subscription due to: ', err.message);
    throw err;
  }
};

const findOneOrCreateSubscription = async (
  params = {},
  community,
  options = {},
  session = null
) => {
  const {
    email,
    learnerId,
    communityCode,
    learnerObjectId,
    promotedSubscription,
  } = params;

  try {
    const isNasAcademyUser =
      endsWithItemsInStringList(params?.email, INTERNAL_EMAIL_DOMAINS) ||
      false;

    const filter = {
      email,
      learnerId,
      communityCode,
      status: {
        $in: [
          communityEnrolmentStatuses.CURRENT,
          communityEnrolmentStatuses.PENDING,
        ],
      },
    };

    // Handle eventual consistency issues
    if (promotedSubscription?._id) {
      filter._id = {
        $ne: promotedSubscription._id,
      };
    }

    const subscriptionQuery = CommunitySubscription.findOne(filter);
    let result = session
      ? await subscriptionQuery.session(session).read('primary').lean()
      : await subscriptionQuery.lean();

    if (!result) {
      if (session) {
        const created = await CommunitySubscription.create(
          [
            {
              email,
              learnerId,
              learnerObjectId,
              communityCode,
              status: communityEnrolmentStatuses.CURRENT,
              memberType: isNasAcademyUser
                ? COMMUNITY_MEMBER_TYPES.NASACADEMY
                : COMMUNITY_MEMBER_TYPES.FREE,
            },
          ],
          { session }
        );
        result = created[0].toObject();
      } else {
        result = (
          await CommunitySubscription.create({
            email,
            learnerId,
            learnerObjectId,
            communityCode,
            status: communityEnrolmentStatuses.CURRENT,
            memberType: isNasAcademyUser
              ? COMMUNITY_MEMBER_TYPES.NASACADEMY
              : COMMUNITY_MEMBER_TYPES.FREE,
          })
        ).toObject();
      }

      await Promise.all([
        batchMetadataService.add({
          batchMetadataModelType: BATCH_METADATA_MODEL_TYPE.SUBSCRIPTION,
          entityObjectId: community._id,
          communityObjectId: community._id,
          community,
          addedObjectId: result._id,
        }),
        actionEventService.sendFreeMembershipActionEvent({
          actionEventType:
            MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_SUCCESS,
          actionEventCreatedAt: result.createdAt,
          subscription: result,
          community,
        }),
      ]);

      if (options?.sendNotification) {
        const learner = await Learner.findOne({
          learnerId,
        }).lean();

        if (!learner) {
          throw new ParamError(`Learner not found for ${learnerId}`);
        }

        const notificationOptions = {
          bypassPendingApproval: !!options?.bypassPendingApproval,
        };

        await communityNotificationService.sendEnrollmentNotification({
          community,
          learner,
          options: notificationOptions,
        });
      }
    }

    return result;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

const updateSubscription = async (subscriptionObjectId, updatePayload) => {
  try {
    const updatedSubscription =
      await CommunitySubscription.findOneAndUpdate(
        { _id: subscriptionObjectId },
        updatePayload,
        {
          new: true,
        }
      );
    return updatedSubscription;
  } catch (err) {
    logger.error('Unable to update subscription due to: ', err.message);
    throw err;
  }
};

const sendApplicationAcceptaceemail = async (
  existingLearner,
  communityInfo
) => {
  const ctaLink = `${REROUTE_MEMBER_LINK}?activeCommunityId=${communityInfo?._id}&memberExperience=1`;
  logger.info(`CTA link found and sending email: ${ctaLink}`);

  const authServiceRpc = new AuthServiceRpc();
  const { token } = await authServiceRpc.generateEmailToken(
    existingLearner?.email
  );

  const mailParams = {
    student_header_name: `Welcome ${existingLearner?.firstName ?? ''}`,
    community_link: `${ctaLink}&accessToken=${token}`,
    community_profile_image:
      communityInfo?.thumbnailImgData?.mobileImgData?.src ||
      DEFAULT_COMMUNITY_PROFILE_IMAGE,
    community_host: communityInfo?.By,
    community_name: communityInfo?.title,
    host_profile_image:
      communityInfo?.hostProfileImage ||
      DEFAULT_COMMUNITY_HOST_PROFILE_IMAGE,
  };
  const mailReqBody = {
    mailType: 'COMMUNITY_ENROLMENT_APPLICATION_APPROVED',
    mailCourse: communityInfo?.code || 'All',
    mailCourseOffer: 'All',
    toMail: [existingLearner?.email],
    data: mailParams,
    requesterServiceName: 'Admin Portal',
    mailSubject: `Welcome to ${communityInfo.title} Community`,
    toMailName: existingLearner?.firstName
      ? [
          `${existingLearner?.firstName} ${
            existingLearner?.lastName ?? ''
          }`,
        ]
      : [''],
    ...(communityInfo.By && { fromMailName: communityInfo.By }),
    ...(communityInfo.hostEmail && { fromMail: communityInfo.hostEmail }),
  };

  logger.info('Sending email with given data: ', mailParams);
  await sendEmail(mailReqBody);
};

const convertPaidToPromotedSubscription = async (subscriptionData) => {
  try {
    const { stripeSubscriptionId = null, subscriptionId = null } =
      subscriptionData;
    if (!stripeSubscriptionId && !subscriptionId) {
      logger.error(
        'Unable to convert paid to promoted subscription due to invalid subscriptionId'
      );
      throw new Error(
        'Unable to convert paid to promoted subscription due to invalid subscriptionId'
      );
    }

    const cancellationReason = 'User being promoted as Community Manager';
    await cancelSubscriptionService.removeSubscriptionForNonStripe(
      subscriptionData,
      cancellationReason
    );

    const token = jwt.sign({ stripeSubscriptionId }, JWT_SECRET_KEY);
    let axiosResponse = null;
    let updatedSubscription = null;
    if (subscriptionData?.status === communityEnrolmentStatuses.CURRENT) {
      axiosResponse = await axios({
        method: 'post',
        url: `${MAIN_PAYMENT_BACKEND_URL}/api/v1/community-cancel`,
        headers: {
          Authorization: `Bearer ${token}`,
        },
        data: {
          cancellationReason: 'User being promoted as Community Manager',
        },
      });
      logger.info('Axios Response ', axiosResponse?.data);
    }

    try {
      // we update old cancelled subscription status to promoted
      if (axiosResponse?.data) {
        updatedSubscription = await CommunitySubscription.findOneAndUpdate(
          { subscriptionId },
          { status: communityEnrolmentStatuses.PROMOTED },
          { returnOriginal: false }
        );
      }
      return updatedSubscription;
    } catch (error) {
      // console.log('Error on promoting paid subscription: ', error);
      logger.error('Error on promoting paid subscription: ', error);
      error.status = error.response.status;
      throw error;
    }
  } catch (error) {
    // console.log(
    //   'Unable to convert paid to promoted subscription: ',
    //   error
    // );
    logger.error(
      'Unable to convert paid to promoted subscription: ',
      error
    );
    throw error;
  }
};

const convertFreeToPromotedSubscription = async (subscriptionData) => {
  try {
    const { subscriptionId = null } = subscriptionData;
    if (!subscriptionId) {
      logger.error(
        'Unable to convert free to promoted subscription due to invalid subscriptionId'
      );
      throw new Error(
        'Unable to convert free to promoted subscription due to invalid subscriptionId'
      );
    }
    let updatedSubscription = null;
    try {
      if (
        subscriptionData?.status === communityEnrolmentStatuses.CURRENT
      ) {
        updatedSubscription = await CommunitySubscription.findOneAndUpdate(
          { subscriptionId },
          { status: communityEnrolmentStatuses.PROMOTED },
          { returnOriginal: false }
        );
      }
      return updatedSubscription;
    } catch (error) {
      // console.log(
      //   'Error on promoting free subscription: ',
      //   error
      // );
      logger.error('Error on promoting free subscription: ', error);
      error.status = error.response.status;
      throw error;
    }
  } catch (error) {
    // console.log('Unable to convert free to promoted subscription: ', error);
    logger.error(
      'Unable to convert free to promoted subscription: ',
      error
    );
    throw error;
  }
};

const getCommunitySubscriptionInfo = async (params = {}) => {
  try {
    logger.info('Received params: ', params);
    const communitySubscriptions = await CommunitySubscription.aggregate([
      { $match: params },
      {
        $group: {
          _id: '$communityCode',
          communitySubscriptionObjectId: { $last: '$_id' },
          status: { $last: '$status' },
          userConfirmedJoiningChat: { $last: '$userConfirmedJoiningChat' },
        },
      },
      {
        $project: {
          _id: '$communitySubscriptionObjectId',
          communityCode: '$_id',
          status: 1,
          userConfirmedJoiningChat: 1,
        },
      },
    ]);
    return communitySubscriptions;
  } catch (error) {
    logger.error(error.message);
    throw error;
  }
};

const getPaymentDetailsFromStripe = async ({
  stripeProductId,
  paymentProvider,
}) => {
  let stripeClient = null;

  await stripeService.init();
  if (paymentProvider === PAYMENT_PROVIDER.STRIPE) {
    stripeClient = stripeService.getStripeGlobalClient();
  } else {
    stripeClient = stripeService.getStripeIndiaClient();
  }

  const response = await stripeClient.prices.list({
    product: stripeProductId,
    active: true,
    type: 'recurring',
    limit: 100,
  });

  return response?.data;
};

const getStripePriceOptionsForAllCurrencies = async ({
  stripeProductId,
  paymentType = 'recurring',
  recurringInterval = 'month',
  paymentProvider,
}) => {
  const stripePricingData = await getPaymentDetailsFromStripe({
    stripeProductId,
    paymentProvider,
  });

  let defaultPricingData = null;

  const localCurrencyPricingData = [];

  logger.info('Stripe pricing data: ', stripePricingData);

  for (const pricingData of stripePricingData) {
    // this code was added on main website backend (need to check if it is required here)
    if (pricingData?.recurring?.interval === recurringInterval) {
      if (pricingData.type === paymentType) {
        if (
          pricingData.currency.toUpperCase() ===
          DEFAULT_CURRENCY.toUpperCase()
        ) {
          defaultPricingData = pricingData;
        } else {
          localCurrencyPricingData.push(pricingData);
        }
      }
    }
  }

  const data = {
    currencies: localCurrencyPricingData,
    defaultCurrency: defaultPricingData,
  };

  return data;
};
const getCommunityProductPricing = async (
  stripeProductId,
  communityPaymentMethods
) => {
  try {
    const paymentProvider =
      await PaymentProviderUtils.retrievePaymentProvider(
        communityPaymentMethods
      );

    if (stripeProductId) {
      const { currencies, defaultCurrency } =
        await getStripePriceOptionsForAllCurrencies({
          stripeProductId,
          paymentProvider,
        });
      const localiseCountries =
        await CountryCurrencyMappingModel.aggregate([
          {
            $match: {
              localisePrice: true,
            },
          },
          {
            $group: {
              _id: '$currencyCode',
              countries: {
                $push: '$country',
              },
            },
          },
        ]);

      const localiseCountriesCache = localiseCountries.reduce(
        (acc, { _id, countries }) => acc.set(_id, countries),
        new Map()
      );

      const localCurrencies = currencies.map((currencyData) => {
        const currency = currencyData.currency.toUpperCase();
        const localCurrency = {
          currency,
          country: localiseCountriesCache.get(currency) ?? '',
          price: getAmountInDollarOrCents(
            currencyData.unit_amount,
            currency
          ),
        };

        return localCurrency;
      });

      const productPricing = defaultCurrency.unit_amount / 100;

      return {
        localCurrencies,
        productPricing,
      };
    }
  } catch (error) {
    logger.error('Error fetching community pricing details:', error);
  }

  return {
    localCurrencies: [],
    productPricing: 0,
  };
};

const verifyPaidSubscriptionPayment = async (
  signupId,
  subscriptionId,
  isLoginToken
) => {
  if (!signupId) {
    throw new ParamError('Missing signup id');
  }

  if (!ObjectId.isValid(signupId)) {
    throw new ParamError('Invalid signup id');
  }

  const { envVarData = null } = await getConfigByType(
    CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
  );
  const { MAX_RETRIES = 8, TIMEOUT_SECONDS = 0.75 } = envVarData;

  let isValid = false;
  let attempts = 0;
  let purchaseTransaction;

  const isPaymentSuccessful = (transaction) =>
    transaction?.payment_details?.status === PAYMENT_STATUSES.SUCCESS;
  const isPaymentFailed = (transaction) =>
    transaction?.payment_details?.status === PAYMENT_STATUSES.FAILED;

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  do {
    // eslint-disable-next-line no-await-in-loop
    purchaseTransaction = await PurchaseTransactionModel.findById(
      signupId
    ).lean();

    if (!purchaseTransaction) {
      throw new ResourceNotFoundError('Transaction not found');
    }

    isValid = isPaymentSuccessful(purchaseTransaction);

    // Fetch the razorpay subscription status and finish the enrolment flow
    // For free trial via ebanx, it wont have subscription id attached before charge
    if (
      !isValid &&
      (purchaseTransaction.stripeSubscriptionId || subscriptionId)
    ) {
      switch (purchaseTransaction.payment_details.paymentProvider) {
        case PAYMENT_PROVIDER.RAZORPAY:
          // eslint-disable-next-line no-await-in-loop
          await paymentBackendRpc.fetchRazorpaySubscriptionAndUpdate(
            purchaseTransaction.stripeSubscriptionId
          );
          break;
        case PAYMENT_PROVIDER.EBANX:
          // eslint-disable-next-line no-await-in-loop
          await paymentBackendRpc.ebanxFetchAndUpdatePaymentStatus(
            purchaseTransaction.stripeSubscriptionId
          );
          break;
        case PAYMENT_PROVIDER.PAYPAL:
          // eslint-disable-next-line no-await-in-loop
          await paymentBackendRpc.paypalFetchPaymentAndUpdate(
            PURCHASE_TYPE.SUBSCRIPTION,
            purchaseTransaction._id,
            subscriptionId
          );
          break;
        default:
          break;
      }
    }

    if (isPaymentFailed(purchaseTransaction)) {
      const failureCode = purchaseTransaction.payment_details.failureCode;
      const paymentError = mapFailureCodeToPaymentError(failureCode);

      return {
        isValid,
        isFailedPayment: true,
        paymentError,
        error: {
          message:
            purchaseTransaction.payment_details?.failureReason ??
            'Payment is failed',
        },
      };
    }

    if (!isValid && attempts < MAX_RETRIES) {
      // eslint-disable-next-line no-await-in-loop
      await sleepForSeconds(parseFloat(TIMEOUT_SECONDS));
    }

    ++attempts;
  } while (!isValid && attempts <= MAX_RETRIES);

  logger.info(
    `verifyPaidSubscriptionPayment: ${signupId} payment verify is ${isValid}`
  );

  if (!isValid) {
    return {
      isValid,
      error: {
        message: 'Unable to verify subscription payment due to Timeout!',
      },
    };
  }

  const community = await Community.findOne(
    {
      code: purchaseTransaction.community_code,
    },
    { platforms: 1, isWhatsappExperienceCommunity: 1, config: 1 }
  ).lean();

  if (!community) {
    throw new ResourceNotFoundError(
      `Community cannot be found: ${purchaseTransaction.community_code}`
    );
  }

  await membersLimitNotificationService.notifyMembersLimit({
    community,
    additionalEmails: 0,
  });

  const metadata = {};

  if (community.isWhatsappExperienceCommunity && community.platforms) {
    const whatsappPlatform = community.platforms.filter(
      (platform) => platform.name === 'whatsapp'
    );

    if (whatsappPlatform.length > 0) {
      metadata['whatsappInvitationLink'] = whatsappPlatform[0].link;
    }
  }

  const email = purchaseTransaction.email;

  // Only update if user is first purchase to prevent
  // sending the access token for next purchase
  let updatedUser = await UserModel.findOneAndUpdate(
    {
      email,
      isActive: true,
      isFirstPurchase: true,
    },
    { isFirstPurchase: false },
    { new: true }
  ).lean();

  if (!updatedUser) {
    // For the case that
    // - Backend update the user and return success response with DIRECT token to FE
    // - But FE didnt get BE response because of 500 error
    // - FE will trigger second api call, but the user has alr updated,
    // - Hence BE return EMAIL token to FE because user is alr updated (which is wrong)

    // Fix here: just fetch user again, and verify if first purchase item has same signup id as current signup
    updatedUser = await UserModel.findOne({
      email,
      isActive: true,
    }).lean();
  }
  const isFirstPurchaseItem =
    updatedUser?.firstPurchaseInfo?.signupId?.toString() ===
    signupId.toString();

  const access =
    isLoginToken || isFirstPurchaseItem
      ? VERIFY_PAYMENT_ACCESS_TYPE.DIRECT
      : VERIFY_PAYMENT_ACCESS_TYPE.EMAIL;

  const result = {
    isValid,
    metadata,
    access,
  };

  if (!isLoginToken && isFirstPurchaseItem) {
    const authServiceRpc = new AuthServiceRpc();

    const { token, refreshToken } =
      await authServiceRpc.generateLoginAccessAndRefreshToken(email);

    result.token = token;
    result.refreshToken = refreshToken;
  }

  return result;
};

const verifyPaidPlanPayment = async (signupId) => {
  if (!signupId) {
    throw new ParamError('Missing signup id');
  }

  if (!ObjectId.isValid(signupId)) {
    throw new ParamError('Invalid signup id');
  }

  const { envVarData = null } = await getConfigByType(
    CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
  );
  const { MAX_RETRIES = 8, TIMEOUT_SECONDS = 0.75 } = envVarData;

  let isValid = false;
  let attempts = 0;
  let planOrder;

  const isPaymentSuccessful = (order) =>
    order?.paymentDetails?.status === PAYMENT_STATUSES.SUCCESS;
  const isPaymentFailed = (order) =>
    order?.paymentDetails?.status === PAYMENT_STATUSES.FAILED;

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  do {
    // eslint-disable-next-line no-await-in-loop
    planOrder = await planOrderService.retrievePlanOrder(signupId);

    if (!planOrder) {
      throw new ResourceNotFoundError('Transaction not found');
    }

    isValid = isPaymentSuccessful(planOrder);

    if (!isValid && planOrder.paymentProviderSubscriptionId) {
      switch (planOrder.paymentDetails.paymentProvider) {
        case PAYMENT_PROVIDER.EBANX:
          // eslint-disable-next-line no-await-in-loop
          await paymentBackendRpc.ebanxFetchAndUpdatePaymentStatus(
            planOrder.paymentProviderSubscriptionId
          );
          break;
        default:
          break;
      }
    }

    if (isPaymentFailed(planOrder)) {
      const failureCode = planOrder.paymentDetails.failureCode;
      const paymentError = mapFailureCodeToPaymentError(failureCode);

      return {
        isValid,
        isFailedPayment: true,
        paymentError,
        error: {
          message:
            planOrder.paymentDetails?.failureReason ?? 'Payment is failed',
        },
      };
    }

    if (!isValid && attempts < MAX_RETRIES) {
      // eslint-disable-next-line no-await-in-loop
      await sleepForSeconds(parseFloat(TIMEOUT_SECONDS));
    }

    ++attempts;
  } while (!isValid && attempts <= MAX_RETRIES);

  logger.info(
    `verifyPaidPlanPayment: ${signupId} payment verify is ${isValid}`
  );

  if (!isValid) {
    return {
      isValid,
      error: {
        message: 'Unable to verify plan payment due to Timeout!',
      },
    };
  }

  return {
    isValid,
  };
};

/**
 * Optimized subscription creation for community owners
 * Creates both main community and regional community subscriptions in a single operation
 * Runs external operations (SQS) concurrently for better performance
 */
const createCommunityOwnerSubscriptions = async ({
  user,
  learner,
  community,
  session,
}) => {
  try {
    // Determine regional community code based on country
    let regionalCommunityCode = LEGENDS_COMMUNITY_CODE;
    const communityCountry = community.countryCreatedIn;

    if (communityCountry === COUNTRY_CREATED.INDIA) {
      regionalCommunityCode = INDIAUPI_COMMUNITY_CODE;
    } else if (latamCountriesArray.includes(communityCountry)) {
      regionalCommunityCode = LATAM_COMMUNITY_CODE;
    }

    // Check if regional subscription already exists
    const regionalExists = await CommunitySubscription.exists({
      email: user.email,
      learnerId: learner.learnerId,
      communityCode: regionalCommunityCode,
      status: {
        $in: [
          communityEnrolmentStatuses.CURRENT,
          communityEnrolmentStatuses.PENDING,
        ],
      },
    });

    // Prepare subscription data
    const isNasAcademyUser =
      endsWithItemsInStringList(user.email, INTERNAL_EMAIL_DOMAINS) ||
      false;
    const memberType = isNasAcademyUser
      ? COMMUNITY_MEMBER_TYPES.NASACADEMY
      : COMMUNITY_MEMBER_TYPES.FREE;

    const subscriptionsToCreate = [
      // Main community subscription (always create)
      {
        email: user.email,
        learnerId: learner.learnerId,
        learnerObjectId: learner._id,
        communityCode: community.code,
        status: communityEnrolmentStatuses.CURRENT,
        memberType,
      },
    ];

    // Fetch regional community if we need to create the subscription
    let regionalCommunity = null;
    if (!regionalExists) {
      regionalCommunity = await Community.findOne({
        code: regionalCommunityCode,
      }).lean();

      if (regionalCommunity) {
        subscriptionsToCreate.push({
          email: user.email,
          learnerId: learner.learnerId,
          learnerObjectId: learner._id,
          communityCode: regionalCommunityCode,
          status: communityEnrolmentStatuses.CURRENT,
          memberType,
        });
      }
    }

    // Create subscriptions in batch
    const createdSubscriptions = await CommunitySubscription.create(
      subscriptionsToCreate,
      { session }
    );

    // Prepare external operations for concurrent execution
    const externalOperations = [];

    // Process main community subscription (first created)
    const mainSubscription = createdSubscriptions[0];

    // Add batch metadata for main subscription
    externalOperations.push(
      batchMetadataService.add({
        batchMetadataModelType: BATCH_METADATA_MODEL_TYPE.SUBSCRIPTION,
        entityObjectId: community._id,
        communityObjectId: community._id,
        community,
        addedObjectId: mainSubscription._id,
      })
    );

    // Add action event for main subscription
    externalOperations.push(
      actionEventService.sendFreeMembershipActionEvent({
        actionEventType:
          MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_SUCCESS,
        actionEventCreatedAt: mainSubscription.createdAt,
        subscription: mainSubscription,
        community,
      })
    );

    // Process regional subscription if created
    if (createdSubscriptions.length > 1 && regionalCommunity) {
      const regionalSubscription = createdSubscriptions[1];

      // Add batch metadata for regional subscription with regional community
      externalOperations.push(
        batchMetadataService.add({
          batchMetadataModelType: BATCH_METADATA_MODEL_TYPE.SUBSCRIPTION,
          entityObjectId: regionalCommunity._id,
          communityObjectId: regionalCommunity._id,
          community: regionalCommunity,
          addedObjectId: regionalSubscription._id,
        })
      );

      // Add action event for regional subscription with regional community
      externalOperations.push(
        actionEventService.sendFreeMembershipActionEvent({
          actionEventType:
            MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_SUCCESS,
          actionEventCreatedAt: regionalSubscription.createdAt,
          subscription: regionalSubscription,
          community: regionalCommunity,
        })
      );
    }

    // Execute all external operations concurrently
    await Promise.all(externalOperations);

    logger.info('Community owner subscriptions created successfully', {
      communityId: community._id,
      userEmail: user.email,
      subscriptionsCreated: createdSubscriptions.length,
      regionalExists,
      regionalCommunityCode,
    });

    return {
      mainSubscription: createdSubscriptions[0],
      regionalSubscription: createdSubscriptions[1] || null,
      subscriptionsCreated: createdSubscriptions.length,
    };
  } catch (error) {
    logger.error('Error creating community owner subscriptions', {
      communityId: community._id,
      userEmail: user.email,
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

module.exports = {
  getMemberListByCommunityCode,
  getMemberCountByCommunityCode,
  getMemberCountByCommunityId,
  isLearnerSubscribedToCommunity,
  getCurrentSubscription,
  getUserSubscriptionWithProjection,
  findOneOrCreateSubscription,
  updateSubscription,
  sendApplicationAcceptaceemail,
  convertPaidToPromotedSubscription,
  convertFreeToPromotedSubscription,
  getCommunitySubscriptionInfo,
  getCommunityProductPricing,
  verifyPaidSubscriptionPayment,
  verifyPaidPlanPayment,
  createCommunityOwnerSubscriptions,
};
