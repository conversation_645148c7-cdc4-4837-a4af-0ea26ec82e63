// Models

// Services and utilities
const { sendEmail } = require('@src/services/notification');
const logger = require('@src/services/logger.service');
const PaymentBackendRpc = require('@src/rpc/paymentBackend');
const notificationCommonService = require('@services/communityNotification/email/common.service');
const { ENROLMENT_MAIL_TYPES } = require('@services/mail/constants');
const nameUtils = require('@utils/name.util');
const mailUtils = require('@utils/mail.util');
const CommunitySubscriptionsModel = require('../../models/communitySubscriptions.model');
const CommunityModel = require('../../models/community.model');

// Constants
const {
  COMMUNITY_SUBSCRIPTION_STATUSES,
  PAYMENT_PROVIDER,
} = require('../../../constants/common');

const {
  CANCELLATION_REVOKE_BUFFER_MS,
  CANCELLATION_REVOKE_BUFFER_HOURS,
} = require('../../constants');

// Utilities
const { getDateInUTC } = require('../../utils');
const {
  ResourceNotFoundError,
  ParamError,
  ToUserError,
} = require('../../../utils/error.util');

// CommunitiesAPI services
const WhatsappService = require('./whatsapp.service');
const LearnerModel = require('@/src/models/learners.model');
const { GENERIC_ERROR } = require('@/src/constants/errorCode');

/**
 * Send cancellation revoke notifications
 */
async function sendCancellationRevokeNotifications({
  subscription,
  community,
}) {
  const [communityOwnerInfo, subscriber] = await Promise.all([
    notificationCommonService.retrieveCommunityOwnerInfo(community.code),
    LearnerModel.findById(subscription.learnerObjectId).lean(),
  ]);

  const communityOwnerEmail = communityOwnerInfo.email;
  const communityOwnerName = nameUtils.getName(
    communityOwnerInfo.firstName,
    communityOwnerInfo.lastName,
    communityOwnerInfo.email
  );

  const managerEmailConfig = mailUtils.retrieveManagerMailConfig(
    community.title,
    community.link,
    communityOwnerEmail,
    communityOwnerName
  );

  const subscriberName = nameUtils.getName(
    subscriber.firstName,
    subscriber.lastName,
    subscriber.email
  );

  try {
    // Send email to user
    const userMailBody = {
      mailType: ENROLMENT_MAIL_TYPES.MEMBER_REVOKE_CANCELLATION_MAIL,
      mailCourse: community.code,
      mailCourseOffer: 'All',
      fromMailName: managerEmailConfig.fromMailName,
      fromMail: managerEmailConfig.fromMail,
      toMail: [subscription.email],
      toMailName: [subscriberName || 'Member'],
      replyToMail: managerEmailConfig.replyToMail,
      replyToMailName: managerEmailConfig.replyToMailName,
      data: {
        community_name: community.title,
        community_profile_image:
          community.thumbnailImgData?.mobileImgData?.src,
      },
      requesterServiceName: 'Learn Portal Backend',
    };

    await sendEmail(userMailBody);
    logger.info(
      'Cancellation revoke notification sent to user successfully'
    );
  } catch (error) {
    logger.error(
      'Failed to send cancellation revoke notifications:',
      error
    );
  }
}

/**
 * Find user's scheduled-for-cancellation paid subscription for a community
 */
async function findUserScheduledCancellationSubscription({
  communityId,
  userId,
}) {
  const community = await CommunityModel.findById(communityId).lean();
  if (!community) {
    throw new ResourceNotFoundError('Community not found');
  }

  const subscription = await CommunitySubscriptionsModel.findOne({
    communityCode: community.code,
    learnerObjectId: userId,
    status: COMMUNITY_SUBSCRIPTION_STATUSES.CANCELLED,
    stripeSubscriptionId: { $exists: true, $ne: null }, // Only paid subscriptions
    cancelledAt: { $exists: true }, // Must have scheduled cancellation
  })
    .populate('learnerObjectId')
    .lean();

  if (!subscription) {
    throw new ParamError(
      'No scheduled cancellation found for this community'
    );
  }

  return { subscription, community };
}

/**
 * Validate if scheduled cancellation can be revoked
 */
function validateCancellationRevokeEligibility(subscription) {
  const now = new Date();

  // Can revoke cancellation until 2 hours before the scheduled cancellation time
  const cancelledAt = new Date(subscription.cancelledAt);
  const cutoffTime = new Date(
    cancelledAt.getTime() - CANCELLATION_REVOKE_BUFFER_MS
  );

  if (now >= cancelledAt) {
    throw new ParamError(
      'Cancellation has already taken effect, cannot revoke'
    );
  }

  if (now >= cutoffTime) {
    throw new ParamError(
      `Cannot revoke cancellation within ${CANCELLATION_REVOKE_BUFFER_HOURS} hours of scheduled cancellation time`
    );
  }

  // Additional validation: ensure it's actually a paid subscription
  if (!subscription.stripeSubscriptionId) {
    throw new ParamError(
      'Only paid subscriptions can have their cancellation revoked'
    );
  }

  // Ensure subscription is in cancelled status (scheduled for cancellation)
  if (subscription.status !== COMMUNITY_SUBSCRIPTION_STATUSES.CANCELLED) {
    throw new ParamError('Subscription is not scheduled for cancellation');
  }

  if (subscription.disableRevokeCancellation) {
    throw new ToUserError(
      'Your cancellation is still being processed. Please undo it after 2 minutes.',
      GENERIC_ERROR.MEMBERSHIP_CANCELLATION_IN_PROGRESS
    );
  }
}

/**
 * Revoke scheduled cancellation at payment provider level
 */
async function revokeScheduledCancellationAtProvider(subscription) {
  const { paymentProvider, stripeSubscriptionId } = subscription;

  if (!stripeSubscriptionId) {
    throw new ParamError(
      'No subscription ID found for cancellation revoke'
    );
  }

  switch (paymentProvider) {
    case PAYMENT_PROVIDER.STRIPE:
    case PAYMENT_PROVIDER.STRIPE_US:
    case PAYMENT_PROVIDER.STRIPE_INDIA:
      try {
        const paymentBackendRpc = new PaymentBackendRpc();
        await paymentBackendRpc.init();

        // Using reactivateStripeSubscription which actually calls /revoke-cancellation endpoint
        await paymentBackendRpc.reactivateStripeSubscription({
          subscriptionId: stripeSubscriptionId,
          paymentProvider,
        });

        logger.info(
          'Stripe subscription cancellation revoked successfully',
          {
            subscriptionId: stripeSubscriptionId,
            paymentProvider,
            subscriptionObjectId: subscription._id.toString(),
          }
        );
      } catch (error) {
        logger.error('Failed to revoke Stripe subscription cancellation', {
          subscriptionId: stripeSubscriptionId,
          paymentProvider,
          subscriptionObjectId: subscription._id.toString(),
          error: error.message,
        });
        throw new ParamError(
          `Failed to revoke Stripe subscription cancellation: ${error.message}`
        );
      }
      break;

    case PAYMENT_PROVIDER.EBANX:
      // EBANX cancellation revoke is handled by database flag (scheduledCancellation: false)
      // No API call needed - will be set in database update
      logger.info(
        'EBANX subscription cancellation will be revoked via database flag',
        {
          subscriptionObjectId: subscription._id.toString(),
          subscriptionId: stripeSubscriptionId,
        }
      );
      break;

    default:
      throw new ParamError(
        `Payment provider ${paymentProvider} not supported for cancellation revoke`
      );
  }
}

/**
 * Revoke scheduled cancellation for a paid community subscription
 */
async function revokeScheduledCancellation({
  subscription,
  community,
  reason,
}) {
  const now = getDateInUTC(new Date());

  try {
    // Step 1: Revoke scheduled cancellation at payment provider
    await revokeScheduledCancellationAtProvider(subscription);

    // Step 2: Update subscription status in database - remove cancellation
    const restoredSubscription =
      await CommunitySubscriptionsModel.findByIdAndUpdate(
        subscription._id,
        {
          $set: {
            status: COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT,
            cancellationRevokedAt: now,
            cancellationRevokeReason: reason,
            // For EBANX, ensure scheduledCancellation is false
            ...(subscription.paymentProvider ===
              PAYMENT_PROVIDER.EBANX && {
              scheduledCancellation: false,
            }),
          },
          $unset: {
            cancelledAt: '',
            cancellationReason: '',
            unsubscribedAt: '',
            webhookTriggered: '',
            disableRevokeCancellation: '',
          },
          $push: {
            cancellationRevokeHistory: {
              revokedAt: now,
              reason,
              paymentProvider: subscription.paymentProvider,
              revokedAtProvider: true,
            },
          },
        },
        { new: true }
      ).lean();

    if (!restoredSubscription) {
      throw new ParamError('Failed to restore subscription status');
    }

    // Step 3: Handle WhatsApp restoration (if needed)
    if (community.isWhatsappExperienceCommunity) {
      try {
        await WhatsappService.addWhatsappMemberBySubscription(
          community._id,
          subscription._id
        );
      } catch (error) {
        logger.warn(
          'Failed to handle WhatsApp restoration:',
          error.message
        );
      }
    }

    // Step 4: Send notifications
    await sendCancellationRevokeNotifications({
      subscription: restoredSubscription,
      community,
      reason,
    });

    // Step 5: Send action events - temporarily commented out for testing
    // TODO: Create proper cancellation revoke action event
    // await ActionEventService.sendMembershipActionEvent({
    //   subscription: restoredSubscription,
    //   community,
    //   purchaseType: 'SUBSCRIPTION',
    // });

    logger.info('Successfully revoked scheduled cancellation', {
      subscriptionId: subscription._id,
      communityId: community._id,
      paymentProvider: subscription.paymentProvider,
    });

    return restoredSubscription;
  } catch (error) {
    logger.error('Failed to revoke scheduled cancellation:', {
      error: error.message,
      subscriptionId: subscription._id,
      communityId: community._id,
    });
    throw error;
  }
}

/**
 * Main cancel scheduled cancellation service function - handles paid subscriptions only
 */
const cancelScheduledCancellation = async ({
  communityId,
  userId,
  reason,
}) => {
  // Find and validate scheduled cancellation subscription
  const { subscription, community } =
    await findUserScheduledCancellationSubscription({
      communityId,
      userId,
    });

  // Validate cancellation revoke eligibility (before cancellation takes effect)
  validateCancellationRevokeEligibility(subscription);

  // Revoke the scheduled cancellation
  const result = await revokeScheduledCancellation({
    subscription,
    community,
    reason,
  });

  return result;
};

module.exports = {
  cancelScheduledCancellation,
};
