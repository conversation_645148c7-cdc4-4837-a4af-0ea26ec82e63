/* eslint-disable no-param-reassign */
/* eslint-disable no-unused-vars */
/* eslint-disable prefer-const */
/* eslint-disable no-await-in-loop */
/* eslint-disable no-param-reassign */
const { DateTime } = require('luxon');
const jwt = require('jsonwebtoken');

const mongoose = require('mongoose');

const crypto = require('crypto');
const axios = require('../../../clients/axios.client');
const { getLock } = require('../../../redisLock');
const regexUtils = require('../../../utils/regex.util');

const ObjectId = mongoose.Types.ObjectId;
// models
const AddonTransaction = require('../../models/communityAddonTransactions.model');
const PendingTransaction = require('../../models/pendingTransaction.model');
const EventModel = require('../../models/communityEvents.model');
const EventAttendees = require('../../models/eventAttendees.model');
const CommunityAddonTransaction = require('../../models/communityAddonTransactions.model');
const Learner = require('../../../models/learners.model');
const CommunityModel = require('../../models/community.model');
const PurchaseTransactionModel = require('../../models/communityPurchaseTransactions.model');
const CommunitySubscriptions = require('../../models/communitySubscriptions.model');
const WhatsappConnectedGroup = require('../../models/whatsappConnectedGroup.model');
const SubscriptionModel = require('../../models/communitySubscriptions.model');
const CommunityRole = require('../../models/communityRole.model');
const UserModel = require('../../../models/users.model');
// services
const logger = require('../../../services/logger.service');
const eventAttendees = require('./eventAttendees.service');
const communityHostService = require('../web/communityHost.service');
const {
  discountValidationForEntities,
  discountCreationForEntities,
} = require('./communityDiscounts.service');
const EventService = require('../../../services/event');
const eventCommonService = require('../../../services/event/common.service');
const eventIcsService = require('../../../services/event/ics.service');
const {
  createEventNotifications,
  updateEventScheduledNotifications,
} = require('../mobile/mobileNotifications.service');
const { getCommunityCodesByIds } = require('./community.service');
const {
  sendMobileNotification,
} = require('../../../services/notification/mobileNotifications.service');
const {
  getAddonPriceInLocalCurrency,
} = require('./communityAddonPrice.service');
const { getConfigByType } = require('../../../services/config.service');
// utils
const { getCurrentUTCTime } = require('./dateUtils');
const { getTimeBeforeStartTime } = require('../../utils/eventUtils');
const { isValidURL } = require('../../../utils/url_handling');
const { sleepForSeconds } = require('../../utils/timeOutRoutes');
const {
  defaultPaginatedResponse,
  getPaginationDataAggregatePipelineStage,
  getNextAndPrevious,
} = require('../../../utils/pagination.util');
const membershipService = require('../../../services/membership');
const communityService = require('../../../services/community');
const ActionEventService = require('../../../services/actionEvent');
// constants
const {
  DISCORD_URL,
  DISCORD_AUTH,
  NAS_IO_FRONTEND_URL,
} = require('../../../config');
const {
  communityEnrolmentStatuses,
  MOBILE_NOTIFICATION_TYPES,
  MOBILE_NOTIFICATION_RECIPIENTS_SCOPES,
  EVENT_TYPES,
  CONFIG_TYPES,
  PURCHASE_TYPE,
  PENDING_TRANSACTION_ENTITY_TYPE,
  eventTimeIntervalEnum,
  eventTimeInterval,
  DEFAULT_CURRENCY,
  ADDON_ACTION_EVENT_TYPES,
  MILESTONE_ACTIVITY_TYPES,
  MAX_QUANTITY_PER_PURCHASE,
  BATCH_METADATA_MODEL_TYPE,
} = require('../../../constants/common');
const {
  EVENT_STATUS,
  COMMUNITY_EVENT_STATUSES,
  DEFAULT_COUNTRY_WISE_PRICE_CONFIG,
  COMMUNITY_EVENT_ACCESS_TYPES,
  COMMUNITY_EVENT_ATTENDEE_PURCHASE_TYPES,
  EVENT_PAYMENT_STATUSES,
  COMMUNITY_ONE_TIME_PAYMENT_ENTITIES,
  COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES,
  PAYMENT_STATUSES,
} = require('../../constants');
const CheckInternalMemberUtils = require('../../../utils/checkInternalMember.util');
const EventUtils = require('../../../utils/event.util');
const discountService = require('./communityDiscounts.service');
const slugUtils = require('../../../utils/slug.util');
const {
  getExistingMemberParams,
} = require('../../../utils/communitySubscription.util');
const pricingService = require('../../../services/pricing');
const fraudService = require('../../../services/fraud');
const { affiliateProductService } = require('../../../services/affiliate');
const PublishProductUsageService = require('../../../services/featurePermissions/publishProductUsage.service');

const {
  UnauthorizedError,
  ParamError,
  ToUserError,
  ResourceNotFoundError,
  InternalError,
} = require('../../../utils/error.util');
const { EVENT_ERROR } = require('../../../constants/errorCode');
const mailConfigService = require('../../../services/mail/mailConfigs.service');
const { EVENT_MAIL_TYPES } = require('../../../services/mail/constants');
const entityCurrencyUtil = require('../../../utils/entityCurrency.util');
const { affiliateService } = require('../../../services/affiliate');
const {
  removeEmailReminderForAbandonedCheckout,
} = require('../../../services/abandonedCarts/abandonedCarts.service');
const batchMetadataService = require('../../../services/batchMetadata');
const {
  getIsCommunityManagerByRole,
} = require('../../../services/membership/utils/roles.utils');
const receiptService = require('../../../services/receipts/receipts.service');
const {
  generateCoverMediaItems,
  hasVideoCoverMediaItems,
} = require('../../../utils/multipleCoverMediaItems.util');
const {
  COVER_MEDIA_ENTITY_TYPES,
} = require('../../../constants/coverMediaItems.constant');
const { isCommunityIndexable } = require('./utils');
const {
  deleteCoverMediaItems,
  deleteRemovedVideoCoverMediaItems,
} = require('../../../services/coverMediaItems/common.service');
const {
  getEntityCampaignInfo,
} = require('../../../services/magicAudience/magicAudienceCampaign.service');
const SyncProductDataService = require('../../../services/product/syncProductData.service');
const ProductChangeLogService = require('../../../services/product/productChangeLog.service');
const {
  PRODUCT_TYPE,
  PRODUCT_CHANGE_LOG_TYPE,
} = require('../../../services/product/constants');
const { withTransaction } = require('../../../utils/mongodb.util');
const {
  purgeEntityLandingPageCache,
  ENTITY_LANDING_PAGE,
} = require('../../../utils/memberPortalLinks.utils');

const convertAnswerToProperType = (dataField, answer) => {
  if (answer == null) {
    return;
  }

  let convertedAnswer;
  if (dataField.fieldDataType === 'number') {
    convertedAnswer = Number(answer);
  }

  if (dataField.fieldDataType === 'checkbox') {
    convertedAnswer = [];
    // eslint-disable-next-line no-unused-expressions
    dataField?.options?.forEach((option) => {
      if (answer[option.value]) {
        convertedAnswer.push(option.label);
      }
    });
  }

  if (dataField.fieldDataType === 'radio') {
    // eslint-disable-next-line no-unused-expressions
    dataField?.options?.forEach((option) => {
      if (answer === option.value) {
        convertedAnswer = {
          value: answer,
          label: option.label,
        };
      }
    });
  }

  if (
    dataField.fieldDataType === 'text' ||
    dataField.fieldDataType === 'url'
  ) {
    convertedAnswer = answer;
  }

  return convertedAnswer;
};

const getCommunitiesByIds = async (arrayOfIds) => {
  if (arrayOfIds.length === 0) return [];

  const communities = await CommunityModel.find({
    _id: { $in: arrayOfIds },
  })
    .read('primary')
    .lean();

  return communities;
};

async function checkIfExistsPendingSubscription(community) {
  const { code: communityCode } = community;

  const existPendingSubscription = await SubscriptionModel.exists({
    communityCode,
    status: communityEnrolmentStatuses.PENDING,
  }).read('primary');

  return !!existPendingSubscription;
}

const checkEventForFraud = async ({
  updatedPayload,
  community,
  eventId,
}) => {
  const contentList = [];
  const contentSourceList = [];
  if (updatedPayload?.title) {
    contentList.push(updatedPayload.title);
    contentSourceList.push('title');
  }
  if (updatedPayload?.description) {
    contentList.push(updatedPayload.description);
    contentSourceList.push('description');
  }
  if (contentList.length > 0) {
    const fraudEngine = new fraudService.FraudEngine({
      communityId: community._id,
      eventName: fraudService.INTERESTED_EVENTS.UPDATE_CONTENT,
      entityType: 'event',
      entityId: eventId,
      data: {
        content: contentList.join(', '),
        contentSource: contentSourceList.join(' & '),
      },
      checksToPerform: [fraudService.COMMON_FRAUD_CHECKS.FREE_INPUT],
      autoConsequencesToApply: [
        fraudService.COMMON_CONSEQUENCES.RESTRICT_CHECKOUT,
        fraudService.COMMON_CONSEQUENCES.RESTRICT_CUSTOM_EMAIL,
        fraudService.COMMON_CONSEQUENCES.RESTRICT_MAGIC_REACH,
        fraudService.COMMON_CONSEQUENCES.NOT_INDEXABLE,
        fraudService.COMMON_CONSEQUENCES.RECOMMEND_DEACTIVATE,
      ],
    });

    try {
      await fraudEngine.performCheck();
    } catch (error) {
      logger.error('Error in fraud check:', error.message, error.stack);
    }
  }
};

const createOneEvent = async (params = {}) => {
  if (Object.keys(params).length === 0 && params.constructor === Object) {
    throw new ParamError('Empty Params');
  }

  const communitiesDetails = await getCommunitiesByIds(params.communities);
  if (!communitiesDetails || communitiesDetails.length === 0) {
    throw new ResourceNotFoundError('Communities not found');
  }

  const community = communitiesDetails[0];

  const paramsToUpdate =
    await eventCommonService.validateAndFormatEventParams(
      params,
      community
    );

  if (!paramsToUpdate) {
    throw new ParamError('No params to update');
  }

  await discountValidationForEntities(
    community.code,
    params.newDiscountsToApply,
    params.discountsToAdd
  );

  if (paramsToUpdate.isNewHost && paramsToUpdate.host) {
    logger.info('Creating Community guest host for new event:');
    const guestHostParams = {
      firstName: paramsToUpdate.host.firstName,
      lastName: paramsToUpdate.host.lastName ?? '',
      profileImage: paramsToUpdate.host.profileImage,
      communityObjectId: new ObjectId(params.communities[0]),
    };
    await communityHostService.createGuestHost(guestHostParams);
  }

  let event;
  logger.info(
    `Creating Community Events with following params: ${JSON.stringify(
      params
    )}`
  );
  // For backward compatibility
  params.status = EVENT_STATUS.PUBLISHED;
  params.isActive = true;

  event = await EventModel.create(params);
  logger.info(`Created Community event with new id ${event?._id}`);

  logger.info(
    `Generating ics file link for newly created event ${event?._id}`
  );
  const eventIcs = await eventIcsService.fetchIcsFileUrl(event);
  logger.info(
    `Saving generated ics file link ${eventIcs} for newly created event ${event?._id} `
  );
  event = await EventModel.findByIdAndUpdate(
    event?._id,
    { icsFileLink: eventIcs ?? null },
    {
      new: true,
      upsert: false,
    }
  ).lean();

  event = await discountCreationForEntities(
    community.code,
    null,
    event,
    PURCHASE_TYPE.EVENT,
    params?.createdBy,
    EventModel,
    params.newDiscountsToApply,
    params.discountsToRemove,
    params.discountsToAdd,
    params.discountsToDisable
  );

  if (community?.isDemo) {
    // Check for Demo community here so as not to send notifications and create discrod event for it
    return event;
  }

  try {
    const startTimeDate = DateTime.fromJSDate(params.startTime);
    const communityCode = community.code;
    const communityTitle = community.title;

    const subscriptions = await CommunitySubscriptions.find({
      communityCode,
      status: communityEnrolmentStatuses.CURRENT,
    }).lean();

    const userIds = subscriptions.map(
      (subscription) => subscription.learnerId
    );
    const eventNotificationResponse = await createEventNotifications({
      communityTitle,
      communityCode,
      eventObjectId: event._id,
      userIds,
      startTimeDate,
      title: params.title,
    });
    logger.info(
      'Event notification response: ',
      eventNotificationResponse
    );
  } catch (err) {
    logger.error('Error while sending event notification: ', err);
  }
  // create discord event
  try {
    const communitiesPromises = communitiesDetails.map(async (comm) => {
      const communityLink = comm?.link;
      const eventLink = communityLink
        ? `${NAS_IO_FRONTEND_URL}${communityLink}${params.slug}`
        : NAS_IO_FRONTEND_URL;

      const discordServer = comm?.bots?.filter(
        (bot) => bot?.type === 'Discord'
      );
      if (discordServer.length > 0) {
        const serverKey = discordServer?.[0]?.serverKey;
        const discordEventData = {
          startTime: params.startTime,
          endTime: params.endTime,
          name: params.title,
          description: params?.description?.slice(0, 999),
          location: eventLink,
        };
        logger.info(
          'Creating discord event ',
          JSON.stringify(discordEventData)
        );
        const requestUrl = `${DISCORD_URL}/api/v1/createEvent/${serverKey}`;
        const authHeader = jwt.sign({}, DISCORD_AUTH);
        const response = await axios.post(requestUrl, discordEventData, {
          headers: {
            Authorization: `Bearer ${authHeader}`,
          },
        });
        if (response?.data) {
          logger.info('Discord event creation response: ', response?.data);
          const discordEventId = response?.data?.event;
          event = await EventModel.findByIdAndUpdate(
            event?._id,
            { discordEventId },
            {
              new: true,
              upsert: false,
            }
          );
        }
      }
    });
    await Promise.all(communitiesPromises);
  } catch (error) {
    const errorMessage = 'Unable to create discord event';
    logger.error(`createOneEvent error|error=${errorMessage}`);
    error.message = errorMessage;
    error.status = error.INTERNAL_SERVER_ERROR;
    throw error;
  }

  try {
    logger.info('createOneEvent: Sending event create email to manager');
    await eventCommonService.formatAndSendEventTypeEmail({
      mailType: EVENT_MAIL_TYPES.MANAGER_COMMUNITY_EVENT_CREATED,
      event,
      community,
      learnerObjectId: params?.createdByLearnerObjectId,
      isManagerEmail: true,
    });
  } catch (err) {
    logger.error('Error sending event create email to manager: ', err);
  }

  await ActionEventService.sendMilestoneEvent({
    actionEventType: MILESTONE_ACTIVITY_TYPES.MILESTONE_FIRST_EVENT,
    communityCode: community.code,
    communityObjectId: community._id,
    learnerObjectId: params?.createdByLearnerObjectId,
  });

  // to move to worker pool
  checkEventForFraud({
    community,
    eventId: event._id,
    updatedPayload: event,
  });

  return event;
};

const sendEventReminderEmails = async (params) => {
  try {
    const now = DateTime.utc();
    const fiveMinLater = now.plus({ minutes: 5 });

    await Promise.allSettled(
      eventTimeIntervalEnum.map(async (type) => {
        let mailType;
        switch (type) {
          case eventTimeInterval.ONE_HOUR:
            mailType = EVENT_MAIL_TYPES.MEMBER_COMMUNITY_EVENT_REMINDER_1H;
            break;
          case eventTimeInterval.ONE_DAY:
            mailType =
              EVENT_MAIL_TYPES.MEMBER_COMMUNITY_EVENT_REMINDER_24H;
            break;
          default:
            logger.info(
              `sendEventReminderEmails: eventTimeIntervalEnum not recognised ${type}`
            );
            return;
        }
        const events = await EventModel.aggregate([
          {
            $match: {
              isActive: true,
              timeBeforeStartTime: {
                $elemMatch: {
                  type,
                  date: { $gte: now, $lt: fiveMinLater },
                },
              },
            },
          },
          {
            $lookup: {
              from: 'event_attendees',
              localField: '_id',
              foreignField: 'eventObjectId',
              as: 'attendees',
            },
          },
        ]);
        await Promise.allSettled(
          events.map(async (event) => {
            const community = await CommunityModel.findById(
              event?.communities?.[0]
            );
            const isEnabled = await mailConfigService.getIsMailEnabled({
              mailType,
              mailCourseOffer: event?._id.toString(),
              mailCourse: community?.code,
            });
            if (!isEnabled) {
              logger.info(
                `sendEventReminderEmails: ${type} is not enabled for event ${event._id}`
              );
              return;
            }
            const attendees = event.attendees;
            logger.info(
              `sendEventReminderEmails: Sending reminder emails to all attendees of event ${event?._id}`
            );
            await Promise.allSettled(
              attendees.map(async (attendee) => {
                if (attendee?.learnerObjectId) {
                  if (
                    attendee.status !==
                    COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING
                  ) {
                    return;
                  }
                  try {
                    try {
                      logger.info(
                        'sendEventReminderEmails: Sending event rsvp reminder email'
                      );
                      await eventCommonService.formatAndSendEventTypeEmail(
                        {
                          mailType,
                          event,
                          community,
                          learnerObjectId: attendee?.learnerObjectId,
                        }
                      );
                    } catch (err) {
                      logger.error(
                        'Error sending rsvp reminder email: ',
                        err,
                        err.stack
                      );
                      throw err;
                    }
                  } catch (error) {
                    logger.error(
                      'Error setting up and sending the email data:',
                      error,
                      error.stack
                    );
                    throw new Error(
                      'Error setting up and sending the email data: ',
                      error
                    );
                  }
                }
              })
            );
          })
        );
      })
    );
    return {};
  } catch (error) {
    logger.error(`sendEventReminderEmails error|error=${error}`);
    throw new Error('Cannot delete event with given params: ', params);
  }
};

const deleteOneEvent = async (eventId, userLearnerObjectId) => {
  try {
    logger.info(
      `deleteOneEvent: Delete event ${eventId} by user ${userLearnerObjectId}`
    );
    let event = await EventModel.findById(eventId).lean();
    const communityId = event?.communities?.[0];
    const [attendees, community, publishedEventsCount] = await Promise.all(
      [
        EventAttendees.find({
          eventObjectId: event?._id,
        }).lean(),
        CommunityModel.findById(communityId).lean(),
        EventModel.countDocuments({
          communities: communityId,
          status: EVENT_STATUS.PUBLISHED,
        }),
      ]
    );

    if (!community) {
      throw new ParamError('Invalid community id');
    }

    const wasAPublishedEvent = event.status === EVENT_STATUS.PUBLISHED;

    const discordEventId = event?.discordEventId;
    // check if discordEventId exists
    if (discordEventId) {
      // delete discord event
      logger.info(
        'deleteOneEvent: Event has discordEventId. Deleting discord event',
        discordEventId
      );
      try {
        const discordServer = community?.bots?.filter(
          (bot) => bot?.type === 'Discord'
        );
        if (discordServer?.length > 0) {
          const serverKey = discordServer?.[0]?.serverKey;
          const requestUrl = `${DISCORD_URL}/api/v1/deleteEvent/${serverKey}/${discordEventId}`;
          const authHeader = jwt.sign({}, DISCORD_AUTH);
          const response = await axios.delete(requestUrl, {
            headers: {
              Authorization: `Bearer ${authHeader}`,
            },
          });
        }
      } catch (error) {
        logger.error('Error deleting discord event: ', error, error.stack);
        throw new Error('Error deleting discord event: ', error);
      }
    }

    event = await withTransaction(async (session) => {
      const updatedEvent = await EventModel.findByIdAndUpdate(
        eventId,
        { isActive: false, status: EVENT_STATUS.DELETED },
        { session, new: true }
      ).lean();

      const product = await SyncProductDataService.syncProductData({
        productType: PRODUCT_TYPE.EVENT,
        entity: updatedEvent,
        session,
      });

      // Log product deletion
      const changeLog =
        await ProductChangeLogService.addCommunityProductLog({
          communityObjectId: updatedEvent.communities?.[0],
          communityCode: community.code,
          productType: PRODUCT_TYPE.EVENT,
          entityObjectId: updatedEvent._id,
          changeLogType: PRODUCT_CHANGE_LOG_TYPE.PRODUCT_DELETED,
          operatorLearnerObjectId: userLearnerObjectId,
          metadata: { product },
        });
      updatedEvent.changeLogId = changeLog._id;

      return updatedEvent;
    });

    // update video folder item status to deleted
    const coverMediaItems = event?.coverMediaItems ?? [];
    if (hasVideoCoverMediaItems(coverMediaItems)) {
      await deleteCoverMediaItems({
        coverMediaItems: coverMediaItems ?? [],
      });
    }

    await affiliateProductService.disableAffiliateProduct({
      communityObjectId: community._id,
      entityType: PURCHASE_TYPE.EVENT,
      entityObjectId: eventId,
    });

    try {
      logger.info('Sending event cancelled email to manager');
      await eventCommonService.formatAndSendEventTypeEmail({
        mailType: EVENT_MAIL_TYPES.MANAGER_COMMUNITY_EVENT_CANCELLED_V2,
        learnerObjectId: userLearnerObjectId,
        event,
        community,
      });
    } catch (err) {
      logger.error(
        'Error sending event cancelled email to manager: ',
        err
      );
      throw err;
    }

    // Notes: move the mail notification for event cancelled to services/product/productChangeLog.service.js

    // Delete abandoned checkout emails
    await removeEmailReminderForAbandonedCheckout({
      entityType: PURCHASE_TYPE.EVENT,
      entityObjectId: eventId,
    });

    // purge landing page cache
    if (wasAPublishedEvent) {
      await purgeEntityLandingPageCache({
        community,
        purgeCommunityLandingPage: publishedEventsCount === 1, // if deleted an only published event.
        entityType: ENTITY_LANDING_PAGE.EVENT,
        entitySlug: event.slug,
      });
    }
    return event;
  } catch (error) {
    logger.error('Error deleting event:', error, error.stack);
    throw new Error(
      `Cannot delete event ${eventId} by user: `,
      userLearnerObjectId
    );
  }
};
//END OF EVENT DELETE

const updateOneEvent = async (eventId, params, timezoneId) => {
  const event = await EventModel.findById(eventId).lean();

  if (!event) {
    throw new ResourceNotFoundError('Event not found');
  }

  const firstCommunity = await CommunityModel.findById(
    event.communities?.[0],
    {
      baseCurrency: 1,
      code: 1,
      request_approval: 1,
      link: 1,
    }
  )
    .read('primary')
    .lean();

  if (!firstCommunity) {
    throw new ResourceNotFoundError(
      `Community not found for ${event.communities?.[0]}`
    );
  }

  const paramsToUpdate =
    await eventCommonService.validateAndFormatEventParams(
      params,
      firstCommunity,
      event
    );

  if (!paramsToUpdate) {
    throw new ParamError('No params to update');
  }

  if (
    paramsToUpdate.isRegistrationClosed !== undefined &&
    paramsToUpdate.isRegistrationClosed !== null
  ) {
    paramsToUpdate.isSoldOut = paramsToUpdate.isRegistrationClosed;
  }

  await discountValidationForEntities(
    firstCommunity.code,
    params.newDiscountsToApply,
    params.discountsToAdd
  );

  let updatedData = await EventModel.findByIdAndUpdate(
    eventId,
    paramsToUpdate,
    {
      new: true,
    }
  ).lean();

  if (params.coverMediaItems && params?.coverMediaItems?.length > 0) {
    // delete removed video cover media items by marking folder item status as deleted.
    const oldCoverMediaItems = event?.coverMediaItems ?? [];
    const newCoverMediaItems = updatedData?.coverMediaItems ?? [];
    if (
      Array.isArray(oldCoverMediaItems) &&
      hasVideoCoverMediaItems(oldCoverMediaItems)
    ) {
      await deleteRemovedVideoCoverMediaItems({
        oldCoverMediaItems,
        newCoverMediaItems,
      });
    }
  }

  try {
    const eventIcs = await eventIcsService.fetchIcsFileUrl(updatedData);
    logger.info(
      'Generated ics file link while updating event: ',
      eventIcs
    );
    updatedData = await EventModel.findByIdAndUpdate(
      eventId,
      { icsFileLink: eventIcs },
      {
        new: true,
      }
    ).lean();
  } catch (error) {
    logger.error('Unable to fetch ics file while updating event: ', error);
    throw new Error(
      `Unable to fetch ics file while updating event: ${error.message}`
    );
  }

  if (
    params.newDiscountsToApply ||
    params.discountsToRemove ||
    params.discountsToAdd ||
    params.discountsToDisable
  ) {
    updatedData = await discountCreationForEntities(
      firstCommunity.code,
      event,
      updatedData,
      PURCHASE_TYPE.EVENT,
      params?.createdBy,
      EventModel,
      params.newDiscountsToApply,
      params.discountsToRemove,
      params.discountsToAdd,
      params.discountsToDisable
    );
  }

  // TODO - @AmanMinhas - handle inPersonLocationMetadata below
  // remove inPersonLocation if liveLink is present in payload and vice versa
  if (paramsToUpdate?.liveLink && !paramsToUpdate?.inPersonLocation) {
    updatedData.inPersonLocation = '';
  }
  if (paramsToUpdate?.inPersonLocation && !paramsToUpdate?.liveLink) {
    updatedData.liveLink = '';
  }
  let communities;
  try {
    communities = await getCommunitiesByIds(updatedData.communities);
    if (!communities) {
      const error = new Error('Community not found');
      throw new Error('Error finding community: ', error);
    }
  } catch (err) {
    const error = `Community not found ${JSON.stringify(err)}`;
    throw new Error('Error finding community: ', error);
  }
  const discordEventId = updatedData?.discordEventId;
  const communityIds = updatedData.communities;
  const communityCodes = await getCommunityCodesByIds(communityIds);
  const currentTime = getCurrentUTCTime();
  // check if discordEventId exists, discord doesn't allow updating finished events
  if (discordEventId && updatedData?.endTime > currentTime) {
    // update the event with the new data from the event
    try {
      const communitiesPromises = communities?.map(async (community) => {
        const communityLink = community?.link;
        const eventLink = communityLink
          ? `${NAS_IO_FRONTEND_URL}${communityLink}${updatedData?.slug}`
          : NAS_IO_FRONTEND_URL;
        const discordServer = community?.bots?.find(
          (bot) => bot?.type === 'Discord'
        );
        if (discordServer) {
          const serverKey = discordServer?.serverKey;
          const discordEventData = {
            startTime: updatedData?.startTime,
            endTime: updatedData?.endTime,
            name: updatedData?.title,
            description: updatedData?.description,
            location: eventLink,
          };
          logger.info(
            'Updating discord event ',
            JSON.stringify(discordEventData)
          );
          const requestUrl = `${DISCORD_URL}/api/v1/editEvent/${serverKey}/${discordEventId}`;
          const authHeader = jwt.sign({}, DISCORD_AUTH);
          const response = await axios.patch(
            requestUrl,
            discordEventData,
            {
              headers: {
                Authorization: `Bearer ${authHeader}`,
              },
            }
          );
          if (response?.data) {
            logger.info('Discord event update response: ', response?.data);
          }
        }
      });
      await Promise.all(communitiesPromises);
    } catch (error) {
      const errorMessage = 'Unable to update discord event';
      logger.error(`updateOneEvent error|error=${errorMessage}`);
      error.message = errorMessage;
      error.status = error.INTERNAL_SERVER_ERROR;
      throw error;
    }
  }

  const [attendees, existsPendingAttendee] = await Promise.all([
    EventAttendees.aggregate([
      {
        $match: {
          eventObjectId: new ObjectId(eventId),
          status: COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING,
        },
      },
      {
        $lookup: {
          from: 'learners',
          localField: 'learnerObjectId',
          foreignField: '_id',
          as: 'learner',
        },
      },
      {
        $unwind: {
          path: '$learner',
          preserveNullAndEmptyArrays: true,
        },
      },
    ]),
    EventAttendees.exists({
      eventObjectId: eventId,
      status: COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING,
    }),
  ]);

  const userIds = attendees.map((attendee) => {
    return attendee.learner?.learnerId;
  });
  // Refactored: fieldsChanged is an array of field names, additionalFields is an object with previous values
  const fieldsChanged = [];
  const additionalFields = {};

  if (
    updatedData?.startTime.toISOString() !== event?.startTime.toISOString()
  ) {
    fieldsChanged.push('startTime');
    additionalFields['previous_event_start_date_iso'] =
      event?.startTime?.toISOString();
  }
  if (
    updatedData?.endTime.toISOString() !== event?.endTime.toISOString()
  ) {
    fieldsChanged.push('endTime');
    additionalFields['previous_event_end_date_iso'] =
      event?.endTime?.toISOString();
  }
  if (updatedData?.title !== event?.title) {
    fieldsChanged.push('title');
    additionalFields['previous_event_name'] = event?.title;
    additionalFields['previous_title'] = event?.title;
  }
  // Location-related changes
  if (updatedData.type !== event?.type) {
    fieldsChanged.push('type');
  }
  if (updatedData?.liveLink !== event?.liveLink) {
    fieldsChanged.push('liveLink');
  }
  if (updatedData?.inPersonLocation !== event?.inPersonLocation) {
    fieldsChanged.push('inPersonLocation');
  }
  // Add for recordingLink
  if (updatedData?.recordingLink !== event?.recordingLink) {
    fieldsChanged.push('recordingLink');
  }
  if (
    updatedData.type !== event?.type ||
    updatedData?.liveLink !== event?.liveLink ||
    updatedData?.inPersonLocation !== event?.inPersonLocation
  ) {
    additionalFields['previous_event_location'] =
      event?.type === 'live' ? event?.liveLink : event?.inPersonLocation;
  }
  if (
    fieldsChanged.length > 0 &&
    [EVENT_STATUS.PUBLISHED, 'Active'].includes(updatedData.status)
  ) {
    const community = await CommunityModel.findById(
      event.communities[0]
    ).lean();
    if (!community) throw new Error('Community not found');

    // Send manager notification
    await eventCommonService.formatAndSendEventTypeEmail({
      mailType: EVENT_MAIL_TYPES.MANAGER_COMMUNITY_EVENT_UPDATE_V2,
      learnerObjectId: event.createdByLearnerObjectId,
      event: updatedData,
      community,
      additionalFields,
    });

    // Determine changeLogType
    let changeLogType = PRODUCT_CHANGE_LOG_TYPE.FIELD_UPDATED;
    if (
      fieldsChanged.length === 1 &&
      fieldsChanged[0] === 'recordingLink'
    ) {
      changeLogType = PRODUCT_CHANGE_LOG_TYPE.EVENT_RECORD_ADDED;
    }

    // Log community product changes for notify attendees
    const changeLog = await ProductChangeLogService.addCommunityProductLog(
      {
        communityObjectId: event.communities[0],
        communityCode: community.code,
        productType: PRODUCT_TYPE.EVENT,
        entityObjectId: updatedData._id,
        changeLogType,
        fieldsChanged,
        beforeData: event,
        afterData: updatedData,
        operatorLearnerObjectId: params?.createdByLearnerObjectId,
        metadata: additionalFields,
      }
    );

    updatedData.changeLogId = changeLog._id;

    if (paramsToUpdate?.startTime) {
      await updateEventScheduledNotifications(
        eventId,
        paramsToUpdate?.startTime
      );
    }
  }

  await SyncProductDataService.syncProductData({
    productType: PRODUCT_TYPE.EVENT,
    entity: updatedData,
  });

  // to move to worker pool
  checkEventForFraud({
    community: firstCommunity,
    eventId: updatedData._id,
    updatedPayload: updatedData,
  });

  updatedData.isRegistrationClosed = updatedData.isSoldOut;

  const requiresApprovalEmail =
    !!existsPendingAttendee || (updatedData.requiresApproval ?? false);
  updatedData.requiresApprovalEmail = requiresApprovalEmail;

  // purge landing page cache
  if (updatedData?.status === EVENT_STATUS.PUBLISHED) {
    await purgeEntityLandingPageCache({
      community: firstCommunity,
      purgeCommunityLandingPage: false, // on update action, only purge event landing page
      entityType: ENTITY_LANDING_PAGE.EVENT,
      entitySlug: updatedData.slug,
    });
  }

  return updatedData;
};

async function countGoingAttendees(eventId) {
  const count = await EventAttendees.countDocuments({
    eventObjectId: eventId,
    status: {
      $in: [
        COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING,
        COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING,
      ],
    },
  });

  return count;
}

async function countAttendeesIncludingPending(
  event,
  quantity = 1,
  email = ''
) {
  const [goingAttendees, community] = await Promise.all([
    EventAttendees.find(
      {
        eventObjectId: event._id,
        status: {
          $in: [
            COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING,
            COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING,
          ],
        },
      },
      { email: 1, quantity: 1 }
    )
      .lean()
      .read('primary'),
    CommunityModel.findById(event.communities[0], { code: 1 }).lean(),
  ]);

  if (!community) {
    throw new InternalError('Invalid community for event');
  }

  const attendeesEmail = goingAttendees.map((attendee) => attendee.email);

  const subscriptions = await SubscriptionModel.find(
    {
      communityCode: community.code,
      email: { $nin: attendeesEmail },
      status: communityEnrolmentStatuses.PENDING,
    },
    { communitySignupId: 1 }
  ).lean();

  const purchaseTransactionObjectIds = subscriptions.map(
    ({ communitySignupId }) => new ObjectId(communitySignupId)
  );

  const purchaseTransactions = await PurchaseTransactionModel.find(
    {
      _id: { $in: purchaseTransactionObjectIds },
      'post_approval_processes.origin': event._id,
    },
    { email: 1, 'post_approval_processes.$': 1 }
  ).lean();

  const pendingSubscriptionApprovalEmails = purchaseTransactions.map(
    (purchaseTransaction) => purchaseTransaction.email
  );

  const pendingTransactions = await PendingTransaction.find(
    {
      communityObjectId: event.communities[0],
      entity: PENDING_TRANSACTION_ENTITY_TYPE.EVENT,
      entityObjectId: event._id,
      email: {
        $nin: [...attendeesEmail, ...pendingSubscriptionApprovalEmails],
      },
    },
    { email: 1, quantity: 1 }
  ).lean();

  const attendeeQuantity = goingAttendees.reduce(
    (acc, attendee) => acc + (attendee.quantity ?? 1),
    0
  );

  const pendingSubscriptionApprovalQuantity = purchaseTransactions.reduce(
    (acc, transaction) =>
      acc + (transaction.post_approval_processes?.[0]?.quantity ?? 1),
    0
  );

  const pendingTransactionQuantity = pendingTransactions.reduce(
    (acc, transaction) => acc + (transaction.quantity ?? 1),
    0
  );

  let totalQuantity =
    attendeeQuantity +
    pendingSubscriptionApprovalQuantity +
    pendingTransactionQuantity;

  if (email) {
    const reducePendingTransaction = pendingTransactions.find(
      (pendingTransaction) => pendingTransaction.email === email
    );

    if (reducePendingTransaction) {
      totalQuantity -= reducePendingTransaction.quantity ?? 1;
      totalQuantity += quantity;
    }
  }

  return totalQuantity;
}

async function getAttendeeCountsByStatus(eventId) {
  const eventAttendeesList = await EventAttendees.find({
    eventObjectId: eventId,
  }).lean();

  const counts = eventAttendeesList.reduce(
    (acc, attendee) => {
      const { quantity, status } = attendee;
      switch (status) {
        case COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING:
          acc.pendingAttendeesCount += quantity ?? 1;
          break;
        case COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING:
          acc.goingAttendeesCount += quantity ?? 1;
          break;
        case COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.NOT_GOING:
          acc.notGoingAttendeesCount += quantity ?? 1;
          break;
        case COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.REJECTED:
          acc.rejectedAttendeesCount += quantity ?? 1;
          break;
        default:
          break;
      }
      return acc;
    },
    {
      pendingAttendeesCount: 0,
      goingAttendeesCount: 0,
      notGoingAttendeesCount: 0,
      rejectedAttendeesCount: 0,
    }
  );
  return counts;
}

async function checkAttendeeLimitReached(event, quantity, email) {
  if (!event.attendeeLimit || !event.isCapacitySet) {
    return false;
  }

  const attendeeCount = await countAttendeesIncludingPending(
    event,
    quantity,
    email
  );

  // The attendee count includes the number of tickets being purchased in this request
  return attendeeCount > event.attendeeLimit;
}

const getEventFromSlug = async (community, slug) => {
  // TODO: Event Draft - To test flow here
  const communityObjectId = community?._id;
  const event = await EventModel.findOne({
    $and: [
      { communities: { $elemMatch: { $eq: communityObjectId } } },
      { slug },
      {
        status: {
          $in: [EVENT_STATUS.DRAFT, EVENT_STATUS.PUBLISHED, 'Active'],
        },
      },
    ],
  }).lean();

  if (!event) {
    const errorMessage = `No events found with slug: ${slug}`;

    throw new ResourceNotFoundError(errorMessage);
  }
  if (!event.communities[0]) {
    const errorMessage = `No community attached to event with slug: ${slug}`;
    throw new Error(errorMessage);
  }

  event.coverMediaItems = await generateCoverMediaItems({
    entity: event,
    entityType: COVER_MEDIA_ENTITY_TYPES.EVENT,
  });

  return event;
};

const getCommunityEventPage = async (
  community,
  event,
  ip = null,
  quantity = 1,
  selectedAmount = null,
  affiliateCode = null,
  paymentMethodCountryCode = null,
  paymentProvider = null
) => {
  try {
    const communityObjectId = community?._id;
    const eventInfo = { ...event };
    eventInfo.isRegistrationClosed = eventInfo.isSoldOut;

    if (event?.access === COMMUNITY_EVENT_ACCESS_TYPES.PAID) {
      const priceDetails = await getAddonPriceInLocalCurrency({
        ip,
        addon: event,
        communityObjectId,
        quantity,
        selectedAmount,
        paymentMethodCountryCode,
        paymentProvider,
      });
      logger.info('Price in local currency: ', priceDetails);
      eventInfo.priceDetails = priceDetails;
    }

    const priceFieldsToRemove = [
      'localiseForAllCountries',
      'countryWisePrice',
    ];
    priceFieldsToRemove.forEach(
      (priceFieldToRemove) => delete eventInfo[priceFieldToRemove]
    );
    const communityInfo = community;

    const memberCountInfo =
      await membershipService.countService.countCommunityMembers({
        communityCode: community?.code,
      });

    communityInfo.members = memberCountInfo?.count;
    communityInfo.totalMemberCount = memberCountInfo?.count;
    if (communityInfo?.isWhatsappExperienceCommunity) {
      const communityBots = communityInfo?.bots ?? [];
      const whatsappBot = communityBots.filter(
        (bot) => bot.type === 'Whatsapp'
      );
      if (whatsappBot.length > 0 && whatsappBot[0]?.serverKey) {
        const whatsappGroup = await WhatsappConnectedGroup.findOne({
          whatsappGroupId: whatsappBot[0]?.serverKey,
        }).select('name');
        if (whatsappGroup) {
          communityInfo.whatsappGroupName = whatsappGroup.name;
          communityInfo.whatsappMemberCount =
            memberCountInfo?.statusBreakdown?.inWhatsappGroup;
        }
      }
    }
    communityInfo.profileImage =
      communityInfo.thumbnailImgData.desktopImgData.src;

    const attendees = await EventAttendees.countDocuments({
      eventObjectId: new ObjectId(event._id),
      status: {
        $in: [
          COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING,
          COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING,
        ],
      },
    });
    eventInfo['attendees'] = attendees;

    const dpLimit = 5; // number of display images to use
    const learners = await eventAttendees.getEventAttendeesProfileImage(
      event._id,
      dpLimit
    );

    const profileImages = learners.map(
      ({ learnerProfileImage }) => learnerProfileImage
    );

    eventInfo['profileImages'] = profileImages;

    const affiliateInfo = await affiliateService.retrieveAffiliateInfo({
      communityObjectId,
      affiliateCode,
      entityObjectId: event._id,
      entityType: PURCHASE_TYPE.EVENT,
    });

    eventInfo.affiliateInfo = affiliateInfo;

    return { eventInfo, communityInfo };
  } catch (err) {
    logger.error(`getCommunityEventPage error`, err, err.stack);
    throw err;
  }
};

async function getEventsAttendeesDetails(eventObjectIds, sortOrder) {
  // Retrieve all events based on eventObjectIds
  const events = await EventModel.find(
    {
      _id: { $in: eventObjectIds },
    },
    { _version: 0, _validity: 0 }
  )
    .sort({ startTime: sortOrder })
    .lean();

  // Retrieve 3 event attendees profile image and
  // retrieve all going event attendees count for each event
  const NO_OF_PROFILE_IMAGES = 3;

  const eventsInfo = await Promise.all(
    events.map(async (event) => {
      const [
        eventAttendeesProfileImage,
        goingEventAttendeesCount,
        coverMediaItems,
      ] = await Promise.all([
        eventAttendees.getEventAttendeesProfileImage(
          event._id,
          NO_OF_PROFILE_IMAGES
        ),
        eventAttendees.getEventAttendeesCount({
          eventObjectId: event._id,
          status: COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING,
        }),
        generateCoverMediaItems({
          entity: event,
          entityType: COVER_MEDIA_ENTITY_TYPES.EVENT,
        }),
      ]);

      event.coverMediaItems = coverMediaItems;

      const profileImages = eventAttendeesProfileImage.map(
        ({ learnerProfileImage }) => learnerProfileImage
      );

      event.attendees = goingEventAttendeesCount;
      event.goingAttendees = goingEventAttendeesCount;
      event.profileImages = profileImages;

      return event;
    })
  );

  return eventsInfo;
}

const getCommunityEvents = async (
  eventFilter,
  sortOrder = 1,
  paginate = 0,
  pageNum = null,
  pageSize = null,
  search = null
) => {
  try {
    if (eventFilter?._id) {
      // eslint-disable-next-line no-param-reassign
      eventFilter._id = new ObjectId(eventFilter._id);
    }
    if (eventFilter?.communities) {
      // eslint-disable-next-line no-param-reassign
      eventFilter.communities = new ObjectId(eventFilter.communities);
    }
    const eventAggregationPipeline = [
      {
        $match: eventFilter,
      },
      {
        $sort: {
          startTime: sortOrder,
        },
      },
    ];
    if (search && search.trim()) {
      const searchWithEscapedRegexSign = regexUtils.escapeRegExp(search);
      eventAggregationPipeline.push({
        $match: {
          title: {
            $regex: `${searchWithEscapedRegexSign}`,
            $options: 'i',
          },
        },
      });
    }

    eventAggregationPipeline.push({
      $project: {
        _id: 1,
      },
    });

    if (paginate === 1) {
      const paginationDataAggregatePipelineStage =
        getPaginationDataAggregatePipelineStage(pageNum, pageSize);

      const paginationPipeline = {
        $facet: {
          metadata: [
            {
              $count: 'total',
            },
          ],
          data: paginationDataAggregatePipelineStage,
        },
      };
      eventAggregationPipeline.push(paginationPipeline);
      eventAggregationPipeline.push({ $unwind: '$metadata' });
    }
    let results = await EventModel.aggregate(eventAggregationPipeline);
    if (paginate === 1) {
      results = results?.[0] || null;
      if (!results) {
        results = defaultPaginatedResponse;
      }
      const { next = null, previous = null } = getNextAndPrevious(
        pageNum,
        pageSize,
        results?.metadata?.total
      );
      results.metadata.next = next;
      results.metadata.previous = previous;

      const eventObjectIds = results.data.map((event) => event._id);
      results.data = await getEventsAttendeesDetails(
        eventObjectIds,
        sortOrder
      );
    } else {
      const eventObjectIds = results.map((event) => event._id);
      results = await getEventsAttendeesDetails(eventObjectIds, sortOrder);
    }
    return results;
  } catch (err) {
    logger.error(`getCommunityEvents error|error=${err.message}`);
    throw err;
  }
};

const isLearnerRegisteredForEvent = async (
  eventObjectId,
  learnerObjectId
) => {
  try {
    const eventAttendee = await EventAttendees.findOne(
      {
        eventObjectId,
        learnerObjectId,
      },
      { status: 1, quantity: 1, ticketReferences: 1 }
    ).lean();

    const registered =
      eventAttendee?.status ===
        COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING ||
      eventAttendee?.status ===
        COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING;

    return {
      eventAttendee,
      registered,
      pendingApproval:
        eventAttendee?.status ===
        COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING,
    };
  } catch (err) {
    logger.error(`isLearnerRegisteredForEvent error|error=${err.message}`);
    throw err;
  }
};

const getPastEventsByCommunityId = async (
  learnerObjectId,
  userObjectId,
  communityObjectId,
  paginate,
  pageNum,
  pageSize,
  sortParams,
  search
) => {
  const eventFilter = {
    communities: communityObjectId,
    status: { $in: [EVENT_STATUS.PUBLISHED, 'Active'] },
    endTime: { $lt: DateTime.utc() },
  };
  const pastEvents = await getCommunityEvents(
    eventFilter,
    -1,
    paginate,
    pageNum,
    pageSize,
    search
  );

  if (learnerObjectId && userObjectId) {
    const existingManager = await CommunityRole.exists({
      communityObjectId,
      userObjectId,
    });

    const resultsPromises = pastEvents.data.map(async (event) => {
      const { eventAttendee, registered, pendingApproval } =
        await isLearnerRegisteredForEvent(event._id, learnerObjectId);

      event.eventAttendee = eventAttendee;
      event.registered = registered;
      event.pendingApproval = pendingApproval;

      if (
        !existingManager &&
        event.access === COMMUNITY_EVENT_ACCESS_TYPES.PAID &&
        (!event.registered || event.pendingApproval)
      ) {
        // remove sensitive info
        delete event.liveLink;
        delete event.recordingLink;
        delete event.icsFileLink;
        delete event.chatGroupLink;

        const hideInPersonLocation = event.hideLocation ?? true;

        if (hideInPersonLocation) {
          delete event.inPersonLocation;
          if (event.inPersonLocationMetadata) {
            event.inPersonLocationMetadata =
              EventUtils.formatInPersonLocationForHiddenState(
                event.inPersonLocationMetadata
              );
          }
        }
      }
    });

    await Promise.all(resultsPromises);
  }

  return pastEvents;
};

const getUpcomingEventsByCommunityId = async (
  learnerObjectId,
  userObjectId,
  communityObjectId,
  paginate,
  pageNum,
  pageSize,
  sortParams,
  search
) => {
  // TODO: Event Draft - To test flow here
  let isManagerView = false;
  const statusesVisible = [EVENT_STATUS.PUBLISHED, 'Active'];
  if (learnerObjectId && userObjectId) {
    const existingManager = await CommunityRole.exists({
      communityObjectId,
      userObjectId,
    });
    if (existingManager) {
      isManagerView = true;
      statusesVisible.push(EVENT_STATUS.DRAFT);
    }
  }

  const eventFilter = {
    communities: communityObjectId,
    status: { $in: statusesVisible },
    endTime: { $gt: DateTime.utc() },
  };
  let events = await getCommunityEvents(
    eventFilter,
    1,
    paginate,
    pageNum,
    pageSize,
    sortParams,
    search
  );
  const eventData = paginate === 1 ? events?.data : events;

  if (learnerObjectId && userObjectId) {
    const resultsPromises = eventData.map(async (event) => {
      const coverMediaItems = await generateCoverMediaItems({
        entity: event,
        entityType: COVER_MEDIA_ENTITY_TYPES.EVENT,
        isCommunityManager: isManagerView,
      });
      event.coverMediaItems = coverMediaItems;

      const { eventAttendee, registered, pendingApproval } =
        await isLearnerRegisteredForEvent(event._id, learnerObjectId);

      event.eventAttendee = eventAttendee;
      event.registered = registered;
      event.isRegistrationClosed = event.isSoldOut;
      event.pendingApproval = pendingApproval;

      if (
        !isManagerView &&
        event?.access === COMMUNITY_EVENT_ACCESS_TYPES.PAID &&
        (!event.registered || event.pendingApproval)
      ) {
        // remove sensitive info
        delete event.liveLink;
        delete event.recordingLink;
        delete event.icsFileLink;
        delete event.chatGroupLink;

        const hideInPersonLocation = event.hideLocation ?? true;

        if (hideInPersonLocation) {
          delete event.inPersonLocation;

          if (event.inPersonLocationMetadata) {
            event.inPersonLocationMetadata =
              EventUtils.formatInPersonLocationForHiddenState(
                event.inPersonLocationMetadata
              );
          }
        }
      }
    });
    await Promise.all(resultsPromises);
  }
  if (paginate === 1) {
    events.data = eventData;
  } else {
    events = eventData;
  }
  return events;
};

const getEventById = async (
  communityId = null,
  eventId = null,
  learnerObjectId = null,
  ip = null,
  quantity = 1,
  selectedAmount = null,
  affiliateCode = null,
  paymentMethodCountryCode = null,
  paymentProvider = null
) => {
  try {
    const community = await CommunityModel.findById(
      communityId,
      'link'
    ).lean();

    if (!community) {
      throw new Error(`Event's community doesn't exist!`);
    }

    // TODO: Event Draft - To test flow here
    // the below query will get the event and event attendees data along with the learner profile image
    const event = await EventModel.aggregate([
      {
        $match: {
          _id: new ObjectId(eventId),
          communities: new ObjectId(communityId),
          isActive: true,
        },
      },
      {
        $lookup: {
          from: 'event_attendees',
          let: { eventId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ['$eventObjectId', '$$eventId'],
                },
                status: {
                  $in: [
                    COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING,
                    COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING,
                  ],
                },
              },
            },
            {
              $limit: 5,
            },
            {
              $lookup: {
                from: 'learners',
                localField: 'learnerObjectId',
                foreignField: '_id',
                as: 'learnerObjectId',
              },
            },
            {
              $unwind: '$learnerObjectId',
            },
            {
              $project: {
                _id: 1,
                profileImage: '$learnerObjectId.profileImage',
                email: '$learnerObjectId.email',
                eventObjectId: 1,
              },
            },
          ],
          as: 'attendees',
        },
      },
    ]);

    if (!event?.[0]) {
      const errorMessage = `No event found`;
      logger.error(`getEventById error|error=${errorMessage}`);
      throw new Error(errorMessage);
    }

    const {
      _id,
      communities,
      description,
      host,
      shortUrl,
      attendees,
      slug,
      title,
      bannerImg,
      startTime,
      endTime,
      type,
      liveLink,
      chatGroupLink,
      inPersonLocation,
      recordingLink,
      icsFileLink,
      access,
      amount,
      currency,
      localiseForAllCountries,
      countryWisePrice = DEFAULT_COUNTRY_WISE_PRICE_CONFIG,
      strikeThroughTotal,
      pricePoints,
      discountInfo,
      customDateText = null,
      isSoldOut = false,
      isCapacitySet,
      attendeeLimit,
      hideLocation,
      requiresApproval,
      applicationConfigDataFields,
      customTrackingEvents,
      bulkPurchaseEnabled,
      maxQuantityPerPurchase,
      pricingConfig,
    } = event?.[0];

    const isRegistrationClosed = isSoldOut;
    if (!event?.[0]?.communities?.[0]) {
      const errorMessage = `No community attached to event with slug: ${slug}`;
      logger.error(`getEventById error|error=${errorMessage}`);
      throw new Error(errorMessage);
    }

    const eventInfo = {
      _id,
      communities,
      slug,
      title,
      bannerImg,
      description,
      host,
      shortUrl,
      attendees,
      startTime,
      endTime,
      type,
      liveLink,
      inPersonLocation,
      recordingLink,
      chatGroupLink,
      icsFileLink,
      access,
      amount,
      currency,
      localiseForAllCountries,
      countryWisePrice,
      strikeThroughTotal,
      pricePoints,
      discountInfo,
      customDateText,
      isRegistrationClosed,
      isCapacitySet,
      attendeeLimit,
      hideLocation,
      requiresApproval,
      applicationConfigDataFields,
      customTrackingEvents,
      bulkPurchaseEnabled,
      maxQuantityPerPurchase,
      pricingConfig,
    };

    if (eventInfo?.access === COMMUNITY_EVENT_ACCESS_TYPES.PAID) {
      const priceDetails = await getAddonPriceInLocalCurrency({
        ip,
        addon: eventInfo,
        communityObjectId: communityId,
        quantity,
        selectedAmount,
        paymentMethodCountryCode,
        paymentProvider,
      });
      logger.info('Price in local currency: ', priceDetails);
      eventInfo.priceDetails = priceDetails;
    }
    const priceFieldsToRemove = [
      'localiseForAllCountries',
      'countryWisePrice',
    ];
    priceFieldsToRemove.forEach(
      (priceFieldToRemove) => delete eventInfo[priceFieldToRemove]
    );

    if (learnerObjectId) {
      logger.info(
        `Checking if learner ${learnerObjectId} is registered for event ${event.eventInfo?._id}`
      );

      const { eventAttendee, registered, pendingApproval } =
        await isLearnerRegisteredForEvent(eventInfo?._id, learnerObjectId);

      eventInfo.eventAttendee = eventAttendee;
      eventInfo.registered = registered;
      eventInfo.isRegistrationClosed = isRegistrationClosed;
      eventInfo.isCapacitySet = isCapacitySet;
      eventInfo.attendeeLimit = attendeeLimit;
      eventInfo.pendingApproval = pendingApproval;

      if (
        eventInfo?.access === COMMUNITY_EVENT_ACCESS_TYPES.PAID &&
        (!eventInfo.registered || eventInfo.pendingApproval)
      ) {
        // remove sensitive info
        delete eventInfo.liveLink;
        delete eventInfo.recordingLink;
        delete eventInfo.icsFileLink;
        delete eventInfo.chatGroupLink;

        const hideInPersonLocation = eventInfo.hideLocation ?? true;

        if (hideInPersonLocation) {
          delete eventInfo.inPersonLocation;

          if (eventInfo.inPersonLocationMetadata) {
            eventInfo.inPersonLocationMetadata =
              EventUtils.formatInPersonLocationForHiddenState(
                eventInfo.inPersonLocationMetadata
              );
          }
        }
      }
    } else {
      // remove sensitive info
      delete eventInfo.liveLink;
      delete eventInfo.recordingLink;
      delete eventInfo.icsFileLink;
      delete eventInfo.chatGroupLink;

      const hideInPersonLocation = eventInfo.hideLocation ?? true;

      if (hideInPersonLocation) {
        delete eventInfo.inPersonLocation;

        if (eventInfo.inPersonLocationMetadata) {
          eventInfo.inPersonLocationMetadata =
            EventUtils.formatInPersonLocationForHiddenState(
              eventInfo.inPersonLocationMetadata
            );
        }
      }
    }

    const currentTime = DateTime.utc();
    let eventStatus = null;

    if (eventInfo?.endTime <= currentTime) {
      eventStatus = COMMUNITY_EVENT_STATUSES.PAST;
    } else if (eventInfo?.startTime > currentTime) {
      eventStatus = COMMUNITY_EVENT_STATUSES.UPCOMING;
    } else if (
      eventInfo?.startTime <= currentTime &&
      eventInfo?.endTime > currentTime
    ) {
      eventStatus = COMMUNITY_EVENT_STATUSES.ONGOING;
    }

    const [goingAttendees, affiliateInfo] = await Promise.all([
      countAttendeesIncludingPending(eventInfo),
      affiliateService.retrieveAffiliateInfo({
        communityObjectId: communities[0],
        affiliateCode,
        entityObjectId: _id,
        entityType: PURCHASE_TYPE.EVENT,
      }),
    ]);

    eventInfo.campaignInfo =
      (await getEntityCampaignInfo({
        entityObjectId: _id,
        entityType: 'event',
      })) || null;
    eventInfo.goingAttendees = goingAttendees;
    eventInfo.status = eventStatus;
    eventInfo.affiliateInfo = affiliateInfo;

    return eventInfo;
  } catch (error) {
    logger.error(
      `Unable to get event by id ${eventId} for community id ${communityId} due to: `,
      error
    );
    throw error;
  }
};

const getEventByIdForAdmin = async (eventId, communityId) => {
  try {
    logger.info(`Getting event by id: ${eventId} ${communityId}`);

    const [eventInfo, community] = await Promise.all([
      EventModel.findOne({
        _id: new ObjectId(eventId),
        communities: { $eq: [new ObjectId(communityId)] },
      }).lean(),
      CommunityModel.findById(communityId, { code: 1 }).lean(),
    ]);

    if (!eventInfo || !community) {
      throw new ResourceNotFoundError(
        'Event or community cannot be found'
      );
    }

    const [
      noOfAttendees,
      discountsApplied,
      existsPendingAttendee,
      attendeeStatusCounts,
      goingAttendees,
      coverMediaItems,
      campaignInfo,
    ] = await Promise.all([
      EventAttendees.countDocuments({
        eventObjectId: eventId,
        status: {
          $in: [
            COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING,
            COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING,
          ],
        },
      }),
      discountService.retrieveAllDiscountsRelatedToEntity(
        eventInfo,
        community.code
      ),
      EventAttendees.exists({
        eventObjectId: eventId,
        status: COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING,
      }),
      getAttendeeCountsByStatus(eventId),
      countAttendeesIncludingPending(eventInfo),
      generateCoverMediaItems({
        entity: eventInfo,
        entityType: COVER_MEDIA_ENTITY_TYPES.EVENT,
        isCommunityManager: true,
      }),
      getEntityCampaignInfo({
        entityObjectId: eventId,
        entityType: 'event',
      }),
    ]);

    eventInfo.goingAttendees = goingAttendees;
    eventInfo.coverMediaItems = coverMediaItems;

    const requiresApprovalEmail =
      !!existsPendingAttendee || (eventInfo.requiresApproval ?? false);

    eventInfo.requiresApprovalEmail = requiresApprovalEmail;

    if (discountsApplied) {
      logger.info(
        `Found discounts for event id ${eventId}:  ${JSON.stringify(
          discountsApplied
        )}`
      );
      eventInfo.discountsApplied = discountsApplied;
    }

    eventInfo.campaignInfo = campaignInfo || null;
    eventInfo.isRegistrationClosed = eventInfo.isSoldOut;
    eventInfo.attendees = noOfAttendees;
    eventInfo.existsPendingAttendee = existsPendingAttendee;
    eventInfo.attendeeStatusCounts = attendeeStatusCounts;
    eventInfo.publishSelectedProductAllowed =
      await PublishProductUsageService.publishSelectedProductAllowed(
        community._id,
        eventInfo.createdAt
      );
    return { data: eventInfo, error: false };
  } catch (error) {
    logger.error(
      `Unable to get event by id ${eventId} for community id ${communityId} due to: `,
      error
    );
    return { data: error, error: true };
  }
};

const getEventBySlug = async (
  communityLink,
  slug,
  learnerObjectId = null,
  ip = null,
  quantity = 1,
  selectedAmount = null,
  affiliateCode = null,
  paymentMethodCountryCode = null,
  paymentProvider = null
) => {
  // const linkRegex = { $regex: new RegExp(`^${communityLink}$`, 'i') };
  // const community = await Community.findOne({ link: linkRegex });
  const community =
    await communityService.getCommunityService.getCommunityByLink({
      link: communityLink,
      projection: {
        $project: {
          createdBy: 0,
          'platforms.link': 0,
          platformPreviousData: 0,
          taskMetaData: 0,
          botsPreviousData: 0,
        },
      },
    });
  if (!community) {
    throw new ResourceNotFoundError(
      `Community doesnt exist with community link: ${communityLink}`
    );
  }

  community.indexable = isCommunityIndexable(community);
  const event = await getEventFromSlug(community, slug);
  let isManagerView = false;
  if (learnerObjectId) {
    const user = await UserModel.findOne(
      { learner: learnerObjectId, isActive: true },
      { _id: 1 }
    ).lean();

    if (!user) {
      throw new ResourceNotFoundError('User not found');
    }

    const existingManager = await CommunityRole.findOne({
      communityObjectId: community._id,
      userObjectId: user._id,
    }).lean();

    if (existingManager) {
      isManagerView = true;
    } else if (event.status === EVENT_STATUS.DRAFT) {
      throw new UnauthorizedError(
        'You have no access to this event draft'
      );
    }
  }

  const eventPageInfo = await getCommunityEventPage(
    community,
    event,
    ip,
    quantity,
    selectedAmount,
    affiliateCode,
    paymentMethodCountryCode,
    paymentProvider
  );
  const goingAttendees = await countAttendeesIncludingPending(
    eventPageInfo.eventInfo
  );
  eventPageInfo.eventInfo.goingAttendees = goingAttendees;

  if (learnerObjectId) {
    const { eventAttendee, registered, pendingApproval } =
      await isLearnerRegisteredForEvent(
        eventPageInfo.eventInfo?._id,
        learnerObjectId
      );

    eventPageInfo.eventAttendee = eventAttendee;
    eventPageInfo.registered = registered;
    eventPageInfo.pendingApproval = pendingApproval;

    if (
      (eventPageInfo.registered && !eventPageInfo.pendingApproval) ||
      eventPageInfo.eventInfo.access ===
        COMMUNITY_EVENT_ACCESS_TYPES.FREE ||
      isManagerView
    ) {
      return eventPageInfo;
    }
  }

  // remove sensitive info
  delete eventPageInfo.eventInfo.liveLink;
  delete eventPageInfo.eventInfo.recordingLink;
  delete eventPageInfo.eventInfo.icsFileLink;
  delete eventPageInfo.eventInfo.chatGroupLink;

  const hideInPersonLocation =
    eventPageInfo.eventInfo.hideLocation ?? true;

  if (hideInPersonLocation) {
    delete eventPageInfo.eventInfo.inPersonLocation;

    if (eventPageInfo.eventInfo.inPersonLocationMetadata) {
      eventPageInfo.eventInfo.inPersonLocationMetadata =
        EventUtils.formatInPersonLocationForHiddenState(
          eventPageInfo.eventInfo.inPersonLocationMetadata
        );
    }
  }

  return eventPageInfo;
};

// TODO: Event Draft - To test flow here
const getCommunityEventByEventId = async (eventObjectId) => {
  const eventFilter = {
    _id: eventObjectId,
    isActive: true,
  };
  const event = await getCommunityEvents(eventFilter);
  if (!event?.length) {
    throw new Error(
      `Something went wrong while getting community event by community event Id ${eventObjectId}`
    );
  }
  return event[0];
};

const getEventTicketReferenceForAttendee = ({
  eventObjectId,
  learnerObjectId,
}) => {
  const ticketReference = crypto
    .createHash('sha256')
    .update(`${eventObjectId}-${learnerObjectId}}`)
    .digest('hex')
    .slice(0, 10)
    .toUpperCase();
  return ticketReference;
};

function retrieveApplicationInfo(
  applicationData,
  applicationConfigDataFields
) {
  const applicationInfo = [];

  if (applicationData && applicationConfigDataFields?.length > 0) {
    applicationConfigDataFields.forEach((dataField) => {
      const { fieldName } = dataField;
      const answer = applicationData?.[fieldName];
      const convertedAnswer = convertAnswerToProperType(dataField, answer);
      applicationInfo.push({
        label: dataField?.label,
        fieldDataType: dataField?.fieldDataType,
        isRequired: dataField?.isRequired,
        answer: convertedAnswer,
      });
    });
  }

  return applicationInfo;
}

const createEventAttendee = async ({
  eventAttendee,
  addonTransaction,
  purchaseType,
  event,
  learner,
  session = undefined,
  applicationData = undefined,
  quantity = 1,
}) => {
  let updatedEventAttendee;

  const { _id: learnerObjectId, learnerId, email } = learner;

  const {
    _id: eventObjectId,
    applicationConfigDataFields,
    requiresApproval,
    communities,
    type,
  } = event;

  const isInPersonEvent = type === EVENT_TYPES.INPERSON;
  const communityObjectId = communities?.[0];

  const applicationInfo = retrieveApplicationInfo(
    applicationData,
    applicationConfigDataFields
  );

  if (eventAttendee) {
    updatedEventAttendee = await EventService.updateEventAttendeeStatus({
      eventId: eventObjectId,
      attendeeId: eventAttendee._id,
      learnerObjectId,
      email,
      applicationInfo,
      session,
      requiresApproval,
      addonTransaction,
      isInPersonEvent,
    });
  } else {
    const eventAttendeeStatus = requiresApproval
      ? COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING
      : COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING;

    const {
      amount = 0,
      currency = DEFAULT_CURRENCY,
      local_amount: localAmount = 0,
      local_currency: localCurrency = DEFAULT_CURRENCY,
      _id: addonTransactionObjectId,
    } = addonTransaction ?? {};

    const multipleTicketReferenceInfo =
      await EventUtils.generateTicketReferences({
        eventObjectId,
        communityObjectId,
        learnerObjectId,
        quantity,
        shouldCreateQrCodeTicket:
          isInPersonEvent &&
          eventAttendeeStatus ===
            COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING,
      });

    const mainTicketReference =
      multipleTicketReferenceInfo[0].ticketReference;

    const dataToInsert = {
      communityObjectId,
      eventObjectId,
      purchaseType,
      email,
      status: eventAttendeeStatus,
      learnerObjectId,
      learnerId,
      ticketReference: mainTicketReference,
      amount,
      currency,
      local_amount: localAmount,
      local_currency: localCurrency,
      eventCheckoutId: addonTransactionObjectId,
      ticketReferences: multipleTicketReferenceInfo,
      quantity,
    };

    if (applicationInfo.length > 0) {
      dataToInsert.applicationInfo = applicationInfo;
    }

    updatedEventAttendee = await EventAttendees.findOneAndUpdate(
      {
        eventObjectId,
        learnerObjectId,
      },
      dataToInsert,
      { upsert: true, new: true, session }
    ).lean();

    await batchMetadataService.add({
      batchMetadataModelType: BATCH_METADATA_MODEL_TYPE.EVENT_ATTENDEE,
      entityObjectId: eventObjectId,
      communityObjectId: updatedEventAttendee.communityObjectId,
      addedObjectId: updatedEventAttendee._id,
    });
  }

  let mailType = EVENT_MAIL_TYPES.MEMBER_COMMUNITY_EVENT_RSVP;

  let generateReceipt =
    addonTransaction &&
    addonTransaction.amount > 0 &&
    updatedEventAttendee.status ===
      COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING;

  try {
    if (
      updatedEventAttendee.status ===
      COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING
    ) {
      if (
        (addonTransaction && addonTransaction.amount === 0) ||
        !addonTransaction
      ) {
        await ActionEventService.sendFreeAddonActionEvent({
          actionEventType: ADDON_ACTION_EVENT_TYPES.EVENT_SIGNUP,
          entityObjectId: event._id,
          entityCollection: COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.EVENT,
          communityObjectId: event.communities[0],
          addonTransaction,
          learner,
          quantity,
        });
      }
    } else {
      mailType = EVENT_MAIL_TYPES.MEMBER_COMMUNITY_EVENT_PENDING_APPROVAL;
      await EventService.sendPendingEventAttendeeMobileNotification({
        event,
        eventAttendee: updatedEventAttendee,
      });
    }

    const config = receiptService.generateReceiptConfig({
      purchasedId: addonTransaction?._id,
      purchaseType: PURCHASE_TYPE.EVENT,
      entityObjectId: event._id,
      communityObjectId: event.communities[0],
      learnerObjectId,
      generateReceipt,
    });

    await EventService.sendEventEmail({
      event,
      eventAttendeeObjectId: updatedEventAttendee._id,
      addonTransactionObjectId: addonTransaction?._id,
      learnerObjectId,
      eventAttendeePurchaseType: updatedEventAttendee.purchaseType,
      ticketReference: updatedEventAttendee.ticketReference,
      ticketReferences: updatedEventAttendee.ticketReferences,
      mailType,
      session,
      quantity,
      config,
    });
  } catch (err) {
    logger.error(`createEventAttendee: sendEventEmail cannot send`);
  }

  return updatedEventAttendee;
};

const registerForEvent = async (
  eventObjectId,
  learnerObjectId,
  purchaseType = COMMUNITY_EVENT_ATTENDEE_PURCHASE_TYPES.FREE,
  email,
  quantity = 1,
  ip = null,
  userAgent = null
) => {
  try {
    const event = await EventModel.findById(eventObjectId).lean();

    if (!event) {
      throw new ToUserError('Invalid event', EVENT_ERROR.INVALID_EVENT);
    }

    const currentDate = new Date();

    if (event.endTime && event.endTime < currentDate) {
      throw new ToUserError(
        'Cannot register for past event',
        EVENT_ERROR.CANNOT_REGISTER_PAST_EVENT
      );
    }

    if (event.isSoldOut) {
      throw new ToUserError('Event is closed', EVENT_ERROR.EVENT_CLOSED);
    }

    const applicationRequired = event.applicationConfigDataFields?.some(
      (field) => field.isRequired
    );

    if (applicationRequired) {
      throw new ParamError('Event require application form');
    }

    /**
     * START OF LOCK
     */
    const lock = await getLock();
    const done = await lock(event._id, 1000);

    const isLimitReached = await checkAttendeeLimitReached(
      event,
      quantity,
      email
    );

    if (event.isCapacitySet && isLimitReached) {
      throw new ToUserError('Event sold out', EVENT_ERROR.EVENT_SOLD_OUT);
    }

    const [addonTransaction, learner, eventAttendee] = await Promise.all([
      AddonTransaction.findOne({
        entityObjectId: new ObjectId(eventObjectId),
        learnerObjectId: new ObjectId(learnerObjectId),
        entityCollection: COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.EVENT,
      })
        .sort({ createdAt: -1 })
        .lean(),
      Learner.findById(learnerObjectId, { email: 1 }).lean(),
      EventAttendees.findOne({
        eventObjectId: new ObjectId(eventObjectId),
        learnerObjectId: new ObjectId(learnerObjectId),
      }).lean(),
    ]);

    logger.info(
      `addOnTransaction found ${JSON.stringify(addonTransaction)}`
    );

    logger.info(`learner found ${JSON.stringify(learner)}`);

    const isRegistered =
      eventAttendee?.status ===
      COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING;

    logger.info('Is user registered for event? ', isRegistered);

    if (isRegistered) {
      throw new ToUserError(
        'Learner already registered for this event',
        EVENT_ERROR.LEARNER_ALREADY_REGISTERED_FOR_THE_EVENT
      );
    }

    if (event.access !== COMMUNITY_EVENT_ACCESS_TYPES.FREE) {
      throw new ToUserError(
        'Cannot register for paid event',
        EVENT_ERROR.CANNOT_REGISTER_PAID_EVENT
      );
    }

    const applicationData = addonTransaction?.metadata?.applicationData;

    const registrationDetails = await createEventAttendee({
      eventAttendee,
      addonTransaction,
      purchaseType,
      event,
      learner,
      applicationData,
      quantity,
    });

    await done();
    /**
     * END OF LOCK
     */
    return registrationDetails;
  } catch (error) {
    logger.error('Unable to register for event due to: ', error);
    throw error;
  }
};

const autoRegisterEvent = async ({
  learnerObjectId,
  eventObjectId,
  addonTransactionObjectId,
  quantity,
}) => {
  const [addonTransaction, learner, eventAttendee, event] =
    await Promise.all([
      AddonTransaction.findById(addonTransactionObjectId).lean(),
      Learner.findById(learnerObjectId).lean(),
      EventAttendees.findOne({
        eventObjectId,
        learnerObjectId,
      }).lean(),
      EventModel.findById(eventObjectId).lean(),
    ]);

  if (!learner) {
    throw new ResourceNotFoundError(
      `Learner not found for ${learnerObjectId}`
    );
  }

  if (!event) {
    throw new ResourceNotFoundError(
      `Event not found for ${eventObjectId}`
    );
  }

  // For communities requiring approval and free events with application questions,
  // the post-approval process does not include `transactionObjectId` for free add-ons.
  // Therefore, the transaction must be located separately to access the application data.
  const freeAddonTransactionWithApplicationQuestions = !addonTransaction
    ? await AddonTransaction.findOne({
        entityObjectId: eventObjectId,
        learnerObjectId,
        entityCollection: COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.EVENT,
        'payment_details.status': PAYMENT_STATUSES.SUCCESS,
        'metadata.applicationData': { $exists: true },
      }).lean()
    : null;

  const { email } = learner;

  const isInternalDomain =
    CheckInternalMemberUtils.hasInternalDomain(email);

  let purchaseType = COMMUNITY_EVENT_ATTENDEE_PURCHASE_TYPES.FREE;

  const isPaid = addonTransaction && addonTransaction.amount > 0;

  if (isInternalDomain) {
    purchaseType = COMMUNITY_EVENT_ATTENDEE_PURCHASE_TYPES.INTERNAL;
  } else if (isPaid) {
    purchaseType = COMMUNITY_EVENT_ATTENDEE_PURCHASE_TYPES.PAID;
  }

  const applicationData =
    addonTransaction?.metadata?.applicationData ??
    freeAddonTransactionWithApplicationQuestions?.metadata
      ?.applicationData;

  await createEventAttendee({
    eventAttendee,
    addonTransaction,
    purchaseType,
    event,
    learner,
    applicationData,
    quantity: addonTransaction?.quantity ?? quantity,
  });
};

/**
 * Add multiple attendees to an event by CM
 * @param {Object} params
 * @param {ObjectId} params.communityObjectId
 * @param {ObjectId} params.eventObjectId
 * @param {Array<{email, subscriptionObjectId, learnerObjectId}>} params.newAttendeesData
 * @returns {Promise<Array>} addedAttendees
 */
const addMultipleAttendeesByCM = async ({
  communityObjectId,
  eventObjectId,
  newAttendeesData,
}) => {
  // get community & event data
  const [community, event] = await Promise.all([
    CommunityModel.findById(communityObjectId, { code: 1 }).lean(),
    EventModel.findById(eventObjectId).lean(),
  ]);
  if (!community) {
    throw new ParamError('Community not found');
  }
  if (!event) {
    throw new ParamError('Event not found');
  }
  const communityCode = community.code;

  // Step: Validate all new attendees have active subscriptions or are managers
  const {
    allSubscriptionObjectIds,
    allLearnerObjectIds,
    emailsWithoutSubscriptionId,
    emailToLearnerObjectIdMap,
  } = newAttendeesData.reduce(
    (acc, attendeeData) => {
      const { subscriptionObjectId, email, learnerObjectId } =
        attendeeData;
      if (subscriptionObjectId) {
        acc.allSubscriptionObjectIds.push(subscriptionObjectId);
      } else {
        acc.emailsWithoutSubscriptionId.push(email);
      }

      acc.allLearnerObjectIds.push(learnerObjectId);

      acc.emailToLearnerObjectIdMap[email] = learnerObjectId;
      return acc;
    },
    {
      allSubscriptionObjectIds: [],
      allLearnerObjectIds: [],
      emailsWithoutSubscriptionId: [],
      emailToLearnerObjectIdMap: {},
    }
  );

  const existingCommunityMemberFilters = getExistingMemberParams({
    withPendingSubscription: false,
  });
  const subscriptionFilter = {
    _id: { $in: allSubscriptionObjectIds },
    communityCode,
    ...existingCommunityMemberFilters,
  };
  const allSubscriptions = await CommunitySubscriptions.find(
    subscriptionFilter
  ).lean();
  const learnerObjIdToSubscriptionMap = allSubscriptions.reduce(
    (acc, subscription) => {
      acc[subscription.learnerObjectId] = subscription;
      return acc;
    },
    {}
  );

  let learnerObjIdToIsCommunityManagerMap = {};
  if (emailsWithoutSubscriptionId.length) {
    const communityRolesForEmailsWithoutSub = await CommunityRole.find({
      communityCode,
      email: { $in: emailsWithoutSubscriptionId },
    })
      .select('email role')
      .lean();

    const nonManagersWithoutSubscriptionId = [];
    // create learnerObjIdToIsCommunityManagerMap
    communityRolesForEmailsWithoutSub.forEach((communityRoleData) => {
      const { email, role } = communityRoleData;
      const learnerObjectId = emailToLearnerObjectIdMap[email];
      const isCommunityManager = getIsCommunityManagerByRole(role);
      learnerObjIdToIsCommunityManagerMap[learnerObjectId] =
        isCommunityManager;
      if (!isCommunityManager) {
        nonManagersWithoutSubscriptionId.push(email);
      }
    });

    if (nonManagersWithoutSubscriptionId.length) {
      logger.error(
        '[addMultipleAttendeesByCM] subscription id missing for non-manager users in paylaod: ',
        nonManagersWithoutSubscriptionId
      );
      const emails = nonManagersWithoutSubscriptionId.join(', ');
      throw new ToUserError(
        'SubscriptionId not provided for non-community manager email: ' +
          emails,
        EVENT_ERROR.SUBSCRIPTION_ID_NOT_PROVIDED_FOR_NON_CM_EMAIL
      );
    }
  }

  // validate is member or manager for all attendees
  allLearnerObjectIds.forEach((learnerObjectId) => {
    const hasSubscription = Boolean(
      learnerObjIdToSubscriptionMap[learnerObjectId]
    );
    const isCommunityManager = Boolean(
      learnerObjIdToIsCommunityManagerMap[learnerObjectId]
    );
    if (!hasSubscription && !isCommunityManager) {
      throw new ToUserError(
        'Learner not subscribed to community: ' + learnerObjectId,
        EVENT_ERROR.SUBSCRIPTION_ID_NOT_PROVIDED_FOR_NON_CM_LEARNER
      );
    }
  });

  // Step: Validate event
  // check event is not in past
  const currentDate = new Date();
  if (event.endTime && event.endTime < currentDate) {
    throw new ToUserError(
      'Cannot register for past event',
      EVENT_ERROR.CANNOT_REGISTER_PAST_EVENT
    );
  }

  // check event is not sold out
  if (event.isSoldOut) {
    throw new ToUserError('Event is closed', EVENT_ERROR.EVENT_CLOSED);
  }

  // check event is not capacity reached
  const totalNewAttendees = newAttendeesData.length; // quantity
  const isLimitReached = await checkAttendeeLimitReached(
    event,
    totalNewAttendees
  );
  if (event.isCapacitySet && isLimitReached) {
    throw new ToUserError('Event sold out', EVENT_ERROR.EVENT_SOLD_OUT);
  }

  // Step: Create event attendees
  // setup learnerObjIdToLearnerMap and learnerIdToEventAttendeeMap
  const eventAttendeesFilter = {
    eventObjectId,
    learnerObjectId: { $in: allLearnerObjectIds },
  };
  const learnerFilters = {
    _id: { $in: allLearnerObjectIds },
    isActive: true,
  };
  const [eventAttendeesList, learners] = await Promise.all([
    await EventAttendees.find(eventAttendeesFilter).lean(),
    await Learner.find(learnerFilters)
      .select('_id email learnerId')
      .lean(),
  ]);
  const learnerIdToEventAttendeeMap = eventAttendeesList.reduce(
    (acc, eventAttendee) => {
      acc[eventAttendee.learnerObjectId] = eventAttendee;
      return acc;
    },
    {}
  );
  const learnerObjIdToLearnerMap = learners.reduce((acc, learner) => {
    acc[learner._id] = learner;
    return acc;
  }, {});

  let addedAttendees = []; // to return from fn
  for await (const attendeeData of newAttendeesData) {
    const { learnerObjectId, email } = attendeeData;
    const learner = learnerObjIdToLearnerMap[learnerObjectId];

    if (!learner) {
      throw new ParamError(
        `Learner not found for learnerObjectId: ${learnerObjectId} and email: ${email}`
      );
    }
    const eventAttendee = learnerIdToEventAttendeeMap[learnerObjectId];

    const updatedEventAttendee = await createEventAttendee({
      eventAttendee,
      purchaseType: COMMUNITY_EVENT_ATTENDEE_PURCHASE_TYPES.FREE,
      event,
      learner,
      quantity: 1,
    });

    addedAttendees.push(updatedEventAttendee);
  }

  return addedAttendees;
};

const verifyPaidEventPayment = async (eventSignupId) => {
  try {
    let isValid = false;
    const { envVarData = null } = await getConfigByType(
      CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
    );
    const { MAX_RETRIES = 8, TIMEOUT_SECONDS = 0.75 } = envVarData;
    let attempts = 0;
    let eventCheckoutDetails = null;
    do {
      eventCheckoutDetails = await CommunityAddonTransaction.findById(
        eventSignupId
      );
      logger.info('Event Payment Details: ', eventCheckoutDetails);
      if (
        eventCheckoutDetails?.payment_details?.status ===
        EVENT_PAYMENT_STATUSES.SUCCESS
      ) {
        isValid = true;
      }
      if (attempts < MAX_RETRIES) {
        await sleepForSeconds(parseFloat(TIMEOUT_SECONDS));
      }
      ++attempts;
    } while (isValid === false && attempts <= MAX_RETRIES);
    logger.info(eventSignupId, ' payment validity: ', isValid);
    if (!isValid) {
      return {
        isValid,
        error: {
          message: 'Unable to verify event payment due to Timeout!',
        },
      };
    }
    const {
      email,
      eventObjectId = null,
      amount = null,
      currency = null,
      local_amount = null,
      local_currency = null,
    } = eventCheckoutDetails;
    const eventDetails = await EventModel.findOne(
      {
        _id: eventObjectId,
      },
      'communities slug title icsFileLink access description descriptionContent startTime endTime type liveLink inPersonLocation inPersonLocationMetadata'
    );
    const communityDetails = await CommunityModel.findOne(
      {
        _id: eventDetails?.communities?.[0],
      },
      'link title'
    );
    const eventLink = communityDetails?.link
      ? `${NAS_IO_FRONTEND_URL}${communityDetails?.link}${eventDetails?.slug}`
      : NAS_IO_FRONTEND_URL;
    return {
      isValid,
      data: {
        email,
        access: eventDetails?.access,
        amount,
        currency,
        localAmount: local_amount,
        localCurrency: local_currency,
        addToCalendarLink: eventDetails?.icsFileLink,
        eventLink,
        eventTitle: eventDetails?.title,
        eventDescription:
          eventDetails?.descriptionContent || eventDetails?.description,
        communityLink: communityDetails?.link,
        communityTitle: communityDetails?.title,
        startTime: eventDetails?.startTime,
        endTime: eventDetails?.endTime,
        type: eventDetails?.type,
        liveLink: eventDetails?.liveLink,
        inPersonLocation: eventDetails?.inPersonLocation,
        inPersonLocationMetadata: eventDetails?.inPersonLocationMetadata,
      },
    };
  } catch (error) {
    logger.error('Unable to verify paid event payment due to: ', error);
    throw error;
  }
};

const doesCommunityHavePaidEvents = async (communityId) => {
  const data = await EventModel.findOne({
    communities: new ObjectId(communityId),
    access: COMMUNITY_EVENT_ACCESS_TYPES.PAID,
  });
  if (data) {
    return true;
  }
  return false;
};

const countCommunityEvents = async (filter) => {
  return EventModel.countDocuments(filter);
};

module.exports = {
  getCommunityEvents,
  getPastEventsByCommunityId,
  getUpcomingEventsByCommunityId,
  getEventById,
  getEventBySlug,
  registerForEvent,
  getCommunityEventByEventId,
  deleteOneEvent,
  updateOneEvent,
  createOneEvent,
  sendEventReminderEmails,
  getCommunitiesByIds,
  verifyPaidEventPayment,
  doesCommunityHavePaidEvents,
  getEventByIdForAdmin,
  countCommunityEvents,
  getEventTicketReferenceForAttendee,
  createEventAttendee,
  autoRegisterEvent,
  checkAttendeeLimitReached,
  countAttendeesIncludingPending,
  addMultipleAttendeesByCM,
};
