const ObjectId = require('mongoose').Types.ObjectId;
const CommunitySubscriptionsModel = require('../../models/communitySubscriptions.model');
const {
  communityEnrolmentStatuses,
} = require('../../../constants/common');
const { aclRoles } = require('../../constants/index');
const { getDateInUTC } = require('../../utils');
const { sendEmail } = require('../../../services/notification');
const CommunityRoleModel = require('../../models/communityRole.model');
const CommunityModel = require('../../models/community.model');
const logger = require('../../../services/logger.service');
const WhatsappService = require('./whatsapp.service');
const ActionEventService = require('../../../services/actionEvent');
const { ResourceNotFoundError } = require('../../../utils/error.util');
const MemberLimitNotificationService = require('@/src/services/common/membersLimitNotification.service');

const communityLeaveService = async (
  communitySubscriptionObject,
  cancellationReason,
  communityCode,
  userInfo,
  communityId
) => {
  try {
    const { _id } = communitySubscriptionObject;
    const communitySubscription =
      await CommunitySubscriptionsModel.findOneAndUpdate(
        {
          _id: new ObjectId(_id),
        },
        {
          status: communityEnrolmentStatuses.CANCELLED,
          updatedAt: getDateInUTC(new Date()),
          cancellationReason,
          cancelledAt: getDateInUTC(new Date()),
          nextBillingDate: communitySubscriptionObject?.nextBillingDate,
          unsubscribedAt: getDateInUTC(new Date()),
        },
        { new: true }
      ).lean();

    try {
      const communityManagers = await CommunityRoleModel.find({
        communityCode: communitySubscriptionObject.communityCode,
        role: {
          $in: [
            aclRoles.MANAGER,
            aclRoles.ADMIN,
            aclRoles.OWNER,
            aclRoles.SUPER_ADMIN,
          ],
        },
      })
        .select('email communityCode userObjectId')
        .populate([
          'userObjectId',
          {
            path: 'communityObjectId',
            select: 'code title thumbnailImgData',
          },
        ])
        .lean();

      const communityInfo = await CommunityModel.findOne({
        _id: new ObjectId(communityId),
      })
        .select('_id code title thumbnailImgData bots')
        .lean();

      if (!communityInfo) {
        throw new ResourceNotFoundError('Community not found');
      }

      await ActionEventService.sendUnsubscribedFreeMembershipActionEvent({
        subscription: communitySubscription,
        community: communityInfo,
      });

      await MemberLimitNotificationService.calculateTotalMemberCountAndStoreInCommunity(
        {
          communityCode: communityInfo.code,
        }
      );

      if (communityInfo.isWhatsappExperienceCommunity) {
        await WhatsappService.removeWhatsappMemberBySubscription(
          communityId,
          communitySubscriptionObject._id
        );
      }

      const chatConnection = communityInfo?.bots?.[0]?.type;

      const date = new Date();
      const yyyy = date.getFullYear();
      const mm = date.getMonth() + 1;
      const dd = date.getDate();
      const dateInUTC = `${dd}-${mm}-${yyyy}`;
      const info = {
        community_code: communityInfo?.code,
        community_name: communityInfo?.title,
        student_email: communitySubscriptionObject?.email,
        is_sb_email_non_admin_and_external: true,
        student_name: userInfo?.fullName,
        cancelled_at: dateInUTC,
        cancellation_reason: cancellationReason,
        community_profile_pic:
          communityInfo?.thumbnailImgData?.mobileImgData?.src,
        manager_emails: communityManagers?.map(
          (manager) => manager?.email
        ),
        manager_names: communityManagers?.map((manager) => {
          return (
            manager?.userObjectId?.name ??
            manager.userObjectId?.fullName ??
            ''
          );
        }),
        chat_connection: chatConnection ?? null,
      };
      const mailReqBody = {
        mailType: 'COMMUNITY_MEMBER_UNSUBSCRIBE_ALERT',
        mailCourse: communityCode,
        mailCourseOffer: 'All',
        toMail: communityManagers?.map((manager) => manager?.email),
        toMailName: communityManagers?.map((manager) => {
          return (
            manager?.userObjectId?.name ??
            manager.userObjectId?.fullName ??
            ''
          );
        }),
        data: info,
        requesterServiceName: 'Learn Portal Backend',
      };

      await sendEmail(mailReqBody);

      logger.info('CM email sent successfully');

      const memberInfo = {
        community_name: communityInfo?.title ?? '',
        unsubscribed_date: dateInUTC,
        cancellation_reason: cancellationReason,
        community_profile_pic:
          communityInfo?.thumbnailImgData?.mobileImgData?.src,
        student_name: userInfo?.fullName ?? '',
      };

      // sending email to member
      const mailReqBodyMember = {
        mailType: 'COMMUNITY_MEMBER_UNSUBSCRIBE_ALERT_FOR_MEMBER',
        mailSubject: `You have left  ${communityInfo?.title}`,
        mailCourse: communityCode,
        mailCourseOffer: 'All',
        toMail: [communitySubscriptionObject?.email],
        toMailName: [userInfo?.fullName],
        data: memberInfo,
        requesterServiceName: 'Learn Portal Backend',
      };
      await sendEmail(mailReqBodyMember);
      logger.info('member email sent successfully');
    } catch (error) {
      logger.info('error in sending email', error);
    }
    if (!communitySubscription) {
      return { error: 'No subscription found', data: null };
    }

    return { error: null, data: communitySubscription };
  } catch (error) {
    return { error: error.message, data: null };
  }
};

module.exports = { communityLeaveService };
