/* eslint-disable yoda */
/* eslint-disable no-param-reassign */
const mongoose = require('mongoose');

const ObjectId = mongoose.Types.ObjectId;
// models
const CommunityFolders = require('../../models/communityFolders.model');
const FolderPurchaseModel = require('../../models/communityFolderPurchases.model');
const AddonTransactionModel = require('../../models/communityAddonTransactions.model');
const LearnerModel = require('../../../models/learners.model');
const communityFolderAccessLogsModel = require('../../models/communityFolderAccessLogs.model');
const CheckInternalMemberUtils = require('../../../utils/checkInternalMember.util');
const sessionAttendeesModel = require('../../../models/oneOnOneSessions/sessionAttendees.model');
const PrimaryMongooseConnection = require('../../../rpc/primaryMongooseConnection');
const folderViewersModel = require('../../../models/product/folderViewers.model');
// services
const logger = require('../../../services/logger.service');
const CommunityModel = require('../../models/community.model');
const entityCurrencyUtil = require('../../../utils/entityCurrency.util');
const discountService = require('./communityDiscounts.service');
const { affiliateService } = require('../../../services/affiliate');
const {
  discountValidationForEntities,
  discountCreationForEntities,
} = require('./communityDiscounts.service');
const pricingService = require('../../../services/pricing');
const {
  makeUrlShortCode,
  addShortUrl,
} = require('../../../utils/makeShortCode');
const fraudService = require('../../../services/fraud');

const {
  communityLibraryTypesMap,
  communityFolderTypesMap,
  communityLibraryStatusMap,
  communityFolderItemTypesMap,
  COMMUNITY_FOLDER_PURCHASE_TYPES,
  COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES,
} = require('../../constants');
const {
  NASG_CLOUDFRONT_BASE_URL,
  NASG_VIDEO_LINK_REGEX,
  VIDEO_LINK_REGEX,
  COMMUNITY_SHORT_CODE_LEN,
  PURCHASE_TYPE,
} = require('../../../constants/common');
const {
  defaultPaginatedResponse,
  getPaginationDataAggregatePipelineStage,
  getNextAndPrevious,
} = require('../../../utils/pagination.util');
const { regexReplace } = require('../../../utils/string_handling');
const {
  PRIVATE_VIDEO_BASE_URL,
  FRONTEND_APP_LINK,
  NAS_IO_FRONTEND_URL,
} = require('../../../config');
const {
  ResourceNotFoundError,
  ParamError,
} = require('../../../utils/error.util');
const {
  FOLDER_VIEWER_STATUS,
} = require('../../../services/folder/constants');
const nameUtils = require('../../../utils/name.util');
const AuthServiceRpc = require('../../../rpc/authService.rpc');
const { FOLDER_MAIL_TYPES } = require('../../../services/mail/constants');
const formatVariableDataService = require('../../../services/mail/formatVariableData.service');
const mailUtils = require('../../../utils/mail.util');
const notificationCommonService = require('../../../services/communityNotification/email/common.service');
const receiptService = require('../../../services/receipts/receipts.service');
const {
  getUpdateDataForMultipleCoverMediaItems,
  generateCoverMediaItems,
  hasVideoCoverMediaItems,
} = require('../../../utils/multipleCoverMediaItems.util');
const {
  COVER_MEDIA_ENTITY_TYPES,
} = require('../../../constants/coverMediaItems.constant');
const {
  verifyVideoCoverMediaItems,
  deleteRemovedVideoCoverMediaItems,
} = require('../../../services/coverMediaItems/common.service');
const SyncProductDataService = require('../../../services/product/syncProductData.service');
const { PRODUCT_TYPE } = require('../../../services/product/constants');
const {
  purgeEntityLandingPageCache,
  ENTITY_LANDING_PAGE,
} = require('../../../utils/memberPortalLinks.utils');
const AiCofounderProductCreationService = require('@/src/services/featurePermissions/aiCofounderProductCreation.service');

// eslint-disable-next-line no-unused-vars
const generateShortUrl = async (longUrl, communityCode, name) => {
  try {
    const urlShortCodeRand = makeUrlShortCode(COMMUNITY_SHORT_CODE_LEN);
    const urlShortCode = `${communityCode}/${urlShortCodeRand}`;
    const shortUrlData = await addShortUrl(name, urlShortCode, longUrl);
    return `${NAS_IO_FRONTEND_URL}/${shortUrlData.urlCode}`;
  } catch (error) {
    logger.info('Error occured when generating short url:', error.message);
    throw error;
  }
};

const generateSlug = async (communityId) => {
  try {
    const urlShortCodeRand = makeUrlShortCode(COMMUNITY_SHORT_CODE_LEN);
    // check if there is already a resource with the same slug
    const folderExistWithSameSlug = await CommunityFolders.countDocuments({
      resourceSlug: `/${urlShortCodeRand}`,
      communityObjectId: communityId,
    });

    if (folderExistWithSameSlug > 0) {
      //then there exists a same resource slug
      return generateSlug(communityId);
    }
    return urlShortCodeRand;
  } catch (error) {
    logger.error(
      'error occured when generating short url:',
      error.message,
      error.stack
    );
    throw error;
  }
};
const nonManagerViewFilter = {
  status: communityLibraryStatusMap.PUBLISHED,
};
const managerViewFilter = {
  status: {
    $nin: [
      communityLibraryStatusMap.DELETED,
      communityLibraryStatusMap.DRAFT,
    ],
  },
};

const getCorrectLinkUponRegexReplace = (link) => {
  if (link) {
    let newLink = regexReplace(
      link,
      NASG_VIDEO_LINK_REGEX,
      NASG_CLOUDFRONT_BASE_URL
    ).replace(/\s/g, '');
    newLink = regexReplace(
      newLink,
      VIDEO_LINK_REGEX,
      PRIVATE_VIDEO_BASE_URL
    ).replace(/\s/g, '');

    return newLink;
  }

  return null;
};

const getFoldersByCommunityId = async (
  communityObjectId,
  isCommunityManager = false,
  learnerObjectId = null,
  filter = {},
  paginate = 0,
  pageNum = null,
  pageSize = null
) => {
  const permissionFilter = isCommunityManager
    ? managerViewFilter
    : nonManagerViewFilter;
  const params = {
    communityObjectId: new ObjectId(communityObjectId),
    ...filter,
    ...permissionFilter,
  };
  const responseFields = {
    _id: 1,
    index: 1,
    type: 1,
    title: 1,
    description: 1,
    emoji: 1,
    thumbnail: 1,
    shortUrl: 1,
    longUrl: 1,
    tags: 1,
    status: 1,
    createdAt: 1,
    access: 1,
    amount: 1,
    currency: 1,
    resourceSlug: 1,
    earningAnalytics: 1,
    accessCount: 1,
    durationIntervalInMinutes: 1,
    hostInfo: 1,
    pricingConfig: 1,
    coverMediaItems: 1,
  };
  try {
    const community = await CommunityModel.findById(communityObjectId, {
      link: 1,
    }).lean();

    const communityFolderAggregationPipeline = [
      {
        $match: params,
      },
      {
        $lookup: {
          from: 'community_folder_items',
          let: { id: '$_id' },
          pipeline: [
            {
              $match: {
                $and: [
                  { $expr: { $eq: ['$communityFolderObjectId', '$$id'] } },
                  {
                    type: 'video',
                    status: {
                      $nin: [
                        communityLibraryStatusMap.DELETED,
                        communityLibraryStatusMap.DRAFT,
                      ],
                    },
                  },
                ],
              },
            },
          ],
          as: 'videos',
        },
      },
      {
        $lookup: {
          from: 'community_folder_items',
          let: { id: '$_id' },
          pipeline: [
            {
              $match: {
                $and: [
                  { $expr: { $eq: ['$communityFolderObjectId', '$$id'] } },
                  {
                    type: { $ne: 'video' },
                    status: {
                      $nin: [
                        communityLibraryStatusMap.DELETED,
                        communityLibraryStatusMap.DRAFT,
                      ],
                    },
                  },
                ],
              },
            },
          ],
          as: 'otherItems',
        },
      },
      {
        $lookup: {
          from: 'community_folder_items',
          let: { id: '$_id' },
          pipeline: [
            {
              $match: {
                $and: [
                  { $expr: { $eq: ['$communityFolderObjectId', '$$id'] } },
                  {
                    status: {
                      $nin: [
                        communityLibraryStatusMap.DELETED,
                        communityLibraryStatusMap.DRAFT,
                      ],
                    },
                  },
                  {
                    type: { $ne: communityFolderItemTypesMap.SECTION },
                  },
                ],
              },
            },
          ],
          as: 'folderItems',
        },
      },
      {
        $lookup: {
          from: 'learners',
          foreignField: '_id',
          localField: 'hostInfo.hostLearnerObjectId',
          as: 'sessionHost',
        },
      },
      {
        $unwind: {
          path: '$sessionHost',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields: {
          'hostInfo.hostLearnerInfo': {
            $cond: {
              if: '$hostInfo.hostLearnerObjectId',
              then: '$sessionHost',
              else: null,
            },
          },
        },
      },
    ];
    if (!isCommunityManager) {
      communityFolderAggregationPipeline.push({
        $lookup: {
          from: 'community_folder_purchases',
          let: { id: '$_id' },
          pipeline: [
            {
              $match: {
                $and: [
                  { $expr: { $eq: ['$folderObjectId', '$$id'] } },
                  {
                    $expr: {
                      $eq: [
                        '$learnerObjectId',
                        new ObjectId(learnerObjectId),
                      ],
                    },
                  },
                ],
              },
            },
          ],
          as: 'purchases',
        },
      });
      // responseFields['isPurchased'] = { $toBool: { $size: '$purchases' } };
      responseFields['isPurchased'] = {
        $cond: {
          if: { $eq: ['$access', 'free'] },
          then: true,
          else: { $toBool: { $size: '$purchases' } },
        },
      };
    }

    communityFolderAggregationPipeline.push(
      {
        $project: {
          ...responseFields,
          videoCount: {
            $size: '$videos',
          },
          otherFolderItemCount: { $size: '$otherItems' },
          totalItemsCount: {
            $size: '$folderItems',
          },
        },
      },
      {
        $sort: {
          createdAt: 1,
        },
      }
    );
    if (paginate === 1) {
      const paginationDataAggregatePipelineStage =
        getPaginationDataAggregatePipelineStage(pageNum, pageSize);

      const paginationPipeline = {
        $facet: {
          metadata: [
            {
              $count: 'total',
            },
          ],
          data: paginationDataAggregatePipelineStage,
        },
      };
      communityFolderAggregationPipeline.push(paginationPipeline);
      communityFolderAggregationPipeline.push({ $unwind: '$metadata' });
    }
    let result = await CommunityFolders.aggregate(
      communityFolderAggregationPipeline
    );

    const gettingAllFolderViews = result.map(
      async (communityFolder, index) => {
        try {
          result[
            index
          ].shortUrl = `${NAS_IO_FRONTEND_URL}${community.link}${communityFolder.resourceSlug}`;
          result[index]['folderViewCount'] =
            await communityFolderAccessLogsModel.countDocuments({
              communityFolderObjectId: result[index]?._id,
            });
        } catch (error) {
          result[index]['folderViewCount'] = 0;
        }
      }
    );

    const generateCoverMediaItemsForFolders = result.map(
      async (communityFolder, index) => {
        result[index].coverMediaItems = await generateCoverMediaItems({
          entity: communityFolder,
          entityType: COVER_MEDIA_ENTITY_TYPES.FOLDER,
          isCommunityManager,
        });
      }
    );

    if (result.length > 0) {
      await Promise.all([
        ...gettingAllFolderViews,
        ...generateCoverMediaItemsForFolders,
      ]);
    }

    const gettingAllUpcomingSessions = result.map(
      async (communityFolder, index) => {
        try {
          if (
            communityFolder.type === communityFolderTypesMap.SESSION &&
            learnerObjectId
          ) {
            result[index]['upcomingSessions'] =
              (await sessionAttendeesModel
                .findOne({
                  sessionObjectId: communityFolder._id,
                  attendeeLearnerObjectId: learnerObjectId,
                  sessionEndTime: { $gte: new Date() },
                })
                .lean()) || {};
          }
        } catch (error) {
          result[index]['upcomingSessions'] = [];
        }
      }
    );

    if (result.length > 0) {
      await Promise.all(gettingAllUpcomingSessions);
    }
    if (paginate === 1) {
      result = result?.[0] || null;
      if (!result) {
        result = defaultPaginatedResponse;
      }
      const { next = null, previous = null } = getNextAndPrevious(
        pageNum,
        pageSize,
        result?.metadata?.total
      );
      result.metadata.next = next;
      result.metadata.previous = previous;
    }
    const resultData = paginate === 1 ? result?.data : result;
    resultData.forEach((folder) => {
      if (
        folder.type === communityFolderTypesMap.DIGITAL_PRODUCT &&
        folder.thumbnail
      ) {
        folder.thumbnail = getCorrectLinkUponRegexReplace(
          folder.thumbnail
        );
      }
    });
    if (paginate === 1) {
      result.data = resultData;
    } else {
      result = resultData;
    }
    return result;
  } catch (error) {
    logger.error(
      `Error getting CommunityFolders for communityObjectId ${communityObjectId}`,
      error,
      error.stack
    );
    throw new Error(
      `Error getting CommunityFolders for communityObjectId ${communityObjectId}`
    );
  }
};

const getFolderById = async (
  id,
  isCommunityManager = false,
  params = {},
  queryParams = {}
) => {
  const permissionFilter = isCommunityManager
    ? managerViewFilter
    : nonManagerViewFilter;
  try {
    const result = await CommunityFolders.findOne({
      _id: new ObjectId(id),
      ...permissionFilter,
      ...params,
    }).lean();

    if (!result) {
      throw new ParamError('Invalid folder id');
    }

    const community = await CommunityModel.findById(
      result.communityObjectId,
      { link: 1, code: 1 }
    ).lean();

    if (!community) {
      throw new ResourceNotFoundError('Community not found');
    }

    result.coverMediaItems = await generateCoverMediaItems({
      entity: result,
      entityType: COVER_MEDIA_ENTITY_TYPES.FOLDER,
      isCommunityManager,
    });

    result.shortUrl = `${NAS_IO_FRONTEND_URL}${community.link}${result.resourceSlug}`;

    if (!result?.access) {
      result.access = COMMUNITY_FOLDER_PURCHASE_TYPES.FREE;
    }

    if (
      result.type === communityFolderTypesMap.DIGITAL_PRODUCT &&
      result.thumbnail
    ) {
      result.thumbnail = getCorrectLinkUponRegexReplace(result.thumbnail);
    }
    if (isCommunityManager) {
      const discountsApplied =
        await discountService.retrieveAllDiscountsRelatedToEntity(
          result,
          community.code
        );

      result.discountsApplied = discountsApplied;
    } else {
      delete result.discountsApplied;
    }

    const { affiliateCode } = queryParams;

    const affiliateInfo = await affiliateService.retrieveAffiliateInfo({
      communityObjectId: result.communityObjectId,
      affiliateCode,
      entityObjectId: id,
      entityType: PURCHASE_TYPE.FOLDER,
    });

    result.affiliateInfo = affiliateInfo;

    return result;
  } catch (error) {
    logger.error(
      `Error getting CommunityFolders for communityFolderObjectId ${id}`,
      error,
      error.stack
    );
    throw new Error(
      `Error getting CommunityFolders for communityFolderObjectId ${id}`
    );
  }
};

const getFilteredFoldersByCommunityId = async (
  communityId,
  isCommunityManager = false,
  filter
) => {
  const permissionFilter = isCommunityManager
    ? managerViewFilter
    : nonManagerViewFilter;
  const params =
    filter === '' || !filter
      ? {
          $and: [
            {
              communityObjectId: new ObjectId(communityId),
              ...permissionFilter,
            },
          ],
        }
      : {
          $and: [
            {
              communityObjectId: new ObjectId(communityId),
              ...permissionFilter,
            },
            {
              $or: [
                {
                  tags: { $elemMatch: { $regex: filter, $options: 'i' } },
                },
                { title: { $regex: filter, $options: 'i' } },
                { description: { $regex: filter, $options: 'i' } },
              ],
            },
          ],
        };

  try {
    const communityFolders = await CommunityFolders.aggregate([
      {
        $match: params,
      },
      {
        $project: {
          _id: 1,
          communityObjectId: 1,
          communityFolderObjectId: '$_id',
          type: 1,
          title: 1,
          thumbnail: 1,
          emoji: 1,
          shortUrl: 1,
          longUrl: 1,
          description: 1,
          tags: 1,
        },
      },
      {
        $addFields: {
          type: {
            $cond: [
              { $eq: ['$type', communityFolderTypesMap.DIGITAL_PRODUCT] },
              communityLibraryTypesMap.DIGITAL_PRODUCT,
              {
                $cond: [
                  {
                    $eq: [
                      '$type',
                      communityFolderTypesMap.DIGITAL_PRODUCT,
                    ],
                  },
                  communityLibraryTypesMap.DIGITAL_PRODUCT,
                  '',
                ],
              },
            ],
          },
        },
      },
    ]);
    communityFolders.forEach((folder) => {
      if (
        folder.type === communityFolderTypesMap.DIGITAL_PRODUCT &&
        folder.thumbnail
      ) {
        folder.thumbnail = getCorrectLinkUponRegexReplace(
          folder.thumbnail
        );
      }
    });
    logger.info('Community Folders found', communityFolders);
    return communityFolders;
  } catch (err) {
    logger.error(
      'Error filtering folders from the community id: ',
      err,
      err.stack
    );
    throw new Error('Error filtering folders from the community id');
  }
};

const checkFolderForFraud = async ({
  updatedPayload,
  community,
  folderId,
}) => {
  const contentList = [];
  const contentSourceList = [];
  if (updatedPayload?.title) {
    contentList.push(updatedPayload.title);
    contentSourceList.push('title');
  }
  if (updatedPayload?.description) {
    contentList.push(updatedPayload.description);
    contentSourceList.push('description');
  }
  if (contentList.length > 0) {
    const fraudEngine = new fraudService.FraudEngine({
      communityId: community._id,
      eventName: fraudService.INTERESTED_EVENTS.UPDATE_CONTENT,
      entityType: 'folder',
      entityId: folderId,
      data: {
        content: contentList.join(', '),
        contentSource: contentSourceList.join(' & '),
      },
      checksToPerform: [fraudService.COMMON_FRAUD_CHECKS.FREE_INPUT],
      autoConsequencesToApply: [
        fraudService.COMMON_CONSEQUENCES.RESTRICT_CHECKOUT,
        fraudService.COMMON_CONSEQUENCES.RESTRICT_CUSTOM_EMAIL,
        fraudService.COMMON_CONSEQUENCES.RESTRICT_MAGIC_REACH,
        fraudService.COMMON_CONSEQUENCES.NOT_INDEXABLE,
        fraudService.COMMON_CONSEQUENCES.RECOMMEND_DEACTIVATE,
      ],
    });

    try {
      await fraudEngine.performCheck();
    } catch (error) {
      logger.error('Error in fraud check:', error.message, error.stack);
    }
  }
};

const syncFolderData = async (communityFolder, session) => {
  if (communityFolder.type === communityFolderTypesMap.DIGITAL_PRODUCT) {
    await SyncProductDataService.syncProductData({
      productType: PRODUCT_TYPE.DIGITAL_FILES,
      entity: communityFolder,
      session,
    });
  } else if (communityFolder.type === communityFolderTypesMap.COURSE) {
    await SyncProductDataService.syncProductData({
      productType: PRODUCT_TYPE.COURSE,
      entity: communityFolder,
      session,
    });
  } else {
    throw new Error(`Invalid folder type: ${communityFolder.type}`);
  }
};

const createOneFolder = async (params) => {
  if (Object.keys(params).length === 0 && params.constructor === Object) {
    throw new Error('Empty Params');
  }
  // get community short code and code from community object id
  const community = await CommunityModel.findById(
    params?.communityObjectId,
    {
      communityShortCode: 1,
      code: 1,
      isPaidCommunity: 1,
      baseCurrency: 1,
      config: 1,
      featurePermissions: 1,
    }
  ).lean();

  if (!community) {
    throw new ResourceNotFoundError('Community not found');
  }

  await AiCofounderProductCreationService.checkProductCreationEligibility(
    community._id,
    params.templateLibraryId,
    community
  );

  entityCurrencyUtil.validateEntityCurrencyWithCommunityBaseCurrency(
    params.currency,
    community.baseCurrency
  );

  await discountValidationForEntities(
    community.code,
    params.newDiscountsToApply,
    params.discountsToAdd
  );

  if (!params.index) {
    const highestIndexFolder = await CommunityFolders.find({
      type: params.type,
      communityId: params?.communityObjectId,
    })
      .sort({ index: -1 })
      .limit(1);
    params.index = highestIndexFolder.index + 1;
  }

  if (params.coverMediaItems && params?.coverMediaItems?.length > 0) {
    const coverMediaItemsUpdateData =
      await getUpdateDataForMultipleCoverMediaItems({
        communityId: params.communityObjectId,
        entityType: 'folder',
        coverMediaItems: params.coverMediaItems,
      });

    Object.assign(params, coverMediaItemsUpdateData);
  }

  logger.info('Creating community folder with the given params:', params);

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  let communityFolder;

  try {
    const pricing = await pricingService.validateAndFormatPricing(
      params,
      false,
      null,
      community
    );

    if (pricing) {
      params.amount = pricing.amount;
      params.currency = pricing.currency;
      params.pricingConfig = pricing.pricingConfig;
      params.access = pricing.access;
    }

    [communityFolder] = await CommunityFolders.create([params], {
      session,
    });

    if (hasVideoCoverMediaItems(communityFolder.coverMediaItems)) {
      await verifyVideoCoverMediaItems({
        entityObjectId: communityFolder._id,
        coverMediaItems: communityFolder.coverMediaItems,
      });
    }

    // TODO: Need to adjust link if longUrl becomes relevant again
    const longUrl = `${FRONTEND_APP_LINK}/communities/${communityFolder.communityObjectId}/library?folderType=${communityFolder.type}&folderId=${communityFolder._id}`;
    logger.info(
      `Creating short URL for the folder ${communityFolder._id} longURL = ${longUrl}`
    );

    // generate short url for the folder
    const shortUrl = await generateSlug(communityFolder.communityObjectId);
    if (shortUrl) {
      communityFolder = await CommunityFolders.findByIdAndUpdate(
        communityFolder._id,
        { resourceSlug: '/' + shortUrl, longUrl },
        { new: true, upsert: false, session }
      ).lean();
      communityFolder.slug = communityFolder.resourceSlug;
    }

    await syncFolderData(communityFolder, session);

    await session.commitTransaction();
  } catch (error) {
    logger.error('Error creating community folder:', error, error.stack);
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }

  communityFolder = await discountCreationForEntities(
    community.code,
    null,
    communityFolder,
    PURCHASE_TYPE.FOLDER,
    params?.createdBy,
    CommunityFolders,
    params.newDiscountsToApply,
    params.discountsToRemove,
    params.discountsToAdd,
    params.discountsToDisable
  );

  // to move to worker pool
  checkFolderForFraud({
    community,
    updatedPayload: communityFolder,
    folderId: communityFolder._id,
  });

  return communityFolder;
};

const updateOneFolder = async (params, payload) => {
  const folder = await CommunityFolders.findOne(params).lean();

  if (!folder) {
    throw new ResourceNotFoundError('Folder not found');
  }

  const community = await CommunityModel.findOne({
    _id: new ObjectId(folder.communityObjectId),
  })
    .select('communityShortCode code baseCurrency link')
    .lean();

  if (!community) {
    throw new ResourceNotFoundError('Community not found');
  }

  entityCurrencyUtil.validateEntityCurrencyWithCommunityBaseCurrency(
    payload.currency,
    community.baseCurrency
  );

  await discountValidationForEntities(
    community.code,
    payload.newDiscountsToApply,
    payload.discountsToAdd
  );

  const pricing = await pricingService.validateAndFormatPricing(
    payload,
    false,
    folder,
    community
  );

  if (pricing) {
    payload.amount = pricing.amount;
    payload.currency = pricing.currency;
    payload.pricingConfig = pricing.pricingConfig;
    payload.access = pricing.access;
  }

  if (
    Array.isArray(payload.coverMediaItems) &&
    payload.coverMediaItems.length
  ) {
    const coverMediaItemsUpdateData =
      await getUpdateDataForMultipleCoverMediaItems({
        communityId: folder.communityObjectId,
        entityType: 'folder',
        coverMediaItems: payload.coverMediaItems,
      });

    Object.assign(payload, coverMediaItemsUpdateData);
  }

  let diff = 0;
  if (payload.access !== folder.access) {
    if (folder.type === communityFolderTypesMap.DIGITAL_PRODUCT) {
      const statusesToExclude = [
        FOLDER_VIEWER_STATUS.REFUNDED,
        FOLDER_VIEWER_STATUS.CHARGEBACK,
      ];
      if (payload.access === COMMUNITY_FOLDER_PURCHASE_TYPES.PAID) {
        statusesToExclude.push(FOLDER_VIEWER_STATUS.FREE);
      }
      const accessCount = await folderViewersModel.countDocuments({
        folderObjectId: folder._id,
        status: { $nin: statusesToExclude },
      });
      diff = accessCount - (folder.accessCount ?? 0);
    } else if (folder.type === communityFolderTypesMap.SESSION) {
      const accessCount = await sessionAttendeesModel.countDocuments({
        sessionObjectId: folder._id,
        status: COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.BOOKED,
      });
      diff = accessCount - (folder.accessCount ?? 0);
    }
  }

  let updatedData = await CommunityFolders.findByIdAndUpdate(
    folder._id,
    {
      $set: payload,
      $inc: { accessCount: diff },
    },
    {
      new: true,
    }
  ).lean();

  await syncFolderData(updatedData);

  if (folder?.resourceSlug) {
    folder.slug = folder.resourceSlug;
    updatedData.slug = updatedData.resourceSlug;
  }

  if (payload.coverMediaItems) {
    // delete removed video cover media items by marking folder item status as deleted.
    const oldCoverMediaItems = folder?.coverMediaItems ?? [];
    const newCoverMediaItems = updatedData?.coverMediaItems ?? [];
    if (
      Array.isArray(oldCoverMediaItems) &&
      hasVideoCoverMediaItems(oldCoverMediaItems)
    ) {
      await deleteRemovedVideoCoverMediaItems({
        oldCoverMediaItems,
        newCoverMediaItems,
      });
    }
  }

  if (updatedData.status === communityLibraryStatusMap.PUBLISHED) {
    await purgeEntityLandingPageCache({
      community,
      purgeCommunityLandingPage: false, // on update action, only purge folder landing page
      entityType: ENTITY_LANDING_PAGE[updatedData.type],
      entitySlug: updatedData.resourceSlug,
    });
  }

  if (
    payload.newDiscountsToApply ||
    payload.discountsToRemove ||
    payload.discountsToAdd ||
    payload.discountsToDisable
  ) {
    logger.info(
      'Updating community folder with the given params:',
      payload
    );
    updatedData = await discountCreationForEntities(
      community.code,
      folder,
      updatedData,
      PURCHASE_TYPE.FOLDER,
      payload?.createdBy,
      CommunityFolders,
      payload.newDiscountsToApply,
      payload.discountsToRemove,
      payload.discountsToAdd,
      payload.discountsToDisable
    );
  }

  // to move to worker pool
  checkFolderForFraud({
    community,
    updatedPayload: updatedData,
    folderId: updatedData._id,
  });

  return updatedData;
};

const patchOneFolder = async (id, payload, createNew) => {
  try {
    const updatedData = await CommunityFolders.findByIdAndUpdate(
      new ObjectId(id),
      payload,
      {
        new: createNew ?? false,
      }
    ).lean();
    await syncFolderData(updatedData);
    return updatedData;
  } catch (error) {
    logger.error('Error patching folder:', error, error.stack);
    throw new Error('Cannot patch folder');
  }
};

const softDeleteFolderById = async (id) => {
  try {
    const result = await CommunityFolders.findByIdAndUpdate(
      new ObjectId(id),
      { status: communityLibraryStatusMap.DELETED },
      {
        new: true,
      }
    );
    return result;
  } catch (error) {
    logger.error(
      `Error soft deleting CommunityFolder by id ${id}`,
      error,
      error.stack
    );
    throw new Error(`Error soft deleting CommunityFolder by id ${id}`);
  }
};

const doesCommunityHavePaidFolders = async (communityId) => {
  const folderInfo = await CommunityFolders.findOne({
    communityObjectId: communityId,
    access: COMMUNITY_FOLDER_PURCHASE_TYPES.PAID,
    status: communityLibraryStatusMap.PUBLISHED,
  });

  if (folderInfo) {
    return true;
  }
  return false;
};

const getPublishedFolderCount = async (communityId) => {
  const publishedCount = await CommunityFolders.countDocuments({
    communityObjectId: communityId,
    status: communityLibraryStatusMap.PUBLISHED,
  });

  return publishedCount;
};

async function formatAndSendFolderTypeEmail({
  mailType,
  learnerObjectId,
  folder,
  learner = null,
  community,
  addonTransaction,
}) {
  // Only available for paid folder
  if (!addonTransaction) {
    return;
  }

  let newLearner = learner;
  let emailToken;

  if (!newLearner) {
    newLearner = await LearnerModel.findById(learnerObjectId).lean();
    if (!newLearner) {
      logger.info(`Learner with ObjectId ${learnerObjectId} not found`);
      return 'Could not find learner';
    }
  }
  const name = nameUtils.getName(
    newLearner?.firstName,
    newLearner?.lastName,
    newLearner?.email
  );

  const toMail = [newLearner.email];
  const toMailName = [name];

  const generateReceipt = addonTransaction && addonTransaction.amount > 0;

  if (
    [FOLDER_MAIL_TYPES.COMMUNITY_FOLDER_PURCHASE_MEMBER].includes(mailType)
  ) {
    const authServiceRpc = new AuthServiceRpc();
    const { token } = await authServiceRpc.generateEmailToken(toMail[0]);
    emailToken = token;
  }

  const communityVariables = formatVariableDataService.formatCommunityData(
    {
      community,
      emailToken,
    }
  );

  const owner = await notificationCommonService.retrieveCommunityOwnerInfo(
    community.code
  );

  let ownerMailName = owner.name;
  let ownerProfileImage = owner.profileImage;

  if (folder.createdByLearnerObjectId) {
    const folderCreator = await LearnerModel.findById(
      folder.createdByLearnerObjectId
    ).lean();

    if (folderCreator) {
      ownerMailName = nameUtils.getName(
        folderCreator.firstName,
        folderCreator.lastName
      );
      ownerProfileImage = folderCreator.profileImage;
    }
  }

  const emailData = {
    student_header_name: 'Hello',
    student_name: newLearner?.firstName || '',
    folder_name: folder.title || '',
    price: addonTransaction.local_amount / 100,
    currency: addonTransaction.local_currency,
    community_host: ownerMailName,
    host_profile_image: ownerProfileImage,
    folder_url: `${NAS_IO_FRONTEND_URL}${community.link}/products${folder.resourceSlug}?accessToken=${emailToken}`,
    ...communityVariables,
  };

  const communityOwnerEmail = owner.email;
  const communityOwnerName = ownerMailName;

  const managerEmailConfig = mailUtils.retrieveManagerMailConfig(
    community.title,
    community.link,
    communityOwnerEmail,
    communityOwnerName
  );

  const config = receiptService.generateReceiptConfig({
    purchasedId: addonTransaction._id,
    purchaseType: PURCHASE_TYPE.FOLDER,
    entityObjectId: folder._id,
    communityObjectId: folder.communityObjectId,
    learnerObjectId,
    generateReceipt,
  });

  await notificationCommonService.sendMailToQueue(
    mailType,
    community.code ?? 'All',
    folder._id ?? 'All',
    toMail,
    toMailName,
    emailData,
    null,
    managerEmailConfig,
    config
  );
}

async function createFolderPurchase({
  folderObjectId,
  learner,
  purchaseType,
  addonTransaction,
  session = undefined,
}) {
  const folder = await CommunityFolders.findById(folderObjectId).lean();

  if (!folder) {
    logger.warn(`Folder ${folderObjectId} not found`);
    return;
  }

  const community = await CommunityModel.findOne({
    _id: folder.communityObjectId,
  }).lean();

  if (!community) {
    logger.warn(`Community ${folder.communityObjectId} not found`);
    return;
  }

  const { _id: learnerObjectId } = learner;

  const {
    amount,
    currency,
    local_amount: localAmount,
    local_currency: localCurrency,
    _id: addonTransactionObjectId,
  } = addonTransaction;

  const dataToInsert = {
    folderObjectId,
    purchaseType,
    learnerObjectId,
    amount,
    currency,
    local_amount: localAmount,
    local_currency: localCurrency,
    folderCheckoutId: addonTransactionObjectId,
  };

  const result = await FolderPurchaseModel.updateOne(
    {
      folderObjectId,
      learnerObjectId,
    },
    dataToInsert,
    { upsert: true, new: true, session }
  ).lean();

  if (result.upsertedCount > 0) {
    await formatAndSendFolderTypeEmail({
      mailType: FOLDER_MAIL_TYPES.COMMUNITY_FOLDER_PURCHASE_MEMBER,
      learnerObjectId,
      folder,
      learner,
      community,
      addonTransaction,
    });
  }
}

async function autoPurchaseFolder({
  learnerObjectId,
  folderObjectId,
  addonTransactionObjectId,
}) {
  const [addonTransaction, learner] = await Promise.all([
    addonTransactionObjectId
      ? AddonTransactionModel.findById(addonTransactionObjectId).lean()
      : null,
    LearnerModel.findById(learnerObjectId).lean(),
  ]);

  const { email } = learner;

  const isInternalDomain =
    CheckInternalMemberUtils.hasInternalDomain(email);

  let purchaseType = COMMUNITY_FOLDER_PURCHASE_TYPES.FREE;

  if (isInternalDomain) {
    purchaseType = COMMUNITY_FOLDER_PURCHASE_TYPES.INTERNAL;
  } else if (addonTransaction?.amount > 0) {
    purchaseType = COMMUNITY_FOLDER_PURCHASE_TYPES.PAID;
  }

  await createFolderPurchase({
    folderObjectId,
    learner,
    purchaseType,
    addonTransaction,
  });
}

module.exports = {
  getFolderById,
  getFoldersByCommunityId,
  getFilteredFoldersByCommunityId,
  createOneFolder,
  updateOneFolder,
  patchOneFolder,
  softDeleteFolderById,
  doesCommunityHavePaidFolders,
  createFolderPurchase,
  autoPurchaseFolder,
  getPublishedFolderCount,
};
