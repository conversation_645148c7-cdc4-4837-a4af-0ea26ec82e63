const { performance } = require('perf_hooks');
const ObjectId = require('mongoose').Types.ObjectId;
const logger = require('../../../services/logger.service');
const EmailUnsubscribeModel = require('../../models/emailUnsubscribe.model');
const CommunityModel = require('../../models/community.model');
const CommunityMagicReachEmail = require('../../../models/magicReach/communityMagicReachEmail.model');
const { getUploadedFileLink } = require('../../../services/upload');
const { IMAGEASSETBUCKET } = require('../../../constants/common');
const {
  sendDraftTemplateEmail,
} = require('../../../services/notification');
const MagicReachNodeWrapper = require('../../../services/magicReach/contentFormatter/MagicReachNodeWrapper');
const CommunityDraftTemplateModel = require('../../../models/notificationBackend/communityDraftTemplate.model');
const {
  NAS_IO_SERVICE_EMAIL,
  WHATSAPP_SERVICE_SQS_QUEUE_URL,
} = require('../../../config');

const {
  MAGIC_REACH_MESSAGE_STATUS,
  BUCKET_NAMES,
} = require('../../../services/magicReach/constants');
const { getMagicReachPipeline } = require('../web/magicReach.service');
const CommunityMagicReachEmailRecipientsModel = require('../../../models/magicReach/communityMagicReachEmailRecipients.model');
const {
  sendMessageToSQSFifoQueue,
} = require('../../../handlers/sqs.handler');

const createDraftMagicReachEmail = async (magicReachEmailData) => {
  try {
    const {
      learnerId,
      title,
      content,
      communityId,
      sentEmails,
      sentBucketName,
    } = magicReachEmailData;
    const documentData = {
      isDraft: true,
      title,
      content,
      author: learnerId,
      communityId,
      sentEmails,
      sentBucketName,
    };

    const document = await CommunityMagicReachEmail.create(documentData);
    return document;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

const getAllSentMagicReachEmails = async (params = {}) => {
  try {
    const { communityId } = params;
    const pipeline = [
      {
        $match: {
          communityId: new ObjectId(communityId),
          status: {
            $in: [
              MAGIC_REACH_MESSAGE_STATUS.SCHEDULED,
              MAGIC_REACH_MESSAGE_STATUS.SENT,
              MAGIC_REACH_MESSAGE_STATUS.PROCESSING,
              MAGIC_REACH_MESSAGE_STATUS.PROCESSING_FAILED,
              MAGIC_REACH_MESSAGE_STATUS.REVIEWING,
              MAGIC_REACH_MESSAGE_STATUS.BLOCKED,
            ],
          },
          selectedBuckets: { $ne: BUCKET_NAMES.MAGIC_LEADS_OUTREACH },
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'author',
          foreignField: '_id',
          as: 'author',
        },
      },
      {
        $unwind: {
          path: '$author',
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $addFields: {
          learnerObjectId: '$author.learner',
        },
      },
      {
        $lookup: {
          from: 'learners',
          localField: 'learnerObjectId',
          foreignField: '_id',
          as: 'learner',
        },
      },
      {
        $unwind: {
          path: '$learner',
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $lookup: {
          from: 'community_posts',
          localField: 'sentResults.AnnouncementV2.announcementObjectId',
          foreignField: '_id',
          as: 'announcementInfo',
        },
      },
      {
        $unwind: {
          path: '$announcementInfo',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          _id: 1,
          communityId: 1,
          title: 1,
          content: 1,
          sentBucketName: 1,
          isDemo: 1,
          sentPlatforms: 1,
          selectedUsers: 1,
          bucketFilters: 1,
          bucketMetas: 1,
          unselectedUsers: 1,
          selectedBuckets: 1,
          status: 1,
          isDraft: 1,
          sentEmails: 1,
          sentWhatsapp: 1,
          createdAt: 1,
          lastModifiedTimeStamp: 1,
          analyticsData: 1,
          sentOn: 1,
          toSendCount: 1,
          fraudInfo: 1,
          postImpressions: '$announcementInfo.impressions',
          pushedToQueueResults: 1,
          learnerObjectId: 1,
          schedule: 1,
          learner: {
            profileImage: '$learner.profileImage',
            firstName: '$learner.firstName',
            lastName: '$learner.lastName',
          },
          mentionedProducts: 1,
        },
      },
    ];
    const result = await CommunityMagicReachEmail.aggregate(pipeline);

    return result;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

const getAllDraftMagicReachEmails = async (params = {}) => {
  try {
    const { communityId } = params;

    const result = await CommunityMagicReachEmail.find({
      communityId,
      status: {
        $in: [MAGIC_REACH_MESSAGE_STATUS.DRAFT],
      },
      selectedBuckets: { $ne: BUCKET_NAMES.MAGIC_LEADS_OUTREACH },
    }).lean();

    return result;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

const updateDraftMagicReachEmail = async (magicReachEmailData) => {
  try {
    const {
      id,
      learnerId,
      title,
      content,
      communityId,
      sentEmails,
      sentBucketName,
    } = magicReachEmailData;
    const originalDocument = await CommunityMagicReachEmail.findById(id);
    if (!originalDocument) {
      throw new Error('Unable to find Draft Email');
    }

    const documentData = {
      isDraft: true,
      title: title || originalDocument.title,
      content: content || originalDocument.content,
      author: learnerId,
      communityId,
      sentEmails,
      sentBucketName,
    };
    await CommunityMagicReachEmail.findOneAndUpdate(
      { _id: id, communityId, isDraft: true },
      documentData
    );
    const updatedDocument = await CommunityMagicReachEmail.findById(id);
    return updatedDocument;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

const deleteDraftMagicReachEmail = async (params = {}) => {
  try {
    // TODO: Replace with soft delete
    const { id, communityId } = params;
    await CommunityMagicReachEmail.findOneAndDelete({
      _id: id,
      communityId,
      isDraft: true,
    });
    return;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

const sendDraftEmail = async (params = {}) => {
  try {
    const {
      id,
      communityId,
      toMail,
      isPreview,
      replyToMail,
      replyToMailName,
      activeBucketId = null,
      sentFromRecipients,
      platforms,
    } = params;

    logger.info(`sendDraftEmail params`, JSON.stringify(params));

    const communityData = await CommunityModel.findById(communityId);
    const draftEmailData = await CommunityMagicReachEmail.findById(id);
    const unsubscribedEmails = await EmailUnsubscribeModel.aggregate([
      {
        $match: {
          $or: [
            { communityObjectId: new ObjectId(communityId) },
            { communityObjectId: null },
          ],
        },
      },
      {
        $group: {
          _id: 'id',
          emails: { $push: '$email' },
        },
      },
    ]);
    const emailsToAvoid = new Set(unsubscribedEmails?.[0]?.emails ?? []);
    const rootPayload = {
      ...draftEmailData?.content?.root,
      communityData: {
        name: communityData?.title,
        thumbnailSrc: communityData?.thumbnailImgData?.mobileImgData?.src,
        By: replyToMailName,
        title: communityData?.title,
      },
    };
    const htmlGen = new MagicReachNodeWrapper(rootPayload);
    const htmlData = htmlGen.getHTML();

    const location = await getUploadedFileLink(
      IMAGEASSETBUCKET,
      `/nasIO/portal/draft-email-templates/${communityData?.code}`,
      `${id}.html`,
      htmlData,
      'text/html'
    );

    const templateDocumentData = {
      draftId: id,
      communityId: communityData._id,
      communityCode: communityData.code,
      templateLink: location,
      fromMail: NAS_IO_SERVICE_EMAIL,
      fromMailName: `${replyToMailName} from ${communityData?.title}`,
      replyToMail,
      replyToMailName,
      subject: isPreview
        ? `${draftEmailData.title} - PREVIEW`
        : draftEmailData.title,
    };

    logger.info(`Document Data for sendDraftEmail`, templateDocumentData);

    const templateDocument = await CommunityDraftTemplateModel.create(
      templateDocumentData
    );

    if (!activeBucketId && toMail) {
      logger.info(
        `This is going inside not activeBucketId && toMail community id ${communityId} and id ${id}`
      );
      // Selected Members only should get the email
      const toMailEmails = [];
      const toMailNames = [];
      const unsubscribedLinks = [];

      await Promise.all(
        toMail.map(async (t) => {
          if (!emailsToAvoid.has(t.email)) {
            toMailEmails.push(t.email);
            toMailNames.push(t.name);

            // const hash = await bcrypt.hash(
            //   `email: ${t.email}, communityCode: ${communityData.code}`,
            //   10
            // );
            // unsubscribedLinks.push(
            //   `${MAIN_PAYMENT_BACKEND_URL}/api/v1/email/unsubscribe?email=${encodeURIComponent(
            //     t.email
            //   )}&communityCode=${communityData.code}&token=${hash}`
            // );
          }
        })
      );

      const emailParams = {
        draftTemplateId: templateDocument._id,
        toMail: toMailEmails,
        toMailName: toMailNames,
        unsubscribedLinks,
      };

      const response = await sendDraftTemplateEmail(emailParams);
      if (!isPreview) {
        await CommunityMagicReachEmail.findOneAndUpdate(
          { _id: id, communityId, isDraft: true },
          {
            isDraft: false,
            sentOn: new Date(),
            sentEmails: toMail,
            sentPlatforms: platforms,
          }
        );
      }

      return { result: response, location };
    }

    let allData = [];

    if (sentFromRecipients) {
      logger.info(
        `sentFromRecipients is True, set data with recipients with draftId ${id}`
      );
      allData = await CommunityMagicReachEmailRecipientsModel.find({
        draftId: id,
      });
      logger.info(
        `allData inside from sentFromRecipients for draft id ${id} is ${JSON.stringify(
          allData
        )}`
      );
    } else {
      logger.info(
        `sentFromRecipients is False, set data via magic reach pipeline for draftId ${id} - communityId: ${communityId};  magicReachBucket: ${activeBucketId}`
      );
      // Pre Selected Bucket
      const pipelineData = await getMagicReachPipeline({
        communityId,
        magicReachBucket: activeBucketId,
        paginated: false,
      });

      allData = pipelineData[0].data;
      logger.info(
        `allData outside sentFromRecipients for draft id ${id} is ${JSON.stringify(
          allData
        )}`
      );
    }

    if (allData.length <= 0) {
      logger.info(`The email recipients list is empty for draftId ${id}`);
      throw new Error(
        'No email recipients found in the db. Not able to send email for now.'
      );
    }

    const chunks = [];
    const chunkSize = 100;
    // Chunk all data into 1000
    for (let i = 0; i < allData.length; i += chunkSize) {
      chunks.push(allData.slice(i, i + chunkSize));
    }
    logger.info(
      `Emails of length ${allData.length} split to ${chunks.length} chunks with size of ${chunkSize}`
    );
    chunks.forEach(async (chunk, index) => {
      const toMailEmails = [];
      const toMailNames = [];
      const unsubscribedLinks = [];

      await Promise.allSettled(
        chunk.map(async (t) => {
          if (!emailsToAvoid.has(t.email)) {
            toMailEmails.push(t.email);
            toMailNames.push(t.fullName);

            // const hash = await bcrypt.hash(
            //   `email: ${t.email}, communityCode: ${communityData.code}`,
            //   10
            // );
            // unsubscribedLinks.push(
            //   `${MAIN_PAYMENT_BACKEND_URL}/api/v1/email/unsubscribe?email=${encodeURIComponent(
            //     t.email
            //   )}&communityCode=${communityData.code}&token=${hash}`
            // );
          }
        })
      );
      const emailParams = {
        draftTemplateId: templateDocument._id,
        toMail: toMailEmails,
        toMailName: toMailNames,
        unsubscribedLinks,
      };
      const t0 = performance.now();
      logger.info(
        `Before sending to Noti batch ${index} of message ${id}`
      );
      await sendDraftTemplateEmail(emailParams);
      logger.info(
        `After sending to Noti batch ${index} of message ${id}: latency=${
          performance.now() - t0
        }ms`
      );
    });

    if (!isPreview) {
      logger.info(
        `Not a preview, updating draft magic reach email of id ${id} to non-draft state`
      );
      await CommunityMagicReachEmail.findOneAndUpdate(
        { _id: id, communityId, isDraft: true },
        {
          isDraft: false,
          sentOn: new Date(),
          sentEmails: allData.reduce((acc, val) => {
            if (!emailsToAvoid.has(val.email)) {
              return acc.concat(val.email);
            }
            return acc;
          }, []),
        }
      );
      logger.info(
        `Updated draft magic reach email of id ${id} to non-draft state`
      );
    }

    return { result: { success: true }, location };
  } catch (error) {
    logger.error('Error in sendDraftEmail function', error);
    throw error;
  }
};

const sendWhatsappTemplateMsg = async (params = {}) => {
  try {
    const {
      id,
      communityId,
      toMail,
      isPreview,
      isOnlyWhatsapp,
      activeBucketId = null,
      sentFromRecipients,
      platforms,
      templateObjectId,
    } = params;

    logger.info(`sendWhatsappTemplateMsg params`, JSON.stringify(params));

    const community = await CommunityModel.findById(
      new ObjectId(communityId)
    ).select('_id code title');
    const magicReachEmail = await CommunityMagicReachEmail.findOne({
      _id: new ObjectId(id),
    }).select('_id title');

    if (!magicReachEmail) {
      throw new Error(`MagicReachEmail with id ${id} does not exists`);
    }
    if (!magicReachEmail?.title || magicReachEmail?.title === '') {
      throw new Error(
        `MagicReachEmail with id ${id}'s title is not specified. Cannot send message.`
      );
    }

    //TODO: To eventually make this dependent on learnerObjectId instead of email
    //TODO: Run script to adjust all existing data to contain learnerObjectId
    const unsubscribedEmails = await EmailUnsubscribeModel.aggregate([
      {
        $match: {
          $or: [
            { communityObjectId: new ObjectId(communityId) },
            { communityObjectId: null },
          ],
        },
      },
      {
        $group: {
          _id: 'id',
          emails: { $push: '$email' },
        },
      },
    ]);
    const emailsToAvoid = new Set(unsubscribedEmails?.[0]?.emails ?? []);

    if (!activeBucketId && toMail) {
      logger.info(
        `This is going inside not activeBucketId && toMail community id ${communityId} and id ${id}`
      );
      // Selected Members only should get the email
      await Promise.allSettled(
        toMail.map(async (t) => {
          if (!t.phoneNumber || t.phoneNumber === '') {
            logger.info(
              `Learner with email ${t.email} does not have a phoneNumber. Not sending request to sqs.`
            );
          } else if (!emailsToAvoid.has(t.email)) {
            const messageBody = {
              email: t.email,
              phoneNumber: t.phoneNumber,
              templateObjectId,
              magicReachEmailId: id,
              data: {
                body: [community?.title, magicReachEmail?.title],
                buttons: [[`magicReachEmailId_${id}`]],
                communityTitle: community?.title,
                date: new Date().toISOString(),
              },
            };
            sendMessageToSQSFifoQueue(
              WHATSAPP_SERVICE_SQS_QUEUE_URL,
              `whatsapp_template_${templateObjectId}_mre_${id}`,
              messageBody
            );
          }
        })
      );

      if (!isPreview && isOnlyWhatsapp) {
        await CommunityMagicReachEmail.findOneAndUpdate(
          { _id: id, communityId, isDraft: true },
          {
            isDraft: false,
            sentOn: new Date(),
            sentPlatforms: platforms,
            sentWhatsapp: toMail.reduce((acc, val) => {
              if (
                !emailsToAvoid.has(val.email) &&
                val.phoneNumber &&
                val.phoneNumber !== ''
              ) {
                return acc.concat(val.email);
              }
              return acc;
            }, []),
          }
        );
      }

      return { result: { success: true } };
    }

    let allData = [];

    if (sentFromRecipients) {
      logger.info(
        `sentFromRecipients is True, set data with recipients with draftId ${id}`
      );
      allData = await CommunityMagicReachEmailRecipientsModel.find({
        draftId: id,
      });
      logger.info(
        `allData inside from sentFromRecipients for draft id ${id} is ${JSON.stringify(
          allData
        )}`
      );
    } else {
      logger.info(
        `sentFromRecipients is False, set data via magic reach pipeline for draftId ${id} - communityId: ${communityId};  magicReachBucket: ${activeBucketId}`
      );
      // Pre Selected Bucket
      const pipelineData = await getMagicReachPipeline({
        communityId,
        magicReachBucket: activeBucketId,
        paginated: false,
      });

      allData = pipelineData[0].data;
      logger.info(
        `allData outside sentFromRecipients for draft id ${id} is ${JSON.stringify(
          allData
        )}`
      );
    }

    if (allData.length <= 0) {
      logger.info(`The email recipients list is empty for draftId ${id}`);
      throw new Error(
        'No email recipients found in the db. Not able to send email for now.'
      );
    }

    const chunks = [];
    const chunkSize = 100;
    // Chunk all data into 1000
    for (let i = 0; i < allData.length; i += chunkSize) {
      chunks.push(allData.slice(i, i + chunkSize));
    }
    logger.info(
      `Emails of length ${allData.length} split to ${chunks.length} chunks with size of ${chunkSize}`
    );
    // eslint-disable-next-line no-unused-vars
    chunks.map(async (chunk, index) => {
      await Promise.allSettled(
        chunk.map(async (t) => {
          //TODO: To eventually make this dependent on learnerObjectId instead of email
          //TODO: Run script to adjust all existing data to contain learnerObjectId
          if (!t.phoneNumber || t.phoneNumber === '') {
            logger.info(
              `Learner with email ${t.email} does not have a phoneNumber.s Not sending request to sqs.`
            );
          } else if (!emailsToAvoid.has(t.email)) {
            // Send the whatsapp message
            const messageBody = {
              email: t.email,
              phoneNumber: t.phoneNumber,
              templateObjectId,
              magicReachEmailId: id,
              data: {
                body: [community?.title, magicReachEmail?.title],
                buttons: [[`magicReachEmailId_${id}`]],
                communityTitle: community?.title,
                date: new Date().toISOString(),
              },
            };
            sendMessageToSQSFifoQueue(
              WHATSAPP_SERVICE_SQS_QUEUE_URL,
              `whatsapp_template_${templateObjectId}_mre_${id}`,
              messageBody
            );
          }
        })
      );
    });

    if (!isPreview && isOnlyWhatsapp) {
      logger.info(
        `Not a preview, updating draft magic reach email of id ${id} to non-draft state`
      );
      await CommunityMagicReachEmail.findOneAndUpdate(
        { _id: id, communityId, isDraft: true },
        {
          isDraft: false,
          sentOn: new Date(),
          sentPlatforms: platforms,
          sentWhatsapp: allData.reduce((acc, val) => {
            if (
              !emailsToAvoid.has(val.email) &&
              val.phoneNumber &&
              val.phoneNumber !== ''
            ) {
              return acc.concat(val.email);
            }
            return acc;
          }, []),
        }
      );
      logger.info(
        `Updated draft magic reach email of id ${id} to non-draft state`
      );
    }

    return { result: { success: true } };
  } catch (error) {
    logger.error('Error in sendDraftEmail function', error);
    throw error;
  }
};
module.exports = {
  createDraftMagicReachEmail,
  getAllDraftMagicReachEmails,
  getAllSentMagicReachEmails,
  updateDraftMagicReachEmail,
  deleteDraftMagicReachEmail,
  sendDraftEmail,
  sendWhatsappTemplateMsg,
};
