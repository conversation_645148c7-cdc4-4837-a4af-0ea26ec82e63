const aclRoles = {
  SUPER_ADMIN: 'superAdmin',
  ADMIN: 'admin',
  OWNER: 'owner',
  MANAGER: 'manager',
  MEMBER: 'member',
};

const aclRolesEnum = Object.keys(aclRoles);

const aclAccess = {
  ALL: 'all',
  PUBLISHED_VIEW: 'publishedView',
  MANAGER_VIEW: 'managerView',
  REMOVE_MANAGER: 'removeManager',
  ADD_MANAGER: 'addManager',
  DELETE_MEMBER: 'deleteMembers',
  ADD_MEMBERS: 'addMembers',
  TRANSFER_OWNERSHIP: 'transferOwnership',
};

const aclAccessEnum = Object.values(aclAccess);

const analyticsTypesMap = {
  DAILY: 'daily',
  WEEKLY: 'weekly',
  MONTHLY: 'monthly',
  YEARLY: 'yearly',
  OVERALL: 'overall',
};

const analyticsTypes = [
  analyticsTypesMap.DAILY,
  analyticsTypesMap.WEEKLY,
  analyticsTypesMap.MONTHLY,
  analyticsTypesMap.YEARLY,
  analyticsTypesMap.OVERALL,
];

const communityTodoModesMap = {
  WEEKLY: 'weekly',
  OVERALL: 'overall',
};

const communityTodoModes = [
  communityTodoModesMap.WEEKLY,
  communityTodoModesMap.OVERALL,
];

const communityTodoTypesMap = {
  VIDEO: 'video',
  PROFILE: 'profile',
  PLATFORM: 'platform',
  EVENT: 'event',
  MEMBERS: 'members',
  LIBRARY: 'library',
};

const communityTodoTypes = [
  communityTodoTypesMap.EVENT,
  communityTodoTypesMap.LIBRARY,
  communityTodoTypesMap.MEMBERS,
  communityTodoTypesMap.PLATFORM,
  communityTodoTypesMap.PROFILE,
  communityTodoTypesMap.VIDEO,
];

const communityCompletedStepsMap = {
  COMMUNITY_NAME: 'COMMUNITY_NAME',
  CHOOSE_CHAT_PLATFORM: 'CHOOSE_CHAT_PLATFORM',
  COMMUNITY_PLATFORM_CELEBRATION: 'COMMUNITY_PLATFORM_CELEBRATION',
  USER_PROFILE_LOGIN: 'USER_PROFILE_LOGIN',
  USER_PROFILE_CREATION: 'USER_PROFILE_CREATION',
  PRICE_SELECTION: 'PRICE_SELECTION',
};

const communityManagerTodoCodeMap = {
  COMMUNITY_SETUP_NAME: 'COMMUNITY_SETUP_NAME',
  COMMUNITY_SETUP_MEMBER_ACCESS: 'COMMUNITY_SETUP_MEMBER_ACCESS',
  COMMUNITY_SETUP_CHAT: 'COMMUNITY_SETUP_CHAT',
  COMMUNITY_SETUP_BANNER: 'COMMUNITY_SETUP_BANNER',
  COMMUNITY_SETUP_DESCRIPTION: 'COMMUNITY_SETUP_DESCRIPTION',
  COMMUNITY_SETUP_ANNOUNCEMENT: 'COMMUNITY_SETUP_ANNOUNCEMENT',
  COMMUNITY_SETUP_LIBRARY: 'COMMUNITY_SETUP_LIBRARY',
  COMMUNITY_SETUP_EVENT: 'COMMUNITY_SETUP_EVENT',
  COMMUNITY_SETUP_INVITE: 'COMMUNITY_SETUP_INVITE',
  COMMUNITY_SETUP_OWNER_PROFILE_PIC: 'COMMUNITY_SETUP_OWNER_PROFILE_PIC',
  COMMUNITY_SETUP_MAGIC_REACH: 'COMMUNITY_SETUP_MAGIC_REACH',
  COMMUNITY_DISCORD_VERIFICATION_GENERAL:
    'COMMUNITY_DISCORD_VERIFICATION_GENERAL',
  COMMUNITY_DISCORD_VERIFICATION_NFT_HOLDERS:
    'COMMUNITY_DISCORD_VERIFICATION_NFT_HOLDERS',
  COMMUNITY_WHATSAPP_BOT_SETUP: 'COMMUNITY_WHATSAPP_BOT_SETUP',
};

const communityManagerTodoTypesMap = {
  COMMUNITY_SETUP: 'communitySetup',
};

const communityManagerTodoTypes = [
  communityManagerTodoTypesMap.COMMUNITY_SETUP,
];

const communityLibraryTypesMap = {
  CLASS: 'Class',
  COLLECTION: 'Collection',
  VIDEO: 'Video',
  RESOURCE: 'Resource',
  LINK: 'Link',
  DIGITAL_PRODUCT: 'DIGITAL_PRODUCT',
  SESSION: 'SESSION',
};

const communityFolderItemTypesMap = {
  EMBEDDED_VIDEO: 'embedded_video_link',
  VIDEO: 'video',
  LINK: 'external_link',
  FILE: 'file',
  IMG: 'image',
  AUDIO: 'audio',
  SECTION: 'section',
};

const communityLibraryStatusMap = {
  DELETED: 'Deleted',
  PUBLISHED: 'Published',
  UNPUBLISHED: 'Unpublished',
  DRAFT: 'Draft',
  ERROR: 'Error',
  PROCESSING: 'Processing',
};

const FOLDER_ITEM_STATUS = {
  PUBLISHED: 'Published',
  UNPUBLISHED: 'Unpublished',
  PROCESSING: 'Processing',
  DELETED: 'Deleted',
  DRAFT: 'Draft',
  ERROR: 'Error',
};

const communityLibraryTemplatesMap = {
  NEW: 'new',
  COURSE: 'course',
  EBOOK: 'ebook',
  RESOURCE: 'resource',
  CREATIVE_STORYTELLING: 'creative_storytelling',
  SOCIAL_MEDIA_MASTERY: 'social_media_mastery',
  BRAND_CONSULTATION: 'brand_consultation',
  FINANCE_GOALS_TRACKER: 'finance_goals_tracker',
  PERSONAL_FINANCE_COURSE: 'personal_finance_course',
  BUDGET_PLANNING_SESSION: 'budget_planning_session',
  CAREER_TOOLKIT_BUNDLE: 'career_toolkit_bundle',
  PORTFOLIO_BUILDING: 'portfolio_building',
  RESUME_REVIEW: 'resume_review',
  HOME_WORKOUT_GUIDE: 'home_workout_guide',
  STRENGTH_YOGA: 'strength_yoga',
  FITNESS_TRAINING: 'fitness_training',
  HEALTHY_EATING: 'healthy_eating',
  NUTRITION_SCIENCE: 'nutrition_science',
  MEAL_PLANNING: 'meal_planning',
  MEDITATION_AUDIO: 'meditation_audio',
  WORLD_RELIGIONS: 'world_religions',
  SPIRITUAL_CONSULTATION: 'spiritual_consultation',
};

const communityLibraryTemplates = Object.values(
  communityLibraryTemplatesMap
);
const communityLibraryStatus = [
  communityLibraryStatusMap.DELETED,
  communityLibraryStatusMap.PUBLISHED,
  communityLibraryStatusMap.UNPUBLISHED,
  communityLibraryStatusMap.DRAFT,
  communityLibraryStatusMap.ERROR,
  communityLibraryStatusMap.PROCESSING,
];

const communityFolderItemTypes = [
  communityFolderItemTypesMap.EMBEDDED_VIDEO,
  communityFolderItemTypesMap.FILE,
  communityFolderItemTypesMap.LINK,
  communityFolderItemTypesMap.VIDEO,
  communityFolderItemTypesMap.IMG,
  communityFolderItemTypesMap.AUDIO,
  communityFolderItemTypesMap.SECTION,
];

const communityFolderTypesMap = {
  CLASS: 'class',
  COLLECTION: 'collection',
  SESSION: 'SESSION',
  DIGITAL_PRODUCT: 'DIGITAL_PRODUCT',
  COURSE: 'COURSE',
};

const communityFolderTypes = [
  communityFolderTypesMap.SESSION,
  communityFolderTypesMap.DIGITAL_PRODUCT,
  communityFolderTypesMap.COURSE,
];

const EMBEDDED_VIDEO_PLATFORMS = {
  YOUTUBE: 'youtube',
  VIMEO: 'vimeo',
};

const defaultPostUserParams = {
  firstName: 'Nas Academy',
  lastName: 'User',
  profileImage:
    'https://d2yjtdaqamc55g.cloudfront.net/randomProfileImage14.jpg',
};

const DEFAULT_LEARNER_IMAGE_PREFIXES = [
  'https://d2yjtdaqamc55g.cloudfront.net/randomProfileImage',
  'https://d2oi1rqwb0pj00.cloudfront.net/nasIO/community-landing-page/png/placeholder-user-profile-image',
];

const COMMUNITY_FOLDER_STATUS = {
  UNPUBLISHED: 'Unpublished',
  PUBLISHED: 'Published',
  DELETED: 'Deleted',
};

const LEGENDS_COMMUNITY_CODE = 'LEGENDS_COMMUNITY';
const INDIAUPI_COMMUNITY_CODE = 'INDIAUPI';
const LATAM_COMMUNITY_CODE = 'NAS.IO_ESPAOL';
const COUNTRY_CREATED = {
  INDIA: 'India',
  ARGENTINA: 'Argentina',
  CHILE: 'Chile',
  ECUADOR: 'Ecuador',
  PERU: 'Peru',
  GUATEMALA: 'Guatemala',
  DOMINICAN_REPUBLIC: 'Dominican Republic',
  SURINAME: 'Suriname',
  CUBA: 'Cuba',
  BRAZIL: 'Brazil',
  PARAGUAY: 'Paraguay',
  VENEZUELA: 'Venezuela',
  EL_SALVADOR: 'El Salvador',
  HONDURAS: 'Honduras',
  PANAMA: 'Panama',
  GUYANA: 'Guyana',
  BELIZE: 'Belize',
  COLOMBIA: 'Colombia',
  MEXICO: 'Mexico',
  BOLIVIA: 'Bolivia',
  URUGUAY: 'Uruguay',
  COSTA_RICA: 'Costa Rica',
  NICARAGUA: 'Nicaragua',
  HAITI: 'Haiti',
  PUERTO_RICO: 'Puerto Rico',
  UNITED_STATES: 'United States',
};

const latamCountriesArray = [
  COUNTRY_CREATED.ARGENTINA,
  COUNTRY_CREATED.BELIZE,
  COUNTRY_CREATED.BOLIVIA,
  COUNTRY_CREATED.BRAZIL,
  COUNTRY_CREATED.CHILE,
  COUNTRY_CREATED.COLOMBIA,
  COUNTRY_CREATED.COSTA_RICA,
  COUNTRY_CREATED.CUBA,
  COUNTRY_CREATED.DOMINICAN_REPUBLIC,
  COUNTRY_CREATED.ECUADOR,
  COUNTRY_CREATED.EL_SALVADOR,
  COUNTRY_CREATED.GUATEMALA,
  COUNTRY_CREATED.GUYANA,
  COUNTRY_CREATED.HAITI,
  COUNTRY_CREATED.HONDURAS,
  COUNTRY_CREATED.MEXICO,
  COUNTRY_CREATED.NICARAGUA,
  COUNTRY_CREATED.PANAMA,
  COUNTRY_CREATED.PARAGUAY,
  COUNTRY_CREATED.PERU,
  COUNTRY_CREATED.PUERTO_RICO,
  COUNTRY_CREATED.SURINAME,
  COUNTRY_CREATED.URUGUAY,
  COUNTRY_CREATED.VENEZUELA,
];

const discordVerificationChannelInit = {
  nameOfTheCategory: 'verification-channel',
  nameOfTheTextChannel: 'verify-with-nas-io',
  reason: 'to create a channel so that the users can verify',
  message: {
    messageTitle: 'NAS.IO',
    messageDescription: '',
    titleURL: 'https://www.nas.io',
    messageThumbNail:
      'https://s3.ap-southeast-1.amazonaws.com/image-assets.nasdaily.com/nasIO/new_home/png/nas_io_logo.png',
    fieldName: 'Verify your assets',
    fieldInformation:
      'This is a read-only connection. Do not share your private keys. We will never ask for your send phrase. We will never DM you.',
    buttonLabel: "Let's go",
  },
  sendMessage: true,
};

const discordGetAccessVerificationChannelInit = {
  nameOfTheCategory: 'verification-channel',
  nameOfTheTextChannel: 'get-access-here',
  reason: 'to create a channel so that the users can verify',
  message: {
    messageTitle: 'Nas bot',
    messageDescription: '',
    titleURL: 'https://www.nas.io',
    messageThumbNail:
      'https://s3.ap-southeast-1.amazonaws.com/image-assets.nasdaily.com/nasIO/new_home/png/nas_io_circular_logo.png',
    fieldName: 'Join nas.io',
    fieldInformation:
      'Signup and get access to the community portal and Discord by clicking below',
    buttonLabel: 'Verify me',
  },
  sendMessage: true,
};

const BOT_STATUSES = {
  PENDING: 'pending',
  CONNECTED: 'connected',
  ERROR: 'error',
};

const COMMUNITY_USER_STATUSES = {
  ALREADY_ADMIN: 'ALREADY_ADMIN',
  ALREADY_MEMBER: 'ALREADY_MEMBER',
  EXISTING_USER: 'EXISTING_USER',
  NEW_USER: 'NEW_USER',
};

const COMMUNITY_ADMIN_LIMIT = 10;

const EVENT_STATUS = {
  DRAFT: 'draft',
  PUBLISHED: 'published',
  DELETED: 'deleted',
  ACTIVE: 'Active', // to deprecate
};

const COMMUNITY_EVENT_STATUSES = {
  PAST: 'past',
  ONGOING: 'ongoing',
  UPCOMING: 'upcoming',
};

const COMMUNITY_POSTS_VISIBILITY_TYPE_MAP = {
  PUBLIC: 'public', // visible to everyone
  MEMBERS: 'members', // visible to only members
};

const COMMUNITY_POSTS_STATUS = {
  APPROVED: 'approved',
  PENDING: 'pending',
  REJECTED: 'rejected',
};

const MAX_PINNED_ANNOUNCEMENTS = 3;

const COMMUNITY_POSTS_STATUS_LIST = [
  COMMUNITY_POSTS_STATUS.APPROVED,
  COMMUNITY_POSTS_STATUS.PENDING,
  COMMUNITY_POSTS_STATUS.REJECTED,
];
const COMMUNITY_POSTS_POSTED_BY = {
  MEMBER: 'MEMBER',
  CREATOR: 'CREATOR',
};

const COMMUNITY_POSTS_POSTED_BY_LIST = ['MEMBER', 'CREATOR'];
const COMMUNITY_POSTS_VISIBILITY_LIST = [
  COMMUNITY_POSTS_VISIBILITY_TYPE_MAP.PUBLIC,
  COMMUNITY_POSTS_VISIBILITY_TYPE_MAP.MEMBERS,
];

const COMMUNITY_EVENT_ACCESS_TYPES = {
  FREE: 'free',
  PAID: 'paid',
  WEB3: 'web3',
};

const COMMUNITY_EVENT_ATTENDEE_PURCHASE_TYPES = {
  FREE: 'free',
  PAID: 'paid',
  INTERNAL: 'nasacademy',
  NFT: 'nft',
};

const COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES = {
  GOING: 'GOING',
  NOT_GOING: 'NOT_GOING',
  PENDING: 'PENDING',
  REJECTED: 'REJECTED',
  REMOVED: 'REMOVED',
};

// Read more for knowing about versioning: https://nasdaily.larksuite.com/wiki/GhiFwKRcKi0xfBkOXhGu8amFsFc
const COMMUNITY_EVENT_QR_CODE_TICKET_V1 = 'v1';
const COMMUNITY_EVENT_ACTIVE_QR_CODE_TICKET_VERSION =
  COMMUNITY_EVENT_QR_CODE_TICKET_V1;

const PAYMENT_STATUSES = {
  INCOMPLETE: 'incomplete',
  PENDING: 'pending',
  SUCCESS: 'success',
  FAILED: 'failed',
  RENEWAL_FAILED: 'renewal_failed',
  REFUNDED: 'refunded',
  REFUNDED_FAILED: 'refunded_failed',
};

const COMMUNITY_FOLDER_PURCHASE_TYPES = {
  FREE: 'free',
  PAID: 'paid',
  INTERNAL: 'nasacademy',
  NFT: 'nft',
};

const COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES = {
  PENDING: 'PENDING',
  CANCELLED: 'CANCELLED',
  BOOKED: 'BOOKED',
};

const EVENT_PAYMENT_STATUSES = {
  INCOMPLETE: 'incomplete',
  SUCCESS: 'success',
  FAILED: 'failed',
};

const COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES = {
  INCOMPLETE: 'incomplete',
  PENDING: 'pending',
  SUCCESS: 'success',
  FAILED: 'failed',
  REFUNDED: 'refunded',
  REFUNDED_FAILED: 'refunded_failed',
};

const COMMUNITY_ONE_TIME_PAYMENT_ENTITIES = {
  EVENT: 'community_events',
  FOLDER: 'community_folders',
  CHALLENGE: 'program',
  ZERO_LINK: 'zero_link',
};

const COMMUNITY_RECURRING_PAYMENT_ENTITIES = {
  SUBSCRIPTION: 'community_purchase_transactions',
};

const CANCELLATION_REVOKE_BUFFER_HOURS = 2;
const CANCELLATION_REVOKE_BUFFER_MS =
  CANCELLATION_REVOKE_BUFFER_HOURS * 60 * 60 * 1000;

const COUNTRY_WISE_CURRENCY = {
  SINGAPORE: {
    country: 'Singapore',
    currency: 'SGD',
  },
  INDIA: {
    country: 'India',
    currency: 'INR',
  },
  PHILIPPINES: {
    country: 'Philippines',
    currency: 'PHP',
  },
  INDONESIA: {
    country: 'Indonesia',
    currency: 'IDR',
  },
  COLOMBIA: {
    country: 'Colombia',
    currency: 'COP',
  },
};

const DEFAULT_COUNTRY_WISE_PRICE_CONFIG = [
  {
    ...COUNTRY_WISE_CURRENCY.SINGAPORE,
    amount: null,
    localiseBasePrice: true,
  },
  {
    ...COUNTRY_WISE_CURRENCY.INDIA,
    amount: null,
    localiseBasePrice: true,
  },
  {
    ...COUNTRY_WISE_CURRENCY.PHILIPPINES,
    amount: null,
    localiseBasePrice: true,
  },
  {
    ...COUNTRY_WISE_CURRENCY.INDONESIA,
    amount: null,
    localiseBasePrice: true,
  },
];

const STRIPE_PRICE_LIMITS_IN_CENTS = {
  MIN: 50,
  MAX: 99999999,
};

const CENTS_PER_DOLLAR = 100;

const SIGNEDURL_UPLOAD_VIDEO_MIMETYPE = ['video/mp4', 'video/quicktime'];
const SIGNEDURL_UPLOAD_FILE_MIMETYPE = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/zip',
  'text/plain',
  'text/csv',
  'application/zip-compressed',
  'application/x-zip-compressed',
  'multipart/x-zip',
];

const SIGNEDURL_UPLOAD_IMAGE_MIMETYPE = [
  'image/jpeg',
  'image/jpg',
  'image/png',
];

const SIGNEDURL_UPLOAD_AUDIO_MIMETYPE = ['audio/mpeg', 'audio/wav'];

const TAB_FILTERS = {
  ALL: 'all',
  APPROVED: 'approved',
  WHATSAPP: 'whatsapp',
  MANAGER: 'manager',
  PENDING: 'pending',
};

const MEMBERS_DETAILS_DATA = {
  ADD: 'ADD',
  REMOVE: 'REMOVE',
};

const WHATSAPP_QUEUE_EVENTS = {
  REMOVE: 'REMOVE',
};

const MEMBERSHIP_PLAN_TYPE = {
  ANNUALLY: 'Annually',
  MONTHLY: 'Monthly',
  QUARTERLY: 'Quarterly',
  SEMIANNUALLY: 'Semi Annually',
};

const MEMBERSHIP_PLAN_INTERVAL = {
  YEAR: 'year',
  MONTH: 'month',
};

const MEMBERSHIP_PLAN_COMBINED_INTERVAL = {
  ANNUALLY: '1-year',
  MONTHLY: '1-month',
  QUARTERLY: '3-month',
  SEMIANNUALLY: '6-month',
};

const TRANSACTION_STATUS = {
  SUCCESS: 'Success',
  FAILED: 'Failed',
};

module.exports = {
  aclAccess,
  aclAccessEnum,
  aclRoles,
  aclRolesEnum,
  analyticsTypesMap,
  analyticsTypes,
  communityCompletedStepsMap,
  communityTodoModesMap,
  communityTodoModes,
  communityTodoTypesMap,
  communityTodoTypes,
  communityManagerTodoCodeMap,
  communityManagerTodoTypesMap,
  communityManagerTodoTypes,
  communityLibraryTypesMap,
  communityFolderItemTypesMap,
  communityFolderItemTypes,
  communityFolderTypesMap,
  communityFolderTypes,
  communityLibraryStatusMap,
  communityLibraryStatus,
  defaultPostUserParams,
  DEFAULT_LEARNER_IMAGE_PREFIXES,
  COMMUNITY_FOLDER_STATUS,
  LEGENDS_COMMUNITY_CODE,
  discordVerificationChannelInit,
  discordGetAccessVerificationChannelInit,
  BOT_STATUSES,
  EMBEDDED_VIDEO_PLATFORMS,
  COMMUNITY_ADMIN_LIMIT,
  COMMUNITY_USER_STATUSES,
  COMMUNITY_EVENT_STATUSES,
  COMMUNITY_EVENT_ACCESS_TYPES,
  COMMUNITY_EVENT_ATTENDEE_PURCHASE_TYPES,
  COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES,
  COMMUNITY_EVENT_QR_CODE_TICKET_V1,
  COMMUNITY_EVENT_ACTIVE_QR_CODE_TICKET_VERSION,
  COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES,
  COMMUNITY_FOLDER_PURCHASE_TYPES,
  EVENT_STATUS,
  EVENT_PAYMENT_STATUSES,
  COUNTRY_WISE_CURRENCY,
  DEFAULT_COUNTRY_WISE_PRICE_CONFIG,
  SIGNEDURL_UPLOAD_VIDEO_MIMETYPE,
  SIGNEDURL_UPLOAD_FILE_MIMETYPE,
  COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES,
  COMMUNITY_ONE_TIME_PAYMENT_ENTITIES,
  COMMUNITY_RECURRING_PAYMENT_ENTITIES,
  CANCELLATION_REVOKE_BUFFER_HOURS,
  CANCELLATION_REVOKE_BUFFER_MS,
  SIGNEDURL_UPLOAD_IMAGE_MIMETYPE,
  SIGNEDURL_UPLOAD_AUDIO_MIMETYPE,
  STRIPE_PRICE_LIMITS_IN_CENTS,
  CENTS_PER_DOLLAR,
  TAB_FILTERS,
  MEMBERS_DETAILS_DATA,
  WHATSAPP_QUEUE_EVENTS,
  MEMBERSHIP_PLAN_TYPE,
  MEMBERSHIP_PLAN_INTERVAL,
  INDIAUPI_COMMUNITY_CODE,
  COUNTRY_CREATED,
  communityLibraryTemplates,
  communityLibraryTemplatesMap,
  COMMUNITY_POSTS_VISIBILITY_TYPE_MAP,
  COMMUNITY_POSTS_VISIBILITY_LIST,
  MEMBERSHIP_PLAN_COMBINED_INTERVAL,
  LATAM_COMMUNITY_CODE,
  latamCountriesArray,
  PAYMENT_STATUSES,
  COMMUNITY_POSTS_STATUS,
  COMMUNITY_POSTS_STATUS_LIST,
  COMMUNITY_POSTS_POSTED_BY,
  COMMUNITY_POSTS_POSTED_BY_LIST,
  MAX_PINNED_ANNOUNCEMENTS,
  TRANSACTION_STATUS,
  FOLDER_ITEM_STATUS,
};
