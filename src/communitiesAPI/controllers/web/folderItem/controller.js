const { ObjectId } = require('mongoose').Types;
const status = require('http-status');

const storageUsageService = require('@services/featurePermissions/storageUsage.service');
const storageReservationService = require('@services/featurePermissions/storageReservation.service');
const CommunityFolderItemSignedUrlDetails = require('../../../models/communityFolderItemSignedUrlDetails.model');
const CommunityFolderItems = require('../../../models/communityFolderItems.model');
const Community = require('../../../models/community.model');

const { communityFolderItemsService } = require('../../../services/web');
const logger = require('../../../../services/logger.service');
const {
  createFolderItemSchema,
  updateFolderItemSchema,
} = require('../../../apiSchemas/communityFolderItems.schema');
const { signedUrlForUploadService } = require('../../../services/web');
const fraudService = require('../../../../services/fraud');
const {
  communityFolderItemTypesMap,
  communityLibraryStatusMap,
} = require('../../../constants');
const {
  ParamError,
  ToUserError,
} = require('../../../../utils/error.util');
const {
  GENERIC_ERROR,
  FEATURE_PERMISSION_ERROR,
} = require('../../../../constants/errorCode');
const {
  FOLDER_ITEM_COVER_MEDIA_FOLDER_TYPES,
} = require('../../../../constants/coverMediaItems.constant');

const validateIndexForFolderType = (body) => {
  const { folderType, index } = body;

  if (!folderType) return;

  if (index !== undefined) return;

  const coverMediaFolderTypes = Object.values(
    FOLDER_ITEM_COVER_MEDIA_FOLDER_TYPES
  );
  const indexNotRequiredFolderTypes = [
    'challenge_checkpoint',
    ...coverMediaFolderTypes,
  ];

  if (indexNotRequiredFolderTypes.includes(folderType.toLowerCase())) {
    return;
  }

  if (body.index === undefined) {
    throw new ParamError('Index is required');
  }
};

const validateCoverMediaFolderType = (body) => {
  const { folderType } = body;
  if (!folderType) return;

  const isCoverMediaFolderType = Object.values(
    FOLDER_ITEM_COVER_MEDIA_FOLDER_TYPES
  ).includes(folderType?.toLowerCase());

  if (!isCoverMediaFolderType) return;

  // validate video is less than 180s
  if (body.duration && body.duration > 180) {
    throw new ParamError('Video duration should be less than 180s');
  }
};

const createOneFolderItem = async (req, res, next) => {
  try {
    const input = req.body;
    const { signedUrlId } = req.body;
    const { communityId, communityFolderId } = req.params;
    const { learner } = req.user;
    const { VIDEO, FILE, IMG, AUDIO } = communityFolderItemTypesMap;
    input.communityObjectId = communityId;
    input.communityFolderObjectId = communityFolderId;

    logger.info(
      `createOneFolderItem: signedUrlId: ${signedUrlId} for the folder id ${communityFolderId} in the community ${communityId}  `
    );
    let reserveStorageId = null;
    if (signedUrlId) {
      const signedUrlDetails =
        await CommunityFolderItemSignedUrlDetails.findOne({
          _id: new ObjectId(signedUrlId),
        });
      const { videoObjectId, location, s3Key } = signedUrlDetails;
      reserveStorageId = signedUrlDetails.reserveStorageId;

      if (VIDEO) {
        input.videoObjectId = videoObjectId;
      }

      if (input.type === VIDEO) {
        // make sure that video exists on the s3 location
        const videoExists =
          await signedUrlForUploadService.checkIfVideoFileExists(s3Key);

        if (!videoExists) {
          const error = new Error('Video file does not exists');
          error.status = status.BAD_REQUEST;
          throw error;
        }
      }
      if ([FILE, IMG, AUDIO].includes(input.type)) {
        input.link = location;
      }
    }

    validateCoverMediaFolderType(input);

    logger.info(
      `createOneFolderItem: for communityFolderId ${communityFolderId} and communityId ${communityId} and with SignedUrlId ${signedUrlId}  input: `,
      input
    );

    // const isQueryValid = await createFolderItemSchema.isValid(input);
    // if (!isQueryValid) {
    //   throw new Error('Invalid Parameters');
    // }
    try {
      await createFolderItemSchema.validate(input);
    } catch (err) {
      throw new ParamError(`Invalid Parameters: ${err.message}`);
    }

    validateIndexForFolderType(input);
    const body = createFolderItemSchema.cast(input);
    if (body.folderType) {
      body.folderType = body.folderType.toLowerCase();
    }

    // Set file size from request if provided (from fileSize query param in getS3SignendUrl)
    if (input.size) {
      body.size = input.size.toString();
    }

    const folderItem =
      await communityFolderItemsService.createOneFolderItem({
        ...body,
        createdByLearnerObjectId: learner._id,
      });

    const { _id } = folderItem || {};

    if (signedUrlId) {
      await CommunityFolderItemSignedUrlDetails.findOneAndUpdate(
        {
          _id: new ObjectId(signedUrlId),
        },
        {
          $set: {
            communityFolderItemObjectId: _id,
          },
        }
      );
    }

    // Update storage usage after successful folder item creation
    if (folderItem.size) {
      try {
        await storageUsageService.incrementStorageUsage(
          communityId,
          parseInt(folderItem.size, 10)
        );
        logger.info(
          'Storage usage incremented after folder item creation',
          {
            communityId,
            folderItemId: _id,
            fileSize: folderItem.size,
          }
        );

        // Clear storage reservation if it exists
        if (reserveStorageId) {
          await storageUsageService.clearStorageReservation(
            reserveStorageId,
            communityId
          );
        }
      } catch (storageError) {
        logger.error(
          'Failed to increment storage usage after folder item creation',
          {
            communityId,
            folderItemId: _id,
            fileSize: folderItem.size,
            error: storageError.message,
          }
        );
      }
    }
    logger.info('createOneFolderItem: folderItem: ', folderItem);

    res.status(200).json(folderItem);
  } catch (err) {
    logger.error(
      'Error creating folder from the community: ',
      err,
      err.stack
    );
    if (!err.status) {
      err.status = status.INTERNAL_SERVER_ERROR;
    }
    return next(err);
  }
};

const updateOneFolderItem = async (req, res, next) => {
  try {
    const input = req.body;
    const { communityId, communityFolderItemId } = req.params;
    input.communityObjectId = communityId;
    validateIndexForFolderType(input);
    const isQueryValid = await updateFolderItemSchema.isValid(input);

    if (!isQueryValid) {
      throw new ParamError('Invalid Parameters');
    }

    const body = updateFolderItemSchema.cast(input);
    if (body.folderType) {
      body.folderType = body.folderType.toLowerCase();
    }

    let folderItem = await communityFolderItemsService.updateOneFolderItem(
      { _id: new ObjectId(communityFolderItemId) },
      body
    );

    folderItem = await communityFolderItemsService.getFolderItemById(
      communityFolderItemId,
      true,
      { communityObjectId: new ObjectId(communityId) }
    );

    res.json(folderItem);
  } catch (err) {
    logger.error(
      'Error updating folder item from the community: ',
      err,
      err.stack
    );
    if (!err.status) {
      err.status = status.INTERNAL_SERVER_ERROR;
    }
    return next(err);
  }
};

const getFolderItemById = async (req, res) => {
  try {
    const { communityId, communityFolderItemId } = req.params;

    const folderItem = await communityFolderItemsService.getFolderItemById(
      communityFolderItemId,
      true,
      { communityObjectId: new ObjectId(communityId) }
    );

    res.status(status.OK).json({
      data: folderItem,
      message: 'Folder item fetched successfully',
    });
  } catch (error) {
    logger.error(
      'Error getting folder item from the community: ',
      error,
      error.stack
    );
    error.status = 400;
    return res.status(error.status).json({
      data: null,
      errorCode: error.status,
      errorMessage: 'Error getting folder item from the community',
      error: error.message,
    });
  }
};
const deleteOneFolderItem = async (req, res, next) => {
  const { communityId, communityFolderItemId } = req.params;

  try {
    const folderItem = await communityFolderItemsService.getFolderItemById(
      communityFolderItemId,
      true,
      { communityObjectId: new ObjectId(communityId) }
    );

    if (folderItem) {
      // Decrement storage usage before deletion if the item has a size
      if (folderItem.size) {
        try {
          await storageUsageService.decrementStorageUsage(
            communityId,
            parseInt(folderItem.size, 10)
          );
          logger.info(
            'Storage usage decremented after folder item deletion',
            {
              communityId,
              folderItemId: communityFolderItemId,
              fileSize: folderItem.size,
            }
          );
        } catch (storageError) {
          logger.error(
            'Failed to decrement storage usage after folder item deletion',
            {
              communityId,
              folderItemId: communityFolderItemId,
              fileSize: folderItem.size,
              error: storageError.message,
            }
          );
        }
      }

      const deleted =
        await communityFolderItemsService.softDeleteFolderItemById(
          communityFolderItemId
        );

      res.json(deleted);
    } else {
      logger.info(
        `Error deleting folder item. Folder Item with id ${communityFolderItemId} does not exists in community ${communityId}`
      );
      const error = new Error(
        `Error deleting folder item. Folder Item with id ${communityFolderItemId} does not exists in community ${communityId}`
      );
      error.status = status.BAD_REQUEST;
      throw error;
    }
  } catch (err) {
    logger.error(
      'Error deleting folder item from the community: ',
      err,
      err.stack
    );
    err.status = err.status || 500;
    return next(err);
  }
};

function isCommunityRestricted(community) {
  const restriction = fraudService.RESTRICTIONS.UPLOAD;
  if (community.restrictedInfo?.[restriction] === true) {
    return true;
  }
  return false;
}

const getS3SignendUrl = async (req, res, next) => {
  const { _id: userObjectId, email: createdBy } = req.user;

  const { communityId, communityFolderItemId: communityFolderId } =
    req.params;

  const {
    originalname,
    numberOfChunks,
    mimetype,
    type,
    isMultipart,
    signedUrlId,
    partNumber,
    fileSize,
  } = req.query;

  logger.info('getS3SignedUrl request', {
    userObjectId,
    communityId,
    folderId: communityFolderId,
    fileName: originalname,
    mimetype,
    type,
    isMultipart,
    signedUrlId,
    partNumber,
    fileSize,
  });
  try {
    const community = await Community.findOne(
      {
        _id: new ObjectId(communityId),
      },
      { restrictedInfo: 1 }
    );
    if (isCommunityRestricted(community)) {
      throw new ToUserError(
        'Your action cannot be fulfilled. Please verify your identity <NAME_EMAIL> for assistance.',
        GENERIC_ERROR.RISKY_ACTION_ERROR
      );
    }

    // Check storage limit before providing signed URL
    if (fileSize) {
      const storageCheck = await storageUsageService.checkStorageLimit(
        communityId,
        parseInt(fileSize, 10)
      );

      if (!storageCheck.allowed) {
        let errorCode;
        if (
          storageCheck.error ===
          FEATURE_PERMISSION_ERROR.STORAGE_LIMIT_EXCEEDED.name
        ) {
          errorCode = FEATURE_PERMISSION_ERROR.STORAGE_LIMIT_EXCEEDED;
        } else {
          errorCode = FEATURE_PERMISSION_ERROR.STORAGE_NOT_ALLOWED;
        }

        throw new ToUserError(storageCheck.message, errorCode);
      }
    }

    let reserveStorageId = null;

    if (signedUrlId && partNumber) {
      const result =
        await signedUrlForUploadService.getSignedUrlForSinglePart(
          signedUrlId,
          partNumber
        );

      logger.info(
        `getS3SignedUrl for the user ${userObjectId} community ${communityId} folderId ${communityFolderId} the file name ${originalname} result ${JSON.stringify(
          result
        )}`
      );
      res.send(result);
    } else {
      // Create storage reservation if fileSize is provided
      if (fileSize) {
        const reservation =
          await storageReservationService.createReservation({
            communityObjectId: communityId,
            communityFolderObjectId: communityFolderId,
            reservedSizeInBytes: parseInt(fileSize, 10),
            createdByLearnerObjectId: userObjectId,
          });
        reserveStorageId = reservation.reservationId;
      }

      const result = await signedUrlForUploadService.getSignedUrls(
        originalname,
        numberOfChunks,
        userObjectId,
        createdBy,
        mimetype,
        type,
        communityId,
        communityFolderId,
        isMultipart,
        reserveStorageId
      );

      // Add reservation info to response
      if (reserveStorageId) {
        result.reserveStorageId = reserveStorageId;
      }

      logger.info(
        `getS3SignedUrl for the user ${userObjectId} community ${communityId} folderId ${communityFolderId} the file name ${originalname} result ${JSON.stringify(
          result
        )}`
      );
      res.send(result);
    }
  } catch (error) {
    logger.error('Error in getS3SignedUrl', {
      error: error.message,
      stack: error.stack,
      communityId,
      fileSize,
    });
    return next(error);
  }
};

const completeUploadS3SignedUrl = async (req, res) => {
  const { signedChunkDetails, signedUrlId } = req.body;

  logger.info(
    'completeUploadS3SignedUrl: signedChunkDetails: ',
    signedChunkDetails
  );
  const result = await signedUrlForUploadService.completeMultiUpload(
    signedChunkDetails,
    signedUrlId
  );

  res.send(result);
};

const checkFolderItemExists = async (req, res) => {
  try {
    const { videoObjectId } = req.params;
    let doesFolderItemExists = false;

    const folderItem = await CommunityFolderItems.findOne({
      videoObjectId,
    })
      .select({
        _id: 1,
        communityFolderObjectId: 1,
        folderType: 1,
        status: 1,
      })
      .read('primary');

    if (folderItem) {
      doesFolderItemExists = true;
    }

    res.status(status.OK).json({
      data: doesFolderItemExists,
      isOutputVerticle: doesFolderItemExists,
      status: folderItem?.status,
      message: 'Folder item fetched successfully',
    });
  } catch (error) {
    logger.error(
      'Error getting folder item from the community: ',
      error,
      error.stack
    );
    error.status = 400;
    return res.status(error.status).json({
      data: null,
      errorCode: error.status,
      errorMessage: 'Error getting folder item from the community',
      error: error.message,
    });
  }
};

const updateVideoEncodingProgress = async (req, res) => {
  try {
    const { videoObjectId } = req.params;
    const {
      percentage: progress,
      status: currentPhase,
      errorMessage,
      uniqueId,
      currentTranscodingPhase,
    } = req.body || {};

    let updateBody = {
      processingPercentComplete: progress,
      currentProcessingPhase: currentTranscodingPhase,
      s3UniqueId: uniqueId,
    };

    if (currentPhase === 'ERROR') {
      updateBody = {
        currentProcessingPhase: currentPhase,
        status: communityLibraryStatusMap.ERROR,
        processingError: errorMessage,
      };
    }

    logger.info(
      `updateVideoEncodingProgress: ${progress} ${currentPhase}`
    );
    const folderItems = await CommunityFolderItems.updateMany(
      {
        videoObjectId: new ObjectId(videoObjectId),
      },
      {
        $set: {
          ...updateBody,
        },
      }
    );

    res.status(status.OK).json({
      data: folderItems,
      message: 'Folder item fetched successfully',
    });
  } catch (error) {
    logger.error(
      'Error updating video encoding progress: ',
      error,
      error.stack
    );
    error.status = 400;
    return res.status(error.status).json({
      data: null,
      errorCode: error.status,
      errorMessage: 'Error updating video encoding progress',
      error: error.message,
    });
  }
};

const updateVideoLinksForFolderItem = async (req, res) => {
  try {
    const { videoObjectId } = req.params;
    const { thumbnailLink, hlsLink, mpdLink, mp4Link } = req.body;
    logger.info(
      `updateVideoLinksForFolderItem: ${videoObjectId} ${thumbnailLink}`
    );

    const updateFolderItem =
      await communityFolderItemsService.updateFolderItemForVideoLinks({
        videoObjectId,
        thumbnailLink,
        hlsLink,
        mpdLink,
        mp4Link,
      });
    res.status(status.OK).json({
      data: updateFolderItem,
      message: 'Folder item fetched successfully',
    });
  } catch (error) {
    logger.error(
      'Error updating video encoding progress: ',
      error,
      error.stack
    );
    error.status = 400;
    return res.status(error.status).json({
      data: null,
      errorCode: error.status,
      errorMessage: 'Error updating video encoding progress',
      error: error.message,
    });
  }
};

const releaseFolderItemUploadReservation = async (req, res, next) => {
  try {
    const { communityId, folderId } = req.params;
    const { reserveStorageId } = req.body;

    if (!reserveStorageId) {
      return res.status(400).json({
        success: false,
        message: 'reserveStorageId is required',
      });
    }

    const reservation = await storageReservationService.releaseReservation(
      reserveStorageId,
      communityId
    );

    if (!reservation) {
      return res.status(404).json({
        success: false,
        message: 'Storage reservation not found or already expired',
      });
    }

    logger.info('Storage reservation released manually', {
      reserveStorageId,
      communityId,
      folderId,
      sizeInBytes: reservation.reservedSizeInBytes,
    });

    return {
      reserveStorageId,
      releasedSizeInBytes: reservation.reservedSizeInBytes,
    };
  } catch (error) {
    logger.error('Error releasing storage reservation', {
      error: error.message,
      stack: error.stack,
      reserveStorageId: req.body.reserveStorageId,
      communityId: req.params.communityId,
    });
    return next(error);
  }
};

module.exports = {
  createOneFolderItem,
  updateOneFolderItem,
  deleteOneFolderItem,
  getS3SignendUrl,
  completeUploadS3SignedUrl,
  getFolderItemById,
  checkFolderItemExists,
  updateVideoEncodingProgress,
  updateVideoLinksForFolderItem,
  releaseFolderItemUploadReservation,
};
