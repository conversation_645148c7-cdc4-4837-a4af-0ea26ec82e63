/* eslint-disable no-unused-vars */
const yup = require('yup');
const status = require('http-status');
const jwt = require('jsonwebtoken');

const campaignService = require('../../../../services/campaign');
const communityManagerRoleService = require('../../../services/common/communityManager.service');
const logger = require('../../../../services/logger.service');
const {
  JWT_SECRET_KEY,
  JWT_ACCESS_TOKEN_EXPIRES,
  JWT_REFRESH_SECRET_KEY,
  JWT_REFRESH_TOKEN_EXPIRES,
  authCookieDomain,
  cookieTokenMaxAgeMS,
  cookieRefreshTokenMaxAgeMS,
  env,
} = require('../../../../config');

const { DEVELOPMENT } = require('../../../../constants/common');

const createManagerSchema = yup.object().shape({
  communityId: yup.string().required(),
  referralCode: yup.string().notRequired().nullable(),
});

const resendEmailSchema = yup.object().shape({
  communityId: yup.string().required(),
});

const removeMemberEmailSchema = yup.object().shape({
  removeMemberEmail: yup.string().required(),
  removalReason: yup.string().required(),
});

const removeMemberLearnerIdSchema = yup.object().shape({
  learnerId: yup.number().required().positive().integer(),
  removalReason: yup
    .string()
    .transform((value) => (value === '' ? 'other' : value)) // default to 'other' as app send empty string
    .required(),
});

const createCommunityManager = async (req, res, next) => {
  try {
    const isBodyValid = await createManagerSchema.isValid(req.body);
    if (!isBodyValid) {
      const error = new Error('Invalid Body Params');
      error.status = status.BAD_REQUEST;
      return next(error);
    }
    const user = req.user;
    const { communityId, referralCode } = req.body;

    const email = req.user.email;

    const { updatedCommunityData } =
      await communityManagerRoleService.createManagerRole({
        communityId,
        email,
        referralCode,
      });

    await campaignService.claimFiveDollarBonus({ communityId });

    user.communityRoleCount = 1;
    logger.info('Creating token for', user);
    const token = jwt.sign(
      {
        user,
      },
      JWT_SECRET_KEY,
      {
        expiresIn: JWT_ACCESS_TOKEN_EXPIRES,
      }
    );

    logger.info('Token for sign in created');
    const refreshToken = jwt.sign(
      {
        user,
      },
      JWT_REFRESH_SECRET_KEY,
      {
        expiresIn: JWT_REFRESH_TOKEN_EXPIRES,
      }
    );

    logger.info('Refresh token created');
    res.cookie('accessTokenNA', token, {
      domain: authCookieDomain,
      path: '/',
      sameSite: 'None',
      secure: true,
      httpOnly: false,
      maxAge: cookieTokenMaxAgeMS,
    });
    res.cookie('refreshTokenNA', refreshToken, {
      domain: authCookieDomain,
      path: '/',
      sameSite: 'None',
      secure: true,
      httpOnly: false,
      maxAge: cookieRefreshTokenMaxAgeMS,
    });
    res.status(status.OK).json({
      token,
      refreshToken,
      user,
      updatedCommunityData,
    });
  } catch (err) {
    logger.error(
      'Error occured when updating the community:',
      err,
      err.stack
    );
    const error = new Error(
      err || 'Error occured when updating the community:'
    );
    error.status = err.status || 500;
    return next(error);
  }
};

const getAdminCountWithLimit = async (req, res, next) => {
  let statusCode;
  let result;
  try {
    const { communityId } = req?.params;
    const countWithLimit =
      await communityManagerRoleService.getAdminCountWithLimit({
        communityId,
      });
    if (!countWithLimit?.currentCount) {
      const errorMessage = 'This community does not have any admin';
      logger.error(errorMessage);
      statusCode = status.NOT_FOUND;
      result = { error: { code: statusCode, message: errorMessage } };
    }
    statusCode = status.OK;
    result = { data: countWithLimit };
  } catch (error) {
    const errorMessage = 'Unable to get community admin count with limit';
    logger.error(errorMessage, ' due to: ', error);
    statusCode = status.NOT_FOUND;
    result = { error: { code: statusCode, message: errorMessage } };
  }
  return res.status(statusCode).json(result);
};

const getAdminStatus = async (req, res, next) => {
  let statusCode;
  let result;
  try {
    const { communityId } = req?.params;
    const { emails = null } = req?.query;
    const emailsToCheck =
      emails?.split(',')?.map((email) => email?.toLowerCase()) || [];
    const communityAdmins =
      await communityManagerRoleService.getAdminStatus({
        communityId,
        emails: emailsToCheck,
      });
    statusCode = status.OK;
    result = { data: communityAdmins };
  } catch (error) {
    const errorMessage = 'Unable to find community admin status';
    logger.error(errorMessage, ' due to: ', error);
    statusCode = status.NOT_FOUND;
    result = { error: { code: statusCode, message: errorMessage } };
  }
  return res.status(statusCode).json(result);
};

const addAdmins = async (req, res, next) => {
  let statusCode;
  let result;
  const { email: managerEmail, _id: managerUserObjectId } = req?.user;
  const { communityId } = req?.params;
  const { emails = [] } = req?.body;
  const formattedEmails =
    emails?.map((email) => email?.toLowerCase()) || [];
  const communityAdmins = await communityManagerRoleService.addAdmins({
    communityId,
    emails: formattedEmails,
    managerEmail,
    managerUserObjectId,
  });
  return communityAdmins;
};

const removeAdmin = async (req, res, next) => {
  let statusCode;
  let result;
  try {
    const { email: managerEmail } = req?.user;
    const { communityId } = req?.params;
    const { email } = req?.query;
    const formattedEmail = email?.toLowerCase() || null;
    const communityAdmin = await communityManagerRoleService.removeAdmin({
      communityId,
      email: formattedEmail,
      managerEmail,
    });
    statusCode = status.NO_CONTENT;
    result = { data: communityAdmin };
  } catch (error) {
    const errorMessage = 'Unable to remove community admin';
    logger.error(errorMessage, ' due to: ', error);
    statusCode = status.INTERNAL_SERVER_ERROR;
    result = { error: { code: statusCode, message: errorMessage } };
  }
  return res.status(statusCode).json(result);
};

const resendEmailToAdmin = async (req, res, next) => {
  try {
    const isBodyValid = await resendEmailSchema.isValid(req.body);
    if (!isBodyValid) {
      const error = new Error('Invalid Body Data');
      error.status = status.BAD_REQUEST;
      return next(error);
    }
    const { communityId } = req.body;
    const mailSent =
      await communityManagerRoleService.resendCommunityCreatedMail({
        communityId,
      });
    const statusCode = status.OK;
    const result = { data: mailSent };
    return res.status(statusCode).json(result);
  } catch (err) {
    logger.error(
      'Error occured when resending email to admin:',
      err,
      err.stack
    );
    const error = new Error(
      err || 'Error occured when resending email to admin'
    );
    error.status = err.status || 500;
    return next(error);
  }
};

const removeCommunityMember = async (req, res, next) => {
  try {
    const isBodyValid = await removeMemberLearnerIdSchema.isValid(
      req.body
    );

    if (!isBodyValid) {
      const error = new Error('Invalid Body Data');
      error.status = status.BAD_REQUEST;
      return next(error);
    }

    const currentManagerlearnerId = req.user?.learner?._id;
    const { learnerId, removalReason } = removeMemberLearnerIdSchema.cast(
      req.body
    );

    const { communityId } = req.params;
    const memberRemoved = await communityManagerRoleService.removeMember({
      communityId,
      learnerId,
      removalReason,
      currentManagerlearnerId,
    });

    return res.status(status.OK).json({ data: memberRemoved });
  } catch (err) {
    logger.error('Error occured when removing member:', err, err.stack);
    const error = new Error(err || 'Error occured when removing member');
    error.status = err.status || 500;
    return next(error);
  }
};

const removeCommunityMemberWithoutManager = async (req, res, next) => {
  try {
    const isBodyValid = await removeMemberLearnerIdSchema.isValid(
      req.body
    );

    if (!isBodyValid) {
      const error = new Error('Invalid Body Data');
      error.status = status.BAD_REQUEST;
      return next(error);
    }

    const { learnerId, removalReason } = removeMemberLearnerIdSchema.cast(
      req.body
    );

    const { communityId } = req.params;

    const memberRemoved = await communityManagerRoleService.removeMember(
      {
        communityId,
        learnerId,
        removalReason,
      },
      true
    );

    return res.status(status.OK).json({ data: memberRemoved });
  } catch (err) {
    logger.error(
      'Error occured when removing member without manager:',
      err,
      err.stack
    );
    const error = new Error(
      err || 'Error occured when removing member without manageer'
    );
    error.status = err.status || 500;
    return next(error);
  }
};

module.exports = {
  createCommunityManager,
  getAdminCountWithLimit,
  getAdminStatus,
  addAdmins,
  removeAdmin,
  resendEmailToAdmin,
  removeCommunityMember,
  removeCommunityMemberWithoutManager,
};
