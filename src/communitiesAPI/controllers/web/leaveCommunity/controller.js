const ObjectId = require('mongoose').Types.ObjectId;
const HttpStatus = require('http-status');

const {
  communityLeaveService,
} = require('../../../services/common/communityLeave.service');

const CommunitySubscriptionsModel = require('../../../models/communitySubscriptions.model');
const logger = require('../../../../services/logger.service');
const { getUserIP } = require('../../../../utils/headers.util');
const {
  cancelSubscriptionService,
} = require('../../../../services/communitySubscription');
const {
  COMMUNITY_SUBSCRIPTION_STATUSES,
  PAYMENT_PROVIDER,
} = require('../../../../constants/common');

const {
  FromExternalToUserError,
  InternalError,
} = require('../../../../utils/error.util');

const leaveCommunityController = async (req, res, next) => {
  try {
    // check if the payload is in this form { cancellationReason: 'string', communityCode: 'string'}
    const { cancellationReason, communityCode, subscriptionObjectId } =
      req.body;
    const { communityId } = req?.params;
    const ip = getUserIP(req) || null;
    logger.info(
      `Cancel subscription for ${communityCode}, ${subscriptionObjectId}`
    );
    // make sure that there is a cancellation reason and a community Code in the payload
    if (!cancellationReason || !communityCode) {
      return res.status(400).json({
        message: 'cancellationReason and communityCode are required',
      });
    }
    // find the community subscription in the database
    const communitySubscription =
      await CommunitySubscriptionsModel.findOne({
        communityCode,
        _id: new ObjectId(subscriptionObjectId),
      })
        .populate('learnerObjectId')
        .lean();

    const userInfo = {
      fullName: communitySubscription?.learnerObjectId?.firstName ?? '',
    };

    // if there is no community subscription then error out
    if (!communitySubscription) {
      return res.status(400).json({
        message: 'No subscription found',
      });
    }

    if (
      communitySubscription.status !==
      COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT
    ) {
      return res.status(400).json({
        message: 'There is no active subscription',
      });
    }

    // check if the community subscription is not a paid subscription
    // if paid subscription then error out
    // if not paid subscription then proceed to leave the community
    // (since the unsubscription is handled by main website backend not this repo)
    if (communitySubscription.stripeSubscriptionId) {
      if (
        communitySubscription.paymentProvider !== PAYMENT_PROVIDER.EBANX
      ) {
        await CommunitySubscriptionsModel.findOneAndUpdate(
          {
            communityCode,
            _id: new ObjectId(subscriptionObjectId),
          },
          { $set: { disableRevokeCancellation: true } }
        );
      }

      await cancelSubscriptionService.cancelPaidSubscription(
        communitySubscription,
        cancellationReason,
        ip
      );
      return res.status(200).json({
        message: 'Community left successfully',
        data: {},
      });
    }
    // call the service to leave the community
    const { data: communityLeaveResponse, error } =
      await communityLeaveService(
        communitySubscription,
        cancellationReason,
        communityCode,
        userInfo,
        communityId
      );
    if (error) {
      throw new Error(error);
    }
    // if there is a response then return the response
    if (communityLeaveResponse) {
      return res.status(200).json({
        message: 'Community left successfully',
        data: communityLeaveResponse,
      });
    }
  } catch (err) {
    const errorMessage = err.isAxiosError
      ? err.response.data.errorMessage
      : err.message;

    logger.error(
      'leaveCommunityController failed due to',
      errorMessage,
      err.stack
    );

    let error = err;

    if (err.isAxiosError) {
      error =
        err.response.status === HttpStatus.IM_A_TEAPOT
          ? new FromExternalToUserError(err.response.data)
          : new InternalError(errorMessage);
    }

    return next(error);
  }
};

module.exports = {
  leaveCommunityController,
};
