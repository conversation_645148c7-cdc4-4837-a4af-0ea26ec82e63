const communityController = require('./communities');
const communityPostsController = require('./posts');
const communityEventsController = require('./events');
const communitySubscriptionsController = require('./subscriptions');
const communityVideosController = require('./videos');
const communityResourcesController = require('./resources');
const communityBookingController = require('./bookTrainer');
const communityShortUrlsController = require('./shortUrls');
const communityApplicationsController = require('./applications');
const communityAnalyticsController = require('./analytics');
const communityApplicationConfigController = require('./applicationConfig');
const communityUIConfigController = require('./communityUIConfig');
const CommunityGuestHostController = require('./host');
const communityMagicReachController = require('./magicReach');
const communityFolderController = require('./folder');
const communityFolderAccessLogController = require('./folderAccessLogs');
const communityFolderItemController = require('./folderItem');
const communityManagerController = require('./communityManager');
const communityManagerTodosController = require('./managerTodos');
const communityLandingPageController = require('./landingPage');
const communityDiscordRolesController = require('./communityDiscordRoles');
const communityDiscordMessagesController = require('./communityDiscordMessages');
const communityMembersInvite = require('./inviteMembers');
const communityPostReactionController = require('./reactionsForAnnouncements');
const communityRevenueController = require('./revenue');
const communityPayoutController = require('./communityManagerPayout');
const disconectChatController = require('./disconnectChat');
const discordChannelController = require('./discordChannels');
const communityPostCommentController = require('./commentsForAnnouncements');
const communityPostCommentReactionController = require('./reactionsForAnnouncementComments');
const communityEntityController = require('./entity');
const communityLeaveController = require('./leaveCommunity');
const cancelScheduledCancellationController = require('./cancelScheduledCancellation');
const whatsappController = require('./whatsapp');
const signupFlowController = require('./signup');

module.exports = {
  communityController,
  communityPostsController,
  communityEventsController,
  communitySubscriptionsController,
  communityVideosController,
  communityResourcesController,
  communityBookingController,
  communityShortUrlsController,
  communityApplicationsController,
  communityAnalyticsController,
  communityApplicationConfigController,
  communityUIConfigController,
  CommunityGuestHostController,
  communityMagicReachController,
  communityFolderController,
  communityFolderAccessLogController,
  communityFolderItemController,
  communityManagerController,
  communityManagerTodosController,
  communityLandingPageController,
  communityDiscordRolesController,
  communityDiscordMessagesController,
  communityMembersInvite,
  communityPostReactionController,
  communityRevenueController,
  communityPayoutController,
  disconectChatController,
  discordChannelController,
  communityPostCommentController,
  communityPostCommentReactionController,
  communityEntityController,
  communityLeaveController,
  cancelScheduledCancellationController,
  whatsappController,
  signupFlowController,
};
