const {
  cancelScheduledCancellation,
} = require('../../../services/common/cancelScheduledCancellation.service');

const {
  cancelScheduledCancellationSchema,
  cancelScheduledCancellationParamsSchema,
} = require('./schema');

const logger = require('../../../../services/logger.service');

const cancelScheduledCancellationController = async (req, res, next) => {
  try {
    // Validate params
    await cancelScheduledCancellationParamsSchema.validate(req.params);

    // Validate body
    await cancelScheduledCancellationSchema.validate(req.body);

    const { communityId } = req.params;
    const { reason } = req.body;
    const userId = req.user.learner._id;
    const userEmail = req.user.email;

    logger.info(
      `User ${userEmail} attempting to cancel scheduled cancellation for community ${communityId}`
    );

    // Call the service to handle cancellation revoke
    const result = await cancelScheduledCancellation({
      communityId,
      userId,
      userEmail,
      reason,
    });

    return res.status(200).json({
      message: 'Successfully cancelled your scheduled cancellation',
      data: result,
    });
  } catch (error) {
    logger.error('cancelScheduledCancellationController failed:', {
      error: error.message,
      stack: error.stack,
      userId: req.user?._id,
      communityId: req.params?.communityId,
    });

    return next(error);
  }
};

module.exports = {
  cancelScheduledCancellationController,
};
