const yup = require('yup');

exports.cancelScheduledCancellationSchema = yup.object().shape({
  reason: yup
    .string()
    .trim()
    .max(500, 'Reason cannot exceed 500 characters')
    .notRequired()
    .default('User requested to cancel their scheduled cancellation'),
});

exports.cancelScheduledCancellationParamsSchema = yup.object().shape({
  communityId: yup
    .string()
    .trim()
    .required('Community ID is required')
    .matches(/^[0-9a-fA-F]{24}$/, 'Invalid community ID format'),
});
