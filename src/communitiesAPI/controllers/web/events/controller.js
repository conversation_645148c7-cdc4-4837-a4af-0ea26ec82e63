const ObjectId = require('mongoose').Types.ObjectId;
const yup = require('yup');
const status = require('http-status');
const axios = require('../../../../clients/axios.client');
const {
  communityEventsService,
  communityEventAttendeeService,
} = require('../../../services/web');
const logger = require('../../../../services/logger.service');
const { getConfigByType } = require('../../../../services/config.service');
const {
  getUserIP,
  getUserAgent,
} = require('../../../../utils/headers.util');
const { CONFIG_TYPES } = require('../../../../constants/common');
const {
  MAIN_PAYMENT_BACKEND_URL,
  LP_MAIN_WEBSITE_AUTH_KEY,
} = require('../../../../config');
const {
  COMMUNITY_EVENT_ATTENDEE_PURCHASE_TYPES,
} = require('../../../constants');

const idRouteParamSchema = yup.object().shape({
  id: yup.string().required(),
});

const updateInputSchema = yup.object().shape({
  // eventId: yup.string(),
  title: yup.string(),
  description: yup
    .string()
    .max(5000, 'Description cannot exceed 5000 characters'),
  startTime: yup.date(),
  endTime: yup.date(),
  liveLink: yup.string().url(),
  recordingLink: yup.string().url(),
  isActive: yup.boolean(),
  status: yup.string(),
  type: yup.string(),
  communities: yup.array(),
  host: yup.object(),
  bannerImg: yup.string().notRequired(),
  slug: yup.string().notRequired(),
});

const createInputSchema = yup.object().shape({
  // eventId: yup.string().required(),
  title: yup.string().required(),
  description: yup
    .string()
    .max(5000, 'Description cannot exceed 5000 characters'),
  startTime: yup.date().required(),
  endTime: yup.date().required(),
  liveLink: yup.string().url(),
  recordingLink: yup.string().url(),
  isActive: yup.boolean(),
  status: yup.string().required(),
  type: yup.string().required(),
  communities: yup.array(),
  host: yup.object(),
  bannerImg: yup.string().notRequired(),
  slug: yup.string().notRequired(),
});

const getPaidEventsSchema = yup.object().shape({
  communityId: yup.string().required(),
  learnerObjectId: yup.string().required(),
});

const createEvent = async (req, res, next) => {
  const { learner } = req.user;

  try {
    await createInputSchema.validate(req.body);
  } catch (err) {
    return res.status(status.BAD_REQUEST).json({
      message: `Cannot create event: ${err.message}`,
      fieldError: err?.path,
    });
  }
  try {
    const body = createInputSchema.cast(req.body);
    const event = await communityEventsService.createOneEvent({
      ...body,
      createdByLearnerObjectId: learner._id,
    });
    return res.status(status.CREATED).json(event);
  } catch (err) {
    logger.error('Error creating event: ', err);
    err.status = status.BAD_REQUEST;
    next(err);
  }
};

const updateEvent = async (req, res, next) => {
  try {
    await updateInputSchema.validate(req.body);
  } catch (err) {
    return res.status(status.BAD_REQUEST).json({
      message: `Cannot update event: ${err.message}`,
      fieldError: err?.path,
    });
  }
  const body = updateInputSchema.cast(req.body);
  const { timezoneId = 'UTC' } = req.query;
  try {
    const event = await communityEventsService.updateOneEvent(
      req.params.id,
      body,
      timezoneId
    );
    logger.info('Updated event, ready to return response: ', event);
    return res.status(status.OK).json(event);
  } catch (error) {
    logger.error('Error updating event: ', error);
    error.status = status.BAD_REQUEST;
    next(error);
  }
};

const deleteEvent = async (req, res, next) => {
  const params = req.params;
  const isPathValid = await idRouteParamSchema.isValid(params);

  if (!isPathValid) {
    logger.error('Error deleting event due to Invalid Path Parameters');
    const error = new Error('Invalid Path Parameters');
    error.status = status.BAD_REQUEST;
    return next(error);
  }

  try {
    const result = await communityEventsService.deleteOneEvent(
      params.id,
      req.user.learner._id
    );
    return res.status(status.OK).json({
      eventId: result._id,
      changeLogId: result.changeLogId,
      message: `Deleted event with eventId: ${params.id}`,
    });
  } catch (error) {
    logger.error('Error deleting event ', error);
    error.status = status.BAD_REQUEST;
    next(error);
  }
};

const sendEmailToRsvpUsers = async (req, res, next) => {
  try {
    res.json(await communityEventsService.sendEventReminderEmails());
  } catch (err) {
    logger.error('Error on send reminder emails to all: ', err);
    const error = new Error('Error on send reminder emails to all');
    error.status = err.status || 500;
    return next(error);
  }
};

const getCommunityEvents = async (req, res, next) => {
  try {
    res.json(
      await communityEventsService.getCommunityEvents({
        communities: req.params.communityId,
      })
    );
  } catch (err) {
    logger.error('Error on get All Events request: ', err);
    const error = new Error('Error on get request');
    error.status = err.status || 500;
    return next(error);
  }
};

const getEventById = async (req, res, next) => {
  try {
    const { communityId = null, eventId = null } = req?.params;

    const {
      quantity = 1,
      selectedAmount,
      affiliateCode,
      paymentMethodCountryCode,
      paymentProvider,
    } = req.query;
    const learnerObjectId = req?.user?.learner?._id;
    const ip = getUserIP(req) || null;
    const event = await communityEventsService.getEventById(
      communityId,
      eventId,
      learnerObjectId,
      ip,
      quantity,
      selectedAmount,
      affiliateCode,
      paymentMethodCountryCode,
      paymentProvider
    );

    if (!event) {
      const errorMessage = `Unable to get event by id ${eventId} for community id ${communityId}`;
      logger.error(errorMessage);
      const error = new Error(errorMessage);
      error.status = 400;
      return next(error);
    }
    return res.status(200).json(event);
  } catch (err) {
    return next(err);
  }
};

const getEventByIdForAdmin = async (req, res) => {
  try {
    const { communityId = null, eventId = null } = req?.params;
    if (!communityId || !eventId) {
      const errorMessage = `Unable to get event by id ${eventId} for community id ${communityId}`;
      logger.error(errorMessage);
      const error = new Error(errorMessage);
      error.status = 400;
      throw error;
    }
    const { data: event, error: dataError } =
      await communityEventsService.getEventByIdForAdmin(
        eventId,
        communityId
      );
    if (dataError) {
      const errorMessage = `Unable to get event by id ${eventId} for community id ${communityId}`;
      logger.error(errorMessage);
      const error = new Error(errorMessage);
      error.status = 400;
      throw error;
    }
    return res.status(200).json(event);
  } catch (err) {
    logger.error('Error on get Event by id request: ', err, err?.stack);
    const error = new Error('Error on get event by id request');
    error.status = err.status || 500;
    res.status(err.status || 500).json({ error: err.message });
  }
};

const getEventRSVPList = async (req, res, next) => {
  try {
    const { communityId = null, eventId = null } = req?.params;
    let rsvpList = await communityEventAttendeeService.getEventRSVPList(
      communityId,
      eventId
    );
    if (!rsvpList?.length) {
      rsvpList = [];
    }
    return res.status(200).json(rsvpList);
  } catch (err) {
    logger.error(
      'Error on get Event RSVP list request: ',
      err,
      err?.stack
    );
    const error = new Error('Error on get event RSVP list request');
    error.status = err.status || 500;
    return next(error);
  }
};

const getEventBySlug = async (req, res, next) => {
  try {
    const { communityLink, slug } = req.params;
    const {
      quantity = 1,
      selectedAmount,
      affiliateCode,
      paymentMethodCountryCode,
      paymentProvider,
    } = req.query;
    const learnerObjectId = req?.user?.learner?._id;
    const ip = getUserIP(req) || null;
    const event = await communityEventsService.getEventBySlug(
      `/${communityLink}`,
      `/${slug}`,
      learnerObjectId,
      ip,
      quantity,
      selectedAmount,
      affiliateCode,
      paymentMethodCountryCode,
      paymentProvider
    );
    res.json(event);
  } catch (err) {
    logger.error('Error on get Event by slug request: ', err, err?.stack);
    const error = new Error('Error on get request');
    error.status = err.status || 500;
    return next(error);
  }
};

const getPastEventsByCommunityId = async (req, res, next) => {
  try {
    let { paginate = 0, pageNum = null, pageSize = null } = req?.query;
    const searchQuery = req?.query?.searchQuery || null;

    paginate = parseInt(paginate, 10) || 0;
    if (paginate === 1) {
      pageNum = parseInt(pageNum, 10) || 1;
      pageSize = parseInt(pageSize, 10) || 10;
    }
    const learnerObjectId = req?.user?.learner?._id;
    const userObjectId = req?.user?._id;
    const pastEvents =
      await communityEventsService.getPastEventsByCommunityId(
        learnerObjectId,
        userObjectId,
        req?.params?.communityId,
        paginate,
        pageNum,
        pageSize,
        null,
        searchQuery
      );
    res.json(pastEvents);
  } catch (err) {
    logger.error('Error on get past Event request: ', err);
    const error = new Error('Error on get past event request');
    error.status = err.status || 500;
    return next(error);
  }
};

const getUpcomingEventsByCommunityId = async (req, res, next) => {
  try {
    let { paginate = 0, pageNum = null, pageSize = null } = req?.query;
    const searchQuery = req?.query?.searchQuery || null;
    paginate = parseInt(paginate, 10) || 0;
    if (paginate === 1) {
      pageNum = parseInt(pageNum, 10) || 1;
      pageSize = parseInt(pageSize, 10) || 10;
    }
    const learnerObjectId = req?.user?.learner?._id;
    const userObjectId = req?.user?._id;
    res.json(
      await communityEventsService.getUpcomingEventsByCommunityId(
        learnerObjectId,
        userObjectId,
        req?.params?.communityId,
        paginate,
        pageNum,
        pageSize,
        searchQuery
      )
    );
  } catch (err) {
    logger.error('Error on get upcoming Events request: ', err);
    const error = new Error('Error on get upcoming event request');
    error.status = err.status || 500;
    return next(error);
  }
};

const registerForEvent = async (req, res, next) => {
  try {
    const ip = getUserIP(req) || null;
    const userAgent = getUserAgent(req) || null;
    const email = req.user.email;

    const { quantity = 1 } = req.body;

    const registrationDetails =
      await communityEventsService.registerForEvent(
        req?.params?.eventId,
        new ObjectId(req?.user?.learner?._id),
        COMMUNITY_EVENT_ATTENDEE_PURCHASE_TYPES.FREE,
        email,
        quantity,
        ip,
        userAgent
      );
    if (!registrationDetails) {
      logger.error('Error in register for event request');
      const error = new Error('Unable to register for event');
      error.status = 400;
      return next(error);
    }
    res.json(registrationDetails);
  } catch (err) {
    logger.error('Error in register for event request: ', err);
    return next(err);
  }
};

const registerForEventV2 = async (req, res, next) => {
  try {
    const learnerObjectId = new ObjectId(req.body?.learnerObjectId);
    const { timezoneId = 'UTC' } = req.query;
    const registrationDetails =
      await communityEventsService.registerForEvent(
        req?.body?.eventId,
        learnerObjectId,
        timezoneId
      );
    if (!registrationDetails) {
      logger.error('Error in register for event request');
      const error = new Error('Unable to register for event');
      error.status = 400;
      return next(error);
    }
  } catch (err) {
    logger.error('Error in register for event request: ', err);
    const error = new Error('Error on post request');
    error.status = err.status || 500;
    return next(error);
  }
};

const eventSignUp = async (req, res, next) => {
  const ip = getUserIP(req) || null;
  const userAgent = getUserAgent(req) || null;
  const { eventObjectId = null, timezone = 'UTC' } = req?.body;
  if (!eventObjectId) {
    logger.error('Error signing up to event due to Invalid Parameters');
    const error = new Error('Invalid Parameters');
    error.status = status.BAD_REQUEST;
    return next(error);
  }
  const email = req?.user?.email;
  let LP_BE_MW_AUTH_KEY = LP_MAIN_WEBSITE_AUTH_KEY;
  if (!LP_BE_MW_AUTH_KEY) {
    const { envVarData = null } = await getConfigByType(
      CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
    );
    LP_BE_MW_AUTH_KEY = envVarData?.LP_MAIN_WEBSITE_AUTH_KEY;
  }
  const axiosConfig = {
    method: 'POST',
    url: `${MAIN_PAYMENT_BACKEND_URL}/api/v1/community-event-checkout`,
    headers: {
      Authorization: `Bearer ${LP_BE_MW_AUTH_KEY}`,
      'X-requester-service-name': 'Learn Portal Backend',
      'X-client-ip-address': ip,
      'X-client-user-agent': userAgent,
    },
    data: {
      eventObjectId,
      email,
      timezone,
    },
  };
  let statusCode;
  let result;
  try {
    const axiosResponse = await axios(axiosConfig);
    result = { data: axiosResponse?.data };
    statusCode = status.CREATED;
  } catch (error) {
    const errorData = {};
    errorData.message =
      error?.response?.data?.info || new Error('Error in event signup');
    errorData.status =
      error?.response?.data?.status || status.INTERNAL_SERVER_ERROR;
    return next(errorData);
  }
  logger.info('Event Signup Result: ', result);
  return res.status(statusCode).json(result);
};

const eventStripeCheckout = async (req, res, next) => {
  const ip = getUserIP(req) || null;
  const userAgent = getUserAgent(req) || null;
  const { eventSignupId = null } = req?.body;
  if (!eventSignupId) {
    logger.error(
      'Error stripe event checkout due to Invalid Path Parameters'
    );
    const error = new Error('Invalid Path Parameters');
    error.status = status.BAD_REQUEST;
    return next(error);
  }
  let LP_BE_MW_AUTH_KEY = LP_MAIN_WEBSITE_AUTH_KEY;
  if (!LP_BE_MW_AUTH_KEY) {
    const { envVarData = null } = await getConfigByType(
      CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
    );
    LP_BE_MW_AUTH_KEY = envVarData?.LP_MAIN_WEBSITE_AUTH_KEY;
  }
  const axiosConfig = {
    method: 'POST',
    url: `${MAIN_PAYMENT_BACKEND_URL}/api/v1/community-event/get-stripe-checkout`,
    headers: {
      Authorization: `Bearer ${LP_BE_MW_AUTH_KEY}`,
      'X-requester-service-name': 'Learn Portal Backend',
      'X-client-ip-address': ip,
      'X-client-user-agent': userAgent,
    },
    data: {
      eventSignupId,
    },
  };
  let statusCode;
  let result;
  try {
    const axiosResponse = await axios(axiosConfig);
    result = { data: axiosResponse?.data };
    statusCode = status.CREATED;
  } catch (error) {
    const errorData = {};
    errorData.message =
      error?.response?.data?.info ||
      new Error('Error getting event stripe checkout');
    errorData.status =
      error?.response?.data?.status || status.INTERNAL_SERVER_ERROR;
    return next(errorData);
  }
  logger.info('Event Stripe Checkout Result: ', result);
  return res.status(statusCode).json(result);
};

const eventXenditEwalletCheckout = async (req, res, next) => {
  const ip = getUserIP(req) || null;
  const userAgent = getUserAgent(req) || null;
  const {
    eventSignupId = null,
    paymentMethodType = 'PH_GCASH',
    successRedirectURL = null,
    failureRedirectURL = null,
    cancelRedirectURL = null,
  } = req?.body;
  if (!eventSignupId) {
    logger.error(
      'Error xendit ewallet event checkout due to Invalid Path Parameters'
    );
    const error = new Error('Invalid Path Parameters');
    error.status = status.BAD_REQUEST;
    return next(error);
  }
  let LP_BE_MW_AUTH_KEY = LP_MAIN_WEBSITE_AUTH_KEY;
  if (!LP_BE_MW_AUTH_KEY) {
    const { envVarData = null } = await getConfigByType(
      CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
    );
    LP_BE_MW_AUTH_KEY = envVarData?.LP_MAIN_WEBSITE_AUTH_KEY;
  }
  const axiosConfig = {
    method: 'POST',
    url: `${MAIN_PAYMENT_BACKEND_URL}/api/v1/community-event/xendit-ewallet-checkout`,
    headers: {
      Authorization: `Bearer ${LP_BE_MW_AUTH_KEY}`,
      'X-requester-service-name': 'Learn Portal Backend',
      'X-client-ip-address': ip,
      'X-client-user-agent': userAgent,
    },
    data: {
      eventSignupId,
      paymentMethodType,
      successRedirectURL,
      failureRedirectURL,
      cancelRedirectURL,
    },
  };
  let statusCode;
  let result;
  try {
    const axiosResponse = await axios(axiosConfig);
    result = { data: axiosResponse?.data };
    statusCode = status.CREATED;
  } catch (error) {
    const errorData = {};
    errorData.message =
      error?.response?.data?.info ||
      new Error('Error getting event xendit ewallet checkout');
    errorData.status =
      error?.response?.data?.status || status.INTERNAL_SERVER_ERROR;
    return next(errorData);
  }
  logger.info('Event Xendit Ewallet Checkout Result: ', result);
  return res.status(statusCode).json(result);
};

const eventFreeCheckout = async (req, res) => {
  let statusCode;
  let result;
  const learnerObjectId = new ObjectId(req.user?.learner?._id);
  const { eventObjectId, timezoneId = 'UTC' } = req?.body;
  const purchaseType = COMMUNITY_EVENT_ATTENDEE_PURCHASE_TYPES.FREE;
  try {
    result = await communityEventsService.registerForEvent(
      eventObjectId,
      learnerObjectId,
      timezoneId,
      purchaseType
    );
    statusCode = status.CREATED;
  } catch (error) {
    logger.error(`Unable to perform event free checkout due to: `, error);
    result = { error };
    statusCode = error?.response?.status || status.BAD_REQUEST;
  }
  logger.info('Event Free Checkout Result: ', result);
  return res.status(statusCode).json(result);
};

const verifyPaidEventPayment = async (req, res) => {
  let statusCode;
  let result;
  const { eventSignupId = null } = req?.query;
  try {
    result = await communityEventsService.verifyPaidEventPayment(
      eventSignupId
    );
    if (result?.isValid) {
      statusCode = status.OK;
    } else {
      statusCode = status.BAD_REQUEST;
    }
  } catch (error) {
    logger.error(`Unable to verify event payment due to: `, error);
    result = { error };
    statusCode = error?.response?.status || status.BAD_REQUEST;
  }
  logger.info('Paid Event Payment Verification Result: ', result);
  return res.status(statusCode).json(result);
};

const getUpcomingPaidEventsForLearner = async (req, res, next) => {
  try {
    const areParamsValid = await getPaidEventsSchema.isValid(req.params);
    if (!areParamsValid) {
      const error = new Error('Invalid Params Data');
      error.status = status.BAD_REQUEST;
      return next(error);
    }
    const { communityId, learnerObjectId } = req.params;
    const eventsCheck =
      await communityEventAttendeeService.checkForUpcomingPaidEventsForLearner(
        learnerObjectId,
        communityId
      );
    return res.status(200).json(eventsCheck);
  } catch (err) {
    logger.error('Error on get Upcoming Paid Events For Learner: ', err);
    const error = new Error(
      'Error on get Upcoming Paid Events For Learner'
    );
    error.status = 400;
    return next(error);
  }
};

module.exports = {
  getCommunityEvents,
  getPastEventsByCommunityId,
  getUpcomingEventsByCommunityId,
  getEventById,
  getEventBySlug,
  registerForEvent,
  registerForEventV2,
  deleteEvent,
  updateEvent,
  createEvent,
  sendEmailToRsvpUsers,
  eventSignUp,
  eventStripeCheckout,
  eventXenditEwalletCheckout,
  eventFreeCheckout,
  verifyPaidEventPayment,
  getEventRSVPList,
  getUpcomingPaidEventsForLearner,
  getEventByIdForAdmin,
};
