const communityController = require('./communities');
const communityEventsController = require('./events');
const communityMembershipsController = require('./subscriptions');
const communityBookingController = require('./bookTrainer');
const communityTodosController = require('./todos');
const communityVideosController = require('./videos');
const communityResourcesController = require('./resources');
const communityMobileNotificationsController = require('./notifications');
const communityFolderController = require('./folder');
const communityFolderAccessLogController = require('./folderAccessLogs');
const communityFolderItemController = require('./folderItem');
const communityPostsController = require('./posts');
const communityManagerTodosController = require('./managerTodos');
const communityPostReactionController = require('./reactionsForAnnouncements');
const communityPostCommentController = require('./commentsForAnnouncements');
const communityPostCommentReactionController = require('./reactionsForAnnouncementComments');
const communityLeaveController = require('./leaveCommunity');
const cancelScheduledCancellationController = require('./cancelScheduledCancellation');
const mobileWhatsappController = require('./whatsapp');

module.exports = {
  communityController,
  communityEventsController,
  communityMembershipsController,
  communityBookingController,
  communityTodosController,
  communityVideosController,
  communityResourcesController,
  communityMobileNotificationsController,
  communityFolderController,
  communityFolderAccessLogController,
  communityFolderItemController,
  communityPostsController,
  communityManagerTodosController,
  communityPostReactionController,
  communityPostCommentController,
  communityPostCommentReactionController,
  communityLeaveController,
  cancelScheduledCancellationController,
  mobileWhatsappController,
};
