const { Schema, model } = require('mongoose');
const CommunityManagerPayoutModel = require('./communityManagerPayout.model');
const {
  PAYOUT_TYPE,
  PAYOUT_STATUS,
  PAYOUT_CHANNEL,
} = require('../../services/payout/constants');

const feeBreakdownSchema = new Schema(
  {
    gatewayFee: { type: Number, required: true, default: 0 },
    gst: { type: Number, required: true, default: 0 },
    processingFee: { type: Number, required: true, default: 0 },
    gstOnRevenue: { type: Number, required: true, default: 0 },
    whtFee: { type: Number, required: true, default: 0 },
    refundProcessingFee: { type: Number, required: true, default: 0 },
    internationalFee: { type: Number, required: true, default: 0 },
  },
  { _id: false }
);

const refundBreakdownSchema = new Schema(
  {
    discountedItemPrice: { type: Number, required: true, default: 0 },
    netAmount: { type: Number, required: true, default: 0 },
    revenueShare: { type: Number, required: true, default: 0 },
    passOnRevenueShare: { type: Number, required: true, default: 0 },
    fee: { type: feeBreakdownSchema, required: true, default: 0 },
    passOnFee: { type: feeBreakdownSchema, required: true, default: 0 },
  },
  { _id: false }
);

const adjustmentSchema = new Schema(
  {
    type: { type: String, required: true },
    amount: { type: Number, required: true },
    currency: { type: String, required: true },
    reason: { type: String, required: false },
  },
  { _id: false }
);

const advancePayoutSchema = new Schema(
  {
    advancePayoutId: { type: Schema.Types.ObjectId, required: true },
    amountInUsd: { type: Number, required: true },
    amountInLocalCurrency: { type: Number, required: true },
    reason: { type: String, required: false },
  },
  { _id: false }
);

const affiliateCommissionSchema = new Schema(
  {
    affiliateCommissionInUsd: { type: Number, required: true, default: 0 },
    affiliateCommissionInLocalCurrency: {
      type: Number,
      required: true,
      default: 0,
    },
    refundedAffiliateCommissionInUsd: {
      type: Number,
      required: true,
      default: 0,
    },
    refundedAffiliateCommissionInLocalCurrency: {
      type: Number,
      required: true,
      default: 0,
    },
  },
  { _id: false }
);

const referralRewardSchema = new Schema(
  {
    referralRewardInUsd: { type: Number, required: true, default: 0 },
    referralRewardInLocalCurrency: {
      type: Number,
      required: true,
      default: 0,
    },
    refundedReferralRewardInUsd: {
      type: Number,
      required: true,
      default: 0,
    },
    refundedReferralRewardInLocalCurrency: {
      type: Number,
      required: true,
      default: 0,
    },
  },
  { _id: false }
);

const communityPayoutsSchema = new Schema(
  {
    communityObjectId: { type: Schema.Types.ObjectId, required: true },
    communityCode: { type: String, required: true },
    type: {
      type: String,
      required: true,
      enum: Object.values(PAYOUT_TYPE),
    },
    status: {
      type: String,
      required: true,
      enum: Object.values(PAYOUT_STATUS),
    },
    totalAmountInUsd: { type: Number, required: true, default: 0 },
    totalAmountInLocalCurrency: {
      type: Number,
      required: true,
      default: 0,
    },
    totalMemberPaidAmountInUsd: {
      type: Number,
      required: true,
      default: 0,
    },
    totalMemberPaidAmountInLocalCurrency: {
      type: Number,
      required: true,
      default: 0,
    },
    localCurrency: { type: String, required: true },
    payoutCurrency: { type: String, required: true },
    paymentGatewayFeeInUsd: { type: Number, required: true, default: 0 },
    paymentGatewayFeeInLocalCurrency: {
      type: Number,
      required: true,
      default: 0,
    },
    revenueShareAmountInUsd: { type: Number, required: true, default: 0 },
    revenueShareAmountInLocalCurrency: {
      type: Number,
      required: true,
      default: 0,
    },
    refundAmountInUsd: { type: Number, required: true, default: 0 },
    refundAmountInLocalCurrency: {
      type: Number,
      required: true,
      default: 0,
    },
    passOnRevenueShareAmountInUsd: {
      type: Number,
      required: false,
    },
    passOnRevenueShareAmountInLocalCurrency: {
      type: Number,
      required: false,
    },
    refundBreakdownInUsd: {
      type: refundBreakdownSchema,
      required: false,
    },
    refundBreakdownInLocalCurrency: {
      type: refundBreakdownSchema,
      required: false,
    },
    passOnFeeBreakdownInUsd: {
      type: feeBreakdownSchema,
      required: false,
    },
    passOnFeeBreakdownInLocalCurrency: {
      type: feeBreakdownSchema,
      required: false,
    },
    feeBreakdownInUsd: {
      type: feeBreakdownSchema,
      required: true,
    },
    feeBreakdownInLocalCurrency: {
      type: feeBreakdownSchema,
      required: true,
    },
    payoutChannel: {
      type: String,
      required: true,
      enum: Object.values(PAYOUT_CHANNEL),
    },
    adjustments: {
      type: [adjustmentSchema],
      required: false,
      default: [],
    },
    advancePayouts: {
      type: [advancePayoutSchema],
      required: false,
      default: [],
    },
    affiliateCommission: {
      type: affiliateCommissionSchema,
      required: false,
    },
    referralReward: {
      type: referralRewardSchema,
      required: false,
    },
    hasLinkedToGeneralPayout: {
      type: Boolean,
      required: false,
      default: false,
    },
    payoutAmountInUsd: { type: Number, required: true, default: 0 },
    payoutAmountInLocalCurrency: {
      type: Number,
      required: true,
      default: 0,
    },
    bankAccountInfo: {
      type: Object,
      ref: CommunityManagerPayoutModel,
      required: true,
    },
    payoutTitle: { type: String, required: true },
    payoutReason: { type: String, required: false },
    linkedRevenueTransactions: {
      type: Object,
      required: false,
    },
    payoutDate: { type: Date, required: false },
    // use for FE display when status transition happens, only for [PAID,PROCESSING,PENDING_REPLY]
    payoutStatusTransitionDate: { type: Date, required: false },
    operator: { type: String, required: false },
    stripeConnectPayoutDetails: { type: Object, required: false },
  },
  {
    collection: 'community_payouts',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
  }
);

// Ensure unique record and retrieve all records by date and purchase type for that community
// communityPayoutsSchema.index(
//   { communityObjectId: 1, type: 1, payoutDate: 1 },
//   { unique: true }
// );
//
// // Retrieve all transactions for that community
// communityPayoutsSchema.index({
//   communityObjectId: 1,
//   transactionCreatedAt: -1,
// });

module.exports = model('CommunityPayoutsSchema', communityPayoutsSchema);
