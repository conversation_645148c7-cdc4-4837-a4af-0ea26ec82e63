const mongoose = require('mongoose');
const history = require('../../middleware/mongoose/history.middleware');
const Community = require('./community.model');
const EventAttendees = require('./eventAttendees.model');
const {
  eventTimeIntervalEnum,
  EVENT_TYPES,
  DEFAULT_CURRENCY,
} = require('../../constants/common');
const {
  DEFAULT_COUNTRY_WISE_PRICE_CONFIG,
  COMMUNITY_EVENT_ACCESS_TYPES,
  EVENT_STATUS,
} = require('../constants');
const autoIncrement = require('../../middleware/mongoose/autoIncrement.middleware');
const {
  coverMediaItemSchema,
} = require('../../models/common/coverMediaItems.schema');

const collectionName = 'community_events';

const earningAnalyticsSchema = new mongoose.Schema(
  {
    quantity: { type: Number, required: true },
    revenueInUsd: { type: Number, required: true },
    revenueInLocalCurrency: { type: Number, required: true },
  },
  { _id: false }
);

const CountryWisePriceSchema = new mongoose.Schema(
  {
    country: { type: String, required: false },
    currency: { type: String, required: false },
    amount: { type: Number, required: false }, // stored as cents
    localiseBasePrice: { type: Boolean, required: false },
  },
  { _id: false }
);

const defaultPaymentMethods = [
  {
    value: 'stripe',
    label: 'stripe',
    icon: 'https://d2oi1rqwb0pj00.cloudfront.net/na-website/Payment/svg/bank.svg',
  },
];

const timeBeforeStartTime = new mongoose.Schema(
  {
    type: { type: String, required: true, enum: eventTimeIntervalEnum },
    date: { type: Date, required: true },
  },
  { _id: false }
);

const customTrackingEventsSchema = new mongoose.Schema(
  {
    purchase: {
      type: [String],
      required: true,
    },
  },
  { _id: false }
);

const ApplicationConfigDataFieldSchema = new mongoose.Schema(
  {
    label: { type: String, required: true },
    fieldName: { type: String, required: true },
    fieldDataType: {
      type: String,
      required: true,
      enum: [
        'text',
        'number',
        'date',
        'time',
        'boolean',
        'dropdown',
        'multi-select',
        'multi-select-with-other',
        'text-area',
        'phone',
        'radio',
        'checkbox',
        'email',
        'url',
      ],
    },
    inputSectionKey: { type: String, required: true },
    isEditable: { type: Boolean, required: true, default: false },
    isRequired: { type: Boolean, required: true, default: false },
    isVisible: { type: Boolean, required: true, default: true },
    isDisabled: { type: Boolean, required: true, default: false },
    placeholder: { type: String, required: false },
    defaultValue: { type: String, required: false },
    options: { type: Array, required: false },
    isEditableByAdmin: { type: Boolean, required: false },
    isSunlightUrl: { type: Boolean, default: false },
    isDeleted: { type: Boolean, default: false },
  },
  { _id: false }
);

const PricingConfigSchema = new mongoose.Schema(
  {
    priceType: { type: String, required: false },
    minAmount: { type: Number, required: false },
    suggestedAmount: { type: Number, required: false },
  },
  {
    _id: false,
  }
);

const CommunityEventsSchema = new mongoose.Schema(
  {
    // eventId: { type: Number, required: true },
    title: { type: String, default: '', maxlength: 300 },
    description: { type: String, default: '' }, // LEGACY - maxlength removed to allow gradual cleanup of existing data, the description lengthcheck is in the service level
    descriptionContent: { type: Object, required: false },
    startTime: { type: Date, required: false },
    endTime: { type: Date, required: false },
    timeBeforeStartTime: { type: [timeBeforeStartTime] },
    customDateText: { type: String, default: null }, // for custom date text for multi-day event to show on UI
    cardImgData: { type: Object, required: false },
    liveLink: { type: String, required: false },
    recordingLink: { type: String, required: false },
    inPersonLocation: { type: String, required: false }, // LEGACY
    inPersonLocationMetadata: {
      type: {
        name: { type: String, required: true },
        formatted_address: { type: String },
        mask_address: { type: String },
        location: {
          type: {
            lat: { type: Number },
            lng: { type: Number },
          },
        },
        place_id: { type: String },
        url: { type: String },
      },
      required: false,
    },
    hideLocation: { type: Boolean, required: false, default: true },
    isActive: { type: Boolean, required: true },
    shortUrl: { type: String, required: false },
    isFirstPublished: { type: Boolean, required: false },
    status: {
      type: String,
      required: true,
      default: EVENT_STATUS.DRAFT,
      enum: Object.values(EVENT_STATUS),
    },
    type: {
      type: String,
      required: true,
      enum: [EVENT_TYPES.LIVE, EVENT_TYPES.INPERSON],
    },
    communities: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: Community,
        required: false,
      },
    ],
    host: { type: Object, required: false },
    resources: { type: Array, default: [] },
    icsFileLink: { type: String, required: false },
    chatGroupLink: { type: String, required: false },
    discordEventId: { type: String, required: false },
    slug: { type: String, required: false },
    bannerImg: {
      type: String,
      default:
        'https://d2oi1rqwb0pj00.cloudfront.net/nasIO/common/png/default-event-banner-image.png',
    },
    requiresApproval: { type: Boolean, required: false, default: false },
    applicationConfigDataFields: {
      type: [ApplicationConfigDataFieldSchema],
      required: false,
    },
    access: {
      type: String,
      enum: Object.values(COMMUNITY_EVENT_ACCESS_TYPES),
      default: COMMUNITY_EVENT_ACCESS_TYPES.FREE,
    },
    countryWisePrice: {
      type: [CountryWisePriceSchema],
      default: DEFAULT_COUNTRY_WISE_PRICE_CONFIG,
    },
    localiseForAllCountries: { type: Boolean, required: false },
    amount: { type: Number, required: false }, // stored in cents
    currency: { type: String, default: DEFAULT_CURRENCY },
    paymentMethods: { type: Array, default: defaultPaymentMethods },
    isDemo: { type: Boolean, required: false, default: false },
    isSoldOut: { type: Boolean, required: false, default: false },
    createdByLearnerObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
    },
    hideAttendeesCount: { type: Boolean, required: false, default: false },
    config: { type: Object, required: false },
    preferences: { type: Object, required: false },
    discountsApplied: {
      type: [mongoose.Schema.Types.ObjectId],
      default: [],
    },
    attendeeLimit: { type: Number, required: false },
    instalmentOptions: { type: Object, required: false },
    timezoneId: { type: String, required: false },
    isCapacitySet: { type: Boolean, required: false },
    customTrackingEvents: {
      type: customTrackingEventsSchema,
      required: false,
    },
    maxQuantityPerPurchase: { type: Number, required: false },
    bulkPurchaseEnabled: {
      type: Boolean,
      required: false,
      default: false,
    },
    pricingConfig: { type: PricingConfigSchema, required: false },
    earningAnalytics: { type: earningAnalyticsSchema, required: false },
    affiliateEarningAnalytics: {
      type: earningAnalyticsSchema,
      required: false,
    },
    templateLibraryId: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
    },
    duplicationParentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'CommunityEvents',
      required: false,
      default: null,
    },
    coverMediaItems: {
      type: [coverMediaItemSchema],
      required: false,
    },
  },
  {
    toJSON: { virtuals: true }, // So `res.json()` and other `JSON.stringify()` functions include virtuals
    toObject: { virtuals: true }, // So `console.log()` and other functions that use `toObject()` include virtuals
    collection: collectionName,
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'lastModifiedTimeStamp',
    },
  }
);

CommunityEventsSchema.plugin(autoIncrement.plugin, {
  model: 'CommunityEventsSchema',
  field: 'eventId',
  startAt: 40,
  incrementBy: 1,
});

// Specifying a virtual with a `ref` property is how you enable virtual
// population
CommunityEventsSchema.virtual('attendees', {
  ref: EventAttendees,
  localField: '_id',
  foreignField: 'eventObjectId',
  count: true,
});

CommunityEventsSchema.plugin(history, {
  collection: collectionName + '_history',
  mongoose,
});

const CommunityEventsModel = mongoose.model(
  'CommunityEvents',
  CommunityEventsSchema
);

module.exports = CommunityEventsModel;
