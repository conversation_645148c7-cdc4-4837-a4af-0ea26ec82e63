const ObjectId = require('mongoose').Types.ObjectId;
const { Schema, model } = require('mongoose');
const learnersModel = require('../../models/learners.model');

const autoIncrement = require('../../middleware/mongoose/autoIncrement.middleware');

const PlanHistorySchema = new Schema(
  {
    stripePriceId: { type: String, required: true },
    intervalCount: { type: Number, required: true },
    interval: { type: String, required: true },
    nextBillingDate: { type: Date, required: true },
    communitySignupId: { type: String, required: true },
    changedDateTime: { type: Date, required: true },
    billingCycle: { type: Number, required: true },
    planAmount: { type: Number, required: true },
    planCurrency: { type: String, required: true },
    discountDetails: { type: Object, required: false },
  },
  { _id: false }
);

const CommunitySubscriptionSchema = new Schema(
  {
    subscriptionId: { type: Number, required: true },
    communityCode: { type: String, required: true },
    email: { type: String, required: true },
    learnerId: { type: Number, required: true },
    learnerObjectId: {
      type: ObjectId,
      required: false,
      ref: learnersModel,
    },
    stripeSubscriptionId: { type: String, required: false },
    stripeCustomerId: { type: String, required: false },
    stripeProductId: { type: String, required: false },
    stripePriceId: { type: String, required: false },
    stripePrice: { type: Number, required: false },
    stripeCurrency: { type: String, required: false },
    stripeProductProvider: { type: String, required: false },
    amount: { type: Number, required: false },
    currency: { type: String, required: false },
    country: { type: String, required: false },
    memberType: { type: String, required: false },
    isRepurchase: { type: Boolean, default: false },
    billingCycle: { type: Number, default: 1 },
    invoiceDetails: { type: Object, required: false },
    nextBillingDate: { type: Date, required: false },
    interval: { type: String, required: false },
    intervalCount: { type: Number, required: false },
    userInChat: { type: Boolean, required: false },
    reviewerNotes: { type: String, required: false },
    status: { type: String, required: false },
    removedBy: { type: String, required: false },
    removalReason: { type: String, required: false },
    cancellationReason: { type: String, required: false },
    removedAt: { type: Date, required: false },
    isDemo: { type: Boolean, default: false, required: false },
    communitySignupId: { type: String, required: false },
    cancelledAt: { type: Date, required: false },
    unsubscribedAt: { type: Date, required: false },
    // userConfirmedJoiningChat schema is object with each key as community chat platform name as lowercase and value as boolean
    userConfirmedJoiningChat: {
      type: Object,
      required: false,
    },
    phoneNumber: { type: String, required: false },
    paymentProvider: { type: String, required: false },
    onGracePeriod: { type: Boolean, required: false },
    gracePeriodStartedAt: { type: Date, required: false },
    webhookTriggered: { type: Boolean, required: false },
    planHistory: { type: [PlanHistorySchema], required: false },
    // Cancellation revoke fields
    cancellationRevokedAt: { type: Date, required: false },
    cancellationRevokeReason: { type: String, required: false },
    cancellationRevokeHistory: { type: Array, required: false },
    disableRevokeCancellation: { type: Boolean, required: false },
  },
  {
    collection: 'community_subscriptions',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
  }
);

CommunitySubscriptionSchema.plugin(autoIncrement.plugin, {
  model: 'CommunitySubscriptionSchema',
  field: 'subscriptionId',
  startAt: 20000,
  incrementBy: 1,
});

const CommunitySubscriptionsModel = model(
  'CommunitySubscriptions',
  CommunitySubscriptionSchema
);

module.exports = CommunitySubscriptionsModel;
