const express = require('express');
const multer = require('multer');

const communitiesRouter = express.Router({ mergeParams: true });
const CurrencyRouter = require('../../../api/v1/currency/router');
const CommunitySignupRouter = require('../../../api/v1/communitySignup/router');
const TransactionsRouter = require('../../../api/v1/transaction/router');
const EarningsRouter = require('../../../api/v1/earning/router');
const ActivityRouter = require('../../../api/v1/activity/router');
const DiscountRouter = require('../../../api/v1/discounts/router');
const EventRouter = require('../../../api/v1/events/router');
const FolderRouter = require('../../../api/v1/folders/router');
const EntityRouter = require('../../../api/v1/entity/router');
const UpsellRouter = require('../../../api/v1/upsell/router');
const SummaryRouter = require('../../../api/v1/summary/router');
const MailRouter = require('../../../api/v1/mail/router');
const MailContentRouter = require('../../../api/v1/mail/router'); //TODO: To delete after FE remove dependencies
const LeadRouter = require('../../../api/v1/lead/router');
const PayoutRouter = require('../../../api/v1/payout/router');
const AffiliateRouter = require('../../../api/v1/affiliate');
const WebhookRouter = require('../../../api/v1/webhook/router');
const HomeAnnouncementRouter = require('../../../api/v1/announcement/router');
const CommunityRouter = require('../../../api/v1/community/router');
const AnalyticsV2Router = require('../../../api/v1/analyticsV2/router');
const batchMetadataRouter = require('../../../api/v1/batchMetadata/router');
const chatRouter = require('../../../api/v1/chat/router');
const aiCofounderRouter = require('../../../api/v1/aiCofounder/router');
const planRouter = require('../../../api/v1/plan/router');
const priceRouter = require('../../../api/v1/price/router');
const CreateCompleteRouter = require('../../../api/v1/communities/router');

const upload = multer();

// different communities (feel free to come up with a better name :D)
// maybe we make distinction communitiesAPI (for the whole thing) and communities (different communities api)

// as we are tight with time, I think it's safe to do only get endpoint as our default approach for every route
// and if we need other methods on FE, I will point that out

// in the upcoming weeks after the first releases, we will start handling other methods as well
// for now I will be just adding those placeholders for the sake of having an overview of everything, and before the release we can decide to delete them

// if it makes more sense, instead of using one communitiesRouter, you can create a separate router and put it in its own modul for every endpoint

// TO DISCUSS Alek, Sara
/*
- move string literals to constants? 
*/
const {
  communityController,
  communityPostsController,
  communityEventsController,
  communitySubscriptionsController,
  communityVideosController,
  communityResourcesController,
  communityBookingController,
  communityApplicationsController,
  communityAnalyticsController,
  communityApplicationConfigController,
  communityUIConfigController,
  CommunityGuestHostController,
  communityMagicReachController,
  communityFolderController,
  communityFolderItemController,
  communityFolderAccessLogController,
  communityManagerController,
  communityManagerTodosController,
  communityLandingPageController,
  communityDiscordRolesController,
  communityMembersInvite,
  communityPostReactionController,
  communityRevenueController,
  communityPayoutController,
  communityDiscordMessagesController,
  disconectChatController,
  discordChannelController,
  communityPostCommentController,
  communityPostCommentReactionController,
  communityEntityController,
  communityLeaveController,
  cancelScheduledCancellationController,
  whatsappController,
  signupFlowController,
} = require('../../controllers/web');

const {
  uploadCommunityImage,
} = require('../../middlewares/uploadCommunityImage.middleware');

const {
  uploadCommunityFolderImage,
} = require('../../middlewares/uploadFolderImage.middleware');

const {
  uploadMagicReachDraftImage,
} = require('../../middlewares/uploadMagicReachDraftImage.middleware');

const {
  ownerCommunityValidator,
  managerCommunityValidator,
  memberCommunityValidator,
  communityAdminValidator,
  addCommunityEventManagerValidator,
  communityEventManagerValidator,
  communityFolderManagerValidator,
  deleteCommentValidator,
  communityGetFolderManagerValidator,
  communityFolderReadAccessValidator,
  communityPostAccessValidator,
  communityFolderManagerAndProgramManagerValidator,
  managerCommunityValidatorWithoutError,
  communityCreatePostsValidator,
  communityEditPostsValidator,
} = require('../../validations/community.validation');

const tokenValidator = require('../../../validations/token.validation');
const tokenValidatorWithoutError = require('../../../validations/tokenNoError.validation');
const userValidation = require('../../../validations/user.validation');
const userValidationWithoutError = require('../../../validations/userValidationWithoutError.validation');
const transientTokenValidator = require('../../../validations/transientToken.validation');
const payloadSignatureValidator = require('../../../validations/payloadSignature.validation');
const apiKeyValidator = require('../../../validations/apiKey.validation');

const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const copsTokenMiddleware = require('../../../middleware/cops-token.middleware');
const optionalAccessTokenValidationWithoutError = require('../../../validations/optionalToken.validation');
const {
  MODULE_TYPE,
  rateLimitMiddleware,
} = require('../../../utils/rateLimit.util');

const { ACCESS_TOKEN_TYPE } = require('../../../constants/common');

const { handlerWrapper } = require('../../../utils/request.util');

const rejectBlacklistedUser = require('../../../middleware/rejectBlacklisted.middleware');

/*******************
 * NOT ACL PROTECTED
 * *****************
 */
communitiesRouter
  .route('/all')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityController.getAllCommunity
  );

communitiesRouter
  .route('/data')
  .get(
    postRoutePreHandlerMiddleware,
    communityController.getCommunityData
  );

communitiesRouter
  .route('/rsvp-reminder')
  .post(
    postRoutePreHandlerMiddleware,
    apiKeyValidator,
    communityEventsController.sendEmailToRsvpUsers
  );

communitiesRouter
  .route('/community-revenue-calculator')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityRevenueController.calculateRevenue
  );

// Signup
communitiesRouter
  .route('/:communityId/isMember')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    signupFlowController.signupMemberCheck
  );

// Social bot integration route
communitiesRouter
  .route('/:communityId/social-bot-integration')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityController.addBotToCommunity
  )
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityController.deleteBotFromCommunity
  );

communitiesRouter
  .route('/:communityId/discord-roles')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityDiscordRolesController.getCommunityDiscordRoles
  );

communitiesRouter
  .route('/getDiscordRoles/:guildId/:discordUserId')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityDiscordRolesController.assignConfiguredDiscordRoleToUser
  );
communitiesRouter
  .route('/:communityId/discord-role-config/:roleId')
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityDiscordRolesController.updateDiscordRoleConfiguration
  )
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityDiscordRolesController.deleteDiscordRoleConfiguration
  );

communitiesRouter
  .route('/:communityId/bots')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityController.getCommunityBotDetails
  );

communitiesRouter
  .route('/:communityId/user-joined-chat')
  .patch(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communitySubscriptionsController.toggleUserConfirmedJoiningChat
  );

// Communities (need it in onboarding flow)
communitiesRouter
  .route('/')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityController.getCommunities
  );

// Active community API for CMP
communitiesRouter.route('/active').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  handlerWrapper({
    handler: communityController.getActiveCommunity,
  })
);

communitiesRouter.route('/:communityId/learner').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  handlerWrapper({
    handler: communityController.getCommunityDataForLearner,
  })
);

// logging from the front end to the backend
communitiesRouter
  .route('/log')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityController.createLog
  );

communitiesRouter.use('/lead', LeadRouter);
communitiesRouter.use('/signup', CommunitySignupRouter);
communitiesRouter.use('/plans', planRouter);
communitiesRouter.use('/currencies', CurrencyRouter);
communitiesRouter.use('/create-complete', CreateCompleteRouter);
communitiesRouter.use('/:communityId/summary', SummaryRouter);
communitiesRouter.use('/:communityId/events', EventRouter);
communitiesRouter.use('/:communityId/discounts', DiscountRouter);
communitiesRouter.use('/:communityId/transactions', TransactionsRouter);
communitiesRouter.use('/:communityId/earnings', EarningsRouter);
communitiesRouter.use('/:communityId/analyticsV2', AnalyticsV2Router);
communitiesRouter.use('/:communityId/activities', ActivityRouter);
communitiesRouter.use('/:communityId/webhooks', WebhookRouter);
communitiesRouter.use('/:communityId/chats', chatRouter);
communitiesRouter.use('/:communityId/ai-cofounder', aiCofounderRouter);
communitiesRouter.use(
  '/:communityId/affiliates',
  AffiliateRouter.cmpRouter
);
communitiesRouter.use('/:communityId/upsells', UpsellRouter);
communitiesRouter.use('/:communityId/batch-metadata', batchMetadataRouter);
communitiesRouter.use(
  '/:communityId/home-announcements',
  HomeAnnouncementRouter
);
communitiesRouter.use('/:communityId/entity', EntityRouter);
communitiesRouter.use('/:communityId/mail', MailRouter);
communitiesRouter.use('/:communityId/mailContent', MailContentRouter); //TODO: To delete after FE remove dependencies
communitiesRouter.use('/:communityId/payouts', PayoutRouter);
communitiesRouter.use('/:communityId', CommunityRouter);
communitiesRouter.use('/:communityId', priceRouter);

communitiesRouter.route('/:communityId/public').put(
  postRoutePreHandlerMiddleware,
  tokenValidatorWithoutError,
  handlerWrapper({
    handler: communityController.updateCommunityForPublicApi,
  })
);

communitiesRouter.route('/:communityId').put(
  postRoutePreHandlerMiddleware,
  rateLimitMiddleware({
    module: MODULE_TYPE.COMMUNITY_UPDATE,
  }),
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  communityController.updateCommunity
);

communitiesRouter
  .route('/:communityId/prices')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityController.getCommunityProductPrices
  );

communitiesRouter
  .route('/:communityId/subscription-display-prices')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidatorWithoutError,
    communityController.getCommunitySubscriptionDisplayPrices
  );

communitiesRouter
  .route('/:communityId/revenue-goal')
  .patch(
    managerCommunityValidator,
    communityController.updateCommunityRevenueGoal
  );

communitiesRouter
  .route('/:communityId/linkValidation')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityController.linkValidation
  );

communitiesRouter
  .route('/:communityId/thumbnail-image')
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    uploadCommunityImage.single('image'),
    communityController.uploadThumbnailImage
  );

// Applications
communitiesRouter
  .route('/:applicationId/update-application-status')
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityApplicationsController.updateCommunityApplicationStatus
  );

communitiesRouter
  .route('/:applicationId/update-application-by-admin')
  .patch(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityApplicationsController.updateApplicationByAdmin
  );

// Folder access log
communitiesRouter
  .route('/folder/:communityFolderId/log')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityFolderAccessLogController.createFolderAccessLogs
  );

// Manager to do progress update (need it in onboarding flow)
communitiesRouter
  .route('/:communityId/manager-todos-progress/:todoCode')
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityManagerTodosController.updateManagerTodosProgress
  );

// Resources
communitiesRouter
  .route('/:communityId/resources')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityResourcesController.getCommunityResources
  );

// Videos
communitiesRouter
  .route('/:communityId/videos')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityVideosController.getCommunityVideos
  );

// Book trainers
communitiesRouter
  .route('/:communityId/trainers/book')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityBookingController.registerBookingEvent
  )
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityBookingController.registerBookingEvent
  );

communitiesRouter
  .route('/:communityId/trainers/cancel')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityBookingController.cancelBookingEvent
  )
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityBookingController.cancelBookingEvent
  );

// ui config
communitiesRouter
  .route('/ui-config/:communityId/')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityUIConfigController.getCommunityUIConfig
  );

communitiesRouter.route('/community-manager').post(
  postRoutePreHandlerMiddleware,
  rateLimitMiddleware({
    module: MODULE_TYPE.COMMUNITY_ONBOARDING,
  }),
  payloadSignatureValidator,
  tokenValidator(),
  userValidation,
  rejectBlacklistedUser,
  communityManagerController.createCommunityManager
);

communitiesRouter
  .route('/community-manager/resend-email')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityManagerController.resendEmailToAdmin
  );

communitiesRouter
  .route('/:communityId/community-manager/remove-member')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityManagerController.removeCommunityMember
  );

communitiesRouter
  .route('/:communityId/member/remove-member')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityManagerController.removeCommunityMemberWithoutManager
  );

/**************************
 * MANAGER PROTECTED ROUTES
 * ************************
 */

// Application Config
communitiesRouter
  .route('/:communityId/application-config')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityApplicationConfigController.getCommunityApplicationConfig
  );

// Community Admin
communitiesRouter
  .route('/admin/:communityId')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityManagerController.getAdminCountWithLimit
  )
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: communityManagerController.addAdmins,
    })
  )
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityManagerController.removeAdmin
  );

communitiesRouter
  .route('/admin-status/:communityId')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityManagerController.getAdminStatus
  );

// Communities
communitiesRouter
  .route('/:communityId/banner-image')
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    uploadCommunityImage.single('image'),
    communityController.uploadBannerImage
  );

// Folders / Folder Items aka Library - Search
communitiesRouter
  .route('/:communityId/search')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityFolderReadAccessValidator,
    communityFolderController.getFilteredLibraryFromCommunity
  );

// video upload status of a folder Item
communitiesRouter
  .route('/videoUploadStatus')
  .post(
    postRoutePreHandlerMiddleware,
    apiKeyValidator,
    communityFolderController.updateVideoUploadStatus
  );

// Folders
communitiesRouter.use('/:communityId/folders', FolderRouter);
communitiesRouter
  .route('/:communityId/folders')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidatorWithoutError,
    userValidationWithoutError,
    managerCommunityValidatorWithoutError,
    communityFolderController.getFolders
  );
communitiesRouter
  .route('/:communityId/products')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidatorWithoutError,
    userValidationWithoutError,
    communityFolderController.getProducts
  );

communitiesRouter
  .route('/:communityId/folder/updateIndices')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityFolderController.updateFolderIndices
  );

communitiesRouter
  .route('/:communityId/folder')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    uploadCommunityFolderImage.single('image'),
    communityFolderController.createOneFolder
  );

communitiesRouter
  .route('/:communityId/folder/:communityFolderId')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityGetFolderManagerValidator,
    communityFolderController.getFolderById
  )
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityFolderManagerValidator,
    uploadCommunityFolderImage.single('image'),
    communityFolderController.updateOneFolder
  )
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityFolderManagerValidator,
    communityFolderController.deleteOneFolder
  );

communitiesRouter
  .route('/:communityId/folder/:communityFolderId/access')
  .post(
    postRoutePreHandlerMiddleware,
    apiKeyValidator,
    handlerWrapper({
      handler: communityFolderController.autoPurchaseFolder,
    })
  );

communitiesRouter
  .route('/:communityId/folder/:communityFolderId/save-and-publish')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityFolderManagerValidator,
    communityFolderController.saveAndPublishChanges
  );

// Folder Item
communitiesRouter
  .route('/:communityId/folder/:communityFolderId/folder-item')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityFolderManagerAndProgramManagerValidator,
    communityFolderItemController.createOneFolderItem
  );

communitiesRouter
  .route('/:videoObjectId/check-folder-item-exists')
  .get(
    postRoutePreHandlerMiddleware,
    apiKeyValidator,
    communityFolderItemController.checkFolderItemExists
  );

communitiesRouter
  .route('/:videoObjectId/update-folder-item-video-links')
  .post(
    postRoutePreHandlerMiddleware,
    apiKeyValidator,
    communityFolderItemController.updateVideoLinksForFolderItem
  );

// update the video progress of a folder item
communitiesRouter
  .route('/:videoObjectId/update-video-encoding-progress')
  .post(
    postRoutePreHandlerMiddleware,
    apiKeyValidator,
    communityFolderItemController.updateVideoEncodingProgress
  );
communitiesRouter
  .route('/:communityId/check-community-storage/:uploadingFileSize')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityFolderController.checkCommunityStorage
  );

communitiesRouter
  .route('/:communityId/storage')
  .get(
    postRoutePreHandlerMiddleware,
    communityFolderController.retrieveCommunityStorage
  );

communitiesRouter
  .route('/:communityId/folder-item/:communityFolderItemId')
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityFolderManagerAndProgramManagerValidator,
    upload.none(),
    communityFolderItemController.updateOneFolderItem
  )
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityFolderManagerAndProgramManagerValidator,
    communityFolderItemController.deleteOneFolderItem
  )
  .get(communityFolderItemController.getFolderItemById);

// Folder Item - signed url
communitiesRouter
  .route('/:communityId/folder/:communityFolderItemId/folder-item-upload')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityFolderItemController.getS3SignendUrl
  )
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityFolderItemController.completeUploadS3SignedUrl
  );

// Folder Item - release storage reservation
communitiesRouter
  .route('/:communityId/folder/:folderId/folder-item-upload/release')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,

    handlerWrapper({
      handler:
        communityFolderItemController.releaseFolderItemUploadReservation,
    })
  );

communitiesRouter
  .route('/:communityId/folder/:communityFolder/reorder-folderItems')
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityFolderController.reorderFoldersItems
  );

// Guest Host
communitiesRouter
  .route('/:communityId/guest-host')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    CommunityGuestHostController.createGuestHost
  );

communitiesRouter
  .route('/:communityId/hosts')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    CommunityGuestHostController.getCommunityHosts
  );

// Subscription Members
communitiesRouter
  .route('/:communityId/memberships-for-admin')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communitySubscriptionsController.getCommunityMembersForAdmin
  );

communitiesRouter
  .route('/:communityId/memberships-all')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communitySubscriptionsController.getCommunityMemberLeadsForAdmin
  );

communitiesRouter
  .route('/:communityId/whatsapp/csv')
  .get(
    postRoutePreHandlerMiddleware,
    transientTokenValidator,
    whatsappController.getExportWhatsappMembers
  );

communitiesRouter
  .route('/:communityId/whatsapp/:whatsappId')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communitySubscriptionsController.getWhatsappCommunityMember
  )
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communitySubscriptionsController.deleteWhatsappMember
  );

// Magic Reach
communitiesRouter
  .route('/:communityId/magic-reach')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityMagicReachController.getMagicReachBucketData
  );

communitiesRouter
  .route('/:communityId/fetch-bucket-meta-data')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityMagicReachController.fetchBucketMetaData
  );

// Magic Reach - whatsapp
communitiesRouter
  .route('/:communityId/whatsapp-templates')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    whatsappController.getWhatsappTemplates
  );

// Magic Reach - Draft and email API
communitiesRouter
  .route('/:communityId/communicate/create-draft')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityMagicReachController.createMagicReachDraftEmail
  );

communitiesRouter
  .route('/:communityId/communicate/update-draft')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityMagicReachController.updateDraftMagicReachEmail
  );

communitiesRouter
  .route('/:communityId/communicate/get-drafts')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityMagicReachController.getAllDraftMagicReachEmails
  );

communitiesRouter
  .route('/:communityId/communicate/get-sent-emails')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityMagicReachController.getAllSentMagicReachEmails
  );

communitiesRouter
  .route('/:communityId/communicate/delete-draft/:id')
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityMagicReachController.deleteDraftMagicReachEmail
  );

communitiesRouter
  .route('/:communityId/communicate/upload-image/:id')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    uploadMagicReachDraftImage.single('file'),
    communityMagicReachController.uploadDraftMagicReachImage
  );

communitiesRouter
  .route('/:communityId/communicate/send-draft')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityMagicReachController.sendDraftEmail
  );

communitiesRouter
  .route('/:communityId/communicate/send-whatsapp-template-msg')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityMagicReachController.sendWhatsappTemplateMsg
  );

communitiesRouter
  .route('/:communityId/communicate/recipients')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityMagicReachController.getMagicReachRecipientsForDraftId
  )
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityMagicReachController.saveMagicReachEmailsByDraftId
  )
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityMagicReachController.deleteMagicReachRecipientsByDraftId
  )
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityMagicReachController.getAllRecipientsMetaDataForDraftId
  );

// Manager todos progress
communitiesRouter
  .route('/:communityId/manager-todos-progress')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityManagerTodosController.getManagerTodosProgress
  );

// Member Invite
communitiesRouter.route('/:communityId/invite-members').post(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  handlerWrapper({
    handler: communityMembersInvite.inviteMembers,
    wrapResponseInObject: false,
  })
);
communitiesRouter.route('/:communityId/bulk-invite-members').post(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  handlerWrapper({
    handler: communityMembersInvite.bulkInviteMembers,
    wrapResponseInObject: false,
  })
);
communitiesRouter
  .route('/resume-invite')
  .post(
    postRoutePreHandlerMiddleware,
    handlerWrapper({ handler: communityMembersInvite.resumeInvite })
  );

// Disconnect Chat
communitiesRouter
  .route('/:communityId/disconnect-chat')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    disconectChatController.disconnectCommunityChat
  );

communitiesRouter
  .route('/:communityId/disconnect-chat/cops')
  .post(
    postRoutePreHandlerMiddleware,
    copsTokenMiddleware,
    disconectChatController.disconnectCommunityAllChat
  );

communitiesRouter
  .route('/:communityId/send-discord-message')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityDiscordMessagesController.sendDiscordMessageFromMagicReach
  );
communitiesRouter
  .route('/:communityId/getAllChannelsFromDiscord')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    discordChannelController.getAllDiscordChannelsFromAServer
  );

communitiesRouter
  .route('/:communityId/setDiscordAnnouncementChannel')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    discordChannelController.connectDiscordChannelWithCommunityForAnnouncements
  );
/*************************
 * MEMBER PROTECTED ROUTES
 * ***********************
 */

// Folders / Folder Items aka Library - Search
communitiesRouter
  .route('/member/:communityId/search')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityFolderController.getFilteredLibraryFromCommunity
  );

// Folders
communitiesRouter
  .route('/member/:communityId/folders')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityFolderController.getFolders
  );

communitiesRouter
  .route('/member/:communityId/folder/:communityFolderId')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidatorWithoutError,
    userValidationWithoutError,
    communityFolderController.getFolderById
  );

/**
 * HAVE NOT DECIDED YET
 */
communitiesRouter
  .route('/admin-community')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityAdminValidator,
    communityController.getAdminCommunities
  );

communitiesRouter
  .route('/:communityId')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityController.getCommunityById
  );

communitiesRouter
  .route('/set-monetisation-viewed/:communityId')
  .patch(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityController.setMonetisationModalViewed
  );

communitiesRouter
  .route('/:communityId/money-page-viewed')
  .patch(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityController.setMoneyPageViewed
  );

// leave a community
communitiesRouter
  .route('/leave-community/:communityId')
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityLeaveController.leaveCommunityController
  );

// revoke cancellation for a community
communitiesRouter
  .route('/revoke-cancellation/:communityId')
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    cancelScheduledCancellationController.cancelScheduledCancellationController
  );

// posts
communitiesRouter
  .route('/:communityId/posts')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityPostsController.getCommunityPosts
  );

// announcements (announcements are posts)
communitiesRouter
  .route('/:communityId/announcements')
  .get(
    postRoutePreHandlerMiddleware,
    optionalAccessTokenValidationWithoutError,
    communityPostAccessValidator,
    communityPostsController.getAnnouncementPosts
  );

// Pinned announcements (announcements are posts)
communitiesRouter
  .route('/:communityId/pinned-announcements')
  .get(
    postRoutePreHandlerMiddleware,
    optionalAccessTokenValidationWithoutError,
    communityPostAccessValidator,
    communityPostsController.getPinnedAnnouncementPosts
  );

communitiesRouter
  .route('/:communityId/announcement/:announcementId')
  .get(
    postRoutePreHandlerMiddleware,
    optionalAccessTokenValidationWithoutError,
    communityPostAccessValidator,
    communityPostsController.getAnnouncementPost
  );

communitiesRouter
  .route('/:communityId/announcement')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityCreatePostsValidator,
    communityPostsController.createAnnouncementPost
  );

communitiesRouter
  .route('/:communityId/announcement')
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityEditPostsValidator,
    communityPostsController.updateAnnouncementPost
  );

communitiesRouter.route('/:communityId/products/announcement').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  memberCommunityValidator,
  handlerWrapper({
    handler: communityPostsController.getAllCommunityProducts,
  })
);

communitiesRouter.route('/:communityId/user/announcements').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  memberCommunityValidator,
  handlerWrapper({
    handler: communityPostsController.getUserAnnouncementPosts,
  })
);

communitiesRouter.route('/:communityId/announcement/report').post(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  memberCommunityValidator,
  handlerWrapper({
    handler: communityPostsController.reportPost,
  })
);

communitiesRouter
  .route('/:communityId/announcement')
  .patch(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityEditPostsValidator,
    communityPostsController.updateAnnouncementPost
  );

communitiesRouter
  .route('/:communityId/announcement/:announcementId')
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityEditPostsValidator,
    communityPostsController.deleteAnnouncementPost
  );

communitiesRouter
  .route('/revalidate-fe-path')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityAdminValidator,
    communityLandingPageController.revalidateFEPath
  );

// analytics
communitiesRouter
  .route('/:communityId/analytics')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityAnalyticsController.getAnalytics
  );

communitiesRouter
  .route('/:communityId/analytics-graph')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityAnalyticsController.getAnalyticsGraph
  );

// memberships
communitiesRouter
  .route('/:subscriptionId/update-subscription-by-admin')
  .patch(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityAdminValidator,
    communitySubscriptionsController.updateSubscriptionByAdmin
  );

communitiesRouter
  .route('/:communityId/memberships')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communitySubscriptionsController.getCommunityMembers
  );

// events
communitiesRouter
  .route('/event/:eventId/register')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityEventsController.registerForEvent
  );

communitiesRouter
  .route('/:communityId/events')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityEventsController.getCommunityEvents
  );

communitiesRouter
  .route('/:communityId/event-admin/:eventId')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityEventsController.getEventByIdForAdmin
  );

communitiesRouter
  .route('/:communityId/events/upcoming')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidatorWithoutError,
    userValidationWithoutError,
    communityEventsController.getUpcomingEventsByCommunityId
  );
communitiesRouter
  .route('/:communityId/events/past')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidatorWithoutError,
    userValidationWithoutError,
    communityEventsController.getPastEventsByCommunityId
  );

// TODO: Change this after event refactor. To decided if we want to refactor the one to many communities structure
communitiesRouter
  .route('/event/:id')
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityEventManagerValidator,
    communityEventsController.deleteEvent
  )
  .patch(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityEventManagerValidator,
    communityEventsController.updateEvent
  );

communitiesRouter
  .route('/add-event')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    addCommunityEventManagerValidator,
    communityEventsController.createEvent
  );

// event checkout flow APIs
communitiesRouter
  .route('/:communityId/event/signup')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityEventsController.eventSignUp
  );

communitiesRouter
  .route('/:communityId/event/stripe-checkout')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityEventsController.eventStripeCheckout
  );

communitiesRouter
  .route('/:communityId/event/xendit-ewallet-checkout')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityEventsController.eventXenditEwalletCheckout
  );

communitiesRouter
  .route('/:communityId/event/free-checkout')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityEventsController.eventFreeCheckout
  );

communitiesRouter
  .route('/:communityId/event/verify-payment')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityEventsController.verifyPaidEventPayment
  );

communitiesRouter
  .route('/:communityId/event/:eventId/rsvp-list')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityEventsController.getEventRSVPList
  );

communitiesRouter
  .route('/:communityId/:learnerObjectId/paid-events')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityEventsController.getUpcomingPaidEventsForLearner
  );

communitiesRouter
  .route('/:communityId/event/:eventId')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidatorWithoutError,
    userValidationWithoutError,
    communityEventsController.getEventById
  );

// announcement reactions
communitiesRouter
  .route('/:communityId/announcements/:announcementId/reaction')
  .get(
    postRoutePreHandlerMiddleware,
    optionalAccessTokenValidationWithoutError,
    communityPostAccessValidator,
    communityPostReactionController.reactionsForAnnouncement
  )
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityPostReactionController.addReaction
  )
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityPostReactionController.removeReaction
  );

// payout information
communitiesRouter
  .route('/:communityId/payout-information')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityPayoutController.CommunityManagerBankAccountService
      .getPayoutBankAccount
  )
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator([ACCESS_TOKEN_TYPE.EMAIL], [ACCESS_TOKEN_TYPE.EMAIL]),
    userValidation,
    ownerCommunityValidator,
    communityPayoutController.CommunityManagerBankAccountService
      .createPayoutBankAccount
  )
  .patch(
    tokenValidator([ACCESS_TOKEN_TYPE.EMAIL], [ACCESS_TOKEN_TYPE.EMAIL]),
    ownerCommunityValidator,
    communityPayoutController.CommunityManagerBankAccountService
      .updatePayoutBankAccount
  )
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator([ACCESS_TOKEN_TYPE.EMAIL], [ACCESS_TOKEN_TYPE.EMAIL]),
    userValidation,
    ownerCommunityValidator,
    communityPayoutController.CommunityManagerBankAccountService
      .deletePayoutBankAccount
  );

// announcement comments
communitiesRouter
  .route('/:communityId/announcements/:announcementId/comment')
  .get(
    postRoutePreHandlerMiddleware,
    optionalAccessTokenValidationWithoutError,
    communityPostAccessValidator,
    communityPostCommentController.commentsForAnnouncement
  )
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityPostCommentController.addComment
  );

communitiesRouter
  .route('/:communityId/announcements/:announcementId/comment/:commentId')
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    deleteCommentValidator,
    communityPostCommentController.removeComment
  );

communitiesRouter
  .route(
    '/:communityId/announcements/:announcementId/comment/:commentId/reply'
  )
  .get(
    postRoutePreHandlerMiddleware,
    optionalAccessTokenValidationWithoutError,
    communityPostAccessValidator,
    communityPostCommentController.repliesForAnnouncementComment
  );

// announcement comment reactions
communitiesRouter
  .route(
    '/:communityId/announcements/:announcementId/comment/:commentId/reaction'
  )
  .get(
    postRoutePreHandlerMiddleware,
    optionalAccessTokenValidationWithoutError,
    communityPostAccessValidator,
    communityPostCommentReactionController.reactionsForAnnouncementComment
  )
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityPostCommentReactionController.addReaction
  )
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityPostCommentReactionController.removeReaction
  );

// community one time payment entity checkout flow APIs
communitiesRouter
  .route('/:communityId/entity/signup')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityEntityController.entitySignUp
  );

communitiesRouter
  .route('/:communityId/entity/stripe-checkout')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityEntityController.entityStripeCheckout
  );

communitiesRouter
  .route('/:communityId/entity/xendit-ewallet-checkout')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityEntityController.entityXenditEwalletCheckout
  );

communitiesRouter
  .route('/:communityId/entity/verify-payment')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator([ACCESS_TOKEN_TYPE.CHECKOUT]),
    userValidation,
    communityEntityController.verifyPaidEntityPayment
  );

communitiesRouter
  .route('/:communityId/subscription/verify-payment')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator([ACCESS_TOKEN_TYPE.CHECKOUT]),
    userValidation,
    communitySubscriptionsController.verifySubscriptionPayment
  );

communitiesRouter
  .route('/:communityId/plan/verify-payment')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communitySubscriptionsController.verifyPlanPayment
  );

// resources
// PENDING: need more information on whether these resources are gonna be strictly used only for communities or not
// trainers
// QUESTION FOR Sara: can we reuse the existing trainers api when the time comes? Let's discuss this later

// communitiesRouter.get(
//   '*',
//   postRoutePreHandlerMiddleware,
//   tokenValidator(),
//   userValidation,
//   (req, res) => {
//     res.send('Communities API');
//   }
// );

module.exports = communitiesRouter;
