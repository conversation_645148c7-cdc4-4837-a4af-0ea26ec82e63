const express = require('express');

const communitiesMobileRouter = express.Router();

const {
  communityController,
  communityEventsController,
  communityMembershipsController,
  communityBookingController,
  communityTodosController,
  communityVideosController,
  communityResourcesController,
  communityMobileNotificationsController,
  communityFolderController,
  communityFolderItemController,
  communityFolderAccessLogController,
  communityPostsController,
  communityManagerTodosController,
  communityPostReactionController,
  communityPostCommentController,
  communityPostCommentReactionController,
  communityLeaveController,
  cancelScheduledCancellationController,
} = require('../../controllers/mobile');

const {
  managerCommunityValidator,
  memberCommunityValidator,
  communityFolderManagerValidator,
  communityFolderItemManagerValidator,
  deleteCommentValidator,
  communityGetFolderManagerValidator,
  communityFolderReadAccessValidator,
  communityFolderManagerAndProgramManagerValidator,
} = require('../../validations/community.validation');

const tokenValidator = require('../../../validations/token.validation');
const tokenValidatorWithoutError = require('../../../validations/tokenNoError.validation');
const userValidation = require('../../../validations/user.validation');
const userValidationWithoutError = require('../../../validations/userValidationWithoutError.validation');

const {
  uploadCommunityImage,
} = require('../../middlewares/uploadCommunityImage.middleware');

const {
  uploadCommunityFolderImage,
} = require('../../middlewares/uploadFolderImage.middleware');

const {
  uploadCommunityFolderItem,
} = require('../../middlewares/uploadFolderItemRelatedFiles.middleware');

const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');

// communities
communitiesMobileRouter
  .route('/')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityController.getCommunities
  );

communitiesMobileRouter
  .route('/:communityId/bots')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityController.getCommunityBotDetails
  );

communitiesMobileRouter
  .route('/:communityId/user-joined-chat')
  .patch(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityMembershipsController.toggleUserConfirmedJoiningChat
  );

communitiesMobileRouter
  .route('/:communityId/thumbnail-image')
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    uploadCommunityImage.single('image'),
    communityController.uploadThumbnailImage
  );

communitiesMobileRouter
  .route('/:communityId/banner-image')
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    uploadCommunityImage.single('image'),
    communityController.uploadBannerImage
  );

//manager todos progress
communitiesMobileRouter
  .route('/:communityId/manager-todos-progress')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityManagerTodosController.getManagerTodosProgress
  );

communitiesMobileRouter
  .route('/:communityId/manager-todos-progress/:todoCode')
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityManagerTodosController.updateManagerTodosProgress
  );

// events
communitiesMobileRouter
  .route('/:communityId/events')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityEventsController.getCommunityEvents
  );

communitiesMobileRouter
  .route('/:communityId/event/:eventId')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityEventsController.getEventById
  );

communitiesMobileRouter
  .route('/:communityId/event-admin/:eventId')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    communityEventsController.getEventByIdForAdmin
  );

communitiesMobileRouter
  .route('/event/:eventId/register')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityEventsController.registerForEvent
  );

// memberships
communitiesMobileRouter
  .route('/:communityId/memberships')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityMembershipsController.getCommunityMemberships
  );

// todos
communitiesMobileRouter
  .route('/:communityId/orientation')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityTodosController.getOrientationTodos
  );

communitiesMobileRouter
  .route('/todo/:todoId')
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityTodosController.updateTodoProgress
  );

// Book trainers
communitiesMobileRouter
  .route('/calendly-webhook')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityBookingController.calendlyWebhookController
  );

communitiesMobileRouter
  .route('/:communityId/learner-booking')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityBookingController.getBookingEventForLearner
  );

// library APIs
// videos preview
communitiesMobileRouter
  .route('/:communityId/videos/preview')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityVideosController.getCommunityClassPreview
  );

// resources preview
communitiesMobileRouter
  .route('/:communityId/resources/preview')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityResourcesController.getCommunityResourcesPreview
  );

// videos
communitiesMobileRouter
  .route('/:communityId/videos')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityVideosController.getCommunityVideos
  );

// resources
communitiesMobileRouter
  .route('/:communityId/resources')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityResourcesController.getCommunityResources
  );

// leave a community
communitiesMobileRouter
  .route('/leave-community/:communityId')
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityLeaveController.leaveCommunityController
  );

// revoke cancellation for a community
communitiesMobileRouter
  .route('/revoke-cancellation/:communityId')
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    cancelScheduledCancellationController.cancelScheduledCancellationController
  );

// posts
communitiesMobileRouter
  .route('/:communityId/posts')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityPostsController.getCommunityPosts
  );

// Pinned posts
communitiesMobileRouter
  .route('/:communityId/pinned-posts')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityPostsController.getPinnedCommunityPosts
  );

communitiesMobileRouter
  .route('/:communityId/posts/:postId')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityPostsController.getCommunityPost
  );

// search library
communitiesMobileRouter
  .route('/:communityId/search')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityFolderReadAccessValidator,
    communityFolderController.getFilteredLibraryFromCommunity
  );

// folders
communitiesMobileRouter
  .route('/:communityId/folders')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidatorWithoutError,
    userValidationWithoutError,
    communityFolderController.getFolders
  );
communitiesMobileRouter
  .route('/:communityId/products')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidatorWithoutError,
    userValidationWithoutError,
    communityFolderController.getProducts
  );

communitiesMobileRouter
  .route('/:communityId/folder')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    uploadCommunityFolderImage.single('image'),
    communityFolderController.createOneFolder
  );

communitiesMobileRouter
  .route('/:communityId/folder/:communityFolderId')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityGetFolderManagerValidator,
    communityFolderController.getFolderById
  )
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityFolderManagerValidator,
    uploadCommunityFolderImage.single('image'),
    communityFolderController.updateOneFolder
  )
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityFolderManagerValidator,
    communityFolderController.deleteOneFolder
  );

communitiesMobileRouter
  .route('/:communityId/folder/:communityFolderId/save-and-publish')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityFolderManagerValidator,
    communityFolderController.saveAndPublishChanges
  );

// folder access log
communitiesMobileRouter
  .route('/folder/:communityFolderId/log')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityFolderAccessLogController.createFolderAccessLogs
  );

// folder items
communitiesMobileRouter
  .route('/:communityId/folder/:communityFolderId/folder-item')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityFolderManagerAndProgramManagerValidator,
    uploadCommunityFolderItem.single('file'),
    communityFolderItemController.createOneFolderItem
  );

communitiesMobileRouter
  .route('/:communityId/folder-item/:communityFolderItemId')
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityFolderItemManagerValidator,
    communityFolderItemController.updateOneFolderItem
  )
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityFolderItemManagerValidator,
    communityFolderItemController.deleteOneFolderItem
  );

// announcement reactions
communitiesMobileRouter
  .route('/:communityId/announcements/:announcementId/reaction')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityPostReactionController.reactionsForAnnouncement
  )
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityPostReactionController.addReaction
  )
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityPostReactionController.removeReaction
  );

// mobile notifications
communitiesMobileRouter
  .route('/user-mobile-notifications')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityMobileNotificationsController.getUserMobileNotificationsController
  );

communitiesMobileRouter
  .route('/notifications-seen')
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityMobileNotificationsController.notificationsSeen
  );

communitiesMobileRouter
  .route('/user-notification-permissions')
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    communityMobileNotificationsController.userNotificationPermissions
  );

communitiesMobileRouter
  .route('/:communityId')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityController.getCommunityById
  );

// announcement comments
communitiesMobileRouter
  .route('/:communityId/announcements/:announcementId/comment')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityPostCommentController.commentsForAnnouncement
  )
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityPostCommentController.addComment
  );

communitiesMobileRouter
  .route('/:communityId/announcements/:announcementId/comment/:commentId')
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    deleteCommentValidator,
    communityPostCommentController.removeComment
  );

communitiesMobileRouter
  .route(
    '/:communityId/announcements/:announcementId/comment/:commentId/reply'
  )
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityPostCommentController.repliesForAnnouncementComment
  );

// announcement comment reactions
communitiesMobileRouter
  .route(
    '/:communityId/announcements/:announcementId/comment/:commentId/reaction'
  )
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityPostCommentReactionController.reactionsForAnnouncementComment
  )
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityPostCommentReactionController.addReaction
  )
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    communityPostCommentReactionController.removeReaction
  );

module.exports = communitiesMobileRouter;
