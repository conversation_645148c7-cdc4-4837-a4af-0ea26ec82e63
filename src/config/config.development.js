module.exports = {
  CAMPAIGN_NAS_GIFT_AUG_2024_START_DATE:
    process.env.CAMPAIGN_NAS_GIFT_AUG_2024_START_DATE ||
    '2024-07-30T00:00:00.000Z',
  USA_CUSTOM_BASE_PAYOUT_FEE_START_DATE:
    process.env.USA_CUSTOM_BASE_PAYOUT_FEE_START_DATE ||
    '2025-01-10T00:00:00.000Z',
  FEEDBACK_URL: process.env.FEEDBACK_URL || 'https://feedback.dev-nas.io',
  NOTIFICATION_URL:
    process.env.NOTIFICATION_URL || 'https://notification.dev-nas.io',
  MAIN_WEBSITE_URL:
    process.env.MAIN_WEBSITE_URL || 'https://dev.nasacademy.com',
  NAS_IO_BACKEND_URL:
    process.env.NAS_IO_BACKEND_URL || 'https://web3-api.dev-nas.io',
  NAS_IO_FRONTEND_URL:
    process.env.NAS_IO_FRONTEND_URL || 'https://dev-nas.io',
  FRONTEND_APP_LINK:
    process.env.FRONTEND_APP_LINK || 'https://dev-nas.io/member', // deprecated
  REROUTE_MEMBER_LINK:
    process.env.REROUTE_MEMBER_LINK || 'https://dev-nas.io/reroute-user',
  REROUTE_EMAIL_LINK:
    process.env.REROUTE_EMAIL_LINK || 'https://dev-nas.io/email-reroute',
  URL_SHORTENER_LINK:
    process.env.URL_SHORTENER_LINK || 'https://url-shortener.dev-nas.io',
  NAS_IO_APP_DYNAMIC_LINK:
    process.env.NAS_IO_APP_DYNAMIC_LINK || 'https://nasdev.page.link',
  MAIN_PAYMENT_BACKEND_URL:
    process.env.MAIN_PAYMENT_BACKEND_URL || 'https://main.dev-nas.io',
  METAPHI_SDK_ACCOUNT_ID: process.env.METAPHI_SDK_ACCOUNT_ID || 37,
  LEARN_BACKEND_URL:
    process.env.LEARN_BACKEND_URL || 'https://api.dev-nas.io/',
  TELEGRAM_AUTH: process.env.TELEGRAM_AUTH,
  MOBILE_NOTIFICATION_QUEUE_URL:
    process.env.MOBILE_NOTIFICATION_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/mobile-notificaiton-queue-dev',
  TELEGRAM_URL:
    process.env.TELEGRAM_URL ||
    'https://nas-telegram-social-bot.dev-nas.io/api/v1',
  DISCORD_URL: process.env.DISCORD_URL || 'https://discord-bot.dev-nas.io',
  OAUTH_JWT_SECRET: process.env.OAUTH_JWT_SECRET,
  OAUTH_JWT_REFRESH_SECRET: process.env.OAUTH_JWT_REFRESH_SECRET,
  DISCORD_AUTH: process.env.DISCORD_AUTH,
  DISCORD_MEMBER_ROLE_SQS_QUEUE_URL:
    process.env.DISCORD_MEMBER_ROLE_SQS_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/w3-discord-member-role-assignment-dev.fifo',
  GENERATE_AI_TEMPLATE_SQS_QUEUE_URL:
    process.env.GENERATE_AI_TEMPLATE_SQS_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/generate-ai-template-queue-dev',
  SCRAPE_SOCIAL_MEDIA_INFO_SQS_QUEUE_URL:
    process.env.SCRAPE_SOCIAL_MEDIA_INFO_SQS_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/scrape-social-media-info-queue-dev',
  MAGIC_REACH_SEND_EMAIL_QUEUE_URL:
    process.env.MAGIC_REACH_SEND_EMAIL_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/magic-reach-send-email-queue-dev',
  MAGIC_REACH_EMAIL_EVENT_QUEUE_URL:
    process.env.MAGIC_REACH_EMAIL_EVENT_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/magic-reach-email-event-queue-dev.fifo',
  MAGIC_REACH_SEND_WHATSAPP_QUEUE_URL:
    process.env.MAGIC_REACH_SEND_WHATSAPP_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/magic-reach-send-whatsapp-queue-dev',
  MAGIC_REACH_WHATSAPP_EVENT_QUEUE_URL:
    process.env.MAGIC_REACH_WHATSAPP_EVENT_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/magic-reach-whatsapp-event-queue-dev.fifo',
  WHATSAPP_SERVICE_SQS_QUEUE_URL:
    process.env.WHATSAPP_SERVICE_SQS_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/whatsapp-send-msg-queue-dev.fifo',
  AI_LEAD_PROCESSOR_QUEUE_URL:
    process.env.AI_LEAD_PROCESSOR_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/ai-lead-processor-queue-dev',
  BULK_NOTIFICATION_QUEUE_URL:
    process.env.BULK_NOTIFICATION_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/bulk-notification-orchestrator-queue-dev',
  NOTIFICATION_SERVICE_DB:
    process.env.NOTIFICATION_SERVICE_DB_NAME ||
    'notification_service_development',
  EMAIL_ICS_PATH: process.env.EMAIL_ICS_PATH || 'email_assets/ics/dev',
  KLAVIYO_LIST_ID: process.env.KLAVIYO_LIST_ID || 'S465pQ',
  AUTH_SERVICE_URL:
    process.env.AUTH_SERVICE_URL || 'https://auth.dev-nas.io',
  REACTION_QUEUE_URL:
    process.env.REACTION_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/reactions-dev.fifo',
  CHAT_INVITE_LINK_QUEUE_URL:
    process.env.CHAT_INVITE_LINK_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/create-discord-chat-invite-link',
  COMMENT_QUEUE_URL:
    process.env.COMMENT_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/comments-dev.fifo',
  CALCULATE_MEMBER_COUNT_QUEUE_URL:
    process.env.CALCULATE_MEMBER_COUNT_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/calculate-member-count-dev',
  WHATSAPP_FROM_PHONE_NUMBER_ID:
    process.env.WHATSAPP_FROM_PHONE_NUMBER_ID || '***************',
  WHATSAPP_ACCESS_TOKEN: process.env.WHATSAPP_ACCESS_TOKEN,
  WHATSAPP_BUSINESS_ACCOUNT_ID:
    process.env.WHATSAPP_BUSINESS_ACCOUNT_ID || '***************',
  WHATSAPP_SERVICE_QUEUE_BOT_URL:
    process.env.WHATSAPP_SERVICE_QUEUE_BOT_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/whatsapp-service-queue-dev.fifo',
  AXIOS_KEEP_ALIVE_MAX_SOCKETS:
    process.env.AXIOS_KEEP_ALIVE_MAX_SOCKETS || 128, // default 128 / os.cpus().length if running node across multiple CPUs
  AXIOS_KEEP_ALIVE_MAX_FREE_SOCKETS:
    process.env.AXIOS_KEEP_ALIVE_MAX_FREE_SOCKETS || 128, // default 128 / os.cpus().length if running node across multiple CPUs
  AXIOS_KEEP_ALIVE_TIMEOUT: process.env.AXIOS_KEEP_ALIVE_TIMEOUT || 45000, // default active socket keepalive for 45s
  AXIOS_KEEP_ALIVE_FREE_SOCKET_TIMEOUT:
    process.env.AXIOS_KEEP_ALIVE_FREE_SOCKET_TIMEOUT || 45000, // default free socket keepalive for 45s
  CLOUD_WATCH_NAMESPACE:
    process.env.CLOUD_WATCH_NAMESPACE || 'LearningPortalBEDev',
  CLOUD_WATCH_METRIC_INTERVAL:
    process.env.CLOUD_WATCH_METRIC_INTERVAL || 1800,
  PROMETHEUS_COLLECT_METRIC_INTERVAL:
    process.env.PROMETHEUS_COLLECT_METRIC_INTERVAL || 30,
  ENABLE_CLOUD_WATCH_PUSH: process.env.ENABLE_CLOUD_WATCH_PUSH || 0,
  OPENAI_API_KEY:
    process.env.OPENAI_API_KEY ||
    '***************************************************',
  MAGIC_REACH_FRAUD_PROBABILITY_THRESHOLD:
    process.env.MAGIC_REACH_FRAUD_PROBABILITY_THRESHOLD || 75,
  MAGIC_REACH_BLOCK_ON_FRAUD: process.env.MAGIC_REACH_BLOCK_ON_FRAUD || 1,
  MAGIC_REACH_SEND_COUNT_BLOCK_THRESHOLD:
    process.env.MAGIC_REACH_SEND_COUNT_BLOCK_THRESHOLD || 500,
  COPS_JWT_SECRET: process.env.COPS_JWT_SECRET,
  STRIPE_KEY:
    process.env.STRIPE_KEY || 'sk_test_VaWFUKRl83Z0aQ4zRbMUJXiR00ZkDfivjy',
  XENDIT_SECRET_KEY_PH:
    process.env.XENDIT_SECRET_KEY_PH ||
    'xnd_development_LQURHsvv0T4BD3Sx3ZqFIyifNiEZvmyJOqCp6BcSYc5hFe0fTfSSlzdh3Z7QA',
  FRESHDESK_URL:
    process.env.FRESHDESK_URL || 'https://nasacademy.freshdesk.com',
  FRESHDESK_SECRET: process.env.FRESHDESK_SECRET || '********************',
  INTERCOM_URL: process.env.INTERCOM_URL || 'https://api.intercom.io',
  PAYMENT_MAILER_URL:
    process.env.PAYMENT_MAILER_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/payment-mail-dev',
  GENERATE_INVOICE_URL:
    process.env.GENERATE_INVOICE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/generate-invoice-queue-dev',
  ACTION_EVENT_QUEUE_URL:
    process.env.ACTION_EVENT_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/action-event-queue-dev',
  ACTION_EVENT_QUEUE_ARN:
    process.env.ACTION_EVENT_QUEUE_ARN ||
    'arn:aws:sqs:ap-southeast-1:*********937:action-event-queue-dev',
  FIND_IP_URL: process.env.FIND_IP_URL || 'https://api.findip.net',
  NEXT_JS_SECRET_TOKEN:
    process.env.NEXT_JS_SECRET_TOKEN || 'gzkzJrL801lRbcj7wK6Nyg5ogrmBKmD0',
  SUBSCRIPTION_UPDATER_QUEUE_URL:
    process.env.SUBSCRIPTION_UPDATER_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/subscription-updater-queue-dev',
  SUBSCRIPTION_UPDATER_QUEUE_ARN:
    process.env.SUBSCRIPTION_UPDATER_QUEUE_ARN ||
    'arn:aws:sqs:ap-southeast-1:*********937:subscription-updater-queue-dev',
  REVENUE_TRANSACTION_QUEUE_URL:
    process.env.REVENUE_TRANSACTION_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/revenue-transaction-queue-dev',
  WALLET_ERROR_ALERT_LARK_WEBHOOK:
    process.env.WALLET_ERROR_ALERT_LARK_WEBHOOK ||
    'https://open.larksuite.com/open-apis/bot/v2/hook/4926b360-eb85-4b97-a22c-4b9f031a3926',
  PAYOUT_FAILED_ALERT_LARK_WEBHOOK:
    process.env.PAYOUT_FAILED_ALERT_LARK_WEBHOOK ||
    'https://open.larksuite.com/open-apis/bot/v2/hook/4926b360-eb85-4b97-a22c-4b9f031a3926',
  CHALLENGE_CHECKPOINT_NOTIFICATION_QUEUE_URL:
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/checkpoint-reminder-dev',
  S3_RECIPIENT_BASE_PATH:
    process.env.S3_RECIPIENT_BASE_PATH ||
    '/magic-reach-archived-recipients/dev',
  PAYPAL_DASHBOARD_DOMAIN:
    process.env.PAYPAL_DASHBOARD_DOMAIN ||
    'https://www.sandbox.paypal.com',
  CHALLENGE_LEADERBOARD_POINTS_QUEUE_URL:
    process.env.CHALLENGE_LEADERBOARD_POINTS_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/challenge-leaderboard-points-dev.fifo',
  OPEN_AI_FRAUD_CHECK_QUEUE_URL:
    process.env.OPEN_AI_FRAUD_CHECK_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/open-ai-fraud-check-queue-dev',
  ERROR_ALERT_LARK_WEBHOOK:
    process.env.ERROR_ALERT_LARK_WEBHOOK ||
    'https://open.larksuite.com/open-apis/bot/v2/hook/4926b360-eb85-4b97-a22c-4b9f031a3926',
  SUPPORTING_TICKET_SENDER_QUEUE_URL:
    process.env.SUPPORTING_TICKET_SENDER_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/supporting-ticket-sender-queue-dev',
  CHAT_NOTIFICATION_QUEUE_ARN:
    process.env.CHAT_NOTIFICATION_QUEUE_ARN ||
    'arn:aws:sqs:ap-southeast-1:*********937:chat-notification-queue-dev',
  NASIO_PRO_LARK_WEBHOOK:
    process.env.NASIO_PRO_LARK_WEBHOOK ||
    'https://open.larksuite.com/open-apis/bot/v2/hook/00a0f610-c23e-46e0-a63d-d82b7c229160',
  NASIO_PLATINUM_LARK_WEBHOOK:
    process.env.NASIO_PLATINUM_LARK_WEBHOOK ||
    'https://open.larksuite.com/open-apis/bot/v2/hook/00a0f610-c23e-46e0-a63d-d82b7c229160',
  NASIO_PRO_CANCELLED_LARK_WEBHOOK:
    process.env.NASIO_PRO_CANCELLED_LARK_WEBHOOK ||
    'https://open.larksuite.com/open-apis/bot/v2/hook/00a0f610-c23e-46e0-a63d-d82b7c229160',
  NASIO_PLATINUM_CANCELLED_LARK_WEBHOOK:
    process.env.NASIO_PLATINUM_CANCELLED_LARK_WEBHOOK ||
    'https://open.larksuite.com/open-apis/bot/v2/hook/00a0f610-c23e-46e0-a63d-d82b7c229160',
  NASIO_SUBSCRIPTION_LARK_ALERT_QUEUE_URL:
    process.env.NASIO_SUBSCRIPTION_LARK_ALERT_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/nas-subscription-lark-alert-queue-dev',
  CALENDAR_ERROR_LARK_WEBHOOK:
    process.env.CALENDAR_ERROR_LARK_WEBHOOK ||
    'https://open.larksuite.com/open-apis/bot/v2/hook/00a0f610-c23e-46e0-a63d-d82b7c229160',
  CACHE_REVALIDATE_QUEUE_ARN:
    process.env.CACHE_REVALIDATE_QUEUE_ARN ||
    'arn:aws:sqs:ap-southeast-1:*********937:cache-revalidate-queue-dev',
  ABANDONED_CHECKOUT_UPDATE_QUEUE_ARN:
    process.env.ABANDONED_CHECKOUT_UPDATE_QUEUE_ARN ||
    'arn:aws:sqs:ap-southeast-1:*********937:abandoned-checkout-update-queue-dev',
  GENERAL_PURPOSE_TASK_QUEUE_ARN:
    process.env.GENERAL_PURPOSE_TASK_QUEUE_ARN ||
    'arn:aws:sqs:ap-southeast-1:*********937:general-purpose-task-queue-dev',
  GENERAL_PURPOSE_TASK_QUEUE_URL:
    process.env.GENERAL_PURPOSE_TASK_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/general-purpose-task-queue-dev',
  AI_IMAGE_GENERATION_QUEUE_URL:
    process.env.AI_IMAGE_GENERATION_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/ai-image-generator-queue-dev',
  NODE_ENV: process.env.NODE_ENV || 'development',
  CHECKOUT_TOKEN_SESSION_TYPE:
    process.env.CHECKOUT_TOKEN_SESSION_TYPE || '21cd73915ae3',
  EMAIL_TOKEN_SESSION_TYPE:
    process.env.EMAIL_TOKEN_SESSION_TYPE || 'a87dbf2b8dab',
  authCookieDomain: process.env.AUTH_COOKIE_DOMAIN || 'dev-nas.io',
  unsplashApiKey:
    process.env.UNSPLASH_API_KEY ||
    '*******************************************',
  META_APP_ACCESS_TOKEN:
    process.env.META_APP_ACCESS_TOKEN ||
    'EAAJTIqWDI3UBOZBKKVzSfbUlUOyUvEL0HaCR060rNPZALz1BZBEF7JFaqlyI6ZA7i3y4spHVhoTWXpuTCUw66ZBhfboWFQujXZBtC92gDb3kU3apo9pbs0Yse2Vq1yNxV4Su8o59z27og4rzfl3Qe72JeZBAtytbNW8c1oUsoDZAKYyDwGNB3SZCJKQwxmXBOJwZDZD',
  META_AD_WEBHOOK_VERIFY_TOKEN:
    process.env.META_AD_WEBHOOK_VERIFY_TOKEN || '1',
  META_APP_SECRET_KEY:
    process.env.META_APP_SECRET_KEY || '5f1deee1f7c5011bad8aad43dd6803f9',
  META_APP_ID: process.env.META_APP_ID || '654358224249717',
  META_BUSINESS_ID: process.env.BUSINESS_ID || '1131940551054885',
  META_SYSTEM_USER_ID:
    process.env.META_SYSTEM_USER_ID || '688489697491702',
  CHANGE_LOG_NOTIFICATION_QUEUE_URL:
    process.env.CHANGE_LOG_NOTIFICATION_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/*********937/change-log-notification-queue-dev',
};
