const httpStatus = require('http-status');
const yup = require('yup');
const Tasks = require('../../../models/getStartedSuperCharge/tasks.model');
const Localization = require('../../../models/platform/localization.model');
const { getCommunityService } = require('../../../services/community');
const logger = require('../../../services/logger.service');
const superChargeService = require('../../../services/getStartedSuperCharge.service');
const {
  CustomWelcomeMessageConfigService,
} = require('../../../services/config');
const { getLanguagePreference } = require('../../../utils/headers.util');

const getCommunityGetStartedProgress = async (req, res, next) => {
  try {
    const { communityId } = req.params;
    logger.info(
      `getCommunityGetStartedProgress for communityId: ${communityId} started`
    );

    if (!communityId) {
      logger.info('communityId not specified in params', communityId);
      const error = new Error('communityId not specified in params');
      error.status = httpStatus.BAD_REQUEST; // 400
      throw error;
    }

    const languagePreference = getLanguagePreference(req);

    // get community Information from communityId
    const community = await getCommunityService.getCommunityById({
      communityId,
    });

    const communityCategory = community?.communityCategory;

    // we will make use of 'ALL_COMMUNITIES_TASK' for a common task for non categorized communities
    const types = [];
    let filter = [];
    // OTHERS = ALL_COMMUNITIES_TASK or can change this later and add a new type for this in the DB
    if (communityCategory?.id && communityCategory?.id !== 'OTHERS') {
      types.push(communityCategory.id);
    } else {
      types.push('ALL_COMMUNITIES_TASK');
    }
    filter = [
      {
        $match: {
          $or: [
            {
              type: {
                $in: types,
              },
            },
            {
              type: {
                $exists: false,
              },
            },
          ],
        },
      },
      {
        $sort:
          /**
           * Provide any number of field/order pairs.
           */
          {
            index: 1,
          },
      },
    ];
    // finding all the tasks for all the communities
    let allTasks = await Tasks.aggregate(filter);
    logger.info('All tasks for the community', allTasks);

    allTasks = await Promise.all(
      allTasks.map(async (task) => {
        const labelLocale = await Localization.findOne({
          key: `${task.id}-title`,
        }).lean();

        let label = task.label;
        if (labelLocale) {
          label = labelLocale[languagePreference] || labelLocale['en'];
        }

        const subTasks = task.subTasks;
        await Promise.all(
          task.subTasks.map(async (subTask, index) => {
            if (!subTask.id) {
              return;
            }
            const localeKeyMap = new Map();
            const locale = await Localization.find({
              key: { $regex: subTask.id },
            }).lean();

            if (locale.length > 0) {
              locale.forEach((lang) => {
                const value = lang[languagePreference] || lang['en'];
                localeKeyMap.set(lang.key, value);
              });
            }
            subTasks[index].title =
              localeKeyMap.get(`${subTask.id}-title`) || subTask.title;
            subTasks[index].description =
              localeKeyMap.get(`${subTask.id}-description`) ||
              subTask.description;

            if (subTask?.resources?.length > 0) {
              subTask.resources.forEach((resource, resourceIndex) => {
                subTasks[index].resources[resourceIndex].title =
                  localeKeyMap.get(
                    `${subTask.id}-resources-${resourceIndex}-title`
                  ) || resource.title;
              });
            }

            if (subTask?.unlocks?.length > 0) {
              subTask.unlocks.forEach((unlock, unlockIndex) => {
                subTasks[index].unlocks[unlockIndex].title =
                  localeKeyMap.get(
                    `${subTask.id}-unlocks-${unlockIndex}-title`
                  ) || unlock.title;
                if (unlock.tooltip) {
                  subTasks[index].unlocks[unlockIndex].tooltip.title =
                    localeKeyMap.get(
                      `${subTask.id}-unlocks-${unlockIndex}-tooltip-title`
                    ) || unlock.tooltip?.title;
                  subTasks[index].unlocks[unlockIndex].tooltip.desc =
                    localeKeyMap.get(
                      `${subTask.id}-unlocks-${unlockIndex}-tooltip-desc`
                    ) || unlock.tooltip?.desc;
                }
              });
            }
          })
        );

        const resources = task.resources || [];

        if (resources?.length > 0) {
          await Promise.all(
            task?.resources?.map(async (resource, resourceIndex) => {
              const localeKeyMap = new Map();
              const locale = await Localization.find({
                key: { $regex: task.id },
              }).lean();

              if (locale.length > 0) {
                locale.forEach((lang) => {
                  const value = lang[languagePreference] || lang['en'];
                  localeKeyMap.set(lang.key, value);
                });
              }
              resources[resourceIndex].title =
                localeKeyMap.get(
                  `${task.id}-resources-${resourceIndex}-title`
                ) || resource.title;
            })
          );
        }

        const unlocks = task.unlocks || [];

        if (unlocks?.length > 0) {
          await Promise.all(
            task?.unlocks?.map(async (unlock, unlockIndex) => {
              const localeKeyMap = new Map();
              const locale = await Localization.find({
                key: { $regex: task.id },
              }).lean();

              if (locale.length > 0) {
                locale.forEach((lang) => {
                  const value = lang[languagePreference] || lang['en'];
                  localeKeyMap.set(lang.key, value);
                });
              }
              unlocks[unlockIndex].title =
                localeKeyMap.get(
                  `${task.id}-unlocks-${unlockIndex}-title`
                ) || unlock.title;
              if (unlock.tooltip) {
                unlocks[unlockIndex].tooltip.title =
                  localeKeyMap.get(
                    `${task.id}-unlocks-${unlockIndex}-tooltip-title`
                  ) || unlock.tooltip?.title;
                unlocks[unlockIndex].tooltip.desc =
                  localeKeyMap.get(
                    `${task.id}-unlocks-${unlockIndex}-tooltip-desc`
                  ) || unlock.tooltip?.desc;
              }
            })
          );
        }
        return {
          ...task,
          resources,
          label,
          subTasks,
          unlocks,
        };
      })
    );

    const isCommunityCodeBlacklistedForCustomMessage =
      await CustomWelcomeMessageConfigService.isCommunityCodeBlacklisted(
        community.code
      );
    if (isCommunityCodeBlacklistedForCustomMessage) {
      allTasks.forEach((task, index) => {
        if (task.taskId === 'LAUNCH_TASK') {
          const newSubTasks = task.subTasks.filter(
            (subTask) => subTask.taskUniqueId !== 'EDIT_WELCOME_MESSAGE'
          );
          allTasks[index].subTasks = newSubTasks;
        }
      });
    }

    // get the task information for the community and update that community information
    const taskInformationForCommunity =
      await superChargeService.getSuperChargeTasksInformation(
        allTasks,
        community,
        true
      );

    // destructuring the taskInformationForCommunity object to get the tasksProgress and percentageOfTasksCompleted
    const { tasksProgress, percentageOfTasksCompleted } =
      taskInformationForCommunity;

    logger.info(
      `task progress for the community ${communityId}`,
      tasksProgress
    );

    logger.info(
      `percentage of tasks completed for the community ${communityId}`,
      percentageOfTasksCompleted
    );

    logger.info(
      'Task Information for Community',
      taskInformationForCommunity
    );

    return res.status(httpStatus.OK).json({
      message: 'success',
      data: {
        tasksProgress,
        percentageOfTasksCompleted,
        tasks: allTasks,
      },
    });
    // custom logic for each and every task
  } catch (error) {
    logger.error(
      'getCommunityGetStartedProgress failed due to',
      error,
      error.stack
    );
    return res
      .status(error.status || httpStatus.INTERNAL_SERVER_ERROR)
      .json({
        message:
          error.message ||
          'Error in getting Progress for Get Started Page',
        errorMessage:
          error.errorMessage ||
          'Error in getting Progress for Get Started Page',
        errorCode: error.errorCode || 500,
        error,
        data: null,
      });
  }
};

const getCommunityProgressPercentage = async (req, res) => {
  try {
    const { communityId } = req.params;
    const { force: shouldGoThroughAllTasks } = req.query;

    logger.info(
      `getCommunityProgressPercentage for communityId: ${communityId} started`
    );

    const community = await getCommunityService.getCommunityById({
      communityId,
    });

    let percentage = community?.taskMetaData?.tasksCompletionPercentage;
    if (shouldGoThroughAllTasks || !percentage) {
      const allTasks = await Tasks.find({}).lean();
      logger.info('All tasks for the community', JSON.stringify(allTasks));
      const taskInformationForCommunity =
        await superChargeService.getSuperChargeTasksInformation(
          allTasks,
          community,
          false
        );
      percentage = taskInformationForCommunity?.percentageOfTasksCompleted;
    }

    return res.status(httpStatus.OK).json({
      message: 'success',
      data: {
        percentage,
      },
    });
  } catch (error) {
    logger.error(
      `getCommunityProgressPercentage failed due to ${error} and error stack `,
      error.stack
    );

    return res
      .status(error.status || httpStatus.INTERNAL_SERVER_ERROR)
      .json({
        message:
          error.message ||
          'Error in getting Progress for Get Started Page',
        errorMessage:
          error.errorMessage ||
          'Error in getting Progress for Get Started Page',
        errorCode: error.errorCode || 500,
        error,
        data: null,
      });
  }
};

/*
{
  "taskId": String,
    "subTaskId": String,
    "isCompleted": boolean
}
*/
const taskUpdateSchema = yup.object().shape({
  taskId: yup.string().required(),
  subTaskId: yup.string(),
  isCompleted: yup.boolean().required(),
});
const updateCommunityGetStartedProgress = async (req, res) => {
  try {
    const { communityId } = req.params;

    const { taskId, subTaskId, isCompleted } = req.body;
    logger.info(
      `updateCommunityGetStartedProgress for communityId: ${communityId} started`
    );

    if (!communityId) {
      logger.info('communityId not specified in params', communityId);
      const error = new Error('communityId not specified in params');
      error.status = httpStatus.BAD_REQUEST;
      throw error;
    }

    const isValid = await taskUpdateSchema.isValid({
      taskId,
      subTaskId,
      isCompleted,
    });

    if (!isValid) {
      logger.info('Invalid Request Body', req.body);
      const error = new Error('Invalid Request Body');
      error.status = httpStatus.BAD_REQUEST;
      throw error;
    }

    const updateCommunityData =
      await superChargeService.updateCommunityProgress({
        communityId,
        taskId,
        subTaskId,
        isCompleted,
      });

    return res.status(httpStatus.OK).json({
      message: 'success',
      data: updateCommunityData,
    });
  } catch (error) {
    res.status(error.status || httpStatus.INTERNAL_SERVER_ERROR).json({
      message:
        error.message || 'Error in updating Progress for Get Started Page',
      errorMessage:
        error.errorMessage ||
        'Error in updating Progress for Get Started Page',
      errorCode: error.errorCode || 500,
      error,
      data: null,
    });
  }
};
module.exports = {
  getCommunityGetStartedProgress,
  getCommunityProgressPercentage,
  updateCommunityGetStartedProgress,
};
