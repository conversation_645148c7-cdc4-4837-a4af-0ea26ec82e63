const httpStatus = require('http-status');
const ObjectId = require('mongoose').Types.ObjectId;
const schema = require('./schema');
const logger = require('../../../services/logger.service');
const communityEventsService = require('../../../communitiesAPI/services/web/communityEvents.service');
const service = require('../../../services/event');
const manageEventService = require('../../../services/event/manageEvent.service');

const {
  COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES,
} = require('../../../communitiesAPI/constants');
const { AlreadyProcessedError } = require('../../../utils/error.util');

exports.getCommunityEventAndAttendees = async (req, res, next) => {
  try {
    const { communityId, id: eventId } =
      schema.getEventAttendeesPathSchema.cast(req.params);

    const {
      search,
      pageSize,
      pageNo,
      sortBy,
      sortOrder,
      status,
      isCheckedIn,
      registrationDateFrom,
      registrationDateTo,
    } = schema.getEventAttendeesSchema.cast(req.query);

    const result = await service.retrieveEventAndAttendees({
      search,
      pageSize,
      pageNo,
      sortBy,
      sortOrder,
      status: status.split(','),
      communityId,
      eventId,
      isCheckedIn: isCheckedIn?.split(','),
      registrationDateFrom,
      registrationDateTo,
    });

    res.json({ data: result });
  } catch (err) {
    logger.error(
      'getCommunityEventAndAttendees failed due to',
      err,
      err.stack
    );
    return next(err);
  }
};

exports.createEventAttendee = async (req, res, next) => {
  try {
    const { id: eventObjectId } = schema.getEventAttendeesPathSchema.cast(
      req.params
    );

    const { learnerObjectId, addonTransactionObjectId, quantity } =
      schema.postEventAttendeesSchema.cast(req.body);

    await communityEventsService.autoRegisterEvent({
      learnerObjectId,
      eventObjectId,
      addonTransactionObjectId,
      quantity,
    });

    res.json({ success: true });
  } catch (err) {
    logger.error(
      `createEventAttendee failed due to ${err.message}, ${err.stack}`
    );

    if (err instanceof AlreadyProcessedError) {
      logger.info('createEventAttendee: bypass already processed error');
      return res.json({ success: true });
    }

    return next(err);
  }
};

exports.addMultipleAttendeesByCM = async (req) => {
  const { id: eventObjectId, communityId: communityObjectId } =
    schema.getEventAttendeesPathSchema.cast(req.params);

  const { attendees: newAttendeesData } =
    schema.postAddMultipleEventAttendeesByCMSchema.cast(req.body);

  const result = await communityEventsService.addMultipleAttendeesByCM({
    communityObjectId,
    eventObjectId,
    newAttendeesData,
  });

  return result;
};

exports.createCommunityEvent = async (req, res, next) => {
  const payload = await schema.createEventSchema.cast(req.body);
  payload['communityObjectId'] = req.params.communityId;
  payload['communities'] = req.params.communityId
    ? [new ObjectId(req.params.communityId)]
    : [];
  payload['createdByLearnerObjectId'] = req.user.learner._id;
  payload['createdBy'] = req.user.email;

  try {
    const event = await communityEventsService.createOneEvent(payload);
    res.status(httpStatus.CREATED).json({ data: event });
  } catch (err) {
    logger.error('createCommunityEvent failed due to', err);
    return next(err);
  }
};

exports.updateCommunityEvent = async (req, res, next) => {
  const payload = schema.updateEventSchema.cast(req.body);
  payload['communityObjectId'] = req.params.communityId;
  payload['communities'] = req.params.communityId
    ? [new ObjectId(req.params.communityId)]
    : [];
  payload['createdByLearnerObjectId'] = req.user.learner._id;
  payload['createdBy'] = req.user.email;
  const { timezoneId = 'UTC' } = req.query;
  try {
    const event = await communityEventsService.updateOneEvent(
      req.params.id,
      payload,
      timezoneId
    );
    res.status(httpStatus.OK).json({ data: event });
  } catch (err) {
    logger.error('updateCommunityEvent failed due to', err);
    return next(err);
  }
};

exports.updateEventAttendeeStatus = async (req, res, next) => {
  const { id: eventId, attendeeId } =
    schema.putEventAttendeesPathSchema.cast(req.params);

  const learnerObjectId = req.user.learner._id;
  const email = req.user.email;
  const isManager = req.user.isCommunityManager;

  try {
    await service.updateEventAttendeeStatus({
      eventId,
      attendeeId,
      learnerObjectId,
      email,
      isManager,
      status: COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.NOT_GOING,
    });

    res.json({ success: true });
  } catch (err) {
    logger.error('updateEventAttendeeStatus failed due to', err);
    return next(err);
  }
};

exports.updateEventAttendeeStatusFromCops = async (req, res, next) => {
  const { eventId, attendeeId, status, operator } =
    schema.updateEventAttendeesStatusSchema.cast(req.body);

  try {
    await service.updateEventAttendeeStatus({
      eventId,
      attendeeId,
      email: operator,
      status,
      isFromCops: true,
    });

    res.json({ success: true });
  } catch (err) {
    logger.error('updateEventAttendeeStatusFromCops failed due to', err);
    return next(err);
  }
};

exports.getExportEventAttendees = async (req, res, next) => {
  try {
    const { communityId, id: eventId } =
      schema.getEventAttendeesPathSchema.cast(req.params);

    const {
      search,
      sortBy,
      sortOrder,
      status,
      isCheckedIn,
      registrationDateFrom,
      registrationDateTo,
    } = schema.getExportEventAttendeesSchema.cast(req.query);

    let filename = await service.retrieveCsvFilename(communityId, eventId);
    filename = encodeURIComponent(req.query?.fileName ?? filename);
    res.setHeader(
      'Content-Disposition',
      `attachment; filename=${filename}`
    );
    res.setHeader('Content-Type', 'text/csv');

    const pageSize = 100;

    const eventAttendeesCsvStream =
      await service.generateEventAttendeesCsvStream({
        search,
        pageSize,
        sortBy,
        sortOrder,
        status: status.split(','),
        communityId,
        eventId,
        isCheckedIn: isCheckedIn?.split(','),
        registrationDateFrom,
        registrationDateTo,
      });

    eventAttendeesCsvStream.pipe(res);
    eventAttendeesCsvStream.on('end', () => {
      res.end();
    });
  } catch (err) {
    logger.error('exportEventAttendees failed due to', err);
    return next(err);
  }
};

exports.checkInTicketReferences = async (req, res, next) => {
  const { attendeeId } = schema.getEventAttendeePathSchema.cast(
    req.params
  );

  const {
    ticketReferences = [],
    checkInAllTickets,
    checkIn,
  } = schema.checkInTicketReferencesSchema.cast(req.body);

  const learnerObjectId = req.user.learner._id;

  try {
    const result = await service.checkInTicketReferences({
      attendeeId,
      ticketReferences,
      checkIn,
      updatedByLearnerObjectId: learnerObjectId,
      checkInAllTickets,
    });

    return res.json({ data: result });
  } catch (err) {
    logger.error('checkAttendees failed due to', err);
    return next(err);
  }
};

exports.scanQrToCheckIn = async (req) => {
  const { id: eventObjectId, communityId: communityObjectId } =
    schema.scanQrCheckInPathSchema.cast(req.params);

  const { scannedToken } = schema.scanQrCheckInPayloadSchema.cast(
    req.body
  );

  const learnerObjectId = req.user.learner._id;

  const updatedEventAttendee = await service.scanQrToCheckinAttendee({
    eventObjectId,
    communityObjectId,
    scannedToken,
    updatedByLearnerObjectId: learnerObjectId,
  });

  return updatedEventAttendee;
};

exports.approveOrRejectEventAttendee = async (req, res, next) => {
  const { id: eventId, attendeeId } =
    schema.getEventAttendeePathSchema.cast(req.params);

  const { status, sendApprovalEmail } =
    schema.approveOrRejectEventAttendeeSchema.cast(req.body);

  const learnerObjectId = req.user.learner._id;
  const email = req.user.email;
  const isManager = req.user.isCommunityManager;

  try {
    const result = await service.approveOrRejectEventAttendee({
      eventId,
      attendeeId,
      status,
      learnerObjectId,
      email,
      isManager,
      sendApprovalEmail,
    });

    return res.json({ data: result });
  } catch (err) {
    logger.error('approveOrRejectEventAttendee failed due to', err);
    return next(err);
  }
};

exports.getSingleEventAttendee = async (req) => {
  const { id: eventId, attendeeId } =
    schema.getEventAttendeePathSchema.cast(req.params);

  const learnerObjectId = req.user.learner._id;

  const eventAttendeeData = await service.getSingleEventAttendee({
    eventId,
    attendeeId,
    requestingLearnerObjectId: learnerObjectId,
  });

  return eventAttendeeData;
};

// Manage Event service
exports.createCommunityEventDraft = async (req) => {
  const { communityId } = schema.communityIdPathSchema.cast(req.params);
  const payload = await schema.createEventSchema.cast(req.body);
  payload['createdByLearnerObjectId'] = req.user.learner._id;
  payload['createdBy'] = req.user.email;

  const event = await manageEventService.createCommunityEventDraft(
    communityId,
    payload
  );
  return event;
};

exports.publishCommunityEvent = async (req) => {
  const { communityId, eventId } = schema.singleEventPathSchema.cast(
    req.params
  );
  return manageEventService.publishCommunityEvent(communityId, eventId);
};

exports.unpublishCommunityEvent = async (req) => {
  const { communityId, eventId } = schema.singleEventPathSchema.cast(
    req.params
  );
  await manageEventService.unpublishCommunityEvent(communityId, eventId);
};

exports.createEventDuplicates = async (req) => {
  const { id: eventObjectId, communityId: communityObjectId } =
    schema.eventPathSchema.cast(req.params);

  const { duplicationCriteria } = schema.eventDuplicationSchema.cast(
    req.body
  );

  const result = await manageEventService.createEventDuplicates({
    eventObjectId,
    communityObjectId,
    duplicationCriteria,
  });

  return result;
};

exports.sendRsvpEmailToAttendee = async (req) => {
  const { attendeeId } = schema.getEventAttendeePathSchema.cast(
    req.params
  );

  await service.sendRsvpEmailToAttendee({
    attendeeId,
  });

  return { success: true };
};
