const yup = require('yup');
const { DateTime } = require('luxon');
const { sortParams, EVENT_TYPES } = require('../../../constants/common');
const {
  COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES,
} = require('../../../communitiesAPI/constants');
const {
  coverMediaItemsSchema,
} = require('../../../validations/coverMediaItems.validation');

const {
  FREQUENCY,
  END_CONDITION,
  MONTH_RULE_TYPES,
} = require('./constants/eventDuplicationConstants');

exports.communityIdPathSchema = yup.object().shape({
  communityId: yup.string().required(),
});

exports.singleEventPathSchema = yup.object().shape({
  communityId: yup.string().required(),
  eventId: yup.string().required(),
});

// validate communityId and id from api path for events
exports.eventPathSchema = yup.object().shape({
  communityId: yup.string().required(),
  id: yup.string().required(),
});

exports.getEventAttendeesPathSchema = yup.object().shape({
  communityId: yup.string().required(),
  id: yup.string().required(),
});

exports.putEventAttendeesPathSchema = yup.object().shape({
  communityId: yup.string().required(),
  id: yup.string().required(),
  attendeeId: yup.string().required(),
});

exports.updateEventAttendeesStatusSchema = yup.object().shape({
  eventId: yup.string().required(),
  attendeeId: yup.string().required(),
  status: yup.string().required(),
  operator: yup.string().required(),
});

exports.postEventAttendeesSchema = yup.object().shape({
  learnerObjectId: yup.string().required(),
  addonTransactionObjectId: yup.string().notRequired(),
  quantity: yup.number().notRequired(),
});

exports.getEventAttendeesSchema = yup.object().shape({
  search: yup.string().uppercase().notRequired().trim(),
  pageSize: yup.number().default(100).required().integer(),
  pageNo: yup.number().default(1).required().integer(),
  sortBy: yup.string().default(sortParams.CREATED_AT).notRequired().trim(),
  sortOrder: yup.number().default(-1).notRequired(),
  status: yup.string().uppercase().notRequired().trim().default('ALL'),
  isCheckedIn: yup.string().notRequired(),
  registrationDateFrom: yup.string().notRequired(),
  registrationDateTo: yup.string().notRequired(),
});

exports.getExportEventAttendeesSchema = yup.object().shape({
  search: yup.string().uppercase().notRequired().trim(),
  sortBy: yup.string().default(sortParams.CREATED_AT).notRequired().trim(),
  sortOrder: yup.number().default(-1).notRequired(),
  status: yup.string().uppercase().notRequired().trim().default('ALL'),
  isCheckedIn: yup.string().notRequired(),
  registrationDateFrom: yup.date().notRequired(),
  registrationDateTo: yup.date().notRequired(),
});

exports.createEventSchema = yup.object().shape({
  templateLibraryId: yup.string().notRequired(),
  title: yup.string().required(),
  description: yup
    .string()
    .max(5000, 'Description cannot exceed 5000 characters'),
  descriptionContent: yup.object(),
  startTime: yup.date().required(),
  endTime: yup.date().required(),
  liveLink: yup.string().url(),
  recordingLink: yup.string().url(),
  isActive: yup.boolean().default(true).required(),
  type: yup
    .string()
    .oneOf([EVENT_TYPES.LIVE, EVENT_TYPES.INPERSON])
    .required(),
  communities: yup.array(),
  host: yup.object(),
  bannerImg: yup.string().notRequired(), // legacy
  coverMediaItems: coverMediaItemsSchema,
  slug: yup.string().notRequired(),
  discountsToAdd: yup.array().default([]),
  discountsToRemove: yup.array().default([]),
  newDiscountsToApply: yup.array().default([]),
  applicationConfigDataFields: yup.array().default([]),
  hideLocation: yup.bool().default(true),
  requiresApproval: yup.bool().default(false),
  inPersonLocationMetadata: yup
    .object()
    .shape({
      name: yup.string().required(),
      formatted_address: yup.string(),
      mask_address: yup.string(),
      location: yup.object().shape({
        lat: yup.number(),
        lng: yup.number(),
      }),
      place_id: yup.string(),
      url: yup.string().url(),
    })
    .default(undefined) // Ensures validation runs only when field is provided
    .notRequired(),
});

exports.updateEventSchema = yup.object().shape({
  title: yup.string(),
  description: yup
    .string()
    .max(5000, 'Description cannot exceed 5000 characters'),
  descriptionContent: yup.object(),
  startTime: yup.date(),
  endTime: yup.date(),
  liveLink: yup.string().url(),
  recordingLink: yup.string().url(),
  isActive: yup.boolean(),
  type: yup.string().oneOf([EVENT_TYPES.LIVE, EVENT_TYPES.INPERSON]),
  communities: yup.array(),
  host: yup.object(),
  bannerImg: yup.string().notRequired(), // legacy
  coverMediaItems: coverMediaItemsSchema,
  slug: yup.string().notRequired(),
  discountsToAdd: yup.array().default([]),
  discountsToRemove: yup.array().default([]),
  newDiscountsToApply: yup.array().default([]),
  applicationConfigDataFields: yup.array().notRequired(),
  requiresApproval: yup.bool().notRequired(),
  hideAttendeesCount: yup.bool().notRequired(),
  inPersonLocationMetadata: yup
    .object()
    .shape({
      name: yup.string().required(),
      formatted_address: yup.string(),
      mask_address: yup.string(),
      location: yup.object().shape({
        lat: yup.number(),
        lng: yup.number(),
      }),
      place_id: yup.string(),
      url: yup.string().url(),
    })
    .default(undefined) // Ensures validation runs only when field is provided
    .notRequired(),
});

exports.getEventAttendeePathSchema = yup.object().shape({
  communityId: yup.string().required(),
  id: yup.string().required(),
  attendeeId: yup.string().required(),
});

exports.checkInTicketReferencesSchema = yup.object().shape({
  ticketReferences: yup.array().of(yup.string()).notRequired(),
  checkInAllTickets: yup.boolean().notRequired(),
  checkIn: yup.boolean().required(),
});

exports.scanQrCheckInPathSchema = yup.object().shape({
  communityId: yup.string().required(),
  id: yup.string().required(), // eventId
});

exports.scanQrCheckInPayloadSchema = yup.object().shape({
  scannedToken: yup.string().required(),
});

exports.approveOrRejectEventAttendeeSchema = yup.object().shape({
  status: yup
    .string()
    .oneOf([
      COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING,
      COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.REJECTED,
    ])
    .required(),
  sendApprovalEmail: yup.boolean().notRequired(),
});

exports.postAddMultipleEventAttendeesByCMSchema = yup.object().shape({
  attendees: yup.array().of(
    yup.object().shape({
      email: yup.string().required().trim(),
      learnerObjectId: yup.string().required().trim(),
      subscriptionObjectId: yup.string().notRequired().trim(),
    })
  ),
});

exports.eventDuplicationSchema = yup.object().shape({
  duplicationCriteria: yup
    .object()
    .shape({
      minStartDate: yup
        .date()
        .required()
        .test(
          'is-future',
          'Start date must be in the future',
          (value) => DateTime.fromJSDate(value) > DateTime.now()
        ),

      isCustomFrequency: yup.boolean().required(),

      frequency: yup
        .string()
        .required()
        .oneOf([FREQUENCY.DAILY, FREQUENCY.WEEKLY, FREQUENCY.MONTHLY]),

      frequencyInterval: yup.number().positive().integer().default(1),

      endCondition: yup
        .string()
        .required()
        .oneOf([END_CONDITION.OCCURRENCE, END_CONDITION.SPECIFIC_DATE]),

      maxOccurrences: yup.number().when('endCondition', {
        is: END_CONDITION.OCCURRENCE,
        then: yup.number().required().positive().integer().max(30),
        otherwise: yup.number().nullable(),
      }),

      endDate: yup.date().when('endCondition', {
        is: END_CONDITION.SPECIFIC_DATE,
        then: yup
          .date()
          .required()
          .test(
            'is-future',
            'End date must be in the future',
            (value) => DateTime.fromJSDate(value) > DateTime.now()
          )
          .test(
            'after-start-date',
            'End date must be after start date',
            function (value) {
              return (
                DateTime.fromJSDate(value) >
                DateTime.fromJSDate(this.parent.minStartDate)
              );
            }
          ),
        otherwise: yup.date().nullable(),
      }),

      daysOfWeek: yup.array().when(['isCustomFrequency', 'frequency'], {
        is: (isCustomFrequency, frequency) =>
          isCustomFrequency && frequency === FREQUENCY.WEEKLY,
        then: yup
          .array()
          .of(yup.number().min(1).max(7).integer())
          .required()
          .min(1),
        otherwise: yup.array().nullable(),
      }),

      customMonthRules: yup
        .object()
        .when(['isCustomFrequency', 'frequency'], {
          is: (isCustomFrequency, frequency) =>
            isCustomFrequency && frequency === FREQUENCY.MONTHLY,
          then: yup
            .object()
            .shape({
              type: yup
                .string()
                .oneOf([
                  MONTH_RULE_TYPES.NTH_DAY_OF_WEEK,
                  MONTH_RULE_TYPES.SPECIFIC_DAYS,
                ]),

              day: yup.array().when('type', {
                is: MONTH_RULE_TYPES.NTH_DAY_OF_WEEK,
                then: yup
                  .array()
                  .of(yup.number().min(1).max(7).integer())
                  .required()
                  .min(1),
                otherwise: yup.array().nullable(),
              }),

              nth: yup.number().when('type', {
                is: MONTH_RULE_TYPES.NTH_DAY_OF_WEEK,
                then: yup
                  .number()
                  .required()
                  .integer()
                  .min(-3)
                  .max(6)
                  .test(
                    'not-zero',
                    'Nth value cannot be 0',
                    (value) => value !== 0
                  ),
                otherwise: yup.number().nullable(),
              }),

              days: yup.array().when('type', {
                is: MONTH_RULE_TYPES.SPECIFIC_DAYS,
                then: yup
                  .array()
                  .of(yup.number().min(1).max(31).integer())
                  .required()
                  .min(1),
                otherwise: yup.array().nullable(),
              }),
            })
            .required(),
          otherwise: yup.object().nullable(),
        }),
    })
    .required(),
});
