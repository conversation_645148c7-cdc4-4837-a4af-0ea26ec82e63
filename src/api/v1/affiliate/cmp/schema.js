const yup = require('yup');
const { PRODUCT_TYPE } = require('../../../../services/product/constants');
const { PURCHASE_TYPE } = require('../../../../constants/common');

exports.getAffiliateProductsSchema = yup.object().shape({
  pageSize: yup.number().default(20).required().integer(),
  pageNo: yup.number().default(1).required().integer(),
});

exports.getAffiliatesSchema = yup.object().shape({
  pageSize: yup.number().default(20).required().integer(),
  pageNo: yup.number().default(1).required().integer(),
  search: yup.string().trim().notRequired(),
  status: yup.string().uppercase().trim().notRequired(),
});

exports.getAffiliateSchema = yup.object().shape({
  pageSize: yup.number().default(20).required().integer(),
  pageNo: yup.number().default(1).required().integer(),
});

exports.communityIdSchema = yup.object().shape({
  communityId: yup.string().trim().required(),
});

exports.affiliateIdSchema = yup.object().shape({
  communityId: yup.string().trim().required(),
  affiliateId: yup.string().trim().required(),
});

exports.affiliateProductIdSchema = yup.object().shape({
  communityId: yup.string().trim().required(),
  affiliateProductId: yup.string().trim().required(),
});

exports.updateAffiliateSchema = yup.array().of(
  yup.object().shape({
    affiliateProductObjectId: yup.string().trim().required(),
    commissionPercentage: yup.number().required(),
  })
);

exports.postAffiliateSchema = yup.array().of(
  yup.object().shape({
    email: yup.string().trim().required(),
    subscriptionObjectId: yup.string().trim().required(),
  })
);

exports.postAffiliateProductSchema = yup.object().shape({
  entityObjectId: yup.string().trim().required(),
  entityType: yup
    .string()
    .trim()
    .required()
    .oneOf([
      PURCHASE_TYPE.SUBSCRIPTION,
      PURCHASE_TYPE.EVENT,
      PURCHASE_TYPE.FOLDER,
      PURCHASE_TYPE.SESSION,
      PURCHASE_TYPE.CHALLENGE,
      PRODUCT_TYPE.COURSE,
      PRODUCT_TYPE.DIGITAL_FILES,
    ]),
  commissionPercentage: yup.number().required().min(0.01).max(100),
  messageForAffiliates: yup.string().required(),
});

exports.updateAffiliateProductSchema = yup.object().shape({
  isActive: yup.boolean().notRequired(),
  commissionPercentage: yup.number().notRequired(),
  messageForAffiliates: yup.string().trim().notRequired(),
});

exports.getAffiliateInfoSchema = yup.object().shape({
  entityObjectId: yup.string().trim().required(),
  entityType: yup.string().trim().required(),
  affiliateCode: yup.string().trim().required(),
});
