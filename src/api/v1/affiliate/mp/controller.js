const schema = require('./schema');
const {
  affiliateProductService,
  affiliatePayoutAccountService,
  affiliatePayoutService,
  affiliateService,
  commonService,
} = require('../../../../services/affiliate');

exports.addAffiliate = async (req, res) => {
  const { communityObjectId } = schema.postAffiliateSchema.cast(req.body);

  const email = req.user.email;
  const learnerObjectId = req.user.learner._id;

  const community = await commonService.retrieveActiveCommunity(
    communityObjectId
  );

  const subscription = await commonService.retrieveActiveSubscription({
    learnerObjectId,
    email,
    communityCode: community.code,
  });

  const affiliatesToBeAdded = [
    { email, subscriptionObjectId: subscription._id },
  ];

  try {
    const results = await affiliateService.addAffiliates({
      communityObjectId,
      affiliatesToBeAdded,
    });

    return results[0];
  } catch (err) {
    if (err.code === 11000) {
      const affiliate = await affiliateService.retrieveAffiliateByLearner({
        communityObjectId,
        learnerObjectId,
      });

      return affiliate;
    }

    throw err;
  }
};

exports.getAffiliateProductsForMember = async (req, res) => {
  const { pageNo, pageSize } = schema.getAffiliateProductsSchema.cast(
    req.query
  );

  const learnerObjectId = req.user.learner._id;

  const result =
    await affiliateProductService.retrieveMemberAffiliateProducts({
      pageNo,
      pageSize,
      learnerObjectId,
    });

  return result;
};

exports.leaveAffiliate = async (req, res) => {
  const { affiliateProductId: affiliateProductObjectId } =
    schema.affiliateProductIdSchema.cast(req.params);

  const learnerObjectId = req.user.learner._id;

  const result = await affiliateService.leaveAffiliate({
    affiliateProductObjectId,
    learnerObjectId,
  });

  return result;
};

exports.getAffiliatePayouts = async (req, res) => {
  const { pageNo, pageSize } = schema.getAffiliateProductsSchema.cast(
    req.query
  );

  const learnerObjectId = req.user.learner._id;

  const result = await affiliatePayoutService.getAffiliatePayouts({
    pageNo,
    pageSize,
    learnerObjectId,
  });

  return result;
};

exports.getAffiliatePayoutAccount = async (req, res) => {
  const learnerObjectId = req.user.learner._id;

  const result =
    await affiliatePayoutAccountService.getAffiliatePayoutAccount(
      learnerObjectId
    );

  return result;
};

exports.createAffiliatePayoutAccount = async (req, res) => {
  const payoutAccount = schema.createAffiliatePayoutAccountSchema.cast(
    req.body
  );

  const learnerObjectId = req.user.learner._id;

  const result =
    await affiliatePayoutAccountService.createAffiliatePayoutAccount({
      learnerObjectId,
      ...payoutAccount,
    });

  return result;
};

exports.updateAffiliatePayoutAccount = async (req, res) => {
  const payoutAccount = schema.createAffiliatePayoutAccountSchema.cast(
    req.body
  );

  const learnerObjectId = req.user.learner._id;

  const result =
    await affiliatePayoutAccountService.updateAffiliatePayoutAccount({
      learnerObjectId,
      ...payoutAccount,
    });

  return result;
};

exports.deleteAffiliatePayoutAccount = async (req, res) => {
  const learnerObjectId = req.user.learner._id;

  const result =
    await affiliatePayoutAccountService.deleteAffiliatePayoutAccount(
      learnerObjectId
    );

  return result;
};
