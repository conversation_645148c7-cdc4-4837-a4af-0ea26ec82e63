const { DateTime } = require('luxon');

const membershipService = require('../../../services/membership');
const communityUIConfigService = require('../../../communitiesAPI/services/web/communityUIConfig.service');
const applicationService = require('../../../communitiesAPI/services/web/communityApplication.service');
const getActivityLogService = require('../../../services/membership/getActivityLog.service');
const MembershipModel = require('../../../models/membership/membership.model');

const { ParamError } = require('../../../utils/error.util');
const { getLanguagePreference } = require('../../../utils/headers.util');

const logger = require('../../../services/logger.service');

const getCommunityMemberCount = async (req, res, next) => {
  const countTypes = req.query.countTypes?.split(',');
  const results =
    await membershipService.countService.countCommunityMembers({
      communityId: req.params.communityId,
      countTypes,
    });
  return results;
};

const changeStreamHandler = async (req, res, next) => {
  const event = req.body;

  logger.info(
    'Received change stream event',
    `event=${JSON.stringify(event)}}`
  );

  const handler = membershipService.changeStreamService.getHandler(event);
  await handler.handle();
};

const dataSync = async (req, res, next) => {
  await membershipService.dataSyncService.sync({
    target: req.body.target,
    communityCodes: req.body.communityCodes,
    emails: req.body.emails,
    phoneNumbers: req.body.phoneNumbers,
    subscriptionObjectIds: req.body.subscriptionObjectIds,
  });
};

const dataClear = async (req, res, next) => {
  await membershipService.dataSyncService.clear({
    target: req.body.target,
    communityCodes: req.body.communityCodes,
  });
};

const getCommunityMembers = async (req, res, next) => {
  const { queryParams, otherFilters } =
    await membershipService.membershipSearchUtils.processGetMembersQueryParams(
      req.query
    );

  const limit = parseInt(req.query.pageSize || 100, 10);
  let skip = 0;
  if (req.query.pageNo) {
    skip = (parseInt(req.query.pageNo, 10) - 1) * limit;
  }

  const withApplication = req.query.withApplication === 'true';

  const results = await membershipService.getService.getCommunityMembers({
    communityId: req.params.communityId,
    searchString: req.query.searchString,
    communityRole: queryParams.role || [],
    status: queryParams.status || [],
    skip,
    limit,
    sortBy: req.query.sortBy,
    sortOrder: req.query.sortOrder,
    otherFilters,
    withApplication,
  });
  return results;
};

const countCommunityMembersWithFilters = async (req, res, next) => {
  const { queryParams, otherFilters } =
    await membershipService.membershipSearchUtils.processGetMembersQueryParams(
      req.query
    );

  const count =
    await membershipService.countService.countCommunityMembersWithFilters({
      communityId: req.params.communityId,
      searchString: req.query.searchString,
      communityRole: queryParams.role || [],
      status: queryParams.status || [],
      otherFilters,
    });

  return {
    count,
  };
};

const getCommunityMember = async (req, res, next) => {
  const communityId = req.params.communityId;
  const membershipId = req.params.membershipId;

  return membershipService.getService.getCommunityMember({
    communityId,
    membershipId,
  });
};

const getCommunityMemberActivity = async (req, res, next) => {
  const communityId = req.params.communityId;
  const membershipId = req.params.membershipId;

  // Get membership details to extract email
  const membership = await MembershipModel.findById(membershipId)
    .select('email')
    .lean();

  if (!membership) {
    throw new ParamError('Membership not found');
  }

  return getActivityLogService.getMergedActivityLogs(
    communityId,
    membership.email,
    null // leadObjectId will be looked up by email in getMergedActivityLogs
  );
};

const exportCommunityMembers = async (req, res) => {
  try {
    const { queryParams, otherFilters } =
      await membershipService.membershipSearchUtils.processGetMembersQueryParams(
        req.query
      );

    const fileName = encodeURIComponent(
      req.query?.fileName ?? 'members.csv'
    );

    res.setHeader(
      'Content-Disposition',
      `attachment; filename=${fileName}`
    );
    res.setHeader('Content-Type', 'text/csv');

    const communityMembersCsvStream =
      await membershipService.getService.generateCommunityMembersCsvStream(
        {
          communityId: req.params.communityId,
          searchString: req.query.searchString,
          communityRole: queryParams.role || [],
          status: queryParams.status || [],
          otherFilters,
          sortBy: req.query.sortBy,
          sortOrder: req.query.sortOrder,
        }
      );
    communityMembersCsvStream.pipe(res);
    communityMembersCsvStream.on('end', () => {
      res.end();
    });
  } catch (error) {
    logger.error('Error exporting community members', error, error.stack);
    return res.status(500).json({
      errorMessage: 'Internal server error',
      errorCode: -1,
      error: 'INTERNAL_ERROR',
    });
  }
};

const getCommunitySegments = async (req, res, next) => {
  const communityId = req.params.communityId;
  const languagePreference = getLanguagePreference(req);
  const results =
    await membershipService.getService.getCommunityMembershipSegments({
      communityId,
      languagePreference,
    });
  return results;
};

const updateCommunityUIConfig = async (req, res, next) => {
  const { configType, configPayload } = req.body;
  if (!configType) {
    throw new ParamError('configType is required');
  }
  const updatedConfig =
    await communityUIConfigService.updateCommunityUIConfig({
      configType,
      configPayload,
    });
  return updatedConfig;
};

const bulkUpdateApplication = async (req, res, next) => {
  const { applicationStatus } = req.body;
  const results = await applicationService.bulkUpdateApplication({
    communityId: req.params.communityId,
    applicationStatus,
  });
  return results;
};

const enrollMembership = async (req, res, next) => {
  const { communityId: communityObjectId } = req.params;
  const { community, subscription, learner, purchaseTransaction } =
    req.body;

  const result =
    await membershipService.enrollmentService.enrollMembership({
      community,
      subscription,
      learner,
      purchaseTransaction,
      communityObjectId,
    });

  return result;
};

module.exports = {
  exportCommunityMembers,
  getCommunityMemberCount,
  getCommunityMembers,
  getCommunityMember,
  getCommunityMemberActivity,
  countCommunityMembersWithFilters,
  getCommunitySegments,
  changeStreamHandler,
  dataSync,
  dataClear,
  updateCommunityUIConfig,
  bulkUpdateApplication,
  enrollMembership,
};
