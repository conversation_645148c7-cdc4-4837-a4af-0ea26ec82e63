const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  MODULE_TYPE,
  rateLimitMiddleware,
} = require('../../../utils/rateLimit.util');
const { handlerWrapper } = require('../../../utils/request.util');

const setupMagicLeadsRouter = function (router) {
  // Get products with leads data
  router.route('/communities/:communityId/magic-leads/products').get(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: MODULE_TYPE.MAGIC_LEADS,
    }),
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.getProductsWithLeadsData,
      requestValidators: {
        params: schema.communityIdSchema,
        query: schema.getProductsQuerySchema,
      },
    })
  );

  // Generate ICP for product
  router
    .route('/communities/:communityId/magic-leads/products/generate-icp')
    .post(
      postRoutePreHandlerMiddleware,
      rateLimitMiddleware({
        module: MODULE_TYPE.MAGIC_LEADS,
      }),
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.generateICPForProduct,
        requestValidators: {
          params: schema.communityIdSchema,
          body: schema.generateICPSchema,
        },
      })
    );

  // Update ICP search fields
  router
    .route(
      '/communities/:communityId/magic-leads/products/icp/:icpProfileId'
    )
    .patch(
      postRoutePreHandlerMiddleware,
      rateLimitMiddleware({
        module: MODULE_TYPE.MAGIC_LEADS,
      }),
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.updateICPProfile,
        requestValidators: {
          params: schema.icpProfileIdSchema,
          body: schema.updateICPSchema,
        },
      })
    );

  // Get ICP profiles and leads for product (with query parameters)
  router.route('/communities/:communityId/magic-leads/products/leads').get(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: MODULE_TYPE.MAGIC_LEADS,
    }),
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.getICPProfilesAndLeads,
      requestValidators: {
        params: schema.communityIdSchema,
        query: schema.getLeadsQuerySchema,
      },
    })
  );

  router
    .route('/communities/:communityId/magic-leads/outreach-purposes')
    .get(
      postRoutePreHandlerMiddleware,
      rateLimitMiddleware({
        module: MODULE_TYPE.MAGIC_LEADS,
      }),
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.getOutreachPurposes,
        requestValidators: {
          params: schema.communityIdSchema,
        },
      })
    );

  router
    .route(
      '/communities/:communityId/magic-leads/icp-matches/:icpLeadMatchId/email-templates'
    )
    .post(
      postRoutePreHandlerMiddleware,
      rateLimitMiddleware({
        module: MODULE_TYPE.MAGIC_LEADS,
      }),
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.generateEmailTemplate,
        requestValidators: {
          params: schema.icpLeadMatchIdSchema,
          body: schema.generateEmailTemplateSchema,
        },
      })
    );

  router
    .route(
      '/communities/:communityId/magic-leads/icp-matches/:icpLeadMatchId/email/send'
    )
    .post(
      postRoutePreHandlerMiddleware,
      rateLimitMiddleware({
        module: MODULE_TYPE.MAGIC_LEADS,
      }),
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.sendEmail,
        requestValidators: {
          params: schema.icpLeadMatchIdSchema,
          body: schema.sendEmailSchema,
        },
      })
    );

  // Generate magic leads for ICP profile
  router
    .route(
      '/communities/:communityId/magic-leads/products/icp/:icpProfileId/generate-leads'
    )
    .post(
      postRoutePreHandlerMiddleware,
      rateLimitMiddleware({
        module: MODULE_TYPE.MAGIC_LEADS,
      }),
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.generateLeadsForICP,
        requestValidators: {
          params: schema.icpProfileIdSchema,
        },
      })
    );

  // Batch get leads by IDs for AI summary polling
  router
    .route(
      '/communities/:communityId/magic-leads/products/leads/batch-get'
    )
    .post(
      postRoutePreHandlerMiddleware,
      rateLimitMiddleware({
        module: MODULE_TYPE.MAGIC_LEADS,
      }),
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.getLeadsByBatchIds,
        requestValidators: {
          params: schema.communityIdSchema,
          body: schema.batchGetLeadsSchema,
        },
      })
    );

  // Get lead details with activity logs
  router
    .route(
      '/communities/:communityId/magic-leads/products/leads/:leadObjectId'
    )
    .get(
      postRoutePreHandlerMiddleware,
      rateLimitMiddleware({
        module: MODULE_TYPE.MAGIC_LEADS,
      }),
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.getLeadDetails,
        requestValidators: {
          params: schema.leadDetailsSchema,
        },
      })
    );

  // Get ICP list for product
  router
    .route('/communities/:communityId/magic-leads/products/icpList')
    .get(
      postRoutePreHandlerMiddleware,
      rateLimitMiddleware({
        module: MODULE_TYPE.MAGIC_LEADS,
      }),
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.getICPList,
        requestValidators: {
          params: schema.communityIdSchema,
          query: schema.getICPListQuerySchema,
        },
      })
    );
};

const setupRouter = function (router) {
  setupMagicLeadsRouter(router);
};

module.exports = {
  setupRouter,
};
