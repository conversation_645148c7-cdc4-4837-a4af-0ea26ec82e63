const yup = require('yup');
const { PRODUCT_TYPE } = require('../../../services/product/constants');
const {
  SEARCH_FIELD_TYPE,
  OUTREACH_PURPOSE,
} = require('../../../services/magicLeads/constants');

const productTypeEnum = Object.values(PRODUCT_TYPE);
const searchFieldTypeEnum = Object.values(SEARCH_FIELD_TYPE);

exports.generateICPSchema = yup.object().shape({
  productType: yup
    .string()
    .trim()
    .oneOf(
      productTypeEnum,
      `Product type must be one of: ${productTypeEnum.join(', ')}`
    )
    .required('Product type is required'),
  entityObjectId: yup
    .string()
    .trim()
    .required('Entity object ID is required'),
  icpSummary: yup
    .string()
    .trim()
    .min(1, 'ICP summary cannot be empty')
    .optional(),
});

exports.updateICPSchema = yup
  .object()
  .shape({
    searchFields: yup
      .array()
      .of(
        yup.object().shape({
          type: yup
            .string()
            .oneOf(
              searchFieldTypeEnum,
              `Search field type must be one of: ${searchFieldTypeEnum.join(
                ', '
              )}`
            )
            .required('Search field type is required'),
          values: yup
            .array()
            .of(
              yup
                .string()
                .trim()
                .min(1, 'Search field value cannot be empty')
            )
            .min(1, 'At least one value is required per search field')
            .required('Search field values are required'),
        })
      )
      .min(1, 'At least one search field is required')
      .max(3, 'Cannot have more than 3 search field types')
      .optional(),
    icpSummary: yup
      .string()
      .trim()
      .min(10, 'ICP summary must be at least 10 characters')
      .max(1000, 'ICP summary cannot exceed 1000 characters')
      .optional(),
  })
  .test(
    'only-one-field',
    'Only one of searchFields or icpSummary can be provided, not both',
    function (value) {
      const hasSearchFields =
        value.searchFields && value.searchFields.length > 0;
      const hasIcpSummary =
        value.icpSummary && value.icpSummary.trim().length > 0;

      // Must have exactly one of them
      return (
        (hasSearchFields && !hasIcpSummary) ||
        (!hasSearchFields && hasIcpSummary)
      );
    }
  );

exports.getICPProfilesSchema = yup.object().shape({
  productType: yup
    .string()
    .trim()
    .oneOf(
      productTypeEnum,
      `Product type must be one of: ${productTypeEnum.join(', ')}`
    )
    .required('Product type is required'),
  entityObjectId: yup
    .string()
    .trim()
    .required('Entity object ID is required'),
});

exports.communityIdSchema = yup.object().shape({
  communityId: yup.string().trim().required('Community ID is required'),
});

exports.icpProfileIdSchema = yup.object().shape({
  communityId: yup.string().trim().required('Community ID is required'),
  icpProfileId: yup.string().trim().required('ICP Profile ID is required'),
});

exports.icpLeadMatchIdSchema = yup.object().shape({
  communityId: yup.string().trim().required('Community ID is required'),
  icpLeadMatchId: yup
    .string()
    .trim()
    .required('ICP Lead Match ID is required'),
});

exports.getLeadsQuerySchema = yup.object().shape({
  productType: yup
    .string()
    .trim()
    .oneOf(
      productTypeEnum,
      `Product type must be one of: ${productTypeEnum.join(', ')}`
    )
    .required('Product type is required'),
  entityObjectId: yup
    .string()
    .trim()
    .required('Entity object ID is required'),
  icpProfileId: yup.string().trim().optional(),
  pageNo: yup
    .number()
    .integer('Page number must be an integer')
    .min(1, 'Page number must be at least 1')
    .max(1000, 'Page number cannot exceed 1000')
    .optional()
    .default(1),
  pageSize: yup
    .number()
    .integer('Page size must be an integer')
    .min(1, 'Page size must be at least 1')
    .max(100, 'Page size cannot exceed 100')
    .optional()
    .default(20),
  searchName: yup
    .string()
    .trim()
    .min(1, 'Search name must be at least 1 character')
    .max(100, 'Search name cannot exceed 100 characters')
    .optional(),
  status: yup.string().trim().optional(),
});

exports.generateEmailTemplateSchema = yup.object().shape({
  outreachPurpose: yup
    .string()
    .trim()
    .oneOf(Object.keys(OUTREACH_PURPOSE))
    .required(),
});

exports.sendEmailSchema = yup.object().shape({
  outreachPurpose: yup
    .string()
    .trim()
    .oneOf(Object.keys(OUTREACH_PURPOSE))
    .required(),
  title: yup.string().trim().required(),
  content: yup.object().required(),
});

exports.getProductsQuerySchema = yup.object().shape({
  pageNo: yup
    .number()
    .integer('Page number must be an integer')
    .min(1, 'Page number must be at least 1')
    .max(1000, 'Page number cannot exceed 1000')
    .optional()
    .default(1),
  pageSize: yup
    .number()
    .integer('Page size must be an integer')
    .min(1, 'Page size must be at least 1')
    .max(100, 'Page size cannot exceed 100')
    .optional()
    .default(15),
});

exports.batchGetLeadsSchema = yup.object().shape({
  icpLeadsMatchObjectId: yup
    .array()
    .of(
      yup
        .string()
        .trim()
        .matches(/^[0-9a-fA-F]{24}$/, 'Invalid ObjectId format')
        .required('ObjectId is required')
    )
    .min(1, 'At least one ObjectId is required')
    .max(100, 'Cannot exceed 100 ObjectIds')
    .required('icpLeadsMatchObjectId array is required')
    .test(
      'is-array',
      'icpLeadsMatchObjectIds must be a non-empty array',
      function (value) {
        return Array.isArray(value) && value.length > 0;
      }
    ),
});

exports.leadDetailsSchema = yup.object().shape({
  communityId: yup
    .string()
    .trim()
    .matches(/^[0-9a-fA-F]{24}$/, 'Invalid community ObjectId format')
    .required('Community ID is required'),
  leadObjectId: yup
    .string()
    .trim()
    .matches(/^[0-9a-fA-F]{24}$/, 'Invalid lead ObjectId format')
    .required('Lead Object ID is required'),
});

exports.getICPListQuerySchema = yup.object().shape({
  productType: yup
    .string()
    .trim()
    .oneOf(
      productTypeEnum,
      `Product type must be one of: ${productTypeEnum.join(', ')}`
    )
    .required('Product type is required'),
  entityObjectId: yup
    .string()
    .trim()
    .matches(/^[0-9a-fA-F]{24}$/, 'Invalid entity ObjectId format')
    .required('Entity object ID is required'),
});
