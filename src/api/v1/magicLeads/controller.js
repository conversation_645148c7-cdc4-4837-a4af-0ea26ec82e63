const schema = require('./schema');
const {
  getProductsWithLeadsData,
} = require('../../../services/magicLeads/productList.service');
const {
  generateICPForProduct,
  updateICPProfile,
} = require('../../../services/magicLeads/icpProfile.service');
const {
  generateLeadsForICP,
} = require('../../../services/magicLeads/leadsGeneration.service');
const {
  getICPProfilesAndLeads,
  getLeadsByBatchIds,
} = require('../../../services/magicLeads/leadsRetrieval.service');
const emailService = require('@/src/services/magicLeads/email.service');
const {
  getLeadDetails,
} = require('../../../services/magicLeads/leadDetails.service');
const icpListService = require('../../../services/magicLeads/icpList.service');
const { getLanguagePreference } = require('../../../utils/headers.util');
const CommunityModel = require('../../../communitiesAPI/models/community.model');

exports.getProductsWithLeadsData = async (req) => {
  const { communityId } = schema.communityIdSchema.cast(req.params);
  const { pageNo, pageSize } = schema.getProductsQuerySchema.cast(
    req.query
  );

  return getProductsWithLeadsData({
    communityId,
    pageNo,
    pageSize,
  });
};

exports.generateICPForProduct = async (req) => {
  const { communityId } = req.params;
  const { productType, entityObjectId, icpSummary } =
    schema.generateICPSchema.cast(req.body);
  const learnerObjectId = req.user.learner._id;

  return generateICPForProduct(
    communityId,
    productType,
    entityObjectId,
    icpSummary,
    learnerObjectId
  );
};

exports.updateICPProfile = async (req) => {
  const { icpProfileId } = req.params;
  const { searchFields, icpSummary } = schema.updateICPSchema.cast(
    req.body
  );

  return updateICPProfile(icpProfileId, searchFields, icpSummary);
};

exports.generateLeadsForICP = async (req) => {
  const { icpProfileId } = req.params;

  return generateLeadsForICP(icpProfileId);
};

exports.getOutreachPurposes = async () => {
  return emailService.retrieveOutreachPurposes();
};

exports.generateEmailTemplate = async (req) => {
  const {
    communityId: communityObjectId,
    icpLeadMatchId: icpLeadMatchObjectId,
  } = schema.icpLeadMatchIdSchema.cast(req.params);

  const { outreachPurpose } = schema.generateEmailTemplateSchema.cast(
    req.body
  );

  const languagePreference = getLanguagePreference(req);

  return emailService.generateOutreachEmailTemplate({
    communityObjectId,
    icpLeadMatchObjectId,
    outreachPurpose,
    languagePreference,
  });
};

exports.sendEmail = async (req) => {
  const {
    communityId: communityObjectId,
    icpLeadMatchId: icpLeadMatchObjectId,
  } = schema.icpLeadMatchIdSchema.cast(req.params);

  const languagePreference = getLanguagePreference(req);

  const { outreachPurpose, title, content } = schema.sendEmailSchema.cast(
    req.body
  );

  const userObjectId = req.user._id;
  const learnerObjectId = req.user.learner._id;

  const result = await emailService.sendOutreachEmail({
    communityObjectId,
    icpLeadMatchObjectId,
    outreachPurpose,
    title,
    content,
    userObjectId,
    learnerObjectId,
    languagePreference,
  });

  return result;
};

exports.getICPProfilesAndLeads = async (req) => {
  const { communityId } = req.params;
  const {
    productType,
    entityObjectId,
    icpProfileId,
    pageNo,
    pageSize,
    searchName,
    status,
  } = schema.getLeadsQuerySchema.cast(req.query);

  const statusArray = status ? status.split(',') : null;
  return getICPProfilesAndLeads(
    communityId,
    productType,
    entityObjectId,
    icpProfileId,
    pageNo,
    pageSize,
    searchName,
    statusArray
  );
};

exports.getLeadsByBatchIds = async (req) => {
  const { icpLeadsMatchObjectId } = schema.batchGetLeadsSchema.cast(
    req.body
  );

  const data = await getLeadsByBatchIds(icpLeadsMatchObjectId);
  return {
    leadsSummary: data,
  };
};

exports.getLeadDetails = async (req) => {
  const { communityId, leadObjectId } = schema.leadDetailsSchema.cast(
    req.params
  );

  return getLeadDetails(communityId, leadObjectId);
};

exports.getICPList = async (req) => {
  const { communityId } = schema.communityIdSchema.cast(req.params);
  const { productType, entityObjectId } =
    schema.getICPListQuerySchema.cast(req.query);

  const data = await icpListService.getICPList(
    communityId,
    productType,
    entityObjectId
  );

  return data;
};
