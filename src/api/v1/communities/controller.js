const logger = require('@services/logger.service');
const {
  createCompleteService,
} = require('./services/createComplete.service');

const createCompleteController = async (req, res) => {
  logger.info('Starting unified community creation', {
    userId: req.user._id,
    email: req.user.email,
    ip: req.ip,
    communityName: req.body.community?.name,
  });

  try {
    const result = await createCompleteService(req, res);
    return result;
  } catch (error) {
    logger.error('Error in unified community creation controller', {
      userId: req.user._id,
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

module.exports = {
  createCompleteController,
};
