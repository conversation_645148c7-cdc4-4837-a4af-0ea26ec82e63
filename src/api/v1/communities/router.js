const { Router } = require('express');
const tokenValidator = require('@src/validations/token.validation');
const userValidation = require('@src/validations/user.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('@src/middleware/request.middleware');
const rejectBlacklistedUser = require('@src/middleware/rejectBlacklisted.middleware');
const ipCountryDetectionMiddleware = require('@src/middleware/ipCountryDetection.middleware');
const rejectBlockedRegions = require('@src/middleware/rejectBlockedRegions.middleware');
const {
  MODULE_TYPE,
  OPERATION,
  IDENTIFIER_TYPE,
  mongoRateLimitMiddleware,
} = require('@utils/rateLimit.util');
const { handlerWrapper } = require('@utils/request.util');
const controller = require('./controller');
const schema = require('./schema');

const router = Router({ mergeParams: true });

router.route('/').post(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  rejectBlacklistedUser,
  ipCountryDetectionMiddleware,
  rejectBlockedRegions,
  mongoRateLimitMiddleware({
    module: MODULE_TYPE.COMMUNITY_ONBOARDING,
    operation: OPERATION.CREATE_COMMUNITY,
    identifierFn: IDENTIFIER_TYPE.USER_ID,
  }),
  handlerWrapper({
    handler: controller.createCompleteController,
    requestValidators: {
      body: schema.createCompleteSchema,
    },
  })
);

module.exports = router;
