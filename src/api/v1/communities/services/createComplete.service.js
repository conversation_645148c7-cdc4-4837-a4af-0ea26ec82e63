const PrimaryMongooseConnection = require('@src/rpc/primaryMongooseConnection');
const logger = require('@services/logger.service');
const jwt = require('jsonwebtoken');
const {
  createDefaultAnnouncementPost,
} = require('@src/communitiesAPI/services/web/communityPosts.service');
// Service imports
const {
  updateTrainerOrLearnerProfile,
} = require('@services/update_profile.service');
const {
  checkCommunityForFraud,
  getSocialLinksToBeAdded,
} = require('@src/communitiesAPI/services/common/community.service');
const fraudService = require('@services/fraud');

// Configuration
const {
  JWT_SECRET_KEY,
  JWT_ACCESS_TOKEN_EXPIRES,
  JWT_REFRESH_SECRET_KEY,
  JWT_REFRESH_TOKEN_EXPIRES,
  authCookieDomain,
  cookieTokenMaxAgeMS,
  cookieRefreshTokenMaxAgeMS,
} = require('@src/config');

// Constants
const { aclRoles } = require('@src/communitiesAPI/constants');
const {
  TRANSACTION_TYPE,
  PURCHASE_TYPE,
} = require('@src/constants/common');

// Error handling
const { ToUserError } = require('@src/utils/error.util');
const { GENERIC_ERROR } = require('@src/constants/errorCode');
const Community = require('@src/communitiesAPI/models/community.model');
const Learner = require('@src/models/learners.model');
const RevenueTransactionRpc = require('@src/rpc/revenueTransaction.rpc');
const { createOwnerRoleForNewCommunity } = require('./ownerRole.service');
const {
  createLandingPageTemplate,
} = require('./landingPageTemplate.service');
const {
  createCommunityDataObject,
} = require('./communityGeneration.service');
const { selectCommunityThumbnail } = require('./imageSelection.service');
const {
  communityCreatedKlaviyoEvents,
} = require('@/src/communitiesAPI/services/common/communityTriggers.service');

/**
 * Main orchestration service for unified community creation
 * Handles all operations within a MongoDB transaction for atomicity
 */
const createCompleteService = async (req, res) => {
  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();

  try {
    logger.info('Starting unified community creation transaction', {
      userId: req.user._id,
      email: req.user.email,
      communityName: req.body.community?.name,
    });

    const languagePreference =
      req.user.learner?.languagePreference || 'en';

    let result;
    let bonusTransaction = null; // Store bonus transaction data to process after commit

    // Start MongoDB transaction for atomicity
    await session.withTransaction(async () => {
      const {
        community: communityData,
        socialMedia,
        referralCode,
      } = req.body;
      const user = req.user;

      // Step 1: Fetch learner data early (optimization for community creation)
      const learner = await Learner.findOne({ email: user.email })
        .select(
          '_id learnerId firstName lastName languagePreference socialMedia'
        )
        .lean();

      if (!learner) {
        throw new Error('Learner not found for authenticated user');
      }

      // Step 1.5: Check bonus eligibility (optimization: single query vs expensive aggregation)
      const hasExistingReward = await Community.exists({
        isActive: true,
        createdBy: user.email,
        'config.aug2024CampaignRewardReceived': true,
      });

      const shouldReceiveBonus = !hasExistingReward;

      logger.info('Bonus eligibility checked', {
        userId: user._id,
        email: user.email,
        hasExistingReward,
        shouldReceiveBonus,
      });

      // Step 2: Select thumbnail image (custom or random default)
      const imageResult = selectCommunityThumbnail(
        communityData.thumbnailImageUrl
      );

      logger.info('Image selection completed', {
        selectedImage: imageResult.selectedImage,
        isCustomImage: imageResult.isCustomImage,
      });

      // Step 3: Create community with all data (includes currency mapping and activation)
      const communityPayload = {
        name: communityData.name,
        description: communityData.description,
        category: communityData.category,
        timezone: communityData.timezone,
        expectedSize: communityData.expectedSize,
        communityGoal: communityData.communityGoal,
        country: req.userCountry?.name,
        countryCode: req.userCountry?.code,
        trainerId: user.trainer?._id || user.learner?._id,
        trackingData: communityData.trackingData,
        // Add override fields from request
        baseCurrency: communityData.baseCurrency, // Allow override from request
        // Add image configuration properly
        thumbnailImgData: imageResult.imageConfig.thumbnailImgData,
        communityCheckoutCardData:
          imageResult.imageConfig.communityCheckoutCardData,
        // Pass user and learner for creating community in final active state
        user,
        learner,
      };

      const { communityDataObject, referrerFirstName } =
        await createCommunityDataObject({
          ...communityPayload,
          referralCode, // Pass referral code for processing
          shouldReceiveBonus, // Pass bonus eligibility flag
        });

      // Save community to database
      const community = new Community(communityDataObject);
      await community.save({ session });

      logger.info(
        'Community created successfully (active from creation)',
        {
          communityId: community._id,
          communityCode: community.code,
          communityLink: community.link,
          baseCurrency: community.baseCurrency,
          isActive: community.isActive,
          isDraft: community.isDraft,
          By: community.By,
          createdBy: community.createdBy,
        }
      );

      // Step 4: Create landing page template
      const landingPageResult = await createLandingPageTemplate(
        community,
        session
      );

      logger.info('Landing page template created', {
        templateId: landingPageResult.template._id,
        sectionOrder: landingPageResult.template.sectionOrder,
      });

      // Step 5: Update user profile if social media provided
      let updatedProfile = null;
      if (socialMedia && socialMedia.length > 0) {
        const newSocialLinks = getSocialLinksToBeAdded(
          learner.socialMedia,
          socialMedia
        );
        await Community.updateOne(
          {
            _id: community._id,
          },
          {
            $set: {
              socialMedia,
            },
          },
          { session }
        );

        const profileData = {
          socialMedia: [...learner.socialMedia, ...newSocialLinks],
          countryId: req.countryInfo?.countryId,
        };
        updatedProfile = await updateTrainerOrLearnerProfile(
          user,
          profileData
        );
        logger.info('Profile updated with social media', {
          userId: user._id,
          socialMediaCount: socialMedia.length,
        });
      }

      // Step 6: Create owner role using optimized function (no community update needed)
      const managerResult = await createOwnerRoleForNewCommunity({
        user,
        learner,
        community,
        referralCode,
        referrerFirstName,
        session,
      });
      logger.info('Owner role assigned successfully (optimized)', {
        communityId: community._id,
        userEmail: user.email,
        roles: managerResult.roles,
        optimization: 'no_community_update_needed',
      });

      // Step 7: Prepare $5 campaign bonus for later processing (after transaction commits)
      if (shouldReceiveBonus) {
        bonusTransaction = {
          communityObjectId: community._id,
          learnerObjectId: learner._id,
          email: user.email,
          transactionCreatedAt: new Date(),
          transactionType: TRANSACTION_TYPE.INBOUND,
          purchaseType: PURCHASE_TYPE.CAMPAIGN_REWARD,
          metadata: {
            type: 'NAS_GIFT_AUG_2024',
            title: 'Nas.io transferred $5 to help you get started! 🎉',
          },
          paymentMethod: 'NIL',
          paymentBrand: 'NIL',
          amountInUsd: 500,
        };
        logger.info(
          'Campaign bonus transaction prepared for post-transaction processing',
          {
            communityId: community._id,
            userEmail: user.email,
          }
        );
      } else {
        logger.info(
          'Campaign bonus skipped - user already received reward',
          {
            communityId: community._id,
            userEmail: user.email,
          }
        );
      }

      // Step 8: Update user role count for token generation
      const currentRoleCount = user.communityRoleCount || 0;
      const updatedUser = {
        ...user,
        communityRoleCount: currentRoleCount + 1,
      };

      // Step 9: Generate new JWT tokens with updated role count
      const accessToken = jwt.sign({ user: updatedUser }, JWT_SECRET_KEY, {
        expiresIn: JWT_ACCESS_TOKEN_EXPIRES,
      });

      const refreshToken = jwt.sign(
        { user: updatedUser },
        JWT_REFRESH_SECRET_KEY,
        { expiresIn: JWT_REFRESH_TOKEN_EXPIRES }
      );

      // Step 10: Set HTTP-only cookies for security
      res.cookie('accessTokenNA', accessToken, {
        domain: authCookieDomain,
        path: '/',
        sameSite: 'None',
        secure: true,
        httpOnly: true, // Enhanced security vs existing APIs
        maxAge: cookieTokenMaxAgeMS,
      });

      res.cookie('refreshTokenNA', refreshToken, {
        domain: authCookieDomain,
        path: '/',
        sameSite: 'None',
        secure: true,
        httpOnly: true, // Enhanced security vs existing APIs
        maxAge: cookieRefreshTokenMaxAgeMS,
      });

      logger.info('Auth tokens generated and secure cookies set');

      // Step: Send community created event to Klaviyo
      const userAgent = req?.headers['user-agent'] || null;
      const deviceCategory =
        userAgent.includes('Android') || userAgent.includes('iPhone')
          ? 'Mobile'
          : 'Web';
      const communityCreatedKlaviyoPayload = {
        country: community?.countryCreatedIn,
        email: user.email,
        chatConnected: community?.bots?.[0]?.type ? 'Yes' : 'No',
        chatPlatform: community?.bots?.[0]?.type,
        communityCreateDate: community?.createdAt.toString(),
        communityApplicationId: community?.communityApplicationId,
        timezone: community?.timezone,
        communityName: community?.name,
        communityCode: community?.code,
        languagePreference,
        userAgent,
        deviceCategory,
      };
      await communityCreatedKlaviyoEvents(communityCreatedKlaviyoPayload);
      logger.info('Klaviyo community created event sent', {
        communityCreatedKlaviyoPayload,
      });

      // Step 11: Format response
      result = {
        community: {
          _id: community._id,
          name: community.name,
          code: community.code,
          link: community.link,
          shortCode: community.shortCode,
          baseCurrency: community.baseCurrency,
          stripePaymentMethod: community.stripePaymentMethod,
          revenueGoal: community.revenueGoal,
          thumbnailImage: imageResult.selectedImage,
          isActive: managerResult.updatedCommunityData?.isActive || true,
          isDraft: managerResult.updatedCommunityData?.isDraft || false,
          restrictedInfo:
            managerResult.updatedCommunityData?.restrictedInfo || {},
          createdAt: community.createdAt,
        },
        user: {
          _id: updatedUser._id,
          email: updatedUser.email,
          communityRoleCount: updatedUser.communityRoleCount,
          roles: managerResult.roles || [
            aclRoles.OWNER,
            aclRoles.ADMIN,
            aclRoles.MANAGER,
            aclRoles.MEMBER,
          ],
        },
        profile: updatedProfile
          ? {
              _id: updatedProfile._id,
              socialMediaUpdated: true,
              socialMediaCount: socialMedia?.length || 0,
            }
          : null,
        landingPage: {
          templateId: landingPageResult.template._id,
          slug: landingPageResult.template.slug,
          sectionOrder: landingPageResult.template.sectionOrder,
        },
        // Note: No tokens in response body for security (they're in HTTP-only cookies)
      };

      logger.info('Unified community creation completed successfully', {
        communityId: community._id,
        userId: user._id,
        newRoleCount: updatedUser.communityRoleCount,
        transactionComplete: true,
      });
    });

    // Process bonus transaction after transaction commits (if applicable)
    if (bonusTransaction) {
      try {
        await RevenueTransactionRpc.sendRawTransactionToQueue(
          bonusTransaction
        );
        logger.info(
          'Campaign bonus transaction sent to queue successfully',
          {
            communityId: bonusTransaction.communityObjectId,
            userEmail: bonusTransaction.email,
            optimization: 'queue_after_transaction_commit',
          }
        );
      } catch (error) {
        logger.warn(
          'Campaign bonus transaction queue send failed (non-critical)',
          {
            error: error.message,
            communityId: bonusTransaction.communityObjectId,
          }
        );
      }
    }

    // Steps 11-12: Run post-transaction operations in parallel (OPTIMIZATION)
    const results = await Promise.allSettled([
      // Step 11: Create default announcement post
      createDefaultAnnouncementPost(
        result.community,
        req.user._id,
        languagePreference
      ),
      // Step 12: Fraud check
      checkCommunityForFraud({
        communityId: result.community._id,
        communityData: result.community,
        eventName: fraudService.INTERESTED_EVENTS.ENABLE_COMMUNITY,
      }),
    ]);

    // Log results
    if (results[0].status === 'fulfilled') {
      logger.info('Default announcement post created successfully');
    } else {
      logger.warn(
        'Failed to create default announcement post (non-critical)',
        {
          error: results[0].reason?.message,
          communityId: result.community._id,
        }
      );
    }

    if (results[1].status === 'fulfilled') {
      logger.info('Community fraud check completed successfully');
    } else {
      logger.warn('Community fraud check failed (non-critical)', {
        error: results[1].reason?.message,
        communityId: result.community._id,
      });
    }
    // Step 13: Refetch community data to get updated restrictedInfo after fraud check
    try {
      const updatedCommunity = await Community.findById(
        result.community._id
      )
        .read('primary')
        .lean();

      if (updatedCommunity) {
        result.community.restrictedInfo =
          updatedCommunity.restrictedInfo || {};
        logger.info(
          'Updated restrictedInfo in response after fraud check',
          {
            restrictedInfo: updatedCommunity.restrictedInfo,
            communityId: result.community._id,
          }
        );
      }
    } catch (error) {
      logger.warn('Failed to refetch community data after fraud check', {
        error: error.message,
        communityId: result.community._id,
      });
    }

    return result;
  } catch (error) {
    logger.error('Error in unified community creation transaction', {
      userId: req.user._id,
      error: error.message,
      stack: error.stack,
      communityName: req.body.community?.name,
    });

    // Re-throw known errors
    if (error instanceof ToUserError) {
      throw error;
    }

    // Wrap unknown errors
    throw new ToUserError(
      'Community creation failed',
      GENERIC_ERROR.INTERNAL_SERVER_ERROR
    );
  } finally {
    await session.endSession();
  }
};

module.exports = {
  createCompleteService,
};
