const logger = require('@services/logger.service');

/**
 * Image Selection Service for Unified Community Creation
 * Follows Python implementation exactly: custom image URL or random default selection
 */

// Default community image constants (matching Python exactly)
const DEFAULT_COMMUNITY_IMAGE_PREFIX =
  'https://d2oi1rqwb0pj00.cloudfront.net/nasIO/onboarding/jpg/default-community-logo-';
const DEFAULT_IMG_COLORS = ['blue', 'green', 'orange', 'pink', 'yellow'];

/**
 * Select thumbnail image for community creation
 * Python logic: Use custom image if provided, otherwise generate random default
 *
 * @param {string|null} thumbnailImageUrl - Custom thumbnail URL from request
 * @returns {Object} Image selection result with configuration
 */
const selectCommunityThumbnail = (thumbnailImageUrl = null) => {
  try {
    let selectedImage;

    if (thumbnailImageUrl) {
      // Use custom image if provided
      selectedImage = thumbnailImageUrl;
      logger.info('Using custom thumbnail image', {
        imageUrl: selectedImage,
      });
    } else {
      // Random default selection (matching Python exactly)
      const randomColor =
        DEFAULT_IMG_COLORS[
          Math.floor(Math.random() * DEFAULT_IMG_COLORS.length)
        ];
      selectedImage = `${DEFAULT_COMMUNITY_IMAGE_PREFIX}${randomColor}.jpg`;
      logger.info('Using default thumbnail image', {
        selectedColor: randomColor,
        imageUrl: selectedImage,
      });
    }

    // Generate image configuration (matching Python structure)
    const imageConfig = {
      // Main thumbnail data
      thumbnailImgData: {
        mobileImgData: {
          src: selectedImage,
        },
        desktopImgData: {
          src: selectedImage,
        },
      },
      // Checkout page image configuration
      communityCheckoutCardData: {
        imgData: {
          mobileImgProps: {
            src: selectedImage,
          },
          desktopImgProps: {
            src: selectedImage,
          },
        },
      },
    };

    return {
      selectedImage,
      imageConfig,
      isCustomImage: !!thumbnailImageUrl,
    };
  } catch (error) {
    logger.error('Error selecting community thumbnail', {
      error: error.message,
      thumbnailImageUrl,
    });

    // Fallback to blue default on error
    const fallbackImage = `${DEFAULT_COMMUNITY_IMAGE_PREFIX}blue.jpg`;
    const fallbackConfig = {
      thumbnailImgData: {
        mobileImgData: { src: fallbackImage },
        desktopImgData: { src: fallbackImage },
      },
      communityCheckoutCardData: {
        imgData: {
          mobileImgProps: { src: fallbackImage },
          desktopImgProps: { src: fallbackImage },
        },
      },
    };

    return {
      selectedImage: fallbackImage,
      imageConfig: fallbackConfig,
      isCustomImage: false,
    };
  }
};

module.exports = {
  selectCommunityThumbnail,
};
