const CommunityLandingPageTemplate = require('@src/communitiesAPI/models/communityLandingPageTemplate.model');
const Config = require('@src/models/config.model');
const logger = require('@services/logger.service');
const { CONFIG_TYPES } = require('@src/constants/common');

/**
 * Creates a landing page template for a community (ported from Python logic)
 * @param {Object} community - The community object
 * @param {Object} session - MongoDB session for transaction
 * @returns {Object} Landing page template result
 */
const createLandingPageTemplate = async (community, session) => {
  try {
    logger.info('Creating landing page template', {
      communityId: community._id,
      communityCode: community.code,
      communityLink: community.link,
    });

    // Get default section order configuration (session-aware)
    const configQuery = Config.findOne({
      configType: CONFIG_TYPES.COMMUNITY_LANDING_PAGE_TEMPLATE_CONFIG_TYPE,
      isActive: true,
    }).select('configData._id configData.sectionOrder');

    const defaultConfig = session
      ? await configQuery.session(session).read('primary').lean()
      : await configQuery.lean();

    // Default section order (from Python logic)
    let sectionOrder = [
      'bannerSection',
      'aboutSection',
      'eventsSection',
      'coursesSection',
      'resourcesSection',
      'paymentSection',
      'benefitSection',
    ];

    if (defaultConfig?.configData?.sectionOrder) {
      sectionOrder = defaultConfig.configData.sectionOrder;
      logger.info('Found default section order', {
        sectionOrder,
        configId: defaultConfig.configData._id,
      });
    }

    // Create landing page template (ported from Python)
    const templateData = {
      communityCode: community.code,
      slug: community.link, // Use community link as slug
      sectionOrder,
      customSectionData: {}, // Empty by default, will be populated later
      overriddenData: {}, // Empty by default, for future customizations
      isDemo: false,
    };

    // Save with session support for transactions
    let savedTemplate;
    if (session) {
      // For transactions, use create with session
      const templates = await CommunityLandingPageTemplate.create(
        [templateData],
        { session }
      );
      savedTemplate = Array.isArray(templates) ? templates[0] : templates;
    } else {
      // For non-transactional operations
      savedTemplate = await CommunityLandingPageTemplate.create(
        templateData
      );
    }

    logger.info('Landing page template created successfully', {
      templateId: savedTemplate._id,
      slug: savedTemplate.slug,
      communityCode: community.code,
      sectionOrder: savedTemplate.sectionOrder,
    });

    return {
      template: savedTemplate,
      success: true,
    };
  } catch (error) {
    logger.error('Failed to create landing page template', {
      communityId: community._id,
      communityCode: community?.code,
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

module.exports = {
  createLandingPageTemplate,
};
