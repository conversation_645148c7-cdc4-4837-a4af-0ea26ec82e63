const CountryInfoMapping = require('@src/models/countryInfoMapping.model');
const logger = require('@services/logger.service');
const {
  DEFAULT_CURRENCY,
  DEFAULT_REVENUE_GOAL,
} = require('@src/constants/common');

/**
 * Get base currency for a country (following Python approach)
 * Checks CountryCurrencyMapping collection for localized pricing
 */
const getBaseCurrency = async (countryCode) => {
  try {
    if (!countryCode) {
      return DEFAULT_CURRENCY;
    }

    // Find country data from country_currency_mapping collection
    const countryData = await CountryInfoMapping.findOne({
      countryCode: countryCode.toUpperCase(),
    });

    if (!countryData) {
      logger.debug(
        'Country not found in mapping, using default currency',
        {
          countryCode,
          defaultCurrency: DEFAULT_CURRENCY,
        }
      );
      return DEFAULT_CURRENCY;
    }

    // Use local currency if localized pricing is enabled
    if (countryData.localisePrice) {
      logger.info('Using localized currency', {
        countryCode,
        currency: countryData.currencyCode,
      });
      return countryData.currencyCode;
    }

    return DEFAULT_CURRENCY;
  } catch (error) {
    logger.error('Error getting base currency', {
      error: error.message,
      countryCode,
    });
    return DEFAULT_CURRENCY;
  }
};

/**
 * Get Stripe payment method based on currency (following Python approach)
 * Special handling for INR → Stripe India
 */
const getStripePaymentMethod = (baseCurrency) => {
  const paymentMethods = [];

  // Use Stripe India for INR transactions
  if (baseCurrency === 'INR') {
    paymentMethods.push({
      value: 'stripe-india',
      label: 'Credit/Debit Card',
      icon: 'https://d2oi1rqwb0pj00.cloudfront.net/na-website/Payment/svg/bank.svg',
    });
  } else {
    paymentMethods.push({
      value: 'stripe',
      label: 'Credit/Debit Card',
      icon: 'https://d2oi1rqwb0pj00.cloudfront.net/na-website/Payment/svg/bank.svg',
    });
  }

  return paymentMethods;
};

/**
 * Get revenue goal based on currency (following Python DEFAULT_REVENUE_GOAL)
 */
const getRevenueGoal = (baseCurrency) => {
  // Use existing constants from common.js
  return DEFAULT_REVENUE_GOAL[baseCurrency] || DEFAULT_REVENUE_GOAL.USD;
};

module.exports = {
  getBaseCurrency,
  getStripePaymentMethod,
  getRevenueGoal,
};
