const ObjectId = require('mongoose').Types.ObjectId;
const logger = require('@services/logger.service');

// Models
const CommunityRoles = require('@src/communitiesAPI/models/communityRole.model');
const Users = require('@src/models/users.model');

// Services
const {
  createCommunityOwnerSubscriptions,
} = require('@src/communitiesAPI/services/common/communitySubscriptions.service');
const {
  communityCreatedEmails,
  communityCreatedReferralAlertEmails,
  communityCreatedReferralEmails,
} = require('@src/communitiesAPI/services/common/communityTriggers.service');
const actionEventService = require('@services/actionEvent');
const {
  MILESTONE_ACTIVITY_TYPES,
  DEFAULT_COMMUNITY_PROFILE_IMAGE,
} = require('@src/constants/common');

// Constants
const { aclRoles } = require('@src/communitiesAPI/constants');

/**
 * Optimized owner role creation for newly created communities
 * This will be implemented in the next step
 */
const createOwnerRoleForNewCommunity = async ({
  user,
  learner,
  community,
  referralCode,
  referrerFirstName,
  session,
}) => {
  try {
    logger.info('Creating owner role for new community (optimized)', {
      userId: user._id,
      communityId: community._id,
      communityCode: community.code,
    });

    // Step 1: Create owner role (no duplicate check needed for new community)
    const insertManager = {
      userObjectId: new ObjectId(user._id),
      email: user.email,
      communityObjectId: new ObjectId(community._id),
      communityCode: community.code,
      role: [
        aclRoles.OWNER,
        aclRoles.ADMIN,
        aclRoles.MANAGER,
        aclRoles.MEMBER,
      ],
    };

    // Create role directly (no need to check if exists for new community)
    await CommunityRoles.create([insertManager], {
      session,
    });

    logger.info('Owner role created', {
      communityId: community._id,
      roles: insertManager.role,
    });

    // Step 2: Update user's community_admin flag if needed (optimization: check first)
    if (!user.community_admin) {
      await Users.updateOne(
        { _id: user._id },
        { community_admin: true },
        { session }
      );
      logger.info('User community_admin flag updated');
    }

    // Step 3-4: Create both main and regional subscriptions optimized (batch + concurrent SQS)
    const subscriptionResult = await createCommunityOwnerSubscriptions({
      user,
      learner,
      community,
      session,
    });

    logger.info('Community owner subscriptions created successfully', {
      communityId: community._id,
      subscriptionsCreated: subscriptionResult.subscriptionsCreated,
      hasRegionalSubscription: !!subscriptionResult.regionalSubscription,
    });

    // Step 5: Send welcome email (if no referral code)
    if (!referralCode) {
      try {
        // Send welcome email to community owner
        await communityCreatedEmails(community.createdBy, community.By, {
          community_code: community.code,
          student_header_name: `Congratulations, ${community.By}!`,
          community_url: `${
            process.env.NAS_IO_FRONTEND_URL || 'https://nas.io'
          }${community.link}`,
          cm_portal_url: `${
            process.env.NAS_IO_FRONTEND_URL || 'https://nas.io'
          }/portal?activeCommunityId=${community._id}`,
          community_name: community.title,
          community_profile_image:
            community.thumbnailImgData?.mobileImgData?.src ??
            DEFAULT_COMMUNITY_PROFILE_IMAGE,
          isWhatsappExperienceCommunity:
            community.isWhatsappExperienceCommunity || false,
        });
        logger.info('Welcome email sent to community owner');
      } catch (error) {
        logger.warn('Welcome email failed (non-critical)', {
          error: error.message,
          communityId: community._id,
        });
      }
    }

    // Step 6: Send milestone event (always, regardless of referral code)
    try {
      await actionEventService.sendMilestoneEvent({
        actionEventType:
          MILESTONE_ACTIVITY_TYPES.MILESTONE_COMMUNITY_CREATED,
        communityCode: community.code,
        communityObjectId: community._id,
        learnerObjectId: learner._id,
      });
      logger.info('Community creation milestone event created');
    } catch (error) {
      logger.warn('Milestone event failed (non-critical)', {
        error: error.message,
        communityId: community._id,
      });
    }

    // Step 7: Handle referral code processing (if referral code exists)
    if (referralCode && community.referralCodeUsed) {
      try {
        // Send referral alert email to referrer
        await communityCreatedReferralAlertEmails(
          community.referrerEmail,
          referrerFirstName ?? community.referrerEmail,
          {
            community_url: `${
              process.env.NAS_IO_FRONTEND_URL || 'https://nas.io'
            }${community.link}`,
            community_name: community.title,
            community_host: community.By,
            community_profile_image:
              community.thumbnailImgData?.mobileImgData?.src ??
              DEFAULT_COMMUNITY_PROFILE_IMAGE,
          }
        );

        // Send referral emails to community creator
        await communityCreatedReferralEmails(
          community.createdBy,
          community.By,
          {
            cm_portal_url: `${
              process.env.NAS_IO_FRONTEND_URL || 'https://nas.io'
            }/portal/money?activeCommunityId=${community._id}`,
            referral_link: `${
              process.env.NAS_IO_FRONTEND_URL || 'https://nas.io'
            }?referral=${community.referralCodeUsed}`,
          }
        );

        logger.info('Referral emails sent successfully', {
          referralCode,
          referrerEmail: community.referrerEmail,
        });
      } catch (error) {
        logger.warn('Referral email sending failed (non-critical)', {
          error: error.message,
          referralCode,
          communityId: community._id,
        });
      }
    }

    // Community is already created with all necessary configs (optimization!)
    // No database update needed - this eliminates a major database operation
    logger.info(
      'Owner role setup completed (no community update needed)',
      {
        communityId: community._id,
        communityCode: community.code,
        optimization: 'community_created_complete',
        referralProcessed: !!community.referralCodeUsed,
      }
    );

    return {
      roles: insertManager.role,
      updatedCommunityData: {
        code: community.code,
        _id: community._id,
        By: community.By,
        isActive: community.isActive,
        isDraft: community.isDraft,
        createdBy: community.createdBy,
        restrictedInfo: {}, // Will be populated by fraud check later
      },
    };
  } catch (error) {
    logger.error('Error creating owner role for new community', {
      communityId: community._id,
      userId: user._id,
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

module.exports = {
  createOwnerRoleForNewCommunity,
};
