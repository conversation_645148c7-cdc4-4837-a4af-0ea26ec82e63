const Community = require('@src/communitiesAPI/models/community.model');
const CommunityRoles = require('@src/communitiesAPI/models/communityRole.model');
const Learner = require('@src/models/learners.model');
const logger = require('@services/logger.service');

// Services for config calculation
const {
  retrievePaymentFeeStructure,
} = require('@services/config/paymentFeeStructureConfig.service');
const feeService = require('@src/communitiesAPI/services/common/fee.service');
const chatService = require('@services/chat');
const nameUtils = require('@src/utils/name.util');

// Constants for config
const { BATCH_METADATA_MODEL_TYPE } = require('@src/constants/common');
const { aclRoles } = require('@src/communitiesAPI/constants');

// Constants
const COMMUNITY_SHORT_CODE_LENGTH = 6; // Increased from 4 to avoid collisions (2.2B combinations)

/**
 * Generate unique community code with conflict resolution
 * Based on community name + random string for uniqueness
 */
const generateUniqueCode = async (communityName) => {
  const baseCode = communityName
    .toUpperCase()
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .replace(/[^A-Z0-9_]/g, '') // Remove special characters except underscores
    .substring(0, 20); // Increased length for readability

  // Generate candidates with numeric suffixes (Python style)
  const candidates = [
    baseCode, // Try without suffix first
    ...Array.from({ length: 9 }, (_, i) => `${baseCode}_${i + 1}`),
  ];

  // Single DB query to check all candidates
  const existingCommunities = await Community.find({
    code: { $in: candidates },
  })
    .select('code')
    .lean();

  // Extract existing codes for comparison
  const existingCodes = existingCommunities.map((c) => c.code);

  // Find first available code
  const availableCode = candidates.find(
    (code) => !existingCodes.includes(code)
  );

  if (availableCode) {
    logger.debug('Generated unique community code', {
      baseCode,
      finalCode: availableCode,
      candidatesChecked: candidates.length,
    });
    return availableCode;
  }

  // Fallback: use timestamp if all candidates are taken
  const timestampCode = `${baseCode}_${Date.now().toString().slice(-6)}`;
  logger.warn('Using timestamp fallback for community code', {
    baseCode,
    finalCode: timestampCode,
    allCandidatesTaken: true,
  });

  return timestampCode;
};

/**
 * Generate unique community short code (6 characters)
 */
const generateUniqueShortCode = async () => {
  const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';

  // Generate 10 candidates (fewer needed with 6 chars - 2.2B combinations)
  const candidates = Array.from({ length: 10 }, () => {
    let shortCode = '';
    for (let i = 0; i < COMMUNITY_SHORT_CODE_LENGTH; i++) {
      shortCode += characters.charAt(
        Math.floor(Math.random() * characters.length)
      );
    }
    return shortCode;
  });

  // Single DB query to check all candidates
  const existingCommunities = await Community.find({
    communityShortCode: { $in: candidates },
  })
    .select('communityShortCode')
    .lean();

  // Extract existing short codes for comparison
  const existingCodes = existingCommunities.map(
    (c) => c.communityShortCode
  );

  // Find first available code
  const availableCode = candidates.find(
    (code) => !existingCodes.includes(code)
  );

  if (availableCode) {
    logger.debug('Generated unique community short code', {
      shortCode: availableCode,
      candidatesChecked: candidates.length,
    });
    return availableCode;
  }

  // If all 10 candidates are taken, it's extremely unlikely with 6 chars
  throw new Error(
    'Failed to generate unique community short code - all 10 candidates taken'
  );
};

/**
 * Generate unique community link/URL with conflict resolution
 */
const generateUniqueLink = async (communityName) => {
  const baseLink =
    '/' +
    communityName
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '') // Remove special chars except spaces
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .substring(0, 50); // Limit length

  const existingCommunity = await Community.findOne({
    link: baseLink,
  }).lean();

  if (!existingCommunity) {
    return baseLink;
  }

  // if link already exists, the count the number of links that start with baseLink followed by "-{number}".
  const countFilter = {
    link: { $regex: new RegExp(`^${baseLink}-\\d+$`, 'i') },
  };
  const count = await Community.countDocuments(countFilter);
  const candidates = Array.from({ length: 10 }, (_, i) => {
    return `${baseLink}-${count + i + 1}`;
  });

  // Use Atlas Search index for better performance with fallback
  // The communities_slug index on "link" field provides optimized searching
  let existingCommunities;
  try {
    existingCommunities = await Community.aggregate([
      {
        $search: {
          index: 'communities_slug',
          compound: {
            filter: [
              {
                term: {
                  path: 'link',
                  query: candidates,
                },
              },
            ],
          },
        },
      },
      {
        $project: {
          link: 1,
          _id: 0,
        },
      },
    ]);
  } catch (error) {
    // Fallback to regular MongoDB query if Atlas Search fails
    logger.warn('Atlas Search failed, using regular query', {
      error: error.message,
    });
    existingCommunities = await Community.find({
      link: { $in: candidates },
    })
      .collation({ locale: 'en', strength: 2 }) // Case-insensitive
      .select('link')
      .lean();
  }

  // Extract existing links for comparison. Convert to lowercase for case-insensitive comparison
  const existingLinksMap = existingCommunities.reduce((acc, c) => {
    const lowerCaseLink = c.link?.toLowerCase();
    if (!lowerCaseLink) return acc;

    acc[lowerCaseLink] = true;
    return acc;
  }, {});

  // Find first available link (case-insensitive comparison)
  const availableLink = candidates.find(
    (link) => !existingLinksMap[link.toLowerCase()]
  );

  if (availableLink) {
    // Link already has leading slash from candidates
    logger.debug('Generated unique community link', {
      baseLink,
      finalLink: availableLink,
      candidatesChecked: candidates.length,
    });
    return availableLink;
  }

  // Fallback: generate more random attempts if all candidates are taken

  // Generate random suffix using existing constant
  const generateRandomSuffix = () => {
    const characters = '0123456789';
    const SLUG_RANDOM_SUFFIX_LENGTH = 7;
    let result = '';
    for (let i = 0; i < SLUG_RANDOM_SUFFIX_LENGTH; i++) {
      result += characters.charAt(
        Math.floor(Math.random() * characters.length)
      );
    }
    return result;
  };

  const fallbackSuffix = generateRandomSuffix();
  const fallbackLink = `${baseLink}-${fallbackSuffix}`;
  logger.warn('Using random fallback for community link', {
    baseLink,
    finalLink: fallbackLink,
    allCandidatesTaken: true,
  });

  return fallbackLink;
};

/**
 * Get next available community ID (Optimized - no DB query needed)
 * Uses timestamp + random for uniqueness instead of sequential IDs
 */
const getNextCommunityId = async () => {
  // Generate unique ID using timestamp + random number
  // This avoids the expensive DB query entirely
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000000); // 6-digit random
  const nextId = parseInt(`${timestamp}${random}`.slice(-10), 10); // Take last 10 digits to keep reasonable size

  logger.debug('Generated unique community ID (optimized)', {
    nextId,
    method: 'timestamp_random',
    originalTimestamp: timestamp,
    randomComponent: random,
  });

  return nextId;
};

/**
 * Process and clean community name
 */
const processName = (name) => {
  if (!name || typeof name !== 'string') {
    throw new Error('Community name is required and must be a string');
  }

  // Trim and normalize whitespace
  const processed = name.trim().replace(/\s+/g, ' ');

  if (processed.length === 0) {
    throw new Error('Community name cannot be empty');
  }

  if (processed.length > 100) {
    throw new Error('Community name cannot exceed 100 characters');
  }

  return processed;
};

const {
  getBaseCurrency,
  getStripePaymentMethod,
  getRevenueGoal,
} = require('./currencyMapping.service');

/**
 * Process referral code and return referral data for community creation
 */
const processReferralCode = async ({ user, learner, referralCode }) => {
  // Default values
  let referralCodeUsed = null;
  let referrerEmail = null;
  let referrerObjectId = null;
  let referrerFirstName = null;

  if (!user || !learner || !referralCode) {
    return {
      referralCodeUsed,
      referrerEmail,
      referrerObjectId,
      referrerFirstName,
    };
  }

  try {
    // Check if user has existing manager role
    const existingManager = await CommunityRoles.findOne({
      email: user.email,
      role: aclRoles.MANAGER,
    }).lean();

    // Find referrer by referral code
    const referrer = await Learner.findOne(
      { referralCode, isActive: true },
      { email: 1, firstName: 1 }
    ).lean();

    // Only process referral if user is not already a manager and referrer exists
    if (referrer && !existingManager) {
      referralCodeUsed = referralCode;
      referrerEmail = referrer.email;
      referrerObjectId = referrer._id;
      referrerFirstName = referrer.firstName;

      logger.info('Referral code processed during community creation', {
        referralCode,
        referrerEmail,
        userEmail: user.email,
      });
    } else {
      logger.info('Referral code not applied', {
        referralCode,
        hasReferrer: !!referrer,
        hasExistingManager: !!existingManager,
        userEmail: user.email,
      });
    }
  } catch (error) {
    logger.warn('Referral processing failed during community creation', {
      error: error.message,
      referralCode,
      userEmail: user.email,
    });
    // Continue without referral if it fails
  }

  return {
    referralCodeUsed,
    referrerEmail,
    referrerObjectId,
    referrerFirstName,
  };
};

/**
 * Create community data object with all required fields
 */
const createCommunityDataObject = async ({
  name,
  description = '',
  category,
  timezone,
  expectedSize,
  communityGoal,
  country,
  countryCode,
  trainerId,
  trackingData,
  thumbnailImgData,
  communityCheckoutCardData,
  baseCurrency, // Allow override from request
  // New parameters for creating community in final active state
  user = null,
  learner = null,
  referralCode = null,
  shouldReceiveBonus = false, // Bonus eligibility flag
}) => {
  // Process and validate name
  const processedName = processName(name);

  // Determine currency configuration - use override or detect from country
  const finalBaseCurrency =
    baseCurrency || (await getBaseCurrency(countryCode));
  const paymentMethods = getStripePaymentMethod(finalBaseCurrency);
  const revenueGoal = getRevenueGoal(finalBaseCurrency);

  // Calculate basic operational configs (fee configs will be calculated after community object is built)
  let batchMetadataEnabledType = {};
  let messageSettings = {};

  if (user && learner) {
    // Calculate batch metadata settings
    batchMetadataEnabledType = Object.keys(
      BATCH_METADATA_MODEL_TYPE
    ).reduce((acc, modelType) => {
      acc[modelType] = true;
      return acc;
    }, {});

    // Get default message settings
    messageSettings =
      chatService.settingService.retrieveDefaultMessageSettings();
  }

  // Process referral code during community creation (optimization)
  const {
    referralCodeUsed,
    referrerEmail,
    referrerObjectId,
    referrerFirstName,
  } = await processReferralCode({
    user,
    learner,
    referralCode,
  });

  // Generate unique identifiers
  const [communityId, code, shortCode, link] = await Promise.all([
    getNextCommunityId(),
    generateUniqueCode(processedName),
    generateUniqueShortCode(),
    generateUniqueLink(processedName),
  ]);

  // Build community data object
  const communityData = {
    // Core identifiers
    communityId,
    name: processedName,
    title: processedName, // Use same as name
    code,
    communityShortCode: shortCode,
    link,

    // Basic information
    description: description || '',
    category,
    timezone,
    expectedSize,
    communityGoal,
    countryCreatedIn: country,

    // Status flags - create community in final active state (optimization)
    isActive: true, // Active from creation (no need to update later)
    isDraft: false, // Not draft (complete community)
    isFreeCommunity: true,
    isWaitlist: false,
    unmanaged: true,

    // Payment and revenue
    baseCurrency: finalBaseCurrency,
    revenueGoal,
    payment_methods: paymentMethods,
    showEventRegistrationPrice: true,
    passOnPaymentGatewayFee: false, // Will be updated after fee calculation
    passOnTakeRate: false, // Will be updated after fee calculation
    basePayoutFeeConfigs: [], // Will be updated after fee calculation

    // Relationships
    trainerId,
    // Owner information (set during creation for optimization)
    By:
      user && learner
        ? nameUtils.getName(learner.firstName, learner.lastName)
        : null,
    createdBy: user ? user.email : null,

    // Referral information (processed during community creation for optimization)
    referralCodeUsed,
    referrerEmail,
    referrerObjectId,

    // Configuration
    request_approval: false,
    applicationConfig: {
      autoApproval: false,
    },
    config: {
      batchMetadataEnabledType,
      messageSettings,
      // Set bonus flag if eligible (optimization: set during creation)
      aug2024CampaignRewardReceived: shouldReceiveBonus,
    },

    // Content and engagement
    robotContentIndexed: true,
    memberBenefits: [],
    platforms: [],

    // Images - use proper structure from imageConfig
    ...(thumbnailImgData && { thumbnailImgData }),
    ...(communityCheckoutCardData && { communityCheckoutCardData }),

    // Analytics and tracking
    trackingData,

    // Timestamps
    lastModifiedTimeStamp: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  // Calculate fee configs using the complete community object (optimization: done during creation)
  if (user && learner) {
    try {
      // Calculate fee structure and settings
      const paymentFeeStructure = await retrievePaymentFeeStructure({
        baseCurrency: finalBaseCurrency,
        planType: communityData.config?.planType || null,
      });

      const {
        feeConfig: basePayoutFeeConfig,
        passOnTakeRate,
        passOnPaymentGatewayFee,
      } = await feeService.retrieveBasePayoutFeeConfigAndOtherSettings({
        community: communityData, // Use complete community object
        paymentFeeStructure,
      });

      // Update community data with calculated fee configs
      communityData.passOnTakeRate = passOnTakeRate;
      communityData.passOnPaymentGatewayFee = passOnPaymentGatewayFee;
      communityData.basePayoutFeeConfigs = [basePayoutFeeConfig];

      logger.info('Fee configs calculated and added to community', {
        communityId,
        passOnTakeRate,
        passOnPaymentGatewayFee,
        hasFeeConfig: !!basePayoutFeeConfig,
      });
    } catch (error) {
      logger.error('Fee config calculation failed', {
        error: error.message,
        communityId,
        baseCurrency: finalBaseCurrency,
      });
      throw new Error(
        `Failed to calculate fee configuration: ${error.message}`
      );
    }
  }

  logger.info('Created community data object with complete config', {
    communityId,
    name: processedName,
    code,
    shortCode,
    link,
    baseCurrency: finalBaseCurrency, // Fixed: use actual currency, not input parameter
    country,
    isActive: !!(user && learner),
    isDraft: !(user && learner),
    hasOperationalConfigs: !!(user && learner),
  });

  return { communityDataObject: communityData, referrerFirstName };
};

module.exports = {
  generateUniqueCode,
  generateUniqueShortCode,
  generateUniqueLink,
  getNextCommunityId,
  processName,
  createCommunityDataObject,
};
