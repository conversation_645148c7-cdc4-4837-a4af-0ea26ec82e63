const yup = require('yup');

const socialMediaSchema = yup.object().shape({
  type: yup.string().notRequired().lowercase().trim(),
  link: yup.string().notRequired().trim(),
  url: yup.string().notRequired().trim(),
  username: yup.string().notRequired().trim(),
});

const communitySchema = yup.object().shape({
  name: yup.string().trim().required().min(1).max(100),
  description: yup.string().trim().max(500),
  category: yup
    .string()
    .trim()
    .oneOf([
      'business',
      'technology',
      'health',
      'education',
      'entertainment',
      'lifestyle',
      'other',
    ]),
  timezone: yup.string().trim(),
  thumbnailImageUrl: yup.string().url().nullable(),
  expectedSize: yup.number().positive().integer(),
  communityGoal: yup.string().trim().max(500),
  otherCategory: yup.string().when('category', {
    is: 'other',
    then: yup.string().required(),
    otherwise: yup.string().nullable(),
  }),
  // Add missing fields that were being filtered out
  trackingData: yup.object().notRequired(),
  languagePreference: yup.string().trim().notRequired(),
  baseCurrency: yup.string().trim().notRequired(),
});

const createCompleteSchema = yup.object().shape({
  community: communitySchema.required(),
  socialMedia: yup.array(socialMediaSchema).notRequired(),
  referralCode: yup.string().notRequired().nullable(),
});

module.exports = {
  createCompleteSchema,
};
