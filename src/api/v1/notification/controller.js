const httpStatus = require('http-status');
const logger = require('../../../services/logger.service');
const schema = require('./schema');
const notificationManagementMailService = require('../../../services/notification/management/mail.service');
const notificationPreferenceMailService = require('../../../services/notification/preferences/emailPreferences.service');
const notificationService = require('../../../services/communityNotification');

exports.unsubscribeFromMailType = async (req, res, next) => {
  try {
    const body = await schema.unsubAndResubSchema.cast(req.body);

    const response = await notificationManagementMailService.unsubscribe(
      body
    );

    return res.json({ data: response });
  } catch (err) {
    logger.error('unsubscribeFromMailType failed due to', err.message);
    return next(err);
  }
};

exports.resubscribeToMailType = async (req, res, next) => {
  try {
    const body = await schema.unsubAndResubSchema.cast(req.body);

    const response = await notificationManagementMailService.resubscribe(
      body
    );

    return res.json({ data: response });
  } catch (err) {
    logger.error('resubscribeToMailType failed due to', err.message);
    return next(err);
  }
};

exports.getNotificationsPreferencesForCM = async (req, res, next) => {
  try {
    const user = req.user;

    const communityId = req.params.communityId;

    const { mailTarget } = schema.notificationPreferenceForCMSchema.cast(
      req.query
    );

    const resp =
      await notificationPreferenceMailService.getNotificationPreferencesForCMService(
        {
          communityId,
          userInfo: user,
          mailTarget,
        }
      );

    res.json({ data: resp });
  } catch (err) {
    logger.error(
      'updateCommunityMailContent failed due to',
      err.message,
      err.stack
    );

    res
      .status(err?.status || httpStatus.INTERNAL_SERVER_ERROR)
      .json({ error: err?.message ?? 'Internal Server Error' });
  }
};

exports.getNotificationsPreferenceForMember = async (req, res, next) => {
  try {
    const user = req.user;

    const response =
      await notificationPreferenceMailService.getNotificationPreferencesForMemberService(
        {
          userInfo: user,
        }
      );
    return res.json({ data: response, status: 'success' });
  } catch (error) {
    logger.error(
      'getNotificationsPreferenceForMember failed due to',
      error.message,
      error.stack
    );
    res
      .status(error?.status || httpStatus.INTERNAL_SERVER_ERROR)
      .json({ error: error?.message ?? 'Internal Server Error' });
  }
};

exports.updateNotificationPreference = async (req, res, next) => {
  try {
    const { triggerName, notificationEnabled } = req.body || {};
    const { communityId } = req.params || {};
    const user = req.user;
    const { learner } = user;

    const response =
      await notificationPreferenceMailService.updateNotificationPreferenceService(
        {
          notificationEnabled,
          learnerObjectId: learner._id,
          communityId,
          triggerName,
        }
      );

    res.status(200).json({ data: response, status: 'success' });
  } catch (error) {
    logger.error(
      'updateNotificationPreferenceForMember failed due to',
      error.message,
      error.stack
    );
    res
      .status(error?.status || httpStatus.INTERNAL_SERVER_ERROR)
      .json({ error: error?.message ?? 'Internal Server Error' });
  }
};

exports.sendEnrollmentNotification = async (req, res, next) => {
  try {
    const { community, purchaseTransaction, learner, application } =
      await schema.enrollmentEmailSchema.cast(req.body);

    const response = await notificationService.sendEnrollmentNotification({
      community,
      learner,
      purchaseTransaction,
      application,
    });

    return res.json({ data: response });
  } catch (err) {
    logger.error(
      'sendEnrollmentNotification failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};

exports.sendRenewalFailureNotification = async (req, res, next) => {
  try {
    const {
      communityId,
      learnerObjectId,
      purchaseTransactionObjectId,
      paymentMetadata,
    } = await schema.renewalFailureSchema.cast(req.body);

    const response =
      await notificationService.sendRenewalFailureNotification({
        purchaseTransactionObjectId,
        communityId,
        learnerObjectId,
        paymentMetadata,
      });

    return res.json({ data: response });
  } catch (err) {
    logger.error(
      'sendRenewalFailureNotification failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};

exports.sendAffiliateSaleNotification = async (req, res, next) => {
  try {
    const {
      paidAmount,
      currency,
      transactionCreatedAt,
      commissionEarningAmount,
      commissionEarningCurrency,
      entityTitle,
      communityObjectId,
      learnerObjectId,
    } = await schema.affiliateSaleEmailSchema.cast(req.body);

    const response =
      await notificationService.sendAffiliateSaleNotification({
        paidAmount,
        currency,
        transactionCreatedAt,
        commissionEarningAmount,
        commissionEarningCurrency,
        entityTitle,
        communityObjectId,
        learnerObjectId,
      });

    return res.json({ data: response });
  } catch (err) {
    logger.error(
      'sendAffiliateSaleNotification failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};

exports.sendSaleNotification = async (req, res, next) => {
  try {
    const {
      paidAmount,
      currency,
      transactionCreatedAt,
      entityTitle,
      entityLink,
      communityObjectId,
      learnerObjectId,
    } = await schema.saleEmailSchema.cast(req.body);

    const response = await notificationService.sendSaleNotification({
      paidAmount,
      currency,
      transactionCreatedAt,
      entityTitle,
      entityLink,
      communityObjectId,
      learnerObjectId,
    });

    return res.json({ data: response });
  } catch (err) {
    logger.error(
      'sendSaleNotification failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};

exports.sendPurchasePlanNotification = async (req, res, next) => {
  try {
    const {
      planOrderObjectId,
      communityObjectId,
      learnerObjectId,
      amount,
      currency,
      nextBillingAmount,
      sendMail,
      sendLarkNoti,
    } = await schema.purchasePlanNotificationSchema.cast(req.body);

    const response =
      await notificationService.sendPurchasePlanNotification({
        planOrderObjectId,
        communityObjectId,
        learnerObjectId,
        amount,
        currency,
        nextBillingAmount,
        sendMail,
        sendLarkNoti,
      });

    return res.json({ data: response });
  } catch (err) {
    logger.error(
      'sendPurchasePlanNotification failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};

exports.sendCancelledPlanNotification = async (req, res, next) => {
  try {
    const {
      planOrderObjectId,
      communityObjectId,
      learnerObjectId,
      subscriptionExpiryDate,
      failureReason,
      sendMail,
      sendLarkNoti,
    } = await schema.cancelledPlanNotificationSchema.cast(req.body);

    const response =
      await notificationService.sendCancelledPlanNotification({
        planOrderObjectId,
        communityObjectId,
        learnerObjectId,
        subscriptionExpiryDate,
        failureReason,
        sendMail,
        sendLarkNoti,
      });

    return res.json({ data: response });
  } catch (err) {
    logger.error(
      'sendCancelledPlanNotification failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};

exports.sendRenewalFailurePlanNotification = async (req, res, next) => {
  try {
    const {
      planOrderObjectId,
      communityObjectId,
      learnerObjectId,
      paymentMetadata,
    } = await schema.renewalFailurePlanNotificationSchema.cast(req.body);

    const response =
      await notificationService.sendRenewalFailurePlanNotification({
        planOrderObjectId,
        communityObjectId,
        learnerObjectId,
        paymentMetadata,
      });

    return res.json({ data: response });
  } catch (err) {
    logger.error(
      'sendRenewalFailurePlanNotification failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};

exports.sendPlanReferralRewardNotification = async (req, res, next) => {
  try {
    const {
      communityObjectId,
      refereeCommunityObjectId,
      rewardAmount,
      currency,
      recurringRewardAmount,
      isFirstBillingCycle,
      planType,
    } = await schema.planReferralRewardNotificationSchema.cast(req.body);

    const response =
      await notificationService.sendPlanReferralRewardNotification({
        communityObjectId,
        refereeCommunityObjectId,
        rewardAmount,
        currency,
        recurringRewardAmount,
        isFirstBillingCycle,
        planType,
      });

    return res.json({ data: response });
  } catch (err) {
    logger.error(
      'sendPlanReferralRewardNotification failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};

exports.sendMembersLimitReachingNotification = async (req, res, next) => {
  try {
    const { communityObjectId } =
      await schema.membersLimitReachingNotificationSchema.cast(req.body);

    const response =
      await notificationService.sendMembersLimitReachingNotification({
        communityObjectId,
      });

    return res.json({ data: response });
  } catch (err) {
    logger.error(
      'sendMembersLimitReachingNotification failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};

exports.sendMembersLimitReachedNotification = async (req, res, next) => {
  try {
    const { communityObjectId } =
      await schema.membersLimitReachedNotificationSchema.cast(req.body);

    const response =
      await notificationService.sendMembersLimitReachedNotification({
        communityObjectId,
      });

    return res.json({ data: response });
  } catch (err) {
    logger.error(
      'sendMembersLimitReachedNotification failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};
