const yup = require('yup');
const {
  ALLOWED_CURRENCIES,
  COMMUNITY_REFERRAL_REWARD_PLAN_TYPE,
} = require('../../../services/communityReferralRewardTemplate/constants');
const {
  GET_REFEREE_SORT_BY,
  GET_REFEREE_STATUS_FILTER,
} = require('../../../services/communityReferral/constants');

exports.communityIdSchema = yup.object().shape({
  communityId: yup.string().required(),
});

exports.templateIdSchema = yup.object().shape({
  templateId: yup.string().required(),
});

exports.getReferrerRewardsSchema = yup.object().shape({
  communityReferralCode: yup.string().uppercase().trim().required(),
  planType: yup.string().uppercase().trim().required(),
  refereeCommunityObjectId: yup.string().trim().required(),
});

exports.getRefereeListSchema = yup.object().shape({
  status: yup
    .string()
    .uppercase()
    .notRequired()
    .trim()
    .oneOf(Object.values(GET_REFEREE_STATUS_FILTER)),
  pageSize: yup.number().default(20).required().integer(),
  pageNo: yup.number().default(1).required().integer(),
  sortBy: yup
    .string()
    .default(GET_REFEREE_SORT_BY.JOIN_DATE)
    .notRequired()
    .trim()
    .oneOf(Object.values(GET_REFEREE_SORT_BY)),
  sortOrder: yup.number().default(-1).notRequired(),
});

exports.getCommunityReferralDetailsSchema = yup.object().shape({
  communityReferralCode: yup.string().uppercase().trim().required(),
  planType: yup.string().uppercase().trim().required(),
  communityObjectId: yup.string().trim().notRequired().nullable(),
});

exports.createCommunityReferralRewardTemplateSchema = yup.object().shape({
  name: yup.string().trim().required(),
  planType: yup
    .string()
    .uppercase()
    .trim()
    .oneOf(Object.values(COMMUNITY_REFERRAL_REWARD_PLAN_TYPE))
    .required(),
  isDisabled: yup.boolean().default(false).required(),
  effectiveTimeStart: yup.date().nullable(),
  effectiveTimeEnd: yup.date().nullable(),
  applyToAll: yup.boolean().required(),
  communityObjectIds: yup.array().default([]).notRequired(),
  isRefereeTrialBillingPlanEnabled: yup.boolean().required(),
  refereeRecurringReward: yup.object().shape({
    'month-1': yup
      .array()
      .of(
        yup.object().shape({
          currency: yup
            .string()
            .uppercase()
            .oneOf(Object.values(ALLOWED_CURRENCIES))
            .required(),
          amount: yup.number().required(),
        })
      )
      .default([])
      .notRequired(),
    'year-1': yup
      .array()
      .of(
        yup.object().shape({
          currency: yup
            .string()
            .uppercase()
            .oneOf(Object.values(ALLOWED_CURRENCIES))
            .required(),
          amount: yup.number().required(),
        })
      )
      .default([])
      .notRequired(),
  }),
  referrerRecurringReward: yup.object().shape({
    'month-1': yup
      .array()
      .of(
        yup.object().shape({
          currency: yup
            .string()
            .uppercase()
            .oneOf(Object.values(ALLOWED_CURRENCIES))
            .required(),
          amount: yup.number().required(),
        })
      )
      .default([])
      .notRequired(),
    'year-1': yup
      .array()
      .of(
        yup.object().shape({
          currency: yup
            .string()
            .uppercase()
            .oneOf(Object.values(ALLOWED_CURRENCIES))
            .required(),
          amount: yup.number().required(),
        })
      )
      .default([])
      .notRequired(),
  }),
  referrerUpfrontReward: yup.object().shape({
    'month-1': yup
      .array()
      .of(
        yup.object().shape({
          currency: yup
            .string()
            .uppercase()
            .oneOf(Object.values(ALLOWED_CURRENCIES))
            .required(),
          amount: yup.number().required(),
        })
      )
      .default([])
      .notRequired(),
    'year-1': yup
      .array()
      .of(
        yup.object().shape({
          currency: yup
            .string()
            .uppercase()
            .oneOf(Object.values(ALLOWED_CURRENCIES))
            .required(),
          amount: yup.number().required(),
        })
      )
      .default([])
      .notRequired(),
  }),
});

exports.updateCommunityReferralRewardTemplateSchema = yup.object().shape({
  communityCodes: yup
    .array()
    .of(yup.string().uppercase().trim())
    .notRequired(),
  communityObjectIds: yup.array().notRequired(),
  name: yup.string().trim().notRequired(),
  planType: yup
    .string()
    .uppercase()
    .trim()
    .oneOf(Object.values(COMMUNITY_REFERRAL_REWARD_PLAN_TYPE))
    .notRequired(),
  isDisabled: yup.boolean().notRequired(),
  effectiveTimeStart: yup.date().nullable().notRequired(),
  effectiveTimeEnd: yup.date().nullable().notRequired(),
  applyToAll: yup.boolean().notRequired(),
  isRefereeTrialBillingPlanEnabled: yup.boolean().notRequired(),
  refereeRecurringReward: yup
    .object()
    .shape({
      'month-1': yup
        .array()
        .of(
          yup.object().shape({
            currency: yup
              .string()
              .uppercase()
              .oneOf(Object.values(ALLOWED_CURRENCIES))
              .required(),
            amount: yup.number().required(),
          })
        )
        .default([])
        .notRequired(),
      'year-1': yup
        .array()
        .of(
          yup.object().shape({
            currency: yup
              .string()
              .uppercase()
              .oneOf(Object.values(ALLOWED_CURRENCIES))
              .required(),
            amount: yup.number().required(),
          })
        )
        .default([])
        .notRequired(),
    })
    .nullable()
    .default(null)
    .notRequired(),
  referrerRecurringReward: yup
    .object()
    .shape({
      'month-1': yup
        .array()
        .of(
          yup.object().shape({
            currency: yup
              .string()
              .uppercase()
              .oneOf(Object.values(ALLOWED_CURRENCIES))
              .required(),
            amount: yup.number().required(),
          })
        )
        .default([])
        .notRequired(),
      'year-1': yup
        .array()
        .of(
          yup.object().shape({
            currency: yup
              .string()
              .uppercase()
              .oneOf(Object.values(ALLOWED_CURRENCIES))
              .required(),
            amount: yup.number().required(),
          })
        )
        .default([])
        .notRequired(),
    })
    .nullable()
    .default(null)
    .notRequired(),
  referrerUpfrontReward: yup
    .object()
    .shape({
      'month-1': yup
        .array()
        .of(
          yup.object().shape({
            currency: yup
              .string()
              .uppercase()
              .oneOf(Object.values(ALLOWED_CURRENCIES))
              .required(),
            amount: yup.number().required(),
          })
        )
        .default([])
        .notRequired(),
      'year-1': yup
        .array()
        .of(
          yup.object().shape({
            currency: yup
              .string()
              .uppercase()
              .oneOf(Object.values(ALLOWED_CURRENCIES))
              .required(),
            amount: yup.number().required(),
          })
        )
        .default([])
        .notRequired(),
    })
    .nullable()
    .default(null)
    .notRequired(),
});
