const moment = require('moment');
const schema = require('./schema');
const logger = require('../../../services/logger.service');
const PayoutServices = require('../../../services/payout/retrievePayout.service');
const CreatePayoutServices = require('../../../services/payout/createPayout.service');
const CreatePayoutTransactionServices = require('../../../services/payout/createPayoutTransaction.service');
const AddAdjustmentServices = require('../../../services/payout/addAdjustment.service');
const UpdatePayoutServices = require('../../../services/payout/updatePayoutStatus.service');
const StripeConnectServices = require('../../../services/payout/stripeConnect.service');
const RetrievePayoutAccountServices = require('../../../services/payout/retrievePayoutAccount.service');
const UpdatePayoutBankAccountService = require('../../../services/payout/updatePayoutBankAccount.service');
const {
  COMMUNITY_PAYOUT_ACCOUNT_TYPE,
} = require('../../../services/payout/constants');

exports.getPayout = async (req, res, next) => {
  try {
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );

    const { pageNo, pageSize } = await schema.filterQuerySchema.cast(
      req.query
    );

    logger.info(`Get payout for ${communityId}`);
    const payoutList = await PayoutServices.retrievePayouts({
      communityId,
      pageNo,
      pageSize,
    });

    return res.json({ data: payoutList });
  } catch (err) {
    logger.error(`Failed to get payout, ${err.message}`);
    return next(err);
  }
};

exports.createPayoutTransaction = async (req, res, next) => {
  try {
    const { payoutObjectId } =
      await schema.createPayoutTransactionSchema.cast(req.body);

    await CreatePayoutTransactionServices.createPayoutTransaction({
      payoutObjectId,
    });

    return res.json({ data: {} });
  } catch (err) {
    logger.error(`Failed to create payout transaction, ${err.message}`);
    return next(err);
  }
};

exports.createAdvancePayout = async (req, res, next) => {
  try {
    const {
      communityCode,
      currency,
      transactionCreatedFrom,
      transactionCreatedTo,
      reason,
      operator,
    } = await schema.createAdvancePayoutSchema.cast(req.body);

    let utcDate = moment.utc(transactionCreatedFrom, 'YYYY-MM-DD');
    const transactionCreatedFromInDate = utcDate.toDate();
    utcDate = moment.utc(transactionCreatedTo, 'YYYY-MM-DD');
    const transactionCreatedToInDate = utcDate.add(1, 'days').toDate();

    logger.info(
      `Create advance payout for ${communityCode}: ${transactionCreatedFromInDate} - ${transactionCreatedToInDate}`
    );
    await CreatePayoutServices.createAdvancePayout(
      communityCode,
      currency,
      transactionCreatedFromInDate,
      transactionCreatedToInDate,
      reason,
      operator
    );

    return res.json({ data: {} });
  } catch (err) {
    logger.error(`Failed to create advance payout, ${err.message}`);
    return next(err);
  }
};

exports.addAdjustments = async (req, res, next) => {
  try {
    const {
      communityCode,
      amount,
      currency,
      transactionReferenceId,
      reason,
      operator,
      adjustmentType,
    } = await schema.addAdjustmentSchema.cast(req.body);

    const result = await AddAdjustmentServices.addAdjustment({
      communityCode,
      amount,
      currency,
      transactionReferenceId,
      reason,
      operator,
      adjustmentType,
    });

    return res.json({ data: result });
  } catch (err) {
    logger.error(`Failed to add adjustment, ${err.message}`);
    return next(err);
  }
};

exports.updatePayoutToPaid = async (req, res, next) => {
  try {
    const { payoutIds, communityCodes, operator, payoutTargetType } =
      await schema.updatePayoutToPaidSchema.cast(req.body);

    const failureResult = await UpdatePayoutServices.updatePayout(
      payoutIds,
      communityCodes,
      payoutTargetType,
      operator
    );

    return res.json({ data: { failureResult } });
  } catch (err) {
    logger.error(`Failed to update payout to paid, ${err.message}`);
    return next(err);
  }
};

exports.updatePayoutToPendingReply = async (req, res, next) => {
  try {
    const { payoutIds, communityCodes, operator, payoutTargetType } =
      await schema.updatePayoutToPendingReplySchema.cast(req.body);

    const failureResult =
      await UpdatePayoutServices.updatePayoutToPendingReply(
        payoutIds,
        communityCodes,
        payoutTargetType,
        operator
      );

    return res.json({ data: { failureResult } });
  } catch (err) {
    logger.error(
      `Failed to update payout to pending reply, ${err.message}`
    );
    return next(err);
  }
};

exports.updatePayoutByWebhook = async (req, res, next) => {
  try {
    const {
      payoutObjectId,
      status,
      failureCode,
      failureReason,
      stripePayoutStatus,
      payoutTargetType,
      payoutDate,
    } = await schema.updatePayoutByWebhookSchema.cast(req.body);

    const failureResult = await UpdatePayoutServices.updatePayoutByWebhook(
      {
        payoutObjectId,
        status,
        failureCode,
        failureReason,
        stripePayoutStatus,
        payoutTargetType,
        payoutDate,
      }
    );

    return res.json({ data: { failureResult } });
  } catch (err) {
    logger.error(`Failed to update payout to paid, ${err.message}`);
    return next(err);
  }
};

exports.updatePayoutBankAccount = async (req, res, next) => {
  try {
    const { payoutIds, communityCodes, payoutChannel, operator } =
      await schema.updatePayoutBankAccountSchema.cast(req.body);
    const failureResult =
      await UpdatePayoutBankAccountService.updatePayoutBankAccount(
        payoutIds,
        communityCodes,
        payoutChannel,
        operator
      );
    return res.json({ data: { failureResult } });
  } catch (err) {
    logger.error(`Failed to update payout bank account, ${err.message}`);
    return next(err);
  }
};

exports.getCommunityPayoutAccount = async (req, res, next) => {
  try {
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );

    const result =
      await RetrievePayoutAccountServices.getCommunityPayoutAccounts({
        communityId,
      });

    return res.json(result);
  } catch (err) {
    logger.error(`Failed to get payout accounts, ${err.message}`);
    return next(err);
  }
};

exports.addCommunityPayoutAccount = async (req, res, next) => {
  try {
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );

    const { payoutAccountType, metadata } =
      await schema.addCommunityPayoutAccountSchema.cast(req.body);

    let accountInfo = {};
    switch (payoutAccountType) {
      case COMMUNITY_PAYOUT_ACCOUNT_TYPE.STRIPE_CONNECT:
        accountInfo = await StripeConnectServices.onboardStripeConnect({
          communityId,
          ...metadata,
        });
        break;
      default:
    }

    return res.json({ data: accountInfo });
  } catch (err) {
    logger.error(`Failed to onboard stripe connect, ${err.message}`);
    return next(err);
  }
};

exports.updateStripeConnectAccount = async (req, res, next) => {
  try {
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );

    const { payoutAccountType, metadata } =
      await schema.addCommunityPayoutAccountSchema.cast(req.body);

    let accountInfo = {};
    switch (payoutAccountType) {
      case COMMUNITY_PAYOUT_ACCOUNT_TYPE.STRIPE_CONNECT:
        accountInfo =
          await StripeConnectServices.getAccountLinkToUpdateStripeConnectAccount(
            {
              communityId,
              ...metadata,
            }
          );
        break;
      default:
    }

    return res.json({ data: accountInfo });
  } catch (err) {
    logger.error(`Failed to get stripe connect link, ${err.message}`);
    return next(err);
  }
};

exports.deleteCommunityPayoutAccount = async (req, res, next) => {
  try {
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );

    const { payoutAccountType } =
      await schema.deleteCommunityPayoutAccountSchema.cast(req.body);

    let accountInfo = {};
    switch (payoutAccountType) {
      case COMMUNITY_PAYOUT_ACCOUNT_TYPE.STRIPE_CONNECT:
        accountInfo =
          await StripeConnectServices.deleteStripeConnectAccount({
            communityId,
          });
        break;
      default:
    }

    return res.json({ data: accountInfo });
  } catch (err) {
    logger.error(
      `Failed to delete stripe connect account, ${err.message}`
    );
    return next(err);
  }
};

// This is for FE to render the notification banner
exports.createStripeConnectAccountSession = async (req, res, next) => {
  try {
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );

    const accountSession =
      await StripeConnectServices.createStripeConnectAccountSession({
        communityId,
      });

    return res.json({ data: accountSession });
  } catch (err) {
    logger.error(
      `Failed to create stripe connect account session, ${err.message}`
    );
    return next(err);
  }
};

exports.updateStripeConnectAccountByWebhookEvents = async (
  req,
  res,
  next
) => {
  try {
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );

    const { stripeAccountId, accountDetails } =
      await schema.stripeConnectWebhookEventsSchema.cast(req.body);

    const accountInfo =
      await StripeConnectServices.updateStripeConnectAccountByWebhookEvents(
        {
          communityId,
          stripeAccountId,
          accountDetails,
        }
      );

    return res.json({ data: accountInfo });
  } catch (err) {
    logger.error(
      `Failed to update account by stripe webhook event, ${err.message}`
    );
    return next(err);
  }
};
