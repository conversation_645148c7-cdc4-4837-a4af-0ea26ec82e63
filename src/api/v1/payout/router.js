const { Router } = require('express');
const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  validateAll,
} = require('../../../middleware/validator.middleware');
const copsTokenMiddleware = require('../../../middleware/cops-token.middleware');
const apiKeyValidation = require('../../../validations/apiKey.validation');

const router = Router({ mergeParams: true });

router.route('/').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  validateAll([
    { schema: schema.filterQuerySchema, location: 'query' },
    {
      schema: schema.communityIdSchema,
      location: 'params',
    },
  ]),
  controller.getPayout
);

router.route('/createPayoutTransaction').post(
  postRoutePreHandlerMiddleware,
  apiKeyValidation, // request from our internal services
  validateAll([
    {
      schema: schema.createPayoutTransactionSchema,
      location: 'body',
    },
  ]),
  controller.createPayoutTransaction
);

router.route('/createAdvancePayout/cops').post(
  postRoutePreHandlerMiddleware,
  copsTokenMiddleware,
  validateAll([
    {
      schema: schema.createAdvancePayoutSchema,
      location: 'body',
    },
  ]),
  controller.createAdvancePayout
);

router.route('/addAdjustment/cops').post(
  postRoutePreHandlerMiddleware,
  copsTokenMiddleware,
  validateAll([
    {
      schema: schema.addAdjustmentSchema,
      location: 'body',
    },
  ]),
  controller.addAdjustments
);

router.route('/updatePayoutToPaid/cops').post(
  postRoutePreHandlerMiddleware,
  copsTokenMiddleware,
  validateAll([
    {
      schema: schema.updatePayoutToPaidSchema,
      location: 'body',
    },
  ]),
  controller.updatePayoutToPaid
);

router.route('/updatePayoutToPendingReply/cops').post(
  postRoutePreHandlerMiddleware,
  copsTokenMiddleware,
  validateAll([
    {
      schema: schema.updatePayoutToPendingReplySchema,
      location: 'body',
    },
  ]),
  controller.updatePayoutToPendingReply
);

// Add new route for updating payout bank account
//
// POST /updatePayoutBankAccount/cops
//
// When a Community Manager updates their bank account (Stripe Connect or manual bank transfer) after a payout is generated,
// the payout record may still be linked to the old account (which might be unavailable).
// This API fetches and updates the payout record with the latest bank account info for the community.
//
router.route('/updatePayoutBankAccount/cops').post(
  postRoutePreHandlerMiddleware,
  copsTokenMiddleware,
  validateAll([
    {
      schema: schema.updatePayoutBankAccountSchema,
      location: 'body',
    },
  ]),
  controller.updatePayoutBankAccount
);

// Callback from payment backend
router.route('/updatePayoutByWebhook').post(
  postRoutePreHandlerMiddleware,
  apiKeyValidation,
  validateAll([
    {
      schema: schema.updatePayoutByWebhookSchema,
      location: 'body',
    },
  ]),
  controller.updatePayoutByWebhook
);

router
  .route('/payoutAccounts')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    validateAll([
      {
        schema: schema.communityIdSchema,
        location: 'params',
      },
    ]),
    controller.getCommunityPayoutAccount
  )
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    validateAll([
      {
        schema: schema.communityIdSchema,
        location: 'params',
      },
      {
        schema: schema.addCommunityPayoutAccountSchema,
        location: 'body',
      },
    ]),
    controller.addCommunityPayoutAccount
  )
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    validateAll([
      {
        schema: schema.communityIdSchema,
        location: 'params',
      },
      {
        schema: schema.addCommunityPayoutAccountSchema,
        location: 'body',
      },
    ]),
    controller.updateStripeConnectAccount
  )
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    validateAll([
      {
        schema: schema.communityIdSchema,
        location: 'params',
      },
      {
        schema: schema.deleteCommunityPayoutAccountSchema,
        location: 'body',
      },
    ]),
    controller.deleteCommunityPayoutAccount
  );

router.route('/stripeConnect/accountSession').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  validateAll([
    {
      schema: schema.communityIdSchema,
      location: 'params',
    },
  ]),
  controller.createStripeConnectAccountSession
);

router.route('/stripeConnect/update').post(
  postRoutePreHandlerMiddleware,
  apiKeyValidation, // request from our internal services
  validateAll([
    {
      schema: schema.communityIdSchema,
      location: 'params',
    },
    {
      schema: schema.stripeConnectWebhookEventsSchema,
      location: 'body',
    },
  ]),
  controller.updateStripeConnectAccountByWebhookEvents
);

module.exports = router;
