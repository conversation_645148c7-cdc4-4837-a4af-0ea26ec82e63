const {
  postRoutePreHandlerMiddleware,
} = require('../../../../middleware/request.middleware');
const apiKeyValidator = require('../../../../validations/apiKey.validation');
const {
  managerCommunityValidator,
} = require('../../../../communitiesAPI/validations/community.validation');
const tokenValidator = require('../../../../validations/token.validation');
const userValidation = require('../../../../validations/user.validation');
const transientTokenValidator = require('../../../../validations/transientToken.validation');

const { handlerWrapper } = require('../../../../utils/request.util');
const {
  // eslint-disable-next-line no-unused-vars
  rateLimitMiddleware,
} = require('../../../../utils/rateLimit.util');
const {
  validateAll,
} = require('../../../../middleware/validator.middleware');

const controller = require('./controller');
const schema = require('./schema');

const setupRouter = async function (router) {
  router.route('/challenge/community/:communityId/challenges').post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.createChallenge,
      requestValidators: {
        body: schema.createChallengeSchema,
      },
    })
  );

  router
    .route('/challenge/community/:communityId/challenges/:challengeId')
    .patch(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.updateChallenge,
        requestValidators: {
          body: schema.updateChallengeSchema,
        },
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/update-checkpoints'
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.updateCheckpoints,
        requestValidators: {
          body: schema.updateCheckpointsSchema,
        },
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/bulk-update-checkpoints'
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.bulkUpdateCheckpoints,
        requestValidators: {
          body: schema.bulkUpdateCheckpointsSchema,
        },
        wrapResponseInObject: false,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/checkpoints/:checkpointId/duplicate-checkpoint'
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.duplicateCheckpoint,
        requestValidators: {
          body: schema.duplicateCheckpointSchema,
        },
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/checkpoints/:checkpointId/event'
    )
    .put(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.updateCheckpointEvent,
        requestValidators: {
          body: schema.updateCheckpointEventSchema,
        },
      })
    )
    .delete(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.deleteCheckpointEvent,
      })
    );

  router.route('/challenge/send-checkpoint-reminder').post(
    postRoutePreHandlerMiddleware,
    apiKeyValidator,
    handlerWrapper({
      handler: controller.sendCheckpointReminderToParticipants,
      requestValidators: {
        body: schema.challengeCheckpointReminderSchema,
      },
    })
  );

  router.route('/challenge/send-challenge-mobile-notification').post(
    postRoutePreHandlerMiddleware,
    apiKeyValidator,
    handlerWrapper({
      handler: controller.sendChallengeMobileNotification,
      requestValidators: {
        body: schema.challengeMobileNotificationSchema,
      },
    })
  );
  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/participants/declare-winner'
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.declareWinners,
        requestValidators: {
          body: schema.declareWinnerSchema,
        },
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/participants/remove'
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.removeParticipants,
        requestValidators: {
          body: schema.removeParticipantsSchema,
        },
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/participants'
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      validateAll([
        {
          schema: schema.addParticipantsBodySchema,
          location: 'body',
        },
      ]),
      handlerWrapper({
        handler: controller.addParticipantsByCM,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/participants/export/csv'
    )
    .get(
      postRoutePreHandlerMiddleware,
      transientTokenValidator,
      controller.exportParticipants
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/participants/:participantId/checkpoints/:checkpointId/points'
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.awardPoints,
        requestValidators: {
          body: schema.awardPointsSchema,
          params: schema.checkpointParamsSchema,
        },
      })
    )
    .delete(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.resetPoints,
        requestValidators: {
          body: schema.resetPointsSchema,
          params: schema.checkpointParamsSchema,
        },
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/participants/:participantId/checkpoints/:checkpointId/get-for-cm'
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.getParticipantCheckpointForCM,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/participants/:participantId'
    )
    .put(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.updateParticipant,
        requestValidators: {
          body: schema.updateParticipantBodySchema,
        },
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/get-end-state'
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.getChallengeEndState,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/duplicate'
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.duplicateChallenge,
      })
    );

  router.route('/challenge/challengeGPT').post(
    postRoutePreHandlerMiddleware,
    handlerWrapper({
      handler: controller.askChallengeGPT,
    })
  );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/checkpoints/:checkpointId/update-schedule'
    )
    .post(
      postRoutePreHandlerMiddleware,
      // tokenValidator(),
      // userValidation,
      // managerCommunityValidator,
      handlerWrapper({
        handler: controller.updateOneCheckpointSchedule,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/checkpoints/:checkpointId/update-dates'
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.updateCheckpointDates,
        requestValidators: {
          body: schema.updateCheckpointDatesSchema,
        },
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/checkpoints/:checkpointId/calculate-dates'
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.calculateCheckpointDates,
        requestValidators: {
          body: schema.updateCheckpointDatesSchema,
        },
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/checkpoints/:checkpointId/move-checkpoint'
    )
    .post(
      postRoutePreHandlerMiddleware,
      // tokenValidator(),
      // userValidation,
      // managerCommunityValidator,
      handlerWrapper({
        handler: controller.moveCheckpoint,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/add-checkpoint'
    )
    .post(
      postRoutePreHandlerMiddleware,
      // tokenValidator(),
      // userValidation,
      // managerCommunityValidator,
      handlerWrapper({
        handler: controller.addCheckpoint,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/participants/clear-completions'
    )
    .post(
      postRoutePreHandlerMiddleware,
      // tokenValidator(),
      // userValidation,
      // managerCommunityValidator,
      handlerWrapper({
        handler: controller.clearCompletions,
      })
    );
};

module.exports = {
  setupRouter,
};
