/* eslint-disable no-unused-vars */
const { ObjectId } = require('mongoose').Types;
const { DateTime } = require('luxon');
const schema = require('./schema');
const programService = require('../../../../services/program/index');
const {
  ParamError,
  ResourceNotFoundError,
} = require('../../../../utils/error.util');
const Community = require('../../../../communitiesAPI/models/community.model');
const {
  PROGRAM_TYPE,
  PARTICIPANT_PROGRAM_STATUS,
  PROGRAM_ITEM_TYPE,
  PROGRAM_STATUS,
} = require('../../../../services/program/constants');
const ProgramModel = require('../../../../models/program/program.model');

const logger = require('../../../../services/logger.service');
const ProgramItemModel = require('../../../../models/program/programItem.model');
const {
  fetchAssistantAndAskAQuestion,
  fetchSalesAgentAndAskAQuestion,
} = require('../../../../clients/openai.client');
const applicationLeadsModel = require('../../../../models/staticBackend/applicationLeads.model');

function getArrayQueryParam(stringValue) {
  return stringValue && stringValue.trim() !== 0
    ? stringValue.split(',')
    : [];
}

const getActiveCommunity = async (communityId) => {
  const community = await Community.findOne({
    _id: communityId,
    isActive: true,
  }).lean();
  if (!community) {
    throw new ParamError('Community not found');
  }
  return community;
};

const createChallenge = async (req, res, next) => {
  const createdBy = req.user;
  const community = await getActiveCommunity(req.params.communityId);
  const challengePayload = schema.createChallengeSchema.cast(req.body);
  const challenge =
    await programService.manageProgramsService.createProgram({
      community,
      programPayload: challengePayload,
      programType: PROGRAM_TYPE.CHALLENGE,
      createdBy,
    });
  return challenge;
};

const updateChallenge = async (req, res, next) => {
  const updatedBy = req.user;
  const community = await getActiveCommunity(req.params.communityId);
  const challengePayload = schema.updateChallengeSchema.cast(req.body);
  const challengeId = req.params.challengeId;
  const challenge =
    await programService.manageProgramsService.updateProgram({
      community,
      programId: challengeId,
      programPayload: challengePayload,
      updatedBy,
    });
  return challenge;
};

const updateCheckpoints = async (req, res, next) => {
  const community = await getActiveCommunity(req.params.communityId);
  const challengeId = req.params.challengeId;
  const checkpoints =
    await programService.manageProgramsItemService.updateProgramItems({
      community,
      programId: challengeId,
      programItems: req.body.checkpoints,
    });
  return checkpoints;
};

const updateCheckpointEvent = async (req, res, next) => {
  const eventData = schema.updateCheckpointEventSchema.cast(req.body);
  const community = await getActiveCommunity(req.params.communityId);
  const checkpoint =
    await programService.manageProgramsItemService.updateOneCheckpointEvent(
      community,
      req.params.challengeId,
      req.params.checkpointId,
      eventData
    );
  return checkpoint;
};

const deleteCheckpointEvent = async (req, res, next) => {
  const checkpoint =
    await programService.manageProgramsItemService.deleteOneCheckpointEvent(
      req.params.challengeId,
      req.params.checkpointId
    );
  return checkpoint;
};

const duplicateCheckpoint = async (req, res, next) => {
  const community = await getActiveCommunity(req.params.communityId);
  const challengeId = req.params.challengeId;
  const source = req.params.checkpointId;
  const checkpoints =
    await programService.manageProgramsItemService.duplicateProgramItem({
      community,
      programId: challengeId,
      source,
      destination: req.body.destination,
      duplicateToAll: req.body.duplicateToAll,
    });
  return checkpoints;
};

const removeParticipants = async (req, res, next) => {
  const { challengeId, communityId } = req.params;

  const community = await getActiveCommunity(communityId);

  const challenge = await ProgramModel.findOne({
    _id: new ObjectId(challengeId),
  }).lean();

  if (!challenge) {
    throw new ParamError('Program not found');
  }

  const removeParticipantsBodyParams =
    schema.removeParticipantsSchema.cast(req.body);

  const participant =
    await programService.manageParticipantsService.removeParticipants(
      removeParticipantsBodyParams,
      challenge,
      community,
      req.user?.learner?._id
    );

  return participant;
};

const declareWinners = async (req, res, next) => {
  const { challengeId, communityId } = req.params;

  const community = await getActiveCommunity(communityId);

  const challenge = await ProgramModel.findOne({
    _id: new ObjectId(challengeId),
  }).lean();

  if (!challenge) {
    throw new ParamError('Program not found');
  }

  const winnerBodyParams = schema.declareWinnerSchema.cast(req.body);

  const winner =
    await programService.manageParticipantsService.declareWinners(
      winnerBodyParams,
      challenge,
      community,
      req.user?.learner?._id
    );

  return winner;
};

const exportParticipants = async (req, res) => {
  try {
    const now = DateTime.now().toUTC();
    const fileName = encodeURIComponent(
      req.query?.fileName ?? `Participants ${now}.csv`
    );
    res.setHeader(
      'Content-Disposition',
      `attachment; filename=${fileName}`
    );
    res.setHeader('Content-Type', 'text/csv');

    const filterProgramItemIds = getArrayQueryParam(
      req.query.filterCheckpointIds
    );
    const filterProgramStatuses = getArrayQueryParam(
      req.query.filterChallengeStatuses
    );
    const filterProgramItemStatuses = getArrayQueryParam(
      req.query.filterCheckpointStatuses
    );
    const pageSize = 100;
    const participantCsvStream =
      await programService.getParticipantsService.generateParticipantsCsvStream(
        {
          communityId: req.params.communityId,
          searchString: req.query.searchString,
          sortBy: req.query.sortBy,
          sortOrder: req.query.sortOrder,
          challengeId: req.params.challengeId,
          filterProgramItemIds,
          filterProgramStatuses,
          filterProgramItemStatuses,
          pageSize,
        }
      );
    participantCsvStream.pipe(res);
    participantCsvStream.on('end', () => {
      res.end();
    });
  } catch (error) {
    logger.error('Error exporting participants', error, error.stack);
    return res.status(500).json({
      errorMessage: 'Internal server error',
      errorCode: -1,
      error: 'INTERNAL_ERROR',
    });
  }
};

const awardPoints = async (req, res, next) => {
  const {
    communityId: communityObjectId,
    challengeId: challengeObjectId,
    participantId: participantObjectId,
    checkpointId: checkpointObjectId,
  } = schema.checkpointParamsSchema.cast(req.params);

  const { reason, awardType, notify } = schema.awardPointsSchema.cast(
    req.body
  );

  const result =
    await programService.manageParticipantsService.awardPoints({
      communityObjectId,
      challengeObjectId,
      participantObjectId,
      checkpointObjectId,
      reason,
      awardType,
      notify,
    });

  return result;
};

const resetPoints = async (req, res, next) => {
  const {
    communityId: communityObjectId,
    challengeId: challengeObjectId,
    participantId: participantObjectId,
    checkpointId: checkpointObjectId,
  } = schema.checkpointParamsSchema.cast(req.params);

  const { reason, notify } = schema.resetPointsSchema.cast(req.body);

  const result =
    await programService.manageParticipantsService.resetPoints({
      communityObjectId,
      challengeObjectId,
      participantObjectId,
      checkpointObjectId,
      reason,
      notify,
    });

  return result;
};

const getParticipantCheckpointForCM = async (req, res, next) => {
  const community = await getActiveCommunity(req.params.communityId);
  const participantCheckpoint =
    await programService.getParticipantsService.getParticipantProgramItemForCM(
      {
        community,
        participantId: req.params.participantId,
        programId: req.params.challengeId,
        programItemId: req.params.checkpointId,
      }
    );
  return participantCheckpoint;
};

const addParticipantsByCM = async (req, res, next) => {
  const { communityId, challengeId } = req.params;

  const addParticipantsBodyParams = schema.addParticipantsBodySchema.cast(
    req.body
  );
  const community = await getActiveCommunity(communityId);
  const challenge = await ProgramModel.findOne({
    _id: new ObjectId(challengeId),
  });

  if (!challenge) {
    throw new ParamError('Program not found');
  }

  const participant =
    await programService.manageParticipantsService.addParticipantsByCM(
      addParticipantsBodyParams,
      challenge,
      community,
      req.user.learner._id
    );

  return participant;
};

const sendCheckpointReminderToParticipants = async (req, res, next) => {
  const { challengeId, checkpointId, jobObjectId, mailType } = req.body;

  const challenge = await ProgramModel.findOne({
    _id: new ObjectId(challengeId),
  }).lean();

  if (!challenge) {
    throw new ParamError('Program not found');
  }

  if (challenge.status !== PROGRAM_STATUS.PUBLISHED) {
    logger.info(
      'Challenge is not published. Skip sending checkpoint reminders'
    );
    return;
  }

  const community = await Community.findOne({
    _id: challenge.communityObjectId,
    isActive: true,
  });

  if (!community) {
    throw new ParamError('Community not found');
  }

  const checkpoint = await ProgramItemModel.findOne({
    _id: new ObjectId(checkpointId),
    type: PROGRAM_ITEM_TYPE.CHECKPOINT,
  }).lean();

  await programService.manageParticipantsService.sendCheckpointReminderToParticipants(
    {
      mailType,
      checkpoint,
      challenge,
      community,
      jobId: jobObjectId,
    }
  );

  return 'Checkpoint reminder triggered successfully';
};

const sendChallengeMobileNotification = async (req, res, next) => {
  const { notificationBody, notificationId } = req.body;

  const notification =
    await programService.manageParticipantsService.sendChallengeMobileNotification(
      {
        notificationBody,
        notificationId,
      }
    );

  return notification;
};
const updateParticipant = async (req, res, next) => {
  const { communityId, challengeId, participantId } = req.params;

  const participant =
    await programService.manageParticipantsService.updateParticipant({
      programId: challengeId,
      participantId,
      payload: req.body,
    });

  return participant;
};

const getChallengeEndState = async (req, res, next) => {
  const community = await getActiveCommunity(req.params.communityId);
  const challenge = await ProgramModel.findOne({
    _id: new ObjectId(req.params.challengeId),
  }).lean();
  if (!challenge) {
    throw new ParamError('Challenge not found');
  }

  const [participatedCount, completedCount, winnersCount, kickedOutCount] =
    await Promise.all([
      programService.commonService.countParticipantRaw({
        filters: {
          programObjectId: challenge._id,
          status: { $ne: PARTICIPANT_PROGRAM_STATUS.SYSTEM_REMOVED },
        },
      }),
      programService.commonService.countParticipantRaw({
        filters: {
          programObjectId: challenge._id,
          completedDate: { $ne: null },
        },
      }),
      programService.commonService.countParticipantRaw({
        filters: {
          programObjectId: challenge._id,
          status: PARTICIPANT_PROGRAM_STATUS.WINNER,
        },
      }),
      programService.commonService.countParticipantRaw({
        filters: {
          programObjectId: challenge._id,
          status: PARTICIPANT_PROGRAM_STATUS.KICKED_OUT,
        },
      }),
    ]);
  const results = {
    countByStatus: {},
  };
  results.countByStatus = {
    [PARTICIPANT_PROGRAM_STATUS.PARTICIPATED]: participatedCount,
    [PARTICIPANT_PROGRAM_STATUS.COMPLETED]: completedCount,
    [PARTICIPANT_PROGRAM_STATUS.WINNER]: winnersCount,
    [PARTICIPANT_PROGRAM_STATUS.KICKED_OUT]: kickedOutCount,
  };
  results.winners =
    await programService.getParticipantsService.getAllParticipantsByProgramStatus(
      {
        community,
        programId: challenge._id,
        statuses: [PARTICIPANT_PROGRAM_STATUS.WINNER],
      }
    );
  return results;
};

const duplicateChallenge = async (req, res, next) => {
  const community = await getActiveCommunity(req.params.communityId);
  const challengeId = req.params.challengeId;
  const duplicatedBy = req.user;
  const targetChallengeType = req.body?.targetChallengeType;
  const challenge =
    await programService.manageProgramsService.duplicateChallenge({
      community,
      challengeId,
      duplicatedBy,
      targetChallengeType,
    });
  return challenge;
};

const askChallengeGPT = async (req, res, next) => {
  const {
    typeOfCommunity,
    challengeDuration,
    aboutCommunity,
    challengeGoal,
    username,
    email,
    currentLocale = 'en',
  } = req.body;

  const emailExistsInLeads = await applicationLeadsModel.findOne({
    email,
    source: 'challengeGPT',
    courseCode: 'CHALLENGE_GPT',
  });

  if (!emailExistsInLeads) {
    throw new ResourceNotFoundError('Email not found in leads');
  }
  let messageContent = `type of community : ${typeOfCommunity}, about community : ${aboutCommunity} challengeDuration: ${challengeDuration}`;

  if (challengeGoal) {
    messageContent += `, challenge goal : ${challengeGoal}`;
  }

  const response = await fetchAssistantAndAskAQuestion({
    messageContent,
    name: username,
    email,
    currentLocale,
  });

  return response;
};

const updateCheckpointDates = async (req, res, next) => {
  const { communityId, challengeId, checkpointId } = req.params;
  const params = schema.updateCheckpointDatesSchema.cast(req.body);
  const community = await getActiveCommunity(communityId);
  const results =
    await programService.manageProgramsItemService.updateCheckpointDates(
      community,
      challengeId,
      checkpointId,
      params
    );
  return results;
};

// From admin js
const updateOneCheckpointSchedule = async (req, res, next) => {
  const { communityId, challengeId, checkpointId } = req.params;
  const { newStartTime, newEndTime } = req.body;
  const community = await getActiveCommunity(communityId);
  if (!newStartTime && !newEndTime) {
    throw new ParamError(
      'At least one of newStartTime or newEndTime is required'
    );
  }
  const updatedCheckpoint =
    await programService.manageProgramsItemService.updateOneCheckpointSchedule(
      {
        community,
        challengeId,
        checkpointId,
        newStartTime,
        newEndTime,
      }
    );

  return updatedCheckpoint;
};

const moveCheckpoint = async (req, res, next) => {
  const { communityId, challengeId, checkpointId } = req.params;
  const { newIndex } = req.body;
  const community = await getActiveCommunity(communityId);
  const updatedCheckpoint =
    await programService.manageProgramsItemService.moveCheckpoint({
      community,
      challengeId,
      checkpointId,
      newIndex,
    });

  return updatedCheckpoint;
};

const addCheckpoint = async (req, res, next) => {
  const { communityId, challengeId } = req.params;
  const community = await getActiveCommunity(communityId);
  const index = req.body.index;
  const durationInSeconds = req.body.durationInSeconds;

  const newCheckpoint =
    await programService.manageProgramsItemService.addCheckpoint({
      community,
      challengeId,
      index,
      durationInSeconds,
    });

  return newCheckpoint;
};

const clearCompletions = async (req, res, next) => {
  const { communityId, challengeId } = req.params;
  const { checkpointId } = req.body;
  const community = await getActiveCommunity(communityId);
  const results =
    await programService.manageParticipantsService.clearCompletions({
      community,
      programId: challengeId,
      programItemId: checkpointId,
    });

  return {
    results,
  };
};

// Update checkpoints include event and upsell
const bulkUpdateCheckpoints = async (req, res, next) => {
  const community = await getActiveCommunity(req.params.communityId);
  const user = req.user;
  const challengeId = req.params.challengeId;
  const params = schema.bulkUpdateCheckpointsSchema.cast(req.body);
  const { updatedProgramItems, changeLogId } =
    await programService.manageProgramsItemBulkUpdateService.bulkUpdateProgramItems(
      {
        community,
        programId: challengeId,
        params,
        managerEmail: req.user?.email,
        managerLearnerId: user?.learner?._id,
      }
    );
  return { data: updatedProgramItems, changeLogId };
};

const calculateCheckpointDates = async (req, res, next) => {
  const { communityId, challengeId, checkpointId } = req.params;
  const params = schema.updateCheckpointDatesSchema.cast(req.body);
  const community = await getActiveCommunity(communityId);
  const results =
    await programService.manageProgramsItemService.calculateCheckpointDates(
      {
        community,
        challengeId,
        checkpointId,
        params,
      }
    );
  return results;
};

module.exports = {
  createChallenge,
  updateChallenge,
  updateCheckpoints,
  updateCheckpointDates,
  updateCheckpointEvent,
  deleteCheckpointEvent,
  duplicateCheckpoint,
  removeParticipants,
  declareWinners,
  exportParticipants,
  addParticipantsByCM,
  getParticipantCheckpointForCM,
  updateParticipant,
  getChallengeEndState,
  sendCheckpointReminderToParticipants,
  duplicateChallenge,
  sendChallengeMobileNotification,
  updateOneCheckpointSchedule,
  moveCheckpoint,
  addCheckpoint,
  askChallengeGPT,
  clearCompletions,
  bulkUpdateCheckpoints,
  calculateCheckpointDates,
  awardPoints,
  resetPoints,
};
