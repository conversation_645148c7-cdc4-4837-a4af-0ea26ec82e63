const { Router } = require('express');
const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const transientTokenValidator = require('../../../validations/transientToken.validation');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  validateAll,
} = require('../../../middleware/validator.middleware');
const { handlerWrapper } = require('../../../utils/request.util');
const apiKeyValidation = require('../../../validations/apiKey.validation');
const copsTokenMiddleware = require('../../../middleware/cops-token.middleware');

const router = Router({ mergeParams: true });

router
  .route('/')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    validateAll([
      { schema: schema.transactionsQuerySchema, location: 'query' },
      {
        schema: schema.communityIdSchema,
        location: 'params',
      },
    ]),
    controller.getTransactions
  )
  .post(
    postRoutePreHandlerMiddleware,
    apiKeyValidation, // request from our internal services
    validateAll([
      {
        schema: schema.communityIdSchema,
        location: 'params',
      },
      {
        schema: schema.createTransactionSchema,
        location: 'body',
      },
    ]),
    controller.createTransaction
  );

router.route('/filters/zero-links').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  handlerWrapper({
    handler: controller.getTransactionZeroLinkFilterInfo,
    requestValidators: {
      params: schema.communityIdSchema,
      query: schema.filterQuerySchema,
    },
  })
);

router.route('/filters/events').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  validateAll([
    {
      schema: schema.communityIdSchema,
      location: 'params',
    },
    {
      schema: schema.filterQuerySchema,
      location: 'query',
    },
  ]),
  controller.getTransactionEventFilterInfo
);

router.route('/filters/products').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  validateAll([
    {
      schema: schema.communityIdSchema,
      location: 'params',
    },
    {
      schema: schema.filterQuerySchema,
      location: 'query',
    },
  ]),
  controller.getTransactionProductFilterInfo
);

router.route('/filters/challenges').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  validateAll([
    {
      schema: schema.communityIdSchema,
      location: 'params',
    },
    {
      schema: schema.filterQuerySchema,
      location: 'query',
    },
  ]),
  controller.getTransactionChallengeFilterInfo
);

router.route('/filters/discounts').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  validateAll([
    {
      schema: schema.communityIdSchema,
      location: 'params',
    },
    {
      schema: schema.filterQuerySchema,
      location: 'query',
    },
  ]),
  controller.getTransactionDiscountFilterInfo
);

router.route('/csv').get(
  postRoutePreHandlerMiddleware,
  transientTokenValidator,
  validateAll([
    { schema: schema.transactionsCsvQuerySchema, location: 'query' },
    {
      schema: schema.communityIdSchema,
      location: 'params',
    },
  ]),
  controller.getExportTransactionsCsv
);

router.route('/revenues').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  validateAll([
    { schema: schema.revenueQuerySchema, location: 'query' },
    {
      schema: schema.communityIdSchema,
      location: 'params',
    },
  ]),
  controller.getRevenues
);

router.route('/:transactionId/refunds/request').post(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  validateAll([
    {
      schema: schema.refundsParamSchema,
      location: 'params',
    },
    {
      schema: schema.refundsBodySchema,
      location: 'body',
    },
  ]),
  controller.postRequestRefund
);

router.route('/:transactionId/invoice/generate').post(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  handlerWrapper({
    handler: controller.generateInvoiceTemplate,
    requestValidators: {
      body: schema.generateInvoiceSchema,
    },
  })
);

router.route('/:transactionId/invoice/config').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  handlerWrapper({
    handler: controller.getInvoiceConfig,
  })
);

router.route('/:transactionId/invoice').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  handlerWrapper({
    handler: controller.getInvoice,
  })
);
router.route('/chargeback').post(
  postRoutePreHandlerMiddleware,
  apiKeyValidation, // request from our internal services
  validateAll([
    {
      schema: schema.chargebackSchema,
      location: 'body',
    },
  ]),
  controller.chargebackRequest
);

router.route('/create-refund/cops').post(
  postRoutePreHandlerMiddleware,
  copsTokenMiddleware,
  validateAll([
    {
      schema: schema.createRefundSchema,
      location: 'body',
    },
  ]),
  controller.createRefundRequest
);

router.route('/update-payment-provider-fee').post(
  postRoutePreHandlerMiddleware,
  apiKeyValidation,
  validateAll([
    {
      schema: schema.updatePaymentProviderFeeSchema,
      location: 'body',
    },
  ]),
  controller.updatePaymentProviderFee
);

module.exports = router;
