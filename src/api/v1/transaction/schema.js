const yup = require('yup');
const {
  PURCHASE_TYPE,
  TRANSACTION_TYPE,
  PAYMENT_PROVIDER,
} = require('../../../constants/common');

exports.communityIdSchema = yup.object().shape({
  communityId: yup.string().required(),
});

exports.filterQuerySchema = yup.object().shape({
  search: yup.string().default('').lowercase().notRequired().trim(),
  pageSize: yup.number().default(20).required().integer(),
  pageNo: yup.number().default(1).required().integer(),
});

exports.transactionsQuerySchema = yup.object().shape({
  search: yup.string().uppercase().notRequired().trim(),
  pageSize: yup.number().default(20).required().integer(),
  pageNo: yup.number().default(1).required().integer(),
  sortBy: yup
    .string()
    .default('transactionCreatedAt')
    .notRequired()
    .trim(),
  sortOrder: yup.number().default(-1).notRequired(),
  purchaseType: yup.string().uppercase().notRequired().trim(),
  discountCodes: yup.string().uppercase().notRequired().trim(),
  eventName: yup.string().notRequired().trim(),
  folderName: yup.string().notRequired().trim(),
  zeroLinkIds: yup.string().notRequired().trim(),
  eventsIds: yup.string().notRequired().trim(),
  productsIds: yup.string().notRequired().trim(),
  challengesIds: yup.string().notRequired().trim(),
  subscriptionIntervals: yup.string().notRequired().trim(),
  transactionType: yup.string().notRequired().trim(),
  startDate: yup.string().notRequired().trim(),
  endDate: yup.string().notRequired().trim(),
  payoutId: yup.string().notRequired().trim(),
});

exports.transactionsCsvQuerySchema = yup.object().shape({
  search: yup.string().uppercase().notRequired().trim(),
  sortBy: yup
    .string()
    .default('transactionCreatedAt')
    .notRequired()
    .trim(),
  sortOrder: yup.number().default(-1).notRequired(),
  purchaseType: yup.string().uppercase().notRequired().trim(),
  discountCodes: yup.string().uppercase().notRequired().trim(),
  eventName: yup.string().notRequired().trim(),
  folderName: yup.string().notRequired().trim(),
  zeroLinkIds: yup.string().notRequired().trim(),
  eventsIds: yup.string().notRequired().trim(),
  productsIds: yup.string().notRequired().trim(),
  challengesIds: yup.string().notRequired().trim(),
  subscriptionIntervals: yup.string().notRequired().trim(),
  transactionType: yup.string().notRequired().trim(),
  startDate: yup.string().notRequired().trim(),
  endDate: yup.string().notRequired().trim(),
  payoutId: yup.string().notRequired().trim(),
});

exports.revenueQuerySchema = yup.object().shape({
  duration: yup.string().notRequired().lowercase().trim(),
  startDate: yup.string().notRequired().trim(),
  endDate: yup.string().notRequired().trim(),
});

exports.refundsParamSchema = yup.object().shape({
  communityId: yup.string().required(),
  transactionId: yup.string().required(),
});

exports.refundsBodySchema = yup.object().shape({
  refundReason: yup.string().required().trim(),
  removeAccess: yup.boolean().notRequired(),
  accessRemovalAtEnd: yup.boolean().notRequired().nullable(),
});

exports.generateInvoiceSchema = yup.object().shape({
  memberAddress: yup.string().notRequired(),
  memberId: yup.string().notRequired(),
  memberLabel: yup.string().notRequired(),
  communityAddress: yup.string().notRequired(),
  communityTaxId: yup.string().notRequired(),
  communityTaxPercentage: yup.number().notRequired(),
  communityTaxLabel: yup.string().notRequired(),
  sendEmail: yup.boolean().default(false).notRequired(),
});

exports.createTransactionSchema = yup.object().shape({
  purchasedId: yup.string().required(),
  transactionType: yup
    .string()
    .required()
    .oneOf(Object.values(TRANSACTION_TYPE)),
  purchaseType: yup
    .string()
    .required()
    .oneOf(Object.values(PURCHASE_TYPE)),
  originalAmount: yup.number().required(),
  originalCurrency: yup.string().required(),
  originalPaidAmount: yup.number().required(),
  originalDiscountAmount: yup.number().required(),
  rawFee: yup.object().required(),
  amountInUsd: yup.number().required(),
  feeInUsd: yup.object().required(),
  paymentMethod: yup.string().required(),
  paymentBrand: yup.string().required(),
  paymentProvider: yup
    .string()
    .required()
    .oneOf(Object.values(PAYMENT_PROVIDER)),
  communityObjectId: yup.string().required(),
  learnerObjectId: yup.string().required(),
  email: yup.string().required(),
  transactionCreatedAt: yup.date().required(),
  transactionReferenceId: yup.string().required(),
  revenueShareAmountInUsd: yup.number().required(),
  revenueSharePercentage: yup.number().required(),
  finalFeeInUsd: yup.object().required(),
  totalFeeInUsd: yup.number().required(),
  netAmountInUsd: yup.number().required(),
  amountBreakdownInUsd: yup.object().required(),
  amountBreakdownInLocalCurrency: yup.object().required(),
  amountBreakdownInOriginalCurrency: yup.object().notRequired(),
  metadata: yup.object().notRequired(),
});

exports.chargebackSchema = yup.object().shape({
  paymentProvider: yup.string().required(),
  inboundTransactionReferenceId: yup.string().required(),
  chargebackReferenceId: yup.string().required(),
  chargebackReason: yup.string().notRequired(),
  paymentProviderSource: yup.object().notRequired(),
});

exports.createRefundSchema = yup.object().shape({
  transactionObjectId: yup.string().required(),
  refundReason: yup.string().notRequired(),
  operator: yup.string().notRequired(),
});

exports.updatePaymentProviderFeeSchema = yup.object().shape({
  paymentProvider: yup.string().required(),
  transactionReferenceId: yup.string().required(),
  rawFee: yup.object().required(),
  feeInUsd: yup.object().required(),
});
