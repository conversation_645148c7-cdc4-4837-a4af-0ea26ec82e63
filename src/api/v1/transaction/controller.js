const schema = require('./schema');
const logger = require('../../../services/logger.service');
const service = require('../../../services/transaction');

exports.getTransactions = async (req, res, next) => {
  try {
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );

    const {
      search,
      pageSize,
      pageNo,
      sortBy,
      sortOrder,
      purchaseType,
      discountCodes,
      eventName,
      folderName,
      zeroLinkIds,
      eventsIds,
      productsIds,
      challengesIds,
      subscriptionIntervals,
      transactionType,
      startDate,
      endDate,
      payoutId,
    } = await schema.transactionsQuerySchema.cast(req.query);

    const response = await service.TransactionService.retrieveTransactions(
      {
        search,
        pageSize,
        pageNo,
        sortBy,
        sortOrder,
        purchaseType,
        discountCodes,
        eventName,
        folderName,
        zeroLinkIds,
        eventsIds,
        productsIds,
        challengesIds,
        subscriptionIntervals,
        transactionType,
        startDate,
        endDate,
        communityId,
        payoutId,
      }
    );

    return res.json({ data: response });
  } catch (err) {
    logger.error('getTransactions failed due to', err.message);
    return next(err);
  }
};

exports.createTransaction = async (req, res, next) => {
  try {
    const {
      purchasedId,
      transactionType,
      purchaseType,
      originalAmount,
      originalCurrency,
      originalPaidAmount,
      originalDiscountAmount,
      rawFee,
      amountInUsd,
      feeInUsd,
      paymentMethod,
      paymentBrand,
      paymentProvider,
      communityObjectId,
      learnerObjectId,
      email,
      transactionCreatedAt,
      transactionReferenceId,
      revenueShareAmountInUsd,
      revenueSharePercentage,
      finalFeeInUsd,
      totalFeeInUsd,
      netAmountInUsd,
      amountBreakdownInUsd,
      amountBreakdownInLocalCurrency,
      amountBreakdownInOriginalCurrency,
      metadata,
    } = await schema.createTransactionSchema.cast(req.body);

    const response =
      await service.TransactionCreateService.createTransaction({
        purchasedId,
        transactionType,
        purchaseType,
        originalAmount,
        originalCurrency,
        originalPaidAmount,
        originalDiscountAmount,
        rawFee,
        amountInUsd,
        feeInUsd,
        paymentMethod,
        paymentBrand,
        paymentProvider,
        communityObjectId,
        learnerObjectId,
        email,
        transactionCreatedAt,
        transactionReferenceId,
        revenueShareAmountInUsd,
        revenueSharePercentage,
        finalFeeInUsd,
        totalFeeInUsd,
        netAmountInUsd,
        amountBreakdownInUsd,
        amountBreakdownInLocalCurrency,
        amountBreakdownInOriginalCurrency,
        metadata,
      });

    return res.json({ data: response });
  } catch (err) {
    logger.error(
      'createTransactions failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};

exports.getTransactionZeroLinkFilterInfo = async (req) => {
  const { communityId } = await schema.communityIdSchema.cast(req.params);
  const { search, pageSize, pageNo } = await schema.filterQuerySchema.cast(
    req.query
  );
  const learnerObjectId = req.user.learner._id;

  const response =
    await service.TransactionFilterService.ZeroLinkService.retrieveFilterInfo(
      {
        learnerObjectId,
        communityId,
        search,
        pageSize,
        pageNo,
      }
    );

  return response;
};

exports.getTransactionEventFilterInfo = async (req, res, next) => {
  try {
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );

    const { search, pageSize, pageNo } =
      await schema.filterQuerySchema.cast(req.query);

    const response =
      await service.TransactionFilterService.EventService.retrieveFilterInfo(
        {
          communityId,
          search,
          pageSize,
          pageNo,
        }
      );

    return res.json({ data: response });
  } catch (err) {
    logger.error(
      'getTransactionEventFilterInfo failed due to',
      err.message
    );
    return next(err);
  }
};

exports.getTransactionProductFilterInfo = async (req, res, next) => {
  try {
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );

    const { search, pageSize, pageNo } =
      await schema.filterQuerySchema.cast(req.query);

    const response =
      await service.TransactionFilterService.ProductService.retrieveFilterInfo(
        {
          communityId,
          search,
          pageSize,
          pageNo,
        }
      );

    return res.json({ data: response });
  } catch (err) {
    logger.error(
      'getTransactionProductFilterInfo failed due to',
      err.message
    );
    return next(err);
  }
};

exports.getTransactionChallengeFilterInfo = async (req, res, next) => {
  try {
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );

    const { search, pageSize, pageNo } =
      await schema.filterQuerySchema.cast(req.query);

    const response =
      await service.TransactionFilterService.ChallengeService.retrieveFilterInfo(
        {
          communityId,
          search,
          pageSize,
          pageNo,
        }
      );

    return res.json({ data: response });
  } catch (err) {
    logger.error(
      'getTransactionChallengeFilterInfo failed due to',
      err.message
    );
    return next(err);
  }
};

exports.getTransactionDiscountFilterInfo = async (req, res, next) => {
  try {
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );

    const { search, pageSize, pageNo } =
      await schema.filterQuerySchema.cast(req.query);

    const response =
      await service.TransactionFilterService.DiscountService.retrieveFilterInfo(
        {
          communityId,
          search,
          pageSize,
          pageNo,
        }
      );

    return res.json({ data: response });
  } catch (err) {
    logger.error(
      'getTransactionDiscountFilterInfo failed due to',
      err.message
    );
    return next(err);
  }
};

exports.getExportTransactionsCsv = async (req, res, next) => {
  try {
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );

    const {
      search,
      sortBy,
      sortOrder,
      purchaseType,
      discountCodes,
      eventName,
      folderName,
      zeroLinkIds,
      eventsIds,
      productsIds,
      challengesIds,
      subscriptionIntervals,
      transactionType,
      startDate,
      endDate,
      payoutId,
    } = await schema.transactionsCsvQuerySchema.cast(req.query);

    const [currentDate, currentTime] = new Date().toISOString().split('T');

    const community = await service.ExportService.getCommunity(
      communityId
    );

    const fileName = encodeURIComponent(
      req.query?.fileName ??
        `${community.code}_transactions_${currentDate.replace(
          /-/g,
          ''
        )}_${currentTime.split('.')[0].replace(/:/g, '')}.csv`
    );

    res.setHeader(
      'Content-Disposition',
      `attachment; filename=${fileName}`
    );
    res.setHeader('Content-Type', 'text/csv');

    const pageSize = 100;

    const transactionsCsvStream =
      await service.ExportService.generateTransactionsCsvStream({
        search,
        pageSize,
        sortBy,
        sortOrder,
        purchaseType,
        discountCodes,
        eventName,
        folderName,
        zeroLinkIds,
        eventsIds,
        productsIds,
        challengesIds,
        subscriptionIntervals,
        transactionType,
        startDate,
        endDate,
        communityId,
        community,
        payoutId,
      });

    transactionsCsvStream.pipe(res);
    transactionsCsvStream.on('end', () => {
      res.end();
    });
  } catch (err) {
    logger.error('getExportTransactionsCsv failed due to', err.message);
    return next(err);
  }
};

exports.getRevenues = async (req, res, next) => {
  try {
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );

    const { duration, startDate, endDate } =
      await schema.revenueQuerySchema.cast(req.query);

    const response = await service.RevenueService.retrieveRevenues({
      duration,
      startDate,
      endDate,
      communityId,
    });

    return res.json({ data: response });
  } catch (err) {
    logger.error('getRevenues failed due to', err.message);
    return next(err);
  }
};

exports.postRequestRefund = async (req, res, next) => {
  try {
    const { communityId, transactionId } =
      await schema.refundsParamSchema.cast(req.params);

    const { refundReason, removeAccess, accessRemovalAtEnd } =
      await schema.refundsBodySchema.cast(req.body);

    const emailRequested = req.user.email;
    const learnerRequestedId = req.user.learner._id;

    const response = await service.RefundService.requestRefund({
      communityId,
      transactionId,
      emailRequested,
      learnerRequestedId,
      refundReason,
      removeAccess,
      accessRemovalAtEnd,
    });

    return res.json({ data: response });
  } catch (err) {
    logger.error(
      'postRequestRefund failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};

exports.generateInvoiceTemplate = async (req) => {
  const generateInvoiceSchema = schema.generateInvoiceSchema.cast(
    req.body,
    {
      stripUnknown: true, // extra fields stripped here
    }
  );
  const { communityId, transactionId } = req.params;
  const invoiceGenerated =
    await service.TransactionInvoiceService.generateInvoice({
      generateInvoiceSchema,
      communityId,
      transactionId,
      managerLearnerId: req.user.learner._id,
    });
  return invoiceGenerated;
};

exports.getInvoiceConfig = async (req) => {
  const { transactionId, communityId } = req.params;

  const invoiceConfig =
    await service.TransactionInvoiceService.getInvoiceConfig({
      transactionId,
      communityId,
    });

  return invoiceConfig;
};

exports.getInvoice = async (req) => {
  const { transactionId, communityId } = req.params;

  const invoice = await service.TransactionInvoiceService.getInvoice({
    transactionId,
    communityId,
  });
  return invoice;
};

exports.chargebackRequest = async (req, res, next) => {
  try {
    const {
      paymentProvider,
      inboundTransactionReferenceId,
      chargebackReferenceId,
      chargebackReason,
      paymentProviderSource,
    } = await schema.chargebackSchema.cast(req.body);

    const response = await service.ChargebackService.chargebackRequest({
      paymentProvider,
      inboundTransactionReferenceId,
      chargebackReferenceId,
      chargebackReason,
      paymentProviderSource,
    });

    return res.json({ data: response });
  } catch (err) {
    logger.error(
      'chargebackRequest failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};

exports.createRefundRequest = async (req, res, next) => {
  try {
    const { transactionObjectId, refundReason, operator } =
      await schema.createRefundSchema.cast(req.body);

    const response = await service.RefundService.createRefund({
      transactionObjectId,
      refundReason,
      operator,
    });

    return res.json({ data: response });
  } catch (err) {
    logger.error(
      'createRefundRequest failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};

exports.updatePaymentProviderFee = async (req, res, next) => {
  try {
    const { paymentProvider, transactionReferenceId, rawFee, feeInUsd } =
      await schema.updatePaymentProviderFeeSchema.cast(req.body);

    const response = await service.FeeService.updatePaymentProviderFee({
      paymentProvider,
      transactionReferenceId,
      rawFee,
      feeInUsd,
    });

    return res.json({ data: response });
  } catch (err) {
    logger.error(
      'updatePaymentProviderFee failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};
