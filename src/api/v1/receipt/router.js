const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const apiKeyValidation = require('../../../validations/apiKey.validation');
const { handlerWrapper } = require('../../../utils/request.util');
const controller = require('./controller');
const schema = require('./schema');

const setupRouter = async function (router) {
  router.route('/communities/receipt/generate').post(
    postRoutePreHandlerMiddleware,
    apiKeyValidation,
    handlerWrapper({
      handler: controller.generateReceiptTemplate,
      requestValidators: {
        body: schema.generateReceiptSchema,
      },
    })
  );

  router.route('/communities/invoice/generate').post(
    postRoutePreHandlerMiddleware,
    apiKeyValidation,
    handlerWrapper({
      handler: controller.generateInvoiceTemplate,
      requestValidators: {
        body: schema.generateInvoiceSchema,
      },
    })
  );
  router.route('/communities/receipt/regenerate').post(
    postRoutePreHandlerMiddleware,
    apiKeyValidation,
    handlerWrapper({
      handler: controller.regenerateReceipt,
      requestValidators: {
        body: schema.regenerateReceiptSchema,
      },
    })
  );
};

module.exports = {
  setupRouter,
};
