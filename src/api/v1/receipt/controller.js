const service = require('../../../services/receipts/receipts.service');
const schema = require('./schema');

const generateReceiptTemplate = async (req) => {
  const createReceiptSchema = schema.generateReceiptSchema.cast(req.body);

  const receiptGenerated = await service.generateReceipt(
    createReceiptSchema
  );

  return receiptGenerated;
};
const generateInvoiceTemplate = async (req) => {
  const createReceiptSchema = schema.generateInvoiceSchema.cast(req.body);

  const receiptGenerated = await service.generateInvoice(
    createReceiptSchema
  );

  return receiptGenerated;
};

const regenerateReceipt = async (req) => {
  const { rawTransactionObjectId, saveToRawTransaction } =
    schema.regenerateReceiptSchema.cast(req.body);

  const receiptGenerated = await service.regenerateReceipt({
    rawTransactionObjectId,
    saveToRawTransaction,
  });

  return receiptGenerated;
};

module.exports = {
  generateReceiptTemplate,
  regenerateReceipt,
  generateInvoiceTemplate,
};
