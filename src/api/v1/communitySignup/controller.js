const HttpStatus = require('http-status');
const schema = require('./schema');
const { GENERIC_ERROR } = require('../../../constants/errorCode');
const logger = require('../../../services/logger.service');
const service = require('../../../services/communitySignup');
const {
  getUserIP,
  getUserAgent,
  getLanguagePreference,
} = require('../../../utils/headers.util');
const {
  InternalError,
  FromExternalToUserError,
  ToUserError,
} = require('../../../utils/error.util');
const { ACCESS_TOKEN_TYPE } = require('../../../constants/common');

exports.postCommunitySignup = async (req, res, next) => {
  try {
    const {
      signupToken,
      communityCode,
      timezone,
      requestor,
      paymentProvider,
      trackingData,
      memberInfo,
      upsellIdentityCode,
      items,
      affiliateCode,
      cancelSubscription,
    } = schema.communitySignupBodySchema.cast(req.body);

    const ip = getUserIP(req) || null;
    const userAgent = getUserAgent(req) || null;
    const requestLanguagePreference = getLanguagePreference(req);
    const email = req.user?.email;
    const learnerObjectId = req.user?.learner?._id;
    const isCheckoutToken =
      req.accessTokenType === ACCESS_TOKEN_TYPE.CHECKOUT;

    const result = await service.SignupService.signup({
      ip,
      userAgent,
      requestLanguagePreference,
      email,
      learnerObjectId,
      signupToken,
      communityCode,
      timezone,
      requestor,
      paymentProvider,
      trackingData,
      memberInfo,
      upsellIdentityCode,
      items,
      affiliateCode,
      cancelSubscription,
      isCheckoutToken,
    });

    return res.json({ data: result });
  } catch (err) {
    const errorMessage = err.isAxiosError
      ? err.response?.data?.errorMessage
      : err.message;

    logger.error(
      'postCommunitySignup failed due to',
      errorMessage,
      err.stack
    );

    let error = err;

    if (err.isAxiosError) {
      error =
        err.response.status === HttpStatus.IM_A_TEAPOT
          ? new ToUserError(errorMessage, GENERIC_ERROR.PAYMENT_FAILURE)
          : new InternalError(errorMessage);
    }

    // Remove upsell objects from items if they exist
    // to prevent massive upsell entity data in logs
    if (req.body.items && Array.isArray(req.body.items)) {
      req.body.items = req.body.items.map((item) => {
        const { upsell, ...itemWithoutUpsell } = item;
        return itemWithoutUpsell;
      });
    }

    return next(error);
  }
};

exports.postCommunitySignupDiscount = async (req, res, next) => {
  try {
    const {
      communityCode,
      items,
      upsellIdentityCode,
      memberInfo,
      paymentProvider,
    } = schema.communitySignupDiscountSchema.cast(req.body);
    const learnerObjectId = req.user?.learner?._id;

    const ip = getUserIP(req) || null;

    const result = await service.SignupDiscountService.discount({
      ip,
      communityCode,
      upsellIdentityCode,
      items,
      learnerObjectId,
      memberInfo,
      paymentProvider,
    });

    return res.json({ data: result });
  } catch (err) {
    const errorMessage = err.isAxiosError
      ? err.response?.data?.errorMessage
      : err.message;

    logger.error(
      'postCommunitySignupDiscount failed due to',
      errorMessage,
      err.stack
    );

    let error = err;

    if (err.isAxiosError) {
      error =
        err.response.status === HttpStatus.IM_A_TEAPOT
          ? new ToUserError(errorMessage, GENERIC_ERROR.PAYMENT_FAILURE)
          : new InternalError(errorMessage);
    }

    return next(error);
  }
};

exports.postCommunitySignupConfirm = async (req, res, next) => {
  try {
    const {
      signupToken,
      metadata,
      confirmType,
      paymentMethodId,
      fromSavedPaymentMethod,
    } = schema.communitySignupConfirmBodySchema.cast(req.body);

    const ip = getUserIP(req) || null;
    const userAgent = getUserAgent(req) || null;

    const isEmailToken = req.accessTokenType === ACCESS_TOKEN_TYPE.EMAIL;

    const result = await service.SignupConfirmService.confirm({
      ip,
      userAgent,
      signupToken,
      metadata,
      confirmType,
      paymentMethodId,
      fromSavedPaymentMethod: fromSavedPaymentMethod === 'true',
      isEmailToken,
    });

    return res.json({ data: result });
  } catch (err) {
    const errorMessage = err.isAxiosError
      ? err.response.data.errorMessage
      : err.message;

    logger.error(
      'postCommunitySignupConfirm failed due to',
      errorMessage,
      err.stack
    );

    let error = err;

    if (err.isAxiosError) {
      error =
        err.response.status === HttpStatus.IM_A_TEAPOT
          ? new FromExternalToUserError(err.response.data)
          : new InternalError(errorMessage);
    }

    return next(error);
  }
};

exports.postCommunitySignupAbort = async (req, res, next) => {
  try {
    const { signupToken, failureCode, failureReason } =
      schema.communitySignupAbortBodySchema.cast(req.body);

    const result = await service.SignupAbortService.abort({
      signupToken,
      failureCode,
      failureReason,
    });

    return res.json({ data: result });
  } catch (err) {
    const errorMessage = err.isAxiosError
      ? err.response.data.errorMessage
      : err.message;

    logger.error(
      'postCommunitySignupAbort failed due to',
      errorMessage,
      err.stack
    );

    let error = err;

    if (err.isAxiosError) {
      error =
        err.response.status === HttpStatus.IM_A_TEAPOT
          ? new ToUserError(errorMessage)
          : new InternalError(errorMessage);
    }

    return next(error);
  }
};

exports.postCommunitySignupPaymentAuth = async (req, res, next) => {
  try {
    const { signupId, entityType } =
      schema.communitySignupPaymentAuthBodySchema.cast(req.body);

    const result = await service.PaymentAuthService.auth({
      signupId,
      entityType,
    });

    return res.json({ data: result });
  } catch (err) {
    const errorMessage = err.isAxiosError
      ? err.response.data.errorMessage
      : err.message;

    logger.error(
      'postCommunitySignupPaymentAuth failed due to',
      errorMessage,
      err.stack
    );

    let error = err;

    if (err.isAxiosError) {
      error =
        err.response.status === HttpStatus.IM_A_TEAPOT
          ? new ToUserError(errorMessage)
          : new InternalError(errorMessage);
    }

    return next(error);
  }
};

exports.getCommunityUpdatePaymentMethodInfo = async (req, res, next) => {
  try {
    const { communityObjectId } = schema.communityIdSchema.cast(req.query);
    const learnerObjectId = req.user?.learner?._id;

    const result =
      await service.UpdatePaymentMethodService.getActiveSubscriptionPricing(
        {
          communityObjectId,
          learnerObjectId,
        }
      );

    return res.json({ data: result });
  } catch (err) {
    const errorMessage = err.isAxiosError
      ? err.response.data.errorMessage
      : err.message;

    logger.error(
      'getCommunityUpdatePaymentMethodInfo failed due to',
      errorMessage,
      err.stack
    );

    let error = err;

    if (err.isAxiosError) {
      error =
        err.response.status === HttpStatus.IM_A_TEAPOT
          ? new ToUserError(errorMessage)
          : new InternalError(errorMessage);
    }

    return next(error);
  }
};

exports.postCommunityUpdatePaymentMethodInfo = async (req, res, next) => {
  try {
    const { communityObjectId } = schema.communityIdSchema.cast(req.query);
    const { signupId, metadata, directCharge } =
      schema.updatePaymentMethodBodySchema.cast(req.body);
    const learnerObjectId = req.user?.learner?._id;

    const result =
      await service.UpdatePaymentMethodService.updateSubscriptionPaymentMethod(
        {
          learnerObjectId,
          communityObjectId,
          signupId,
          metadata,
          directCharge,
        }
      );

    return res.json({ data: result });
  } catch (err) {
    const errorMessage = err.isAxiosError
      ? err.response.data.errorMessage
      : err.message;

    logger.error(
      'updateSubscriptionPaymentMethod failed due to',
      errorMessage,
      err.stack
    );

    let error = err;

    if (err.isAxiosError) {
      error =
        err.response.status === HttpStatus.IM_A_TEAPOT
          ? new ToUserError(errorMessage)
          : new InternalError(errorMessage);
    }

    return next(error);
  }
};
