const yup = require('yup');

exports.getWalletSchema = yup.object().shape({
  communityObjectId: yup.string().required(),
});

const feeSchema = yup.object({
  gatewayFee: yup.number().required().default(0),
  gst: yup.number().required().default(0),
  processingFee: yup.number().required().default(0),
  gstOnRevenue: yup.number().required().default(0),
  whtFee: yup.number().required().default(0),
  refundProcessingFee: yup.number().required().default(0),
  internationalFee: yup.number().required().default(0),
  adsCampaignProcessFee: yup.number().required().default(0),
});

const amountBreakdownInUsdSchema = yup.object({
  expectedPaidAmount: yup.number().notRequired(),
  itemPrice: yup.number().notRequired(),
  discountedItemPrice: yup.number().notRequired(),
  exchangeRate: yup.number().required().default(0),
  originalAmount: yup.number().required().default(0),
  discountAmount: yup.number().required().default(0),
  paidAmount: yup.number().required().default(0),
  fee: feeSchema.required(),
  calculatedRawFee: feeSchema.required(),
  revenueShareAmount: yup.number().required().default(0),
  totalFee: yup.number().required().default(0),
  netAmount: yup.number().required().default(0),
  referralShareAmount: yup.number().required().default(0),
  affiliateCommissionAmount: yup.number().required().default(0),
});

const amountBreakdownInBaseCurrencySchema = yup.object({
  expectedPaidAmount: yup.number().notRequired(),
  itemPrice: yup.number().notRequired(),
  discountedItemPrice: yup.number().notRequired(),
  exchangeRate: yup.number().required().default(0),
  currency: yup.string().required(),
  originalAmount: yup.number().required().default(0),
  discountAmount: yup.number().required().default(0),
  paidAmount: yup.number().required().default(0),
  fee: feeSchema.required(),
  calculatedRawFee: feeSchema.required(),
  revenueShareAmount: yup.number().required().default(0),
  totalFee: yup.number().required().default(0),
  netAmount: yup.number().required().default(0),
  referralShareAmount: yup.number().required().default(0),
  affiliateCommissionAmount: yup.number().required().default(0),
});

exports.updateWalletSchema = yup.object().shape({
  communityObjectId: yup.string().required(),
  learnerObjectId: yup.string().required(),
  transactionId: yup.string().required(),
  transactionType: yup.string().required(),
  paymentProvider: yup.string().required(),
  amountBreakdownInBaseCurrency:
    amountBreakdownInBaseCurrencySchema.required(),
  amountBreakdownInUsd: amountBreakdownInUsdSchema.required(),
  transactionCreatedAt: yup.date().required(),
  affiliate: yup.object().notRequired(),
  revenueTransactionPurchasedId: yup.string().notRequired(),
});

exports.resetWalletByTransactionSchema = yup.object().shape({
  transactionId: yup.string().required(),
});
