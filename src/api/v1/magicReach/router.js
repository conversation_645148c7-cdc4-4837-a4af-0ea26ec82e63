const magicReachController = require('./controller');
const {
  ScheduleMessageBodySchema,
  SendMessageBodySchema,
  CreateMessageBodySchema,
  UpdateMessageBodySchema,
  SendTestMessageBodySchema,
  CountRecipientsBodySchema,
  GetLinksBodySchema,
} = require('./schema');

const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const { rateLimitMiddleware } = require('../../../utils/rateLimit.util');
const apiKeyValidator = require('../../../validations/apiKey.validation');
const copsTokenMiddleware = require('../../../middleware/cops-token.middleware');

const {
  managerCommunityValidator,
  memberCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');

const { handlerWrapper } = require('../../../utils/request.util');

const setupCMPMagicReachRouter = function (router) {
  router.route('/communities/:communityId/magic-reach/products').get(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: 'MAGIC_REACH',
    }),
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    handlerWrapper({ handler: magicReachController.getProducts })
  );

  router.route('/communities/:communityId/magic-reach/bucket').get(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: 'MAGIC_REACH',
    }),
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({ handler: magicReachController.getBucket })
  );

  router.route('/communities/:communityId/magic-reach/buckets').get(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: 'MAGIC_REACH',
    }),
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({ handler: magicReachController.getBuckets })
  );

  router
    .route(
      '/communities/:communityId/magic-reach/message/search-recipients'
    )
    .get(
      postRoutePreHandlerMiddleware,
      rateLimitMiddleware({
        module: 'MAGIC_REACH',
      }),
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({ handler: magicReachController.searchRecipients })
    );

  router
    .route(
      '/communities/:communityId/magic-reach/message/search-recipients-internal'
    )
    .post(
      postRoutePreHandlerMiddleware,
      handlerWrapper({
        handler: magicReachController.searchRecipientsInternal,
      })
    );

  router
    .route(
      '/communities/:communityId/magic-reach/message/count-recipients'
    )
    .post(
      postRoutePreHandlerMiddleware,
      rateLimitMiddleware({
        module: 'MAGIC_REACH',
      }),
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: magicReachController.countRecipients,
        requestValidators: {
          body: CountRecipientsBodySchema,
        },
      })
    );

  router.route('/communities/:communityId/magic-reach/message').post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: magicReachController.createMessage,
      requestValidators: {
        body: CreateMessageBodySchema,
      },
    })
  );

  router
    .route('/communities/:communityId/magic-reach/message/:messageId')
    .put(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: magicReachController.updateMessage,
        requestValidators: {
          body: UpdateMessageBodySchema,
        },
      })
    )
    .delete(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: magicReachController.deleteMessage,
      })
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({ handler: magicReachController.getMessage })
    );

  router
    .route(
      '/communities/:communityId/magic-reach/message/:messageId/schedule'
    )
    .put(
      postRoutePreHandlerMiddleware,
      rateLimitMiddleware({
        module: 'MAGIC_REACH',
      }),
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: magicReachController.scheduleMessage,
        requestValidators: {
          body: ScheduleMessageBodySchema,
        },
      })
    )
    .delete(
      postRoutePreHandlerMiddleware,
      rateLimitMiddleware({
        module: 'MAGIC_REACH',
      }),
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: magicReachController.deleteScheduleMessage,
      })
    );

  router
    .route(
      '/communities/:communityId/magic-reach/message/:messageId/send-scheduled'
    )
    .post(
      postRoutePreHandlerMiddleware,
      rateLimitMiddleware({
        module: 'MAGIC_REACH',
      }),
      apiKeyValidator,
      handlerWrapper({
        handler: magicReachController.sendMessage,
        requestValidators: {
          body: SendMessageBodySchema,
        },
      })
    );

  router
    .route('/communities/:communityId/magic-reach/message/:messageId/send')
    .post(
      postRoutePreHandlerMiddleware,
      rateLimitMiddleware({
        module: 'MAGIC_REACH',
      }),
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: magicReachController.sendMessage,
        requestValidators: {
          body: SendMessageBodySchema,
        },
      })
    );

  router
    .route(
      '/communities/:communityId/magic-reach/message/:messageId/sendTest'
    )
    .post(
      postRoutePreHandlerMiddleware,
      rateLimitMiddleware({
        module: 'MAGIC_REACH',
      }),
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: magicReachController.sendTestMessage,
        requestValidators: {
          body: SendTestMessageBodySchema,
        },
      })
    );

  router
    .route(
      '/communities/:communityId/magic-reach/message/:messageId/sendStressTest'
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: magicReachController.stressTestMessage,
      })
    );

  router
    .route(
      '/communities/:communityId/magic-reach/message/:messageId/recipients'
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: magicReachController.getRecipients,
      })
    );

  router
    .route(
      '/communities/:communityId/magic-reach/message/:messageId/links'
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: magicReachController.getLinks,
        requestValidators: {
          query: GetLinksBodySchema,
        },
      })
    );

  router
    .route(
      '/communities/:communityId/magic-reach/message/:messageId/recipients/csv'
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      magicReachController.getRecipientsCSV
    );

  router.route('/communities/:communityId/magic-reach/messages').get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: magicReachController.getMessages,
    })
  );

  router
    .route('/communities/:communityId/magic-reach/limit-and-stats')
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: magicReachController.getCommunityMagicReachLimitAndStats,
      })
    );

  router.route('/communities/:communityId/magic-reach/count-opt-outs').get(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: 'MAGIC_REACH',
    }),
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: magicReachController.countOptOuts,
    })
  );

  router.route('/communities/:communityId/magic-reach/opt-outs').get(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: 'MAGIC_REACH',
    }),
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: magicReachController.getOptOuts,
    })
  );
};

const setupCOPSMagicReachRouter = function (router) {
  router
    .route(
      '/communities/magic-reach/message/:messageId/approveInReviewMessage'
    )
    .post(
      postRoutePreHandlerMiddleware,
      copsTokenMiddleware,
      handlerWrapper({
        handler: magicReachController.approveInReviewMessage,
      })
    );

  router
    .route(
      '/communities/magic-reach/message/:messageId/rejectInReviewMessage'
    )
    .post(
      postRoutePreHandlerMiddleware,
      copsTokenMiddleware,
      handlerWrapper({
        handler: magicReachController.rejectInReviewMessage,
      })
    );

  router.route('/communities/magic-reach/messages').get(
    postRoutePreHandlerMiddleware,
    copsTokenMiddleware,
    handlerWrapper({
      handler: magicReachController.getMessagesForCOPs,
    })
  );

  router.route('/communities/:communityId/magic-reach/stats').get(
    postRoutePreHandlerMiddleware,
    handlerWrapper({
      handler: magicReachController.getCommunityMagicReachStats,
    })
  );

  router.route('/communities/:communityId/magic-reach/limit').get(
    postRoutePreHandlerMiddleware,
    handlerWrapper({
      handler: magicReachController.getCommunityMagicReachLimit,
    })
  );
};

const setupRouter = function (router) {
  setupCMPMagicReachRouter(router);
  setupCOPSMagicReachRouter(router);
};

module.exports = {
  setupRouter,
};
