/* eslint-disable no-unused-vars */
const CommunityMagicReachEmailModel = require('../../../models/magicReach/communityMagicReachEmail.model');

const schema = require('./schema');
const {
  magicReachCompose,
  magicReachSend,
  magicReachList,
  magicReachCOPS,
} = require('../../../services/magicReach');
const membershipService = require('../../../services/membership');
const logger = require('../../../services/logger.service');
const { getFilters } = require('../../../services/magicReach/common');

const { getLanguagePreference } = require('../../../utils/headers.util');
const {
  ParamError,
  ResourceNotFoundError,
} = require('../../../utils/error.util');

const { MAX_PAGINATION_LIMIT } = require('../../../constants/common');

const getProducts = async (req, res, next) => {
  const { communityId } = req.params;
  const { pageSize, search } = req.query;

  if (pageSize > MAX_PAGINATION_LIMIT) {
    throw new ParamError('Pagination limit exceeded');
  }

  const results = await magicReachCompose.getProducts({
    communityId,
    search,
    pageSize,
  });

  return results;
};

const getBucket = async (req, res, next) => {
  const languagePreference = getLanguagePreference(req);
  const results = await magicReachCompose.getBucket({
    communityId: req.params.communityId,
    searchString: req.query.searchString,
    bucketName: req.query.bucketName,
    filters: getFilters(req.query),
    languagePreference,
  });
  return results;
};

const getBuckets = async (req, res, next) => {
  const languagePreference = getLanguagePreference(req);
  const results = await magicReachCompose.getBuckets({
    communityId: req.params.communityId,
    searchString: req.query.searchString,
    filters: getFilters(req.query),
    languagePreference,
  });
  return results;
};

const searchRecipients = async (req, res, next) => {
  const { queryParams, otherFilters } =
    await membershipService.membershipSearchUtils.processGetMembersQueryParams(
      req.query
    );

  const results = await magicReachCompose.searchRecipients({
    communityId: req.params.communityId,
    bucketName: req.query.bucketName ? req.query.bucketName : 'All',
    searchString: req.query.searchString,
    communityRole: queryParams.role || [],
    status: queryParams.status || [],
    pageNo: parseInt(req.query.pageNo, 10),
    pageSize: parseInt(req.query.pageSize, 10),
    sortBy: req.query.sortBy,
    sortOrder: req.query.sortOrder,
    otherFilters,
    filters: getFilters(req.query),
  });
  return results;
};

const searchRecipientsInternal = async (req, res, next) => {
  const results = await magicReachCompose.searchRecipients({
    communityId: req.params.communityId,
    bucketName: req.body.bucketName,
    searchString: req.body.searchString,
    communityRole: req.body.role || [],
    status: req.body.status || [],
    magicReachEmailObjectId: req.body.magicReachEmailObjectId,
    pageNo: req.body.pageNo,
    pageSize: req.body.pageSize,
    sortBy: req.body.sortBy,
    sortOrder: req.body.sortOrder,
    otherFilters: req.body.otherFilters,
    filters: req.body.filters,
    startObjectId: req.body.startObjectId,
    endObjectId: req.body.endObjectId,
  });
  return results;
};

const countRecipients = async (req, res, next) => {
  const count = await magicReachCompose.countRecipients({
    communityId: req.params.communityId,
    selectedUsers: req.body.selectedUsers || [],
    unselectedUsers: req.body.unselectedUsers || [],
    selectedBuckets: req.body.selectedBuckets || [],
    bucketFilters: req.body.bucketFilters || {},
  });
  return { count: count.total };
};

const countOptOuts = async (req, res, next) => {
  const optOutsCount = await magicReachCompose.countOptOuts({
    communityId: req.params.communityId,
  });
  return optOutsCount;
};

const getOptOuts = async (req, res, next) => {
  const limit = parseInt(req.query.pageSize || 100, 10);
  let skip = 0;
  if (req.query.pageNo) {
    skip = (parseInt(req.query.pageNo, 10) - 1) * limit;
  }

  const optOuts = await magicReachCompose.getOptOuts({
    communityId: req.params.communityId,
    skip,
    limit,
    sortBy: req.query.sortBy,
    sortOrder: req.query.sortOrder,
  });
  return optOuts;
};

const createMessage = async (req, res, next) => {
  const messageId = await magicReachCompose.createMessage({
    authorUserObjectId: req.user._id,
    communityId: req.params.communityId,
    messageData: req.body,
  });
  return { messageId };
};

const updateMessage = async (req, res, next) => {
  const messageData = req.body;
  if (!messageData.author) {
    messageData.author = req.userObject?._id;
  }
  await magicReachCompose.updateMessage({
    communityId: req.params.communityId,
    messageId: req.params.messageId,
    messageData,
  });
};

const getMessage = async (req, res, next) => {
  const message = await magicReachCompose.getMessageData(
    req.params.messageId
  );
  return message;
};

const deleteMessage = async (req, res, next) => {
  await magicReachCompose.deleteMessage({
    messageId: req.params.messageId,
  });
};

const scheduleMessage = async (req, res, next) => {
  const messageData = req.body;
  if (!messageData.author) {
    messageData.author = req.userObject?._id;
  }
  await magicReachSend.scheduleMessage({
    communityId: req.params.communityId,
    messageId: req.params.messageId,
    messageData,
  });
};

const deleteScheduleMessage = async (req, res, next) => {
  await magicReachCompose.removeScheduleAndUpdateMessageAsDraft({
    messageId: req.params.messageId,
  });
};

const sendMessage = async (req, res, next) => {
  const messageData = req.body;
  // log content of message data without the messageData.conent
  logger.info('[sendMessageController] req body:', {
    reqBody: {
      ...messageData,
      content: '<<content>>',
    },
  });
  if (!messageData.author) {
    messageData.author = req.userObject?._id;
  }
  await magicReachSend.sendMessage({
    communityId: req.params.communityId,
    messageId: req.params.messageId,
    messageData,
  });
};

const sendTestMessage = async (req, res, next) => {
  const messageData = req.body;
  if (!messageData.author) {
    messageData.author = req.userObject?._id;
  }
  await magicReachSend.sendTestMessage({
    communityId: req.params.communityId,
    messageId: req.params.messageId,
    messageData,
    platform: req.body.platform,
    recipients: req.body.recipients,
  });
};

const stressTestMessage = async (req, res, next) => {
  await magicReachSend.stressTestMessage({
    numberOfRecipients: req.body.numberOfRecipients,
    platform: req.body.platform,
    communityId: req.params.communityId,
    messageId: req.params.messageId,
    messageData: req.body,
    testEmailPrefix: req.body.testEmailPrefix,
    testEmailDomain: req.body.testEmailDomain,
    testPhoneNumbers: req.body.testPhoneNumbers,
  });
};

const getMessages = async (req, res, next) => {
  const results = await magicReachList.getMessages({
    pageNo: parseInt(req.query.pageNo, 10),
    pageSize: parseInt(req.query.pageSize, 10),
    messageStatus: req.query.messageStatus,
    communityId: req.params.communityId,
    messageSearchString: req.params.messageSearchString,
  });
  return results;
};

const getLinks = async (req, res, next) => {
  const { messageId } = req.params;
  const { hasPaid, platform, pageNo, pageSize } =
    schema.GetLinksBodySchema.cast(req.query);
  const magicReachEmail = await CommunityMagicReachEmailModel.findById(
    messageId
  ).lean();
  if (!magicReachEmail) {
    throw new ResourceNotFoundError(
      'No email with this message id is found'
    );
  }
  if (magicReachEmail.archivedRecipientsResult) {
    if (magicReachEmail.archivedRecipientsResult.attemptedAndSkipped) {
      return {};
    }
  }

  const results = await magicReachList.getLinks({
    pageNo,
    pageSize,
    magicReachEmail,
    hasPaid,
    platform,
  });
  return results;
};

const getRecipients = async (req, res, next) => {
  const email = await CommunityMagicReachEmailModel.findById(
    req.params.messageId
  );
  if (!email) {
    const error = new Error('No email with this message id is found');
    error.statusCode = 404;
    throw error;
  }
  if (email.archivedRecipientsResult) {
    if (email.archivedRecipientsResult.attemptedAndSkipped) {
      const error = new Error(
        'This email has been attempted-archived but has no recipients.'
      );
      error.statusCode = 404;
      throw error;
    }
    const error = new Error(
      'The recipients for this email has been archived. Please call /recipients/csv API to retrieve the CSV'
    );
    error.statusCode = 410;
    throw error;
  }
  const results = await magicReachList.getRecipients({
    pageNo: parseInt(req.query.pageNo, 10),
    pageSize: parseInt(req.query.pageSize, 10),
    requestQueryParams: req.query,
    messageType: req.query.messageType,
    communityId: req.params.communityId,
    message: email,
    platform: req.query.platform,
  });
  return results;
};

const getRecipientsCSV = async (req, res, next) => {
  try {
    const csvStream = await magicReachList.getRecipientsCSVStream({
      messageId: req.params.messageId,
    });
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="${req.params.messageId}.csv"`
    );
    res.setHeader('Content-Type', 'text/csv');
    csvStream.pipe(res);
    csvStream.on('end', () => {
      res.end();
    });
    csvStream.on('error', (err) => {
      if (err.code === 'NoSuchKey') {
        logger.error(`File ${req.params.messageId}.csv is not found`, err);
        res
          .status(404)
          .send(`File ${req.params.messageId}.csv is not found.`);
      } else {
        logger.error('Error streaming file from S3:', err);
        res.status(500).send('Error fetching file');
      }
    });
  } catch (err) {
    logger.error('Error when fetching file : ', err);
    res.status(500).send('Error fetching file');
  }
};

const getCommunityMagicReachLimitAndStats = async (req, res, next) => {
  const results =
    await magicReachCompose.getCommunityMagicReachLimitAndStats({
      communityId: req.params.communityId,
      window: req.query.window,
    });
  return results;
};

const approveInReviewMessage = async (req, res, next) => {
  await magicReachCOPS.approveInReviewMessage({
    communityId: req.params.communityId,
    messageId: req.params.messageId,
    operator: req.body.operator,
    approveReason: req.body.approveReason,
    localizedReason: req.body.localizedReason,
  });
};

const rejectInReviewMessage = async (req, res, next) => {
  await magicReachCOPS.rejectInReviewMessage({
    messageId: req.params.messageId,
    operator: req.body.operator,
    localizedReason: req.body.localizedReason,
  });
};

const getMessagesForCOPs = async (req, res, next) => {
  const results = await magicReachCOPS.getMessages({
    messageStatus: req.query.messageStatus,
    fraudStatus: req.query.fraudStatus,
    communitySearchString: req.query.communitySearchString,
    messageSearchString: req.query.messageSearchString,
    isOutreachEmail: Boolean(req.query.isOutreachEmail),
    communityCode: req.query.communityCode,
    creatorEmail: req.query.creatorEmail,
    recipientEmail: req.query.recipientEmail,
    planType: req.query.planType,
    messageObjectId: req.query.messageObjectId,
    reviewStatus: req.query.reviewStatus,
    sentDateFrom: req.query.sentDateFrom,
    sentDateTo: req.query.sentDateTo,
    createdAtDateFrom: req.query.createdAtDateFrom,
    createdAtDateTo: req.query.createdAtDateTo,
    reviewedDateFrom: req.query.reviewedDateFrom,
    reviewedDateTo: req.query.reviewedDateTo,
    pageNo: parseInt(req.query.pageNo, 10),
    pageSize: parseInt(req.query.pageSize, 10),
    sortBy: req.query.sortBy || 'createdAt',
    sortOrder: req.query.sortOrder || -1,
  });
  return results;
};

const getCommunityMagicReachStats = async (req, res, next) => {
  const results = await magicReachCompose.getCommunityMagicReachStats({
    communityId: req.params.communityId,
    window: req.query.window,
  });
  return results;
};

const getCommunityMagicReachLimit = async (req, res, next) => {
  const results = await magicReachCompose.getCommunityMagicReachLimit({
    communityId: req.params.communityId,
  });
  return results;
};

module.exports = {
  getLinks,
  getProducts,
  getBucket,
  getBuckets,
  searchRecipients,
  searchRecipientsInternal,
  countRecipients,
  countOptOuts,
  getOptOuts,
  createMessage,
  updateMessage,
  getMessage,
  sendMessage,
  sendTestMessage,
  stressTestMessage,
  getMessages,
  getRecipients,
  getRecipientsCSV,
  getCommunityMagicReachLimitAndStats,
  approveInReviewMessage,
  rejectInReviewMessage,
  getMessagesForCOPs,
  getCommunityMagicReachStats,
  getCommunityMagicReachLimit,
  scheduleMessage,
  deleteScheduleMessage,
  deleteMessage,
};
