const { SubscriptionService } = require('../../../services/subscriptions');
const LearnerService = require('../../../services/learner.service');
const GetCommunityService = require('../../../services/community/getCommunity.service');
const {
  subscriptionPlanService,
} = require('../../../services/communitySubscription');
const CommunitySubscriptionService = require('../../../communitiesAPI/services/common/communitySubscriptions.service');
const logger = require('../../../services/logger.service');
const schema = require('./schema');
const { InternalError, ParamError } = require('../../../utils/error.util');

exports.getSubscriptions = async (req, res, next) => {
  try {
    const learner = req.user.learner;
    const learnerObjectId = learner._id;
    const { subscriptionId } = await schema.subscriptionIdSchema.cast(
      req.query
    );

    const result = await SubscriptionService.getSubscriptions(
      learnerObjectId,
      subscriptionId
    );
    return res.json({ data: result });
  } catch (err) {
    logger.error('getSubscriptions failed due to', err.message);
    return next(err);
  }
};

exports.changePlan = async (req, res, next) => {
  try {
    const learner = req.user.learner;
    const learnerObjectId = learner._id;

    const { subscriptionId: subscriptionObjectId } =
      await schema.subscriptionIdSchema.cast(req.params);

    const { priceId, discountCode } =
      await schema.postChangePlanSchema.cast(req.body);

    const result = await subscriptionPlanService.changePlan({
      subscriptionObjectId,
      learnerObjectId,
      priceId,
      discountCode,
    });

    return res.json({ data: result });
  } catch (err) {
    const errorMessage = err.isAxiosError
      ? err.response?.data?.errorMessage
      : err.message;

    logger.error('changePlan failed due to', errorMessage, err.stack);

    let error = err;

    if (err.isAxiosError) {
      error = new InternalError(errorMessage);
    }

    return next(error);
  }
};

// Handles PayPal plan changes triggered by PayPal webhooks, called by the payment backend
exports.changePlanUpdate = async (req, res, next) => {
  try {
    const { purchaseTransactionObjectId } =
      await schema.changePlanUpdateSchema.cast(req.body);

    const result = await subscriptionPlanService.changePlanUpdate(
      purchaseTransactionObjectId
    );

    return res.json({ data: result });
  } catch (err) {
    const errorMessage = err.isAxiosError
      ? err.response?.data?.errorMessage
      : err.message;

    logger.error(
      'changePlanUpdate failed due to',
      errorMessage,
      err.stack
    );

    let error = err;

    if (err.isAxiosError) {
      error = new InternalError(errorMessage);
    }

    return next(error);
  }
};

exports.revertPlanChange = async (req) => {
  const learner = req.user.learner;
  const learnerObjectId = learner._id;

  const { subscriptionId: subscriptionObjectId } =
    schema.subscriptionIdSchema.cast(req.params);

  const result = await subscriptionPlanService.revertPlanChange({
    subscriptionObjectId,
    learnerObjectId,
  });

  return result;
};

exports.findOrCreateFreeSubscription = async (req, res, next) => {
  const { learnerObjectId, communityObjectId } =
    await schema.findOrCreateFreeSubscriptionSchema.cast(req.body);

  const [learner, community] = await Promise.all([
    LearnerService.getLearner(
      { _id: learnerObjectId },
      { learnerId: 1, email: 1 }
    ),
    GetCommunityService.getCommunityById({
      communityId: communityObjectId,
    }),
  ]);

  if (!learner) {
    next(new ParamError('Learner not found'));
  }
  if (!community) {
    next(new ParamError('Community not found'));
  }

  const subscriptionDoc = {
    communityCode: community.code,
    learnerObjectId,
    learnerId: learner.learnerId,
    email: learner.email,
  };
  const subscription =
    await CommunitySubscriptionService.findOneOrCreateSubscription(
      subscriptionDoc,
      community,
      { sendNotification: true, bypassPendingApproval: true }
    );
  return res.json({ data: subscription });
};
