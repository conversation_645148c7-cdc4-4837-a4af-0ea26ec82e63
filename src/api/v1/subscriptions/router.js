const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  validateAll,
} = require('../../../middleware/validator.middleware');
const schema = require('./schema');
const controller = require('./controller');
const apiKeyValidation = require('../../../validations/apiKey.validation');
const { handlerWrapper } = require('../../../utils/request.util');

exports.setupRouter = function (router) {
  router.route('/subscriptions').get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    validateAll([
      {
        schema: schema.subscriptionIdSchema,
        location: 'query',
      },
    ]),
    controller.getSubscriptions
  );

  router
    .route('/subscriptions/:subscriptionId/change-plan')
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      validateAll([
        {
          schema: schema.subscriptionIdSchema,
          location: 'params',
        },
        {
          schema: schema.postChangePlanSchema,
          location: 'body',
        },
      ]),
      controller.changePlan
    )
    .put(
      postRoutePreHandlerMiddleware,
      apiKeyValidation, // request from our internal services
      validateAll([
        {
          schema: schema.changePlanUpdateSchema,
          location: 'body',
        },
      ]),
      controller.changePlanUpdate
    );

  router.route('/subscriptions/:subscriptionId/revert-plan-change').put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    handlerWrapper({
      handler: controller.revertPlanChange,
      requestValidators: {
        params: schema.subscriptionIdSchema,
      },
    })
  );

  router.route('/subscriptions/find-or-create-free-subscription').post(
    postRoutePreHandlerMiddleware,
    apiKeyValidation,
    validateAll([
      {
        schema: schema.findOrCreateFreeSubscriptionSchema,
        location: 'body',
      },
    ]),
    controller.findOrCreateFreeSubscription
  );
};
