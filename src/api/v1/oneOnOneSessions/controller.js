const { Info } = require('luxon');
const communityFoldersModel = require('../../../communitiesAPI/models/communityFolders.model');
const logger = require('../../../services/logger.service');
const {
  sessionSchema,
  sessionAttendeesSchema,
  sessionAttendeesCancelledBodySchema,
  sessionBookingsQuerySchema,
  getSessionBookingsQuerySchema,
  sessionBookingsParamsSchema,
  getExportSessionsBookingQuerySchema,
} = require('./schema');
const manageOneOneService = require('../../../services/oneOnOneSession/manageOneOnOneSession.service');
const oneOnOneSessionValidators = require('./validations');
const { PRODUCT_ERROR } = require('../../../constants/errorCode');
const {
  checkIfResourcesPostsOrEventsWithSlugExists,
} = require('../../../communitiesAPI/services/common/utils');
const { ToUserError, ParamError } = require('../../../utils/error.util');
const {
  checkIfValidPriceOfEntity,
} = require('../../../communitiesAPI/services/common/entityPriceValidation');
const {
  COMMUNITY_FOLDER_PURCHASE_TYPES,
} = require('../../../communitiesAPI/constants');
const {
  getAddonPriceInLocalCurrency,
} = require('../../../communitiesAPI/services/common/communityAddonPrice.service');
const communityFolderAccessLogsModel = require('../../../communitiesAPI/models/communityFolderAccessLogs.model');
const { getUserIP } = require('../../../utils/headers.util');
const AiCofounderProductCreationService = require('@/src/services/featurePermissions/aiCofounderProductCreation.service');

exports.createOneOnOneSession = async (req) => {
  const { body } = req;
  const { communityId: communityObjectId } = req.params;
  const createSessionObject = sessionSchema.cast(body);

  const totalNumberOfFolders = await communityFoldersModel.countDocuments({
    communityObjectId,
  });

  // check if a folder exists with the same resourceSlug

  const { duplicateFromSessionId, templateLibraryId } =
    createSessionObject;

  if (duplicateFromSessionId) {
    const sessionToDuplicate = await communityFoldersModel.findOne({
      _id: duplicateFromSessionId,
      communityObjectId,
    });

    if (!sessionToDuplicate) {
      throw new ToUserError('Session to duplicate not found');
    }

    createSessionObject.availability = sessionToDuplicate.availability;
    createSessionObject.unAvailableDates =
      sessionToDuplicate.unAvailableDates;
    createSessionObject.timezoneChosenForAvailability =
      sessionToDuplicate.timezoneChosenForAvailability;

    delete createSessionObject.duplicateFromSessionId;
  }

  if (templateLibraryId) {
    await AiCofounderProductCreationService.checkProductCreationEligibility(
      communityObjectId,
      templateLibraryId
    );
    // generate a slug for the session since its a template
    const resourceSlug = await manageOneOneService.generateSlug(
      communityObjectId
    );
    createSessionObject.resourceSlug = `/${resourceSlug}`;
  }
  const folderWithSameResourceSlug =
    await checkIfResourcesPostsOrEventsWithSlugExists(
      createSessionObject.resourceSlug,
      communityObjectId
    );

  if (folderWithSameResourceSlug) {
    throw new ToUserError(
      'Another session found with the same URL. Please update the URL for this session.',
      PRODUCT_ERROR.DUPLICATE_SESSION_SLUG
    );
  }

  createSessionObject.index =
    totalNumberOfFolders !== 0 ? totalNumberOfFolders + 1 : 0;

  // create session with the help of a service function

  const oneOnOneSession = await manageOneOneService.createOneOnOneSession(
    createSessionObject,
    createSessionObject?.communityObjectId
  );

  return oneOnOneSession;
};

exports.updateOneOnOneSession = async (req) => {
  const { body } = req;
  const { communityId: communityObjectId, sessionId } = req.params;
  const { selectedAmount, paymentMethodCountryCode, paymentProvider } =
    req.query;
  const ip = getUserIP(req) || null;
  const learnerObjectId = req.user?.learner?._id;

  const oneOneOneSession = await communityFoldersModel.findOne({
    _id: sessionId,
    communityObjectId,
  });

  // Validate the folder price
  if (body?.access === COMMUNITY_FOLDER_PURCHASE_TYPES.PAID) {
    await checkIfValidPriceOfEntity(body?.amount, body?.currency);
  }
  const sessionUpdateObj =
    await oneOnOneSessionValidators.editOneSessionValidation(
      sessionSchema.cast(body),
      oneOneOneSession
    );

  let updatedSession = await manageOneOneService.updateOneOnOneSession(
    sessionUpdateObj,
    sessionId,
    communityObjectId,
    learnerObjectId
  );
  const changeLogId = updatedSession.changeLogId;

  updatedSession = await manageOneOneService.getOneOnOneSession(
    learnerObjectId,
    updatedSession._id,
    false,
    updatedSession?.timezoneChosenForAvailability,
    true
  );
  updatedSession.changeLogId = changeLogId;

  let result = updatedSession;
  if (result.access === COMMUNITY_FOLDER_PURCHASE_TYPES.PAID) {
    const priceDetails = await getAddonPriceInLocalCurrency({
      ip,
      addon: updatedSession,
      communityObjectId,
      selectedAmount,
      paymentMethodCountryCode,
      paymentProvider,
    });
    logger.info('Price in local currency: ', priceDetails);
    result = { priceDetails, ...updatedSession };
    result['isPurchased'] = true;
    const priceFieldsToRemove = [
      'localiseForAllCountries',
      'countryWisePrice',
    ];
    priceFieldsToRemove.forEach(
      (priceFieldToRemove) => delete result[priceFieldToRemove]
    );
    const folderViewCount = await communityFolderAccessLogsModel.count({
      communityFolderObjectId: updatedSession._id,
    });
    result['folderViewCount'] = folderViewCount;
  }
  return result;
};

exports.getOneOnOneSessionSlots = async (req) => {
  const { communityId: communityObjectId, sessionId } = req.params;
  const { timezone, rangeStart, rangeEnd } = req.query;

  if (!timezone || !rangeStart || !rangeEnd) {
    throw new ParamError('Invalid Parameters');
  }

  //check if timezone is valid
  const isValid = Info.isValidIANAZone(timezone);

  if (!isValid) {
    throw new ParamError('Invalid Parameters, wrong timezone');
  }

  const oneOneOneSession = await communityFoldersModel
    .findOne({
      _id: sessionId,
      communityObjectId,
    })
    .lean();

  const slotsForTheRange =
    await manageOneOneService.getOneOnOneSessionSlots(
      oneOneOneSession,
      timezone,
      rangeStart,
      rangeEnd
    );

  return slotsForTheRange;
};

exports.getHostInfo = async (req) => {
  const { hostObjectId } = req.params;

  const hostSessions = await manageOneOneService.getHostInfo(hostObjectId);

  return hostSessions;
};

exports.bookSession = async (req) => {
  const { body } = req;
  const { communityId: communityObjectId, sessionId } = req.params;

  const bookingBody = sessionAttendeesSchema.cast({
    ...body,
    sessionObjectId: sessionId,
  });
  const bookingObject = await manageOneOneService.bookSessionService(
    bookingBody,
    communityObjectId
  );

  return bookingObject;
};

exports.getSessionBookings = async (req) => {
  const { communityId: communityObjectId, sessionId } = req.params;
  const { limit, type, page, search, sortBy, order } =
    sessionBookingsQuerySchema.cast(req.query);

  const sortOrder = order === 'asc' ? 1 : -1;

  const bookings = await manageOneOneService.getSessionBookings(
    sessionId,
    communityObjectId,
    limit,
    page,
    type,
    search,
    sortBy,
    sortOrder
  );

  return bookings;
};

exports.cancelSessionBookings = async (req) => {
  const { communityId: communityObjectId, sessionId } = req.params;

  const { requestRefund, reasonForRefund, bookingId, transactionId } =
    sessionAttendeesCancelledBodySchema.cast(req.body);

  const cmInfo = req.user;

  const cancelSession = await manageOneOneService.cancelSession({
    sessionId,
    communityObjectId,
    requestRefund,
    reasonForRefund,
    bookingId,
    transactionId,
    userInfo: cmInfo,
  });

  return cancelSession;
};

exports.getBookings = async (req) => {
  const { status, sessionId } = getSessionBookingsQuerySchema.cast(
    req.query
  );

  const bookings = await manageOneOneService.getBookings(
    status,
    sessionId,
    req.user
  );

  return bookings;
};

exports.getSessionBookingsCSV = async (req, res) => {
  try {
    const { communityId, sessionId } = sessionBookingsParamsSchema.cast(
      req.params
    );
    const { search, type, order } =
      getExportSessionsBookingQuerySchema.cast(req.query);

    const fileName = await manageOneOneService.getFileNameForExport(
      communityId,
      sessionId
    );

    res.setHeader(
      'Content-Disposition',
      `attachment; filename="${fileName}"`
    );

    res.setHeader('Content-Type', 'text/csv');

    const pageSize = 100;
    const sessionAttendeeCsvStream =
      await manageOneOneService.generateSessionAttendeesCsvStream({
        search,
        type,
        pageSize,
        order,
        communityId,
        sessionId,
      });

    if (!sessionAttendeeCsvStream) {
      logger.error('Failed to generate CSV stream');
      res.status(500).json({ error: 'Failed to generate CSV stream' });
    }

    sessionAttendeeCsvStream.on('error', (err) => {
      logger.error('Error occurred while streaming CSV: ', err);
      res.status(500).json({ error: 'Failed to generate CSV stream' });
    });
    sessionAttendeeCsvStream.pipe(res);
    sessionAttendeeCsvStream.on('end', () => {
      res.end();
    });
  } catch (err) {
    logger.error('getExportEventAttendees failed due to', err, err.stack);
    return res.status(500).json({ error: 'Internal Server Error' });
  }
};

exports.sendSessionBookingsReminder = async () => {
  const reminderEmailsInfo =
    await manageOneOneService.sendSessionAttendeesBookingsReminder();

  return reminderEmailsInfo;
};
