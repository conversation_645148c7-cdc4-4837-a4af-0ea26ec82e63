const { DateTime } = require('luxon');

const {
  COMMUNITY_FOLDER_STATUS,
} = require('../../../communitiesAPI/constants');
const { PRODUCT_ERROR } = require('../../../constants/errorCode');
const { ToUserError } = require('../../../utils/error.util');

// Function to calculate the difference in minutes between two time strings
const getMinutesDifference = (fromTime, toTime) => {
  const [fromHours, fromMinutes] = fromTime.split(':').map(Number);
  const [toHours, toMinutes] = toTime.split(':').map(Number);

  const fromMinutesTotal = fromHours * 60 + fromMinutes;
  const toMinutesTotal = toHours * 60 + toMinutes;

  return toMinutesTotal - fromMinutesTotal;
};

function convertIntervalsToUTCUsingDay(day, fromTime, toTime, timezone) {
  const fromDateTime = DateTime.fromFormat(day, 'EEEE', {
    zone: timezone,
  }).set({
    hour: parseInt(fromTime.split(':')[0], 10),
    minute: parseInt(fromTime.split(':')[1], 10),
    second: 0,
  });

  const toDateTime = DateTime.fromFormat(day, 'EEEE', {
    zone: timezone,
  }).set({
    hour: parseInt(toTime.split(':')[0], 10),
    minute: parseInt(toTime.split(':')[1], 10),
    second: 0,
  });

  const fromTimeInUTC = fromDateTime.toUTC();
  const toTimeInUTC = toDateTime.toUTC();

  const isToTime24Hour =
    toTimeInUTC.hour === 0 && toTimeInUTC.minute === 0;
  const crossesDay =
    fromTimeInUTC.startOf('day').toMillis() !==
    toTimeInUTC.startOf('day').toMillis();

  const intervalsInUTC = [];

  if (crossesDay && !isToTime24Hour) {
    intervalsInUTC.push({
      day: fromTimeInUTC.weekdayLong,
      intervals: [{ from: fromTimeInUTC.toFormat('HH:mm'), to: '00:00' }],
    });
    intervalsInUTC.push({
      day: toTimeInUTC.weekdayLong,
      intervals: [{ from: '00:00', to: toTimeInUTC.toFormat('HH:mm') }],
    });
  } else {
    intervalsInUTC.push({
      day: fromTimeInUTC.weekdayLong,
      intervals: [
        {
          from: fromTimeInUTC.toFormat('HH:mm'),
          to: toTimeInUTC.toFormat('HH:mm'),
        },
      ],
    });
  }

  return intervalsInUTC;
}

const convertIntervalsToUTCUsingDate = (
  date,
  fromTime,
  toTime,
  timezone
) => {
  // Parse the input times and create Luxon DateTime objects
  const fromDateTime = DateTime.fromFormat(date, 'dd/MM/yyyy', {
    zone: timezone,
  }).set({
    hour: fromTime.split(':')[0],
    minute: fromTime.split(':')[1],
    second: 0,
  });

  const toDateTime = DateTime.fromFormat(date, 'dd/MM/yyyy', {
    zone: timezone,
  }).set({
    hour: toTime.split(':')[0],
    minute: toTime.split(':')[1],
    second: 0,
  });

  // Convert the from and to times to UTC
  const fromTimeInUTC = fromDateTime.toUTC();
  const toTimeInUTC = toDateTime.toUTC();

  let isToTime24Hour = false;

  if (toTimeInUTC.hour === 0 && toTimeInUTC.minute === 0) {
    isToTime24Hour = true;
  }
  // Check if the interval crosses midnight
  if (fromTimeInUTC.day < toTimeInUTC.day && !isToTime24Hour) {
    // Interval for the first part (from midnight to 'toTime')
    const firstPart = {
      from: fromTimeInUTC.toFormat('HH:mm'),
      to: '00:00',
    };

    // Interval for the second part ('fromTime' to midnight)
    const secondPart = {
      from: '00:00',
      to: toTimeInUTC.toFormat('HH:mm'),
    };

    return [
      {
        day: fromTimeInUTC.toFormat('dd/MM/yyyy'),
        intervals: [firstPart],
      },
      {
        day: toTimeInUTC.toFormat('dd/MM/yyyy'),
        intervals: [secondPart],
      },
    ];
  }

  // Interval for a single day
  return [
    {
      day: fromTimeInUTC.toFormat('dd/MM/yyyy'),
      intervals: [
        {
          from: fromTimeInUTC.toFormat('HH:mm'),
          to: toTimeInUTC.toFormat('HH:mm'),
        },
      ],
    },
  ];
};

const checkIfOverlappingIntervals = (
  previousInterval,
  currentInterval
) => {
  if (!previousInterval || !currentInterval) {
    return false;
  }
  const previousFromTime = DateTime.fromFormat(
    previousInterval.from,
    'HH:mm'
  );

  const previousToTime = DateTime.fromFormat(previousInterval.to, 'HH:mm');

  const currentFromTime = DateTime.fromFormat(
    currentInterval.from,
    'HH:mm'
  );

  const currentToTime = DateTime.fromFormat(currentInterval.to, 'HH:mm');
  // check if there is a overlap of time if you see the interval 17:00 to 18:00 having an overlap of 17:30 to 18:30 so it is wrong there should no other session with in an interval

  // Check if there is an overlap between previous and current intervals
  if (
    (currentFromTime >= previousFromTime &&
      currentFromTime < previousToTime) ||
    (currentToTime > previousFromTime && currentToTime <= previousToTime)
  ) {
    // There is an overlap
    return true;
  }
  // There is no overlap
  return false;
};

const editOneSessionValidation = async (updateBody, currentSessionObj) => {
  const sessionUpdateObj = {
    currentSessionObj,
    ...updateBody,
  };

  const {
    status,
    availability,
    unAvailableDates,
    durationIntervalInMinutes,
    timezoneChosenForAvailability: timezone,
  } = sessionUpdateObj;

  // valudate if updateBody's locationValue is not empty when status === published
  if (
    status === COMMUNITY_FOLDER_STATUS.PUBLISHED &&
    !sessionUpdateObj?.location?.locationValue
  ) {
    throw new ToUserError(
      'Location is required when publishing the session',
      PRODUCT_ERROR.SESSION_LOCATION_REQUIRED
    );
  }

  // make sure that there is availability when publishing the session
  if (
    status === COMMUNITY_FOLDER_STATUS.PUBLISHED &&
    availability.length === 0
  ) {
    throw new ToUserError(
      'Availability is required when publishing the session',
      PRODUCT_ERROR.AVAILABILITY_REQUIRED
    );
  }

  /*
  Validation
   availability in the update body should be in the correct form,
   array of intervals should be correct,
   make sure that durationInInterval is correct
   */

  /*
  Validation
    durationIntervalInMinutes: 60,
    availability: [
    {
      day: 'Monday',
      intervals: [
        {
        from: '15:00',
        to: '16:00',
        }
      ]
    }
    ],
    timezone: 'Asia/Kolkata'
  */

  // convert the time into utc and store it in the availabilityArrayInUTC
  const availabilityArrayInUTC = [];
  for (let i = 0; i < availability.length; i++) {
    const { intervals } = availability[i];

    if (!intervals.length) {
      throw new ToUserError(
        'Intervals are required',
        PRODUCT_ERROR.INTERVALS_REQUIRED
      );
    }

    for (let j = 0; j < intervals.length; j++) {
      // check if the duration of the interval is correct
      const minutesDifference = getMinutesDifference(
        intervals[j].from,
        intervals[j].to === '00:00' ? '24:00' : intervals[j].to
      );

      const previousInterval = intervals[j - 1];

      if (previousInterval) {
        const isOverlapping = checkIfOverlappingIntervals(
          previousInterval,
          intervals[j]
        );

        if (isOverlapping) {
          throw new ToUserError(
            'Intervals are overlapping',
            PRODUCT_ERROR.INTERVALS_OVERLAPPING
          );
        }
      }
      if (minutesDifference < Number(durationIntervalInMinutes)) {
        // throw error
        throw new ToUserError(
          'Interval duration is incorrect',
          PRODUCT_ERROR.INTERVAL_DURATION_INCORRECT
        );
      }

      const allIntervals = convertIntervalsToUTCUsingDay(
        availability[i].day,
        intervals[j].from,
        intervals[j].to,
        timezone
      );

      // check if we have a same day in intervalInUTC and allIntervals
      // if yes then push the interval in that day
      // if no then create a new day and push the interval in that day

      for (let k = 0; k < allIntervals.length; k++) {
        const { day, intervals: computedInterval } = allIntervals[k];
        const isDayPresent = availabilityArrayInUTC.findIndex(
          (item) => item.day === day
        );

        if (isDayPresent !== -1) {
          // push the interval in the intervals array
          availabilityArrayInUTC[isDayPresent].intervals.push(
            ...computedInterval
          );
        }
        if (isDayPresent === -1) {
          // create a new day and push the interval in the intervals array
          availabilityArrayInUTC.push({
            day,
            intervals: computedInterval,
          });
        }
      }
    }
  }

  sessionUpdateObj.availability = availabilityArrayInUTC;

  // convert the time to UTC
  const unAvailableDatesArrayInUTC = [];

  for (let i = 0; i < unAvailableDates.length; i++) {
    const { intervals, date } = unAvailableDates[i];

    for (let j = 0; j < intervals.length; j++) {
      const { from, to } = intervals[j];

      const allIntervals = convertIntervalsToUTCUsingDate(
        date,
        from,
        to,
        timezone
      );

      // check if we have a same date in intervalsInUTC and allIntervals
      // if yes then push the interval in that date
      // if no then create a new date and push the interval in that date

      for (let k = 0; k < allIntervals.length; k++) {
        const { day, intervals: computedInterval } = allIntervals[k];

        const datePresentIndex = unAvailableDatesArrayInUTC.findIndex(
          (item) => item.date === day
        );

        if (datePresentIndex !== -1) {
          // push the interval in the intervals array

          // check if interval is overlapping
          const allIntervalsOfDate =
            unAvailableDatesArrayInUTC[datePresentIndex].intervals;
          for (let l = 0; l < allIntervalsOfDate.length; l++) {
            for (let m = 0; m < computedInterval.length; m++) {
              const isOverlapping = checkIfOverlappingIntervals(
                allIntervalsOfDate[l],
                computedInterval[m]
              );

              if (isOverlapping) {
                throw new ToUserError(
                  'Intervals are overlapping',
                  PRODUCT_ERROR.INTERVALS_OVERLAPPING
                );
              }
            }
          }

          unAvailableDatesArrayInUTC[datePresentIndex].intervals.push(
            ...computedInterval
          );
        }
        if (datePresentIndex === -1) {
          // create a new day and push the interval in the intervals array
          unAvailableDatesArrayInUTC.push({
            date: day,
            intervals: computedInterval,
          });
        }
      }
    }
  }

  sessionUpdateObj.unAvailableDates = unAvailableDatesArrayInUTC;

  return sessionUpdateObj;
};

module.exports = {
  editOneSessionValidation,
  convertIntervalsToUTCUsingDate,
  convertIntervalsToUTCUsingDay,
};
