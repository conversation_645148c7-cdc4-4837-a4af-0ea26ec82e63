const yup = require('yup');

const { INTENT_TYPE } = require('../../../services/chat/constants');
const {
  AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES,
} = require('@/src/constants/common');
const { PLATFORM } = require('@/src/services/aiCofounder/ai/constants');

exports.communityIdSchema = yup.object().shape({
  communityId: yup.string().trim().required(),
});

exports.chatIdSchema = yup.object().shape({
  communityId: yup.string().trim().required(),
  chatId: yup.string().trim().required(),
});

exports.templateIdSchema = yup.object().shape({
  communityId: yup.string().trim().required(),
  templateId: yup.string().trim().required(),
});

exports.messageIdSchema = yup.object().shape({
  communityId: yup.string().trim().required(),
  chatId: yup.string().trim().required(),
  messageId: yup.string().trim().required(),
});

exports.getMessagesSchema = yup.object().shape({
  batchSize: yup.number().notRequired(),
  previousObjectId: yup.string().notRequired(),
});

const attachment = yup.object().shape({
  url: yup.string().trim().required(),
  type: yup.string().trim().required(),
  name: yup.string().trim().required(),
});

const messageSchema = yup.object().shape({
  message: yup
    .string()
    .trim()
    .required()
    .max(2000, 'Message cannot be longer than 2000 characters'),
  attachments: yup.array(attachment).notRequired().nullable().default([]),
  intentType: yup
    .string()
    .trim()
    .required()
    .oneOf(Object.values(INTENT_TYPE)),
  responseId: yup.string().trim().notRequired().nullable(),
  templateObjectId: yup.string().trim().notRequired().nullable(),
  actionType: yup
    .string()
    .trim()
    .when('intentType', {
      is: INTENT_TYPE.ACTION,
      then: (schema) => schema.required(),
      otherwise: (schema) => schema.notRequired().nullable(),
    }),
  platform: yup
    .string()
    .trim()
    .nullable()
    .oneOf([PLATFORM.WEB, PLATFORM.APP])
    .default(PLATFORM.WEB),
  timezone: yup.string().trim().notRequired().nullable(),
});

exports.postMessageSchema = messageSchema;

exports.createChatSchema = yup.object().shape({
  message: yup
    .string()
    .trim()
    .required()
    .max(2000, 'Message cannot be longer than 2000 characters'),
});

exports.batchSchema = yup.object().shape({
  search: yup.string().trim().notRequired(),
  batchSize: yup.number().notRequired().integer(),
  lastObjectId: yup.string().trim().notRequired().nullable(),
});

exports.lastObjectIdSchema = yup.object().shape({
  lastObjectId: yup.string().trim().required(),
});

exports.updateTemplateSchema = yup.object().shape({
  thumbnailImgSrc: yup.string().trim().required(),
  metadata: yup.object().required(),
  pricingConfig: yup.object().required(),
});

exports.templateVersionSchema = yup.object().shape({
  version: yup.number().required(),
});

exports.getTemplatesSchema = yup.object().shape({
  templateObjectIds: yup
    .array()
    .of(yup.string().trim().required())
    .max(100, 'Too many template object ids')
    .required(),
});

exports.generateTemplatesSchema = yup.object().shape({
  templates: yup
    .array()
    .of(
      yup.object().shape({
        type: yup
          .string()
          .trim()
          .required()
          .oneOf([
            AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.ADS,
            AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.LEADS,
          ]),
        assignTemplateObjectId: yup.string().trim().required(),
      })
    )
    .min(1, 'At least one valid template type is required')
    .required(),
});
