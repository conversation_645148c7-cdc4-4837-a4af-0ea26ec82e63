const {
  PRODUCT_STATUS,
  PRODUCT_PRICE_TYPE,
} = require('@/src/services/product/constants');
const schema = require('./schema');
const GetProductDataService = require('../../../services/product/getProductData.service');
const ProductLogService = require('../../../services/product/productChangeLog.service');
const changeLogFraudReviewService = require('../../../services/product/changeLogNotifyMessageFraudReview');

exports.getProductsForMembers = async (req) => {
  const { communityId } = req.params;
  const { user } = req;
  const {
    pageNo,
    pageSize,
    sortBy,
    sortOrder,
    searchString,
    productType,
    priceTypes,
    timingStatus,
  } = schema.productQueryParamSchema.cast(req.query);

  const queryParams = {
    communityObjectId: communityId,
    pageNo,
    pageSize,
    sortBy,
    sortOrder,
    searchString,
    productType,
    status: PRODUCT_STATUS.PUBLISHED,
    priceTypes,
    timingStatus,
    isVisible: true, // Ensure products are visible
  };
  // 1. Fetch product list
  const result = await GetProductDataService.getMemberProducts(
    queryParams
  );

  const products = result.products || [];

  // 2. Enrich all products in parallel
  let enrichedProducts = products;

  // Batch fetch enrolment info if user exists
  if (user?.learner?._id && user?.isCommunityMember) {
    // Batch version: If you have a batch API, use it; otherwise, fallback to Promise.all
    const enrolmentPromises = products.map((product) =>
      GetProductDataService.isUserEnrolledInProduct({
        learnerId: user.learner._id,
        productId: product.entityObjectId,
        productType: product.productType,
        communityObjectId: communityId,
        isProductFree: product.priceType === PRODUCT_PRICE_TYPE.FREE,
      })
    );
    const enrolmentInfos = await Promise.all(enrolmentPromises);

    // Attach enrolmentInfo to each product
    enrichedProducts = products.map((product, idx) => ({
      ...product,
      enrolmentInfo: enrolmentInfos[idx],
    }));
  }

  const extraInfoPromises = enrichedProducts.map((product) =>
    GetProductDataService.getExtraProductInfo({
      productType: product.productType,
      productId: product.entityObjectId,
      enrolmentInfo: product.enrolmentInfo,
    })
  );

  const extraInfos = await Promise.all(extraInfoPromises);

  enrichedProducts = enrichedProducts.map((product, idx) => ({
    ...product,
    ...extraInfos[idx],
  }));

  result.products = enrichedProducts;
  result.communityMetadata =
    await GetProductDataService.addMetadataForCommunity({
      communityObjectId: communityId,
    });
  return result;
};

exports.getAllProductsForReordering = async (req) => {
  const { communityId } = req.params;

  const allProducts =
    await GetProductDataService.getAllProductsForReordering({
      communityObjectId: communityId,
    });

  return allProducts;
};

exports.changeDisplayProducts = async (req) => {
  const { communityId } = req.params;
  const { products } = schema.changeDisplayProductsSchema.cast(req.body);
  const result = await GetProductDataService.changeDisplayProducts({
    communityObjectId: communityId,
    products,
  });
  return result;
};

exports.getTopSellingProducts = async (req) => {
  const { communityId } = req.params;
  const { user } = req;

  const products = await GetProductDataService.getTopSellingProducts({
    communityObjectId: communityId,
  });

  // Enrich products with enrollment info if user exists
  let enrichedProducts = products;

  // Batch fetch enrolment info if user exists
  if (user?.learner?._id && user?.isCommunityMember) {
    const enrolmentPromises = products.map((product) =>
      GetProductDataService.isUserEnrolledInProduct({
        learnerId: user.learner._id,
        productId: product.entityObjectId,
        productType: product.productType,
        communityObjectId: communityId,
        isProductFree: product.priceType === PRODUCT_PRICE_TYPE.FREE,
      })
    );
    const enrolmentInfos = await Promise.all(enrolmentPromises);

    // Attach enrolmentInfo to each product
    enrichedProducts = products.map((product, idx) => ({
      ...product,
      enrolmentInfo: enrolmentInfos[idx],
    }));
  }

  const extraInfoPromises = enrichedProducts.map((product) =>
    GetProductDataService.getExtraProductInfo({
      productType: product.productType,
      productId: product.entityObjectId,
      enrolmentInfo: product.enrolmentInfo,
    })
  );

  const extraInfos = await Promise.all(extraInfoPromises);

  enrichedProducts = enrichedProducts.map((product, idx) => ({
    ...product,
    ...extraInfos[idx],
  }));

  return enrichedProducts;
};

exports.getProducts = async (req) => {
  const { communityId } = req.params;
  const {
    pageNo,
    pageSize,
    sortBy,
    sortOrder,
    searchString,
    productType,
    status,
    priceTypes,
    timingStatus,
  } = schema.productQueryParamSchema.cast(req.query);

  const result = await GetProductDataService.getProducts({
    communityObjectId: communityId,
    pageNo,
    pageSize,
    sortBy,
    sortOrder,
    searchString,
    productType,
    status,
    priceTypes,
    timingStatus,
  });
  return result;
};

exports.notify = async (req) => {
  const { changeLogId, notifyMessage } = schema.changeNotifySchema.cast(
    req.body
  );

  // Use the new notification service
  const result = await ProductLogService.notifyByChangeLogType({
    changeLogId,
    notifyMessage,
  });
  return {
    success: true,
    ...result,
  };
};

exports.getFraudReviewingLogs = async (req) => {
  const { pageNo = 1, pageSize = 15 } = schema.fraudReviewQuerySchema.cast(
    req.query
  );

  // Get paginated REVIEWING logs with product details
  const result = await changeLogFraudReviewService.getReviewingLogs({
    page: pageNo,
    limit: pageSize,
  });

  return result;
};

exports.processFraudReview = async (req) => {
  const { changeLogId, action, operator } = schema.fraudReviewSchema.cast(
    req.body
  );

  const result = await changeLogFraudReviewService.processFraudReview({
    changeLogId,
    action,
    operatorEmail: operator,
  });

  return result;
};
