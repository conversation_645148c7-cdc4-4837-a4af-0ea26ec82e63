const yup = require('yup');
const {
  PRODUCT_TYPE,
  PRODUCT_STATUS,
  PRODUCT_DATA_QUERY_GROUP,
  PRODUCT_SORT_FIELDS,
  FRAUD_REVIEW_OPERATION,
} = require('../../../services/product/constants');

exports.productQueryParamSchema = yup.object().shape({
  pageNo: yup.number().notRequired().default(1),
  pageSize: yup.number().notRequired().default(10),
  sortBy: yup
    .string()
    .oneOf(
      Object.values(PRODUCT_SORT_FIELDS),
      `sortBy must be one of: ${Object.values(PRODUCT_SORT_FIELDS).join(
        ', '
      )}`
    )
    .notRequired(),
  sortOrder: yup
    .number()
    .oneOf(
      [1, -1],
      'sortOrder must be "1" for ascending or "-1" for descending'
    )
    .notRequired(),

  searchString: yup.string().lowercase().notRequired().trim(), // search by product title

  // Filters
  productType: yup
    .string()
    .oneOf(
      Object.values(PRODUCT_TYPE),
      `productType must be one of: ${Object.values(PRODUCT_TYPE).join(
        ', '
      )}`
    )
    .notRequired(),
  status: yup
    .string()
    .oneOf(
      Object.values(PRODUCT_STATUS),
      `status must be one of: ${Object.values(PRODUCT_STATUS).join(', ')}`
    )
    .notRequired(),
  priceTypes: yup.string(),
  timingStatus: yup
    .string()
    .oneOf(
      Object.values(PRODUCT_DATA_QUERY_GROUP),
      `timingStatus must be one of: ${Object.values(
        PRODUCT_DATA_QUERY_GROUP
      ).join(', ')}`
    )
    .notRequired(),
});

exports.changeDisplayProductsSchema = yup.object().shape({
  products: yup.array().of(
    yup.object().shape({
      productId: yup.string().required(),
      index: yup.number().notRequired(),
      isVisible: yup.boolean().notRequired(),
    })
  ),
});

exports.changeNotifySchema = yup.object().shape({
  changeLogId: yup.string().required(),
  notifyMessage: yup.string().notRequired(),
});

exports.fraudReviewQuerySchema = yup.object().shape({
  pageNo: yup.number().integer().min(1).notRequired().default(1),
  pageSize: yup
    .number()
    .integer()
    .min(1)
    .max(100)
    .notRequired()
    .default(15),
});

exports.fraudReviewSchema = yup.object().shape({
  changeLogId: yup.string().required('Change log ID is required'),
  action: yup
    .string()
    .oneOf(
      Object.values(FRAUD_REVIEW_OPERATION),
      `Action must be one of: ${Object.values(FRAUD_REVIEW_OPERATION).join(
        ', '
      )}`
    )
    .required('Action is required'),
  operator: yup
    .string()
    .email('Operator must be a valid email')
    .required('Operator email is required'),
});
