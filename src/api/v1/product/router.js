const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  managerCommunityValidator,
  memberCommunityValidatorWithoutError,
} = require('../../../communitiesAPI/validations/community.validation');
const userValidationWithoutError = require('../../../validations/userValidationWithoutError.validation');
const tokenNoErrorValidator = require('../../../validations/tokenNoError.validation');

const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const { handlerWrapper } = require('../../../utils/request.util');
const copsTokenMiddleware = require('../../../middleware/cops-token.middleware');

const setupRouter = async function (router) {
  // this will be a public endpoint (TODO for MK: after confirming with product: I will add auth to get the enrolled status of the user)
  router.route('/communities/:communityId/members/unifiedProducts').get(
    postRoutePreHandlerMiddleware,
    tokenNoErrorValidator,
    userValidationWithoutError,
    memberCommunityValidatorWithoutError,
    handlerWrapper({
      handler: controller.getProductsForMembers,
      requestValidators: {
        query: schema.productQueryParamSchema,
      },
    })
  );

  router.route('/communities/:communityId/unifiedProducts/all').get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.getAllProductsForReordering,
    })
  );

  router
    .route('/communities/:communityId/unifiedProducts/display/settings')
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.changeDisplayProducts,
        requestValidators: {
          body: schema.changeDisplayProductsSchema,
        },
      })
    );

  router
    .route(
      '/communities/:communityId/unifiedProducts/top-selling-products'
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenNoErrorValidator,
      userValidationWithoutError,
      memberCommunityValidatorWithoutError,
      handlerWrapper({
        handler: controller.getTopSellingProducts,
      })
    );

  router.route('/communities/:communityId/unifiedProducts').get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.getProducts,
      requestValidators: {
        query: schema.productQueryParamSchema,
      },
    })
  );
  router.route('/communities/:communityId/unifiedProducts/notify').post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.notify,
      requestValidators: {
        body: schema.changeNotifySchema,
      },
    })
  );

  // Fraud notification review endpoints for COPS
  router.route('/communities/unifiedProducts/notify/fraud/cops').get(
    postRoutePreHandlerMiddleware,
    copsTokenMiddleware,
    handlerWrapper({
      handler: controller.getFraudReviewingLogs,
      requestValidators: {
        query: schema.fraudReviewQuerySchema,
      },
    })
  );

  router.route('/communities/unifiedProducts/notify/fraud/cops').post(
    postRoutePreHandlerMiddleware,
    copsTokenMiddleware,
    handlerWrapper({
      handler: controller.processFraudReview,
      requestValidators: {
        body: schema.fraudReviewSchema,
      },
    })
  );
};

module.exports = {
  setupRouter,
};
