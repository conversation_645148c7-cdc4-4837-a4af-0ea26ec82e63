const { Schema, model } = require('mongoose');
const Community = require('../../communitiesAPI/models/community.model');
const User = require('../users.model');

const ScheduleSchema = new Schema(
  {
    recurring: { type: Boolean, required: true },
    dueAt: { type: Date, required: false },
  },
  { _id: false }
);

const MagicReachUser = new Schema(
  {
    firstName: { type: String, required: false },
    lastName: { type: String, required: false },
    email: { type: String, required: false },
    phoneNumber: { type: String, required: false },
    subscriptionId: { type: Number, required: false },
    learnerId: { type: Schema.Types.Mixed, required: false },
    inBuckets: { type: [String], required: true },
  },
  { _id: false }
);

const LocalizedReasonSchema = new Schema(
  {
    en: { type: String, required: false },
    es_mx: { type: String, required: false },
    pt_br: { type: String, required: false },
    ja: { type: String, required: false },
  },
  { _id: false }
);

const FraudInfo = new Schema(
  {
    probability: { type: Number, required: false },
    fraudReason: { type: LocalizedReasonSchema, required: false },
    aiSummary: { type: String, required: false },
    localizedReason: { type: LocalizedReasonSchema, required: false },
    reason: { type: String, required: false },
    status: { type: String, required: false },
    reviewStatus: { type: String, required: false },
    approveReason: { type: String, required: false },
    approvedBy: { type: String, required: false },
    rejectedBy: { type: String, required: false },
    reviewedBy: { type: String, required: false },
    reviewedDate: { type: Date, required: false },
    callId: { type: String, required: false },
    responseId: { type: String, required: false },
  },
  { _id: false }
);

const archivedRecipientsResultSchema = new Schema(
  {
    attemptedAndSkipped: { type: Boolean, required: false },
    uploadedToS3: { type: Boolean, required: false },
    deletedFromMongo: { type: Boolean, required: false },
  },
  {
    _id: false,
  }
);

const shardRecipientsResultSchema = new Schema(
  {
    completed: { type: Boolean, required: false },
    shardModel: { type: String, required: false },
  },
  {
    _id: false,
  }
);

const mentionedProductsSchema = new Schema(
  {
    type: { type: String, required: true },
    productObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    archivedProductInfo: { type: Object, required: false },
  },
  {
    _id: false,
  }
);

const earningsInfoSchema = new Schema(
  {
    revenueInLocalCurrency: { type: Number, required: true },
    localCurrency: { type: String, required: true }, // base currency upon purchase
    revenueInUsd: { type: Number, required: true },
  },
  { _id: false }
);

const productInfoSchema = new Schema(
  {
    productObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    type: { type: String, required: true },
  },
  { _id: false }
);

const clickInfoSchema = new Schema(
  {
    linkObjectId: {
      type: Schema.Types.ObjectId,
      index: true,
    },
    linkType: { type: String, required: true },
    linkClicked: { type: String, required: true },
    productInfo: {
      type: productInfoSchema,
      required: false,
    },
    salesCount: { type: Number, required: false },
    clickCount: { type: Number, required: false },
  },
  { _id: false }
);

const linkAnalyticsEmailSchema = new Schema(
  {
    totalOpens: { type: Number, required: false },
    uniqueOpens: { type: Number, required: false },

    totalClicks: { type: Number, required: false },
    uniqueClicks: { type: Number, required: false },

    nasCTAClicks: { type: Number, required: false },
    optOutClicks: { type: Number, required: false },

    sentRecipients: { type: Number, required: false },
    bounceRecipients: { type: Number, required: false },
    failedRecipients: { type: Number, required: false },
    earningsInfo: { type: earningsInfoSchema, required: false },
    links: { type: [clickInfoSchema], required: false },
  },
  {
    _id: false,
  }
);

const analyticsDataSchema = new Schema(
  {
    Email: { type: linkAnalyticsEmailSchema, required: false },
    Whatsapp: { type: Object, required: false },
  },
  {
    _id: false,
  }
);

const CommunityMagicReachEmailSchema = new Schema(
  {
    communityId: {
      type: Schema.Types.ObjectId,
      ref: Community,
      required: true,
    },
    author: { type: Schema.Types.ObjectId, ref: User, required: true },
    title: { type: String, required: false },
    content: { type: Object, required: false },
    sentOn: { type: Schema.Types.Date, required: false },
    sentBucketName: { type: String, required: false },
    analyticsData: { type: analyticsDataSchema, required: false },
    isDemo: { type: Boolean, required: false, default: false },
    sentPlatforms: { type: Schema.Types.Array, required: false },
    selectedUsers: { type: [MagicReachUser], required: false },
    unselectedUsers: { type: [MagicReachUser], required: false },
    selectedBuckets: { type: [String], required: false },
    pushedToQueueResults: { type: Object, required: false },
    sentResults: { type: Object, required: false },
    removeFooter: { type: Boolean, required: false },
    fraudInfo: { type: FraudInfo, required: false },
    status: { type: String, required: false },
    toSendCount: { type: Number, required: false },
    bucketFilters: { type: Object, required: false },
    bucketMetas: { type: Object, required: false },
    // legacy fields
    isDraft: { type: Boolean, required: false },
    sentEmails: { type: Schema.Types.Array, required: false },
    sentWhatsapp: { type: Schema.Types.Array, required: false },
    type: { type: String, required: false },
    schedule: { type: ScheduleSchema, required: false },
    archivedRecipientsResult: {
      type: archivedRecipientsResultSchema,
      required: false,
    },
    mentionedProducts: {
      type: [mentionedProductsSchema],
      required: false,
    },
    shardRecipientsResult: {
      type: shardRecipientsResultSchema,
      required: false,
    },
  },
  {
    collection: 'community_magic_reach_email',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'lastModifiedTimeStamp',
    },
  }
);

const CommunityMagicReachEmailModel = model(
  'CommunityMagicReachEmail',
  CommunityMagicReachEmailSchema
);

module.exports = CommunityMagicReachEmailModel;
