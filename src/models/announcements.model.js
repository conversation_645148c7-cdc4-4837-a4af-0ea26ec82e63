const { Schema, model } = require('mongoose');
const {
  ANNOUNCEMENT_VISIBILITY_SELECTIONS,
  ANNOUNCEMENT_VISIBILITY,
  ANNOUNCEMENT_MARKET,
} = require('../constants/common');

const titleLocalizationSchema = new Schema(
  {
    en: { type: String, required: false, maxlength: 52 },
    es_mx: { type: String, required: false, maxlength: 52 },
    pt_br: { type: String, required: false, maxlength: 52 },
    ja: { type: String, required: false, maxlength: 52 },
  },
  {
    _id: false,
  }
);

const descriptionLocalizationSchema = new Schema(
  {
    en: { type: String, required: false, maxlength: 132 },
    es_mx: { type: String, required: false, maxlength: 132 },
    pt_br: { type: String, required: false, maxlength: 132 },
    ja: { type: String, required: false, maxlength: 132 },
  },
  {
    _id: false,
  }
);

const ctaNameLocalizationSchema = new Schema(
  {
    en: { type: String, required: false, maxlength: 12 },
    es_mx: { type: String, required: false, maxlength: 12 },
    pt_br: { type: String, required: false, maxlength: 12 },
    ja: { type: String, required: false, maxlength: 12 },
  },
  {
    _id: false,
  }
);

const ctaRedirectLinkSchema = new Schema(
  {
    en: { type: String, required: false },
    es_mx: { type: String, required: false },
    pt_br: { type: String, required: false },
    ja: { type: String, required: false },
  },
  {
    _id: false,
  }
);

const ctaSchema = new Schema(
  {
    name: { type: ctaNameLocalizationSchema, required: false },
    link: { type: String, required: false },
    redirectLink: { type: ctaRedirectLinkSchema, required: false },
    openNewTab: { type: Boolean, required: false, default: false },
  },
  { _id: false }
);

const announcementSchema = new Schema(
  {
    visibility: {
      type: String,
      enum: Object.values(ANNOUNCEMENT_VISIBILITY),
      required: false,
    },
    visibilities: {
      type: [
        {
          type: String,
          enum: Object.values(ANNOUNCEMENT_VISIBILITY_SELECTIONS),
        },
      ],
      required: false,
      default: [],
    },
    marketsAllowed: {
      type: [{ type: String, enum: Object.values(ANNOUNCEMENT_MARKET) }],
      required: false,
    },
    effectiveStartTime: { type: Date, required: false },
    effectiveEndTime: { type: Date, required: false },
    disable: { type: Boolean, required: false, default: false },
    title: { type: titleLocalizationSchema, required: true },
    description: { type: descriptionLocalizationSchema, required: false },
    image: { type: String, required: false },
    liveMode: { type: Boolean, required: false, default: false },
    targetCommunitiesCode: { type: [String], required: true },
    targetTestCommunitiesCode: { type: [String], required: true },
    cta: { type: ctaSchema, required: false },
    secondaryCta: { type: ctaSchema, required: false },
  },
  {
    collection: 'announcements',
    timestamps: true,
  }
);

module.exports = model('AnnouncementSchema', announcementSchema);
