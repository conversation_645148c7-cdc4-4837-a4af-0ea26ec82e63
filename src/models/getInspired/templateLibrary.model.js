const mongoose = require('mongoose');
const {
  TEMPLATE_LIBRARY_TYPES,
  TEMPLATE_SOURCE_TYPE,
  PRICE_TYPE,
  AI_TEMPLATE_GENERATION_STATUS,
} = require('../../constants/common');
const {
  PROGRAM_CHALLENGE_TYPE,
} = require('../../services/program/constants');

const {
  icpSearchFieldSchema,
  icpVectorSearchContextSchema,
  icpLocalizationBlockSchema,
} = require('@/src/models/magicLeads/common.schema');

const DigitalProductSectionSchema = new mongoose.Schema(
  {
    title: { type: String, required: true },
    description: { type: String, required: true },
  },
  { _id: false }
);

const EventAgendaSchema = new mongoose.Schema(
  {
    title: { type: String, required: true },
    description: { type: String, required: true },
    durationInMinutes: { type: Number, required: true },
  },
  { _id: false }
);

const submissionQuestionSchema = new mongoose.Schema(
  {
    questionText: { type: String, required: true },
    type: { type: String, required: true },
    required: { type: Boolean, required: true },
  },
  { _id: false }
);

const CheckpointSchema = new mongoose.Schema(
  {
    title: { type: String, required: true },
    description: { type: String, required: true },
    submissionQuestions: {
      type: [submissionQuestionSchema],
      required: false,
      default: undefined,
    },
  },
  { _id: false }
);

const AdsSchema = new mongoose.Schema(
  {
    primaryText: { type: String, required: true },
    headline: { type: String, required: true },
    description: { type: String, required: true },
    imagePrompt: { type: String, required: true },
    imageUrl: { type: String, required: false },
    aiResponseId: { type: String, required: false },
    status: {
      type: String,
      enum: Object.values(AI_TEMPLATE_GENERATION_STATUS),
      required: false,
    },
  },
  { _id: false }
);

const icpSchema = new mongoose.Schema(
  {
    searchFields: { type: [icpSearchFieldSchema], required: false },
    icpSummary: { type: String, required: false },
    icpVectorSearchContext: {
      type: icpVectorSearchContextSchema,
      required: false,
    },
    targetLanguage: {
      type: String,
      required: false,
      default: 'en',
    },
    localization: {
      type: icpLocalizationBlockSchema,
      required: false,
    },
  },
  { _id: false }
);

const socialProfileSchema = new mongoose.Schema(
  {
    platform: { type: String, required: true },
    url: { type: String, required: true },
  },
  { _id: false }
);

const leadsSchema = new mongoose.Schema(
  {
    leadObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    fullName: { type: String, required: false },
    jobTitle: { type: String, required: false },
    companyName: { type: String, required: false },
    countryName: { type: String, required: false },
    cityName: { type: String, required: false },
    stateName: { type: String, required: false },
    socialProfiles: { type: [socialProfileSchema], required: false },
    profileImage: { type: String, required: false },
    emailIsVerified: { type: Boolean, required: false },
    status: {
      type: String,
      enum: Object.values(AI_TEMPLATE_GENERATION_STATUS),
      required: false,
    },
  },
  { _id: false }
);

const outreachEmailSchema = new mongoose.Schema(
  {
    language: { type: String, required: true },
    subject: { type: String, required: true },
    message: { type: String, required: true },
  },
  { _id: false }
);

const MetadataSchema = new mongoose.Schema(
  {
    unsplashImageSearchTitle: { type: String, required: false },

    // ads type
    ads: { type: [AdsSchema], required: false, default: undefined },

    // leads type
    icp: { type: icpSchema, required: false },
    leads: { type: [leadsSchema], required: false, default: undefined },

    callId: { type: String, required: false },
    aiResponseId: { type: String, required: false },
    adsCompletedCount: { type: Number, required: false },

    // outreach
    outreachEmail: { type: outreachEmailSchema, required: false },

    // community type - what ai learnt about you
    aiBio: { type: String, required: false },

    // membership type - what ai learnt about you
    interval: { type: String, required: false },
    intervalCount: { type: Number, required: false },
    perks: { type: [String], required: false, default: undefined },

    // session, event, digital product and challenge type
    title: { type: String, required: false },
    description: { type: String, required: false },

    // session and event type
    location: { type: String, required: false },
    durationInMinutes: { type: Number, required: false },

    // event type
    eventAgenda: {
      type: [EventAgendaSchema],
      required: false,
      default: undefined,
    },

    // digital product type
    sections: {
      type: [DigitalProductSectionSchema],
      required: false,
      default: undefined,
    },

    // challenge type
    challengeType: {
      type: String,
      enum: Object.values(PROGRAM_CHALLENGE_TYPE),
      required: false,
    },
    challengeDuration: { type: Number, required: false },
    checkpointInterval: { type: Number, required: false },
    checkpoints: {
      type: [CheckpointSchema],
      required: false,
      default: undefined,
    },

    // for event and challenge type
    startDateTime: { type: String, required: false },
    endDateTime: { type: String, required: false },
    timezone: { type: String, required: false },
  },
  { _id: false }
);

const PricingConfigSchema = new mongoose.Schema(
  {
    priceType: {
      type: String,
      enum: Object.values(PRICE_TYPE),
      required: true,
    },
    minAmount: { type: Number, required: false },
    suggestedAmount: { type: Number, required: true },
  },
  { _id: false }
);

const copyFromSchema = new mongoose.Schema(
  {
    templateObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    version: { type: Number, required: true },
  },
  { _id: false }
);

const TemplateLibrarySchema = new mongoose.Schema(
  {
    communityObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Community',
      required: false,
    },
    type: {
      type: String,
      enum: Object.values(TEMPLATE_LIBRARY_TYPES),
      required: true,
    },
    thumbnailImgSrc: { type: String, required: false },
    isAIGenerated: { type: Boolean, required: true, default: false },
    version: { type: Number, required: true },
    whyThisIsForYou: { type: String, required: false },
    predictedSales: { type: Number, required: false },
    predictedEarningsInUSD: { type: Number, required: false },
    predictedEarningsInLocalCurrency: { type: Number, required: false },
    localCurrency: { type: String, required: false },
    metadata: { type: MetadataSchema, required: true },
    pricingConfig: { type: PricingConfigSchema, required: false },
    source: {
      type: String,
      required: false,
      enum: Object.values(TEMPLATE_SOURCE_TYPE),
    },
    sourceObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
    },
    copyFrom: { type: copyFromSchema, required: false },
    status: {
      type: String,
      enum: Object.values(AI_TEMPLATE_GENERATION_STATUS),
      required: false,
    },
    errorMessageLocalizedKey: { type: String, required: false },
    linkedTemplateObjectIds: {
      type: [mongoose.Schema.Types.ObjectId],
      required: false,
    },
  },
  {
    collection: 'template_library',
    timestamps: true,
  }
);

const TemplateLibraryModel = mongoose.model(
  'TemplateLibrary',
  TemplateLibrarySchema
);

module.exports = TemplateLibraryModel;
