const { Schema, model } = require('mongoose');
const autoIncrement = require('../middleware/mongoose/autoIncrement.middleware');
const IdentityCounter = require('./identityCounter.model');

const socialMediaSchema = new Schema(
  {
    type: { type: String },
    iconLink: { type: String, default: null },
    link: { type: String, default: null },
    username: { type: String },
    url: { type: String },
    isVisible: { type: Boolean, default: true },
    title: { type: String },
    metadata: {
      type: Object,
    },
    clientId: { type: String, default: null },
  },
  { _id: false }
);

const widgetSchema = new Schema(
  {
    type: { type: String },
    link: { type: String, default: null },
  },
  { _id: false }
);

const creationSchema = new Schema(
  {
    title: { type: String, default: '', maxlength: 50 },
    widget: { type: widgetSchema, default: null },
    description: { type: String, default: '', maxlength: 250 },
  },
  { _id: false }
);

const extraInfoItemSchema = new Schema(
  {
    hasItem: { type: Boolean, required: false, default: false },
    type: { type: String, required: false, default: null },
  },
  { _id: false }
);

const extraInfoSchema = new Schema(
  {
    laptop: { type: extraInfoItemSchema, required: false },
    editingSoftware: { type: extraInfoItemSchema, required: false },
    camera: { type: extraInfoItemSchema, required: false },
  },
  { _id: false }
);

const addressSchema = new Schema(
  {
    additionalAddress: { type: String, required: false },
    address: { type: String, required: false },
    city: { type: String, required: false },
    postalCode: { type: String, required: false },
  },
  { _id: false }
);

const spotlightSchema = new Schema(
  {
    link: { type: String, required: false },
    thumbnail: { type: String, default: null },
    title: { type: String, default: null },
    description: { type: String, default: null },
  },
  { _id: false }
);

const contactUsernameSchema = new Schema(
  {
    type: { type: String, required: false },
    username: { type: String, required: false },
  },
  { _id: false }
);

const PayoutFeeConfigSchema = new Schema(
  {
    paymentProviderFee: { type: Object, required: false },
    revenueShareInPercentage: { type: Number, required: false },
    effectiveTimeStart: { type: Date, required: true },
    effectiveTimeEnd: { type: Date, required: true },
  },
  { _id: false }
);

const UpiInfoSchema = new Schema(
  {
    upiId: { type: String, required: true },
    brand: { type: String, required: true },
  },
  { _id: false }
);

const ReferralConfigSchema = new Schema(
  {
    referralSharePercentage: { type: Number, required: true },
    referralDurationLimitInMonths: { type: Number, required: true },
    effectiveTimeStart: { type: Date, required: true },
    effectiveTimeEnd: { type: Date, required: true },
  },
  { _id: false }
);

const learnerSchema = new Schema(
  {
    lastViewedCourse: { type: String, required: false },
    lastViewedCourseTimeStamp: { type: Date, required: false },
    lastViewedCommunity: { type: String, required: false },
    lastViewedCommunityTimeStamp: { type: Date, required: false },
    learnerId: { type: Number, required: true },
    isActive: { type: Boolean, required: true, default: true },
    firstName: { type: String, required: false },
    lastName: { type: String, required: false },
    timezone: { type: String, required: false },
    isWhatsappSignupUser: { type: Boolean, default: false },
    isWhatsappNumberRegistered: { type: Boolean, required: false },
    phoneNumber: { type: String, required: false, default: null },
    // NOTES: whatsappNumber and whatsappNumberSameAsPhoneNumber wont be used anymore
    whatsAppNumber: { type: String, required: false },
    whatsAppNumberSameAsPhoneNumber: {
      type: Boolean,
      required: true,
      default: false,
    },
    email: { type: String, required: false },
    corporateEmail: { type: String, required: false },
    corporateEmailSameAsLoginEmail: {
      type: Boolean,
      required: true,
      default: false,
    },
    contentStyle: { type: String, required: false },
    countryId: { type: Number, default: null },
    countryCode: { type: String, required: false },
    profileImage: { type: String, default: null },
    description: { type: String, default: '', maxlength: 140 },
    longDescription: { type: String, default: '', maxlength: 250 },
    achievements: { type: [String], default: [] },
    creations: { type: [creationSchema], default: [] },
    socialMedia: { type: [socialMediaSchema], default: [] },
    extraInfo: { type: extraInfoSchema, required: false },
    address: { type: addressSchema, required: false },
    subtitlePreference: { type: String, required: false, default: 'en' },
    showWelcomeToLpModal: {
      type: Boolean,
      required: true,
      default: false,
    },
    showWelcomeToClassroomModal: {
      type: Boolean,
      required: true,
      default: false,
    },
    showWelcomeToCourseVideosModal: {
      type: Boolean,
      required: true,
      default: false,
    },
    isPhoneNumberConfirmedByUser: {
      type: Boolean,
      required: false,
      default: false,
    },
    knowledgeSource: { type: [String], required: false },
    walletAddress: { type: String, required: false },
    walletType: { type: String, default: 'metamask', required: false },
    secondaryWallets: { type: Array, required: false },
    hasSubscribedToCommentEmails: { type: Boolean, default: true },
    // new fields for mobile app
    bio: { type: String, default: '', maxlength: 800 },
    skills: { type: [String], default: [] },
    interests: { type: [String], required: false },
    spotlights: { type: [spotlightSchema], required: false },
    contactUsernames: { type: [contactUsernameSchema], required: false },
    followersCount: { type: Number, required: false },
    whatsAppBusinessNumber: { type: String, required: false },
    discordUserId: { type: String, required: false },
    telegramUserId: { type: String, required: false },
    primaryContact: { type: String, required: false },
    isDemo: { type: Boolean, required: false, default: false },
    referralConfigs: { type: [ReferralConfigSchema], required: false },
    referralCode: { type: String, required: false },
    referralCodeCreatedAt: { type: Date, required: false },
    referralLinkVisits: { type: Number, required: false },
    referralLinkVisitsLastModifiedTimeStamp: {
      type: Date,
      required: false,
    },
    notificationsPreferences: {
      type: Object,
    },
    languagePreference: {
      type: String,
      required: false,
      default: 'en',
    },
    payoutFeeConfigs: {
      type: [PayoutFeeConfigSchema],
      required: false,
    },
    upiIds: { type: [UpiInfoSchema], required: false },
    inactiveReason: { type: String, required: false },
    config: { type: Object, required: false },
  },
  {
    collection: 'learners',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'lastModifiedTimeStamp',
    },
  }
);

learnerSchema.plugin(autoIncrement.plugin, {
  model: 'LearnerSchema',
  field: 'learnerId',
  startAt: 2000,
  incrementBy: 1,
});

learnerSchema.pre('findOneAndUpdate', async function (next) {
  const update = this.getUpdate();
  const isUpsert = this.getOptions().upsert;

  // Only apply when inserting
  if (isUpsert && update?.$setOnInsert && !update.$setOnInsert.learnerId) {
    const counter = await IdentityCounter.findOneAndUpdate(
      { model: 'LearnerSchema', field: 'learnerId' },
      { $inc: { count: 1 } },
      { returnDocument: 'after', upsert: true }
    );

    if (!counter?.count) {
      return next(new Error('Unable to generate learnerId'));
    }

    update.$setOnInsert.learnerId = counter.count;
    this.setUpdate(update); // reapply modified update
  }

  next();
});

module.exports = model('LearnerSchema', learnerSchema);
