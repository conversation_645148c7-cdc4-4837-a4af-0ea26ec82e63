const { Schema, model } = require('mongoose');

const rawFeeSchema = new Schema(
  {
    gatewayFee: { type: Number, required: true, default: 0 },
    gst: { type: Number, required: true, default: 0 },
    processingFee: { type: Number, required: true, default: 0 },
    gstOnRevenue: { type: Number, required: true, default: 0 },
    whtFee: { type: Number, required: true, default: 0 },
    refundProcessingFee: { type: Number, required: true, default: 0 },
    currency: { type: String, required: true },
  },
  { _id: false }
);

const feeSchema = new Schema(
  {
    gatewayFee: { type: Number, required: true, default: 0 },
    gst: { type: Number, required: true, default: 0 },
    processingFee: { type: Number, required: true, default: 0 },
    gstOnRevenue: { type: Number, required: true, default: 0 },
    whtFee: { type: Number, required: true, default: 0 },
    refundProcessingFee: { type: Number, required: true, default: 0 },
  },
  { _id: false }
);

const receiptLinkSchema = new Schema(
  {
    en: { type: String, required: true },
    'es-mx': { type: String, required: true },
    'pt-br': { type: String, required: true },
    ja: { type: String, required: true },
  },
  { _id: false }
);

const receiptInfoSchema = new Schema(
  {
    businessSource: { type: String, required: true },
    transactionDate: { type: String, required: true },
    uniqueCode: { type: String, required: true },
    receiptId: { type: String, required: true },
    receiptLink: { type: receiptLinkSchema, required: false },
    receiptGeneratedAt: { type: Date, required: false },
    receiptGeneratedPayload: { type: Object, required: false },
  },
  { _id: false }
);

const invoiceInfoSchema = new Schema(
  {
    communityTaxPercentage: { type: Number, required: false },
    communityTaxId: { type: String, required: false },
    communityTaxLabel: { type: String, required: false },
    communityAddress: { type: String, required: false },
    memberAddress: { type: String, required: false },
    memberLabel: { type: String, required: false },
    memberId: { type: String, required: false },
    invoiceId: { type: String, required: false },
    invoiceGeneratedAt: { type: Date, required: false },
    // invoiceLink and receiptLink have the same structure
    invoiceLink: { type: receiptLinkSchema, required: false },
    isInvoiceSent: { type: Boolean, default: false },
  },
  {
    _id: false,
  }
);

const rawTransactionsSchema = new Schema(
  {
    purchasedId: { type: Schema.Types.ObjectId, required: true },
    transactionType: { type: String, required: true },
    purchaseType: { type: String, required: true },
    originalAmount: { type: Number, required: true },
    originalCurrency: { type: String, required: true },
    rawFee: { type: rawFeeSchema, required: true },
    amountInUsd: { type: Number, required: true },
    feeInUsd: { type: feeSchema, required: true },
    paymentMethod: { type: String, required: true },
    paymentBrand: { type: String, required: true },
    paymentProvider: { type: String, required: true },
    communityObjectId: { type: Schema.Types.ObjectId, required: false },
    learnerObjectId: { type: Schema.Types.ObjectId, required: false },
    email: { type: String, required: true },
    discountTransactionObjectId: {
      type: Schema.Types.ObjectId,
      required: false,
    },
    discountCode: { type: String, required: false },
    transactionCreatedAt: { type: Date, required: true },
    transactionReferenceId: { type: String, required: true },
    source: { type: Object, required: true },
    checksum: { type: String, required: true },
    status: { type: String, required: true },
    paymentDetails: { type: Object, required: false },
    entityObjectId: { type: Schema.Types.ObjectId, required: false },
    upsellObjectId: { type: Schema.Types.ObjectId, required: false },
    priceType: { type: String, required: false },
    receiptInfo: { type: receiptInfoSchema, required: false },
    invoiceInfo: {
      type: invoiceInfoSchema,
      required: false,
    },
  },
  {
    collection: 'raw_transactions',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
  }
);

// Ensure unique transaction record
rawTransactionsSchema.index(
  { paymentProvider: 1, transactionReferenceId: 1 },
  { unique: true }
);

// Retrieve all transactions for that community
rawTransactionsSchema.index({
  communityObjectId: 1,
  transactionCreatedAt: -1,
});

// Search by purchasedId
rawTransactionsSchema.index({
  purchasedId: 1,
  transactionCreatedAt: -1,
});

module.exports = model('RawTransactionSchema', rawTransactionsSchema);
