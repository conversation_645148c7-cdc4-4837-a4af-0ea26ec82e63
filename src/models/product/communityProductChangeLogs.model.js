const { Schema, model } = require('mongoose');
const {
  PRODUCT_TYPE,
  PRODUCT_CHANGE_LOG_TYPE,
} = require('../../services/product/constants');

const CommunityProductChangeLogsSchema = new Schema(
  {
    communityObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    productType: {
      type: String,
      required: true,
      enum: Object.values(PRODUCT_TYPE),
    },
    entityObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    operatorLearnerObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    notiStatus: {
      type: String,
      required: true,
    },
    notifyMessage: {
      type: String,
      required: false,
    },
    metadata: {
      type: Object,
      required: false,
    },
    changeLogType: {
      type: String,
      required: true,
      enum: Object.values(PRODUCT_CHANGE_LOG_TYPE),
    },
    changeDetails: {
      type: Map,
      of: new Schema(
        {
          before: { type: Schema.Types.Mixed },
          after: { type: Schema.Types.Mixed },
        },
        { _id: false }
      ),
      default: {},
    },
    fraudCheckResult: {
      probability: { type: Number, required: false },
      reason: { type: String, required: false },
      status: { type: String, required: false },
      checkedAt: { type: Date, required: false },
    },
    reviewedAt: {
      type: Date,
      required: false,
    },
    reviewedBy: {
      type: String, // Email address of the operator who reviewed
      required: false,
    },
  },
  {
    collection: 'community_product_change_logs',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
  }
);

const CommunityProductChangeLogsModel = model(
  'community_product_change_logs',
  CommunityProductChangeLogsSchema
);

module.exports = CommunityProductChangeLogsModel;
