const mongoose = require('mongoose');

const magicLeadsUsageSchema = new mongoose.Schema(
  {
    communityObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'Community',
      index: true,
    },
    icpProfileObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'IcpProfiles',
    },
    generatedLeadsCount: {
      type: Number,
      required: true,
      default: 0,
    },
    requestedCount: {
      type: Number,
      required: true,
      default: 0,
    },
    executionTime: {
      type: Number, // in milliseconds
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

// Compound indexes for efficient monthly queries
magicLeadsUsageSchema.index({
  communityObjectId: 1,
  createdAt: -1,
});

// Index for usage counting by community and date
magicLeadsUsageSchema.index({
  communityObjectId: 1,
  createdAt: -1,
});

module.exports = mongoose.model(
  'MagicLeadsUsage',
  magicLeadsUsageSchema,
  'magic_leads_usage'
);
