const mongoose = require('mongoose');
const {
  SEARCH_FIELD_TYPE,
} = require('../../services/magicLeads/constants');

const icpSearchFieldSchema = new mongoose.Schema(
  {
    type: {
      type: String,
      required: true,
      enum: Object.values(SEARCH_FIELD_TYPE),
    },
    values: {
      type: [String],
      required: true,
    },
  },
  { _id: false }
);

const icpVectorSearchContextSchema = new mongoose.Schema(
  {
    text: { type: String, required: false },
    embedding: { type: [Number], required: false },
    generatedAt: { type: Date, required: false },
    model: { type: String, required: false },
    source: { type: String, required: false },
  },
  { _id: false }
);

const icpLocalizationSchema = new mongoose.Schema(
  {
    idealCustomerProfile: { type: String, required: false },
    title: { type: String, required: false },
    searchFields: {
      type: [icpSearchFieldSchema],
      default: [],
    },
    icpVectorSearchContext: {
      type: icpVectorSearchContextSchema,
      default: null,
    },
  },
  { _id: false }
);

const icpLocalizationBlockSchema = new mongoose.Schema(
  {
    en: { type: icpLocalizationSchema, default: {} },
  },
  { _id: false }
);

module.exports = {
  icpSearchFieldSchema,
  icpVectorSearchContextSchema,
  icpLocalizationBlockSchema,
};
