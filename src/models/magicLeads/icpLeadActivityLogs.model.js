const mongoose = require('mongoose');
const { ACTIVITY_TYPES } = require('../../services/magicLeads/constants');

const activityMetadataSchema = new mongoose.Schema(
  {
    productType: {
      type: String,
      required: false,
    },
    entityObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
    },
    icpProfileObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
    },
  },
  { _id: false }
);

const icpLeadActivityLogSchema = new mongoose.Schema(
  {
    leadObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'EnrichedLead',
    },
    communityObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'Community',
    },
    activityType: {
      type: String,
      required: true,
      enum: Object.values(ACTIVITY_TYPES),
    },
    sourceEntityId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    metadata: {
      type: activityMetadataSchema,
      required: false,
    },
  },
  {
    timestamps: true,
    collection: 'icp_lead_activity_logs',
  }
);

// Indexes for efficient querying
icpLeadActivityLogSchema.index({ leadObjectId: 1, createdAt: -1 });
icpLeadActivityLogSchema.index({ communityObjectId: 1, createdAt: -1 });
icpLeadActivityLogSchema.index({ activityType: 1 });

module.exports = mongoose.model(
  'IcpLeadActivityLogs',
  icpLeadActivityLogSchema
);
