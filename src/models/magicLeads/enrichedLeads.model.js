const mongoose = require('mongoose');

const LanguageSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    code: { type: String, required: false },
    proficiency: { type: String, required: false },
    orderInProfile: { type: Number, required: true },
  },
  { _id: false }
);

const CompanySchema = new mongoose.Schema(
  {
    name: { type: String, required: false },
    type: { type: String, required: false },
    industry: { type: String, required: false },
    location: { type: String, required: false },
    size: { type: Number, required: false },
    website: { type: String, required: false },
    keywords: { type: [String], required: false },
    headquarterCountry: { type: String, required: false },
    headquarterCountryCode: { type: String, required: false },
    headquarterRegion: { type: [String], required: false },
    headquarterCity: { type: String, required: false },
    headquarterState: { type: String, required: false },
    lastUpdatedAt: { type: Date, required: false },
  },
  { _id: false }
);

const LocationSchema = new mongoose.Schema(
  {
    countryCode: { type: String, required: false },
    countryName: { type: String, required: false },
    regions: { type: [String], required: false },
    city: { type: String, required: false },
    state: { type: String, required: false },
    full: { type: String, required: false },
  },
  { _id: false }
);

const EmailVerificationSchema = new mongoose.Schema(
  {
    status: { type: String, required: true },
    verifiedAt: { type: Date, required: false },
    provider: { type: String, required: false },
    email: { type: String, required: false },
  },
  { _id: false }
);

const VerificationSchema = new mongoose.Schema(
  {
    email: { type: EmailVerificationSchema, required: true },
  },
  { _id: false }
);

const SocialProfileSchema = new mongoose.Schema(
  {
    platform: { type: String, required: true },
    profileImageUrl: { type: String, required: false },
    url: { type: String, required: true },
  },
  { _id: false }
);

const ExperienceSchema = new mongoose.Schema(
  {
    title: { type: String, required: false },
    department: { type: String, required: false },
    seniority: { type: String, required: false },
    companyLocation: { type: String, required: false },
    companyName: { type: String, required: false },
    companyType: { type: String, required: false },
    companyWebsite: { type: String, required: false },
    companySize: { type: Number, required: false },
    companyIndustry: { type: String, required: false },
    companyDescription: { type: String, required: false },
    companyKeywords: { type: [String], required: false },
    companyHeadquarterAddress: { type: String, required: false },
    companyHeadquarterCountry: { type: String, required: false },
    companyHeadquarterRegion: { type: [String], required: false },
    companyHeadquarterCity: { type: String, required: false },
    companyHeadquarterState: { type: String, required: false },
    companyHeadquarterCountryCode: { type: String, required: false },
    dateFrom: { type: Date, required: false },
    dateFromYear: { type: Number, required: false },
    dateFromMonth: { type: Number, required: false },
    dateTo: { type: Date, required: false },
    dateToYear: { type: Number, required: false },
    dateToMonth: { type: Number, required: false },
    durationMonths: { type: Number, required: false },
    orderInProfile: { type: Number, required: false },
  },
  { _id: false }
);

const UsageSchema = new mongoose.Schema(
  {
    searchCount: { type: Number, required: true },
    emailCount: { type: Number, required: true },
    lastContactedAt: { type: Date, required: false },
  },
  { _id: false }
);

const AIProfileSummarySchema = new mongoose.Schema(
  {
    text: { type: String, required: true },
    keywords: { type: [String], required: true },
    embedding: { type: [Number], required: true }, // vector embeddings
    generatedAt: { type: Date, required: true },
    model: { type: String, required: true },
    source: { type: String, required: true },
  },
  { _id: false }
);

const EnrichmentSchema = new mongoose.Schema(
  {
    provider: { type: String, required: true },
    rawId: { type: String, required: true },
    enrichedAt: { type: Date, required: true },
    raw: { type: mongoose.Schema.Types.Mixed, required: true },
  },
  { _id: false }
);

const EducationSchema = new mongoose.Schema(
  {
    title: { type: String, required: false },
    major: { type: String, required: false },
    institutionUrl: { type: String, required: false },
    description: { type: String, required: false },
    activitiesAndSocieties: { type: String, required: false },
    dateFromYear: { type: Number, required: false },
    dateToYear: { type: Number, required: false },
  },
  { _id: false }
);

const ActivitySchema = new mongoose.Schema(
  {
    activityUrl: { type: String, required: false },
    title: { type: String, required: false },
    action: { type: String, required: false },
    orderInProfile: { type: Number, required: false },
  },
  { _id: false }
);

const RecommendationSchema = new mongoose.Schema(
  {
    recommendation: { type: String, required: false },
    refereeName: { type: String, required: false },
    refereeUrl: { type: String, required: false },
    orderInProfile: { type: Number, required: false },
  },
  { _id: false }
);

const PatentSchema = new mongoose.Schema(
  {
    title: { type: String, required: false },
    status: { type: String, required: false },
    description: { type: String, required: false },
    date: { type: Date, required: false },
    patentUrl: { type: String, required: false },
    patentNumber: { type: String, required: false },
    orderInProfile: { type: Number, required: false },
  },
  { _id: false }
);

const CourseSchema = new mongoose.Schema(
  {
    organizer: { type: String, required: false },
    title: { type: String, required: false },
    orderInProfile: { type: Number, required: false },
  },
  { _id: false }
);

const PublicationSchema = new mongoose.Schema(
  {
    title: { type: String, required: false },
    publisher: { type: String, required: false },
    publisherUrl: { type: String, required: false },
    description: { type: String, required: false },
    date: { type: Date, required: false },
    orderInProfile: { type: Number, required: false },
  },
  { _id: false }
);

const AwardSchema = new mongoose.Schema(
  {
    title: { type: String, required: false },
    issuer: { type: String, required: false },
    description: { type: String, required: false },
    date: { type: Date, required: false },
    orderInProfile: { type: Number, required: false },
  },
  { _id: false }
);

const OrganizationSchema = new mongoose.Schema(
  {
    organization: { type: String, required: false },
    position: { type: String, required: false },
    description: { type: String, required: false },
    dateFrom: { type: Date, required: false },
    dateTo: { type: Date, required: false },
    orderInProfile: { type: Number, required: false },
  },
  { _id: false }
);

const CertificationSchema = new mongoose.Schema(
  {
    issuer: { type: String, required: false },
    issuerUrl: { type: String, required: false },
    title: { type: String, required: false },
    dateFrom: { type: Date, required: false },
    dateTo: { type: Date, required: false },
    orderInProfile: { type: Number, required: false },
  },
  { _id: false }
);

const OptOutFromEmailSchema = new mongoose.Schema(
  {
    communityCodes: { type: [String], required: false, default: [] },
  },
  { _id: false }
);

const ReachInfoSchema = new mongoose.Schema(
  {
    optOutFromEmail: { type: OptOutFromEmailSchema, required: false },
  },
  { _id: false }
);

const EnrichedLeadSchema = new mongoose.Schema(
  {
    fullName: { type: String, required: false },
    firstName: { type: String, required: false },
    lastName: { type: String, required: false },
    jobTitle: { type: String, required: false },
    headline: { type: String, required: false },
    seniority: { type: String, required: false },
    languages: { type: [LanguageSchema], required: false },
    company: { type: CompanySchema, required: true },
    location: { type: LocationSchema, required: true },
    email: { type: String, required: false },
    verification: { type: VerificationSchema, required: true },
    socialProfiles: { type: [SocialProfileSchema], required: true },
    interests: { type: [String], required: false },
    skills: { type: [String], required: false },
    experiences: { type: [ExperienceSchema], required: false },
    educations: { type: [EducationSchema], required: false },
    recommendations: { type: [RecommendationSchema], required: false },
    activities: { type: [ActivitySchema], required: false },
    courses: { type: [CourseSchema], required: false },
    certifications: { type: [CertificationSchema], required: false },
    patents: { type: [PatentSchema], required: false },
    publications: { type: [PublicationSchema], required: false },
    awards: { type: [AwardSchema], required: false },
    organizations: { type: [OrganizationSchema], required: false },
    enrichments: { type: [EnrichmentSchema], required: true },
    aiProfileSummary: { type: AIProfileSummarySchema, required: false },
    autoEmbedOnSummaryEdit: { type: Boolean, required: true },
    usage: { type: UsageSchema, required: true },
    isActive: { type: Boolean, required: true },
    isTestData: { type: Boolean, required: false, default: false },
    exportedToProd: { type: Boolean, required: false },
    reachInfo: { type: ReachInfoSchema, required: false },
  },
  {
    timestamps: true,
    collection: 'enriched_leads',
  }
);

module.exports = mongoose.model('EnrichedLead', EnrichedLeadSchema);
