const mongoose = require('mongoose');
const { PRODUCT_TYPE } = require('../../services/product/constants');
const {
  ICP_PROFILE_STATUS,
} = require('../../services/magicLeads/constants');
const {
  icpSearchFieldSchema,
  icpVectorSearchContextSchema,
  icpLocalizationBlockSchema,
} = require('./common.schema');

const icpProfilesSchema = new mongoose.Schema(
  {
    communityObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'Community',
    },
    entityObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    productType: {
      type: String,
      required: true,
      enum: Object.values(PRODUCT_TYPE),
    },
    title: {
      type: String,
      required: true,
    },
    searchFields: { type: [icpSearchFieldSchema], required: false },
    icpSummary: {
      type: String,
      required: true,
    },
    icpVectorSearchContext: {
      type: icpVectorSearchContextSchema,
      required: false,
    },
    targetLanguage: {
      type: String,
      required: false,
      default: 'en',
    },
    localization: {
      type: icpLocalizationBlockSchema,
      required: false,
    },
    originalAIGeneratedVersion: {
      type: Object,
      required: false,
    },
    status: {
      type: String,
      enum: Object.values(ICP_PROFILE_STATUS),
      default: ICP_PROFILE_STATUS.ACTIVE,
      required: true,
    },
    managerLearnerObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
    },
  },
  {
    timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt' },
  }
);

// Index for finding profiles by community and product
icpProfilesSchema.index({
  communityObjectId: 1,
  entityObjectId: 1,
  productType: 1,
});
// Index for sorting by creation time
icpProfilesSchema.index({ createdAt: -1 });
// Index for status field
icpProfilesSchema.index({ status: 1 });

module.exports = mongoose.model(
  'IcpProfiles',
  icpProfilesSchema,
  'icp_profiles'
);
