const mongoose = require('mongoose');
const { MAGIC_LEADS_STATUS } = require('@constants/common');
const {
  OUTREACH_PURPOSE,
} = require('@/src/services/magicLeads/constants');

// Ensure the EnrichedLead model is registered before this model uses it
require('./enrichedLeads.model');

const outreachEmailTemplateSchema = new mongoose.Schema(
  {
    templateObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    magicReachEmailObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
    },
    outreachPurpose: {
      type: String,
      required: true,
      enum: Object.keys(OUTREACH_PURPOSE),
    },
    index: {
      type: Number,
      required: true,
    },
  },
  { _id: false }
);

const icpLeadMatchesSchema = new mongoose.Schema(
  {
    icpProfileObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'IcpProfiles',
    },
    communityObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'Community',
    },
    leadObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'EnrichedLead',
    },
    vectorSimilarity: {
      type: Number,
      required: true,
      min: 0,
      max: 1,
    },
    aiSummary: {
      type: String,
      required: false,
    },
    outReachCount: {
      type: Number,
      default: 0,
    },
    outreachEmailTemplates: {
      type: [outreachEmailTemplateSchema],
      required: false,
    },
    status: {
      type: String,
      enum: Object.values(MAGIC_LEADS_STATUS),
      default: MAGIC_LEADS_STATUS.NOT_REACHED_OUT,
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

// Index for finding matches by ICP profile
icpLeadMatchesSchema.index({ icpProfileObjectId: 1 });
// Index for finding matches by lead
icpLeadMatchesSchema.index({ leadObjectId: 1 });
// Index for sorting by similarity score
icpLeadMatchesSchema.index({ vectorSimilarity: -1 });

module.exports = mongoose.model(
  'IcpLeadMatches',
  icpLeadMatchesSchema,
  'icp_lead_matches'
);
