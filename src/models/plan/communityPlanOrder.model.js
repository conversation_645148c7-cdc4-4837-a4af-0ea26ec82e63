const { Schema, model } = require('mongoose');
const commonSchema = require('./commonSchema');
const {
  NASIO_PRO_CANCELLATION_REASON,
  TIER_CHANGE_CATEGORY,
} = require('../../services/plan/constants');

const planHistorySchema = new Schema(
  {
    paymentProviderPriceId: { type: String, required: true },
    intervalCount: { type: Number, required: true },
    interval: { type: String, required: true },
    nextBillingDate: { type: Date, required: true },
    changedDateTime: { type: Date, required: true },
    billingCycle: { type: Number, required: true },
    amountInLocalCurrency: { type: Number, required: true },
    localCurrency: { type: String, required: true },
    previousAmountInLocalCurrency: { type: Number, required: false },
    communityReferralCode: { type: String, required: false },
    referrerCommunityObjectId: {
      type: Schema.Types.ObjectId,
      required: false,
    },
    referralRewardTemplateObjectId: {
      type: Schema.Types.ObjectId,
      required: false,
    },
    revoked: { type: Boolean, default: false, required: false },
    revokedAt: { type: Date, required: false },
  },
  { _id: false }
);

const paymentDetailsSchema = new Schema(
  {
    completedPayment: { type: Boolean, required: true },
    status: {
      type: String,
      required: true,
    },
    latestUpdatedTime: { type: Date, required: true },
    paymentProvider: { type: String, required: true },
    recurringPurchase: { type: Boolean, required: true },
    requestId: { type: String, required: true },
    failureCode: { type: String, required: false },
    failureReason: { type: String, required: false },
    userPaymentTokenId: { type: String, required: false },
    isDirectCharge: { type: Boolean, required: false },
    attemptCount: { type: Number, required: false },
    renewalAmount: { type: Number, required: false },
    renewalLock: { type: Boolean, required: false },
  },
  { _id: false }
);

const countrySchema = new Schema(
  {
    name: { type: String, required: true },
    code: { type: String, required: true },
  },
  { _id: false }
);

const cancellationSchema = new Schema(
  {
    key: {
      type: String,
      enum: Object.values(NASIO_PRO_CANCELLATION_REASON),
      required: true,
    },
    reason: { type: String, required: false },
  },
  { _id: false }
);

const firstBillingSchema = new Schema(
  {
    priceId: { type: String, required: false },
    interval: { type: String, required: false },
    intervalCount: { type: Number, required: false },
    isTrial: { type: Boolean, required: false },
    localCurrency: { type: String, required: false },
    paymentSuccessTime: { type: Date, required: false },
    amountInLocalCurrency: { type: Number, required: false }, // plan Amount
    creditsInLocalCurrency: { type: Number, required: false }, // credit used
    paidAmountInLocalCurrency: { type: Number, required: false }, // final paidAmount
    actualNextBillingDate: { type: Date, required: false }, // actual billingDate for priceId
  },
  { _id: false }
);

const metadataSchema = new Schema(
  {
    nextBillingPriceId: { type: String, required: false },
    firstBilling: { type: firstBillingSchema, required: false },
  },
  { _id: false }
);

const linkedPlanOrderSchema = new Schema(
  {
    planOrderObjectId: { type: Schema.Types.ObjectId, required: true },
    entityType: { type: String, required: true },
    interval: { type: String, required: false },
    intervalCount: { type: Number, required: false },
    startDate: { type: Date, required: false },
    endDate: { type: Date, required: false },
  },
  { _id: false }
);

const prorationDetailsSchema = new Schema(
  {
    dateOfCalculation: { type: Date, required: false },
    planDurationInDays: { type: Number, required: false },
    unusedDurationInDays: { type: Number, required: false },
    creditAmountInUsd: { type: Number, required: false },
    creditAmount: { type: Number, required: false },
    creditCurrency: { type: String, required: false },
  },
  { _id: false }
);

const creditSchema = new Schema(
  {
    exchangeRate: { type: Number, required: true },
    amount: { type: Number, required: true },
    currency: { type: String, required: true },
    creditAmount: { type: Number, required: true },
    creditCurrency: { type: String, required: true },
  },
  { _id: false }
);

const creditHistorySchema = new Schema(
  {
    billingCycle: { type: Number, required: false },
    isProratedAmountIncluded: { type: Boolean, required: false },
    originalAmount: { type: Number, required: false },
    paidAmount: { type: Number, required: false },
    paidCreditAmount: { type: Number, required: false },
    paidCredits: { type: [creditSchema], required: false },
  },
  { _id: false }
);

const communityPlanOrderSchema = new Schema(
  {
    tierChangeCategory: {
      type: String,
      enum: Object.values(TIER_CHANGE_CATEGORY),
      required: false,
    },
    previousPlanOrder: { type: linkedPlanOrderSchema, required: false },
    nextPlanOrder: { type: linkedPlanOrderSchema, required: false },
    prorationDetails: { type: prorationDetailsSchema, required: false },
    creditHistory: { type: [creditHistorySchema], required: false },
    cancellationReasons: { type: [cancellationSchema], required: false },
    planObjectId: { type: Schema.Types.ObjectId, required: true },
    entityType: { type: String, required: true },
    communityObjectId: { type: Schema.Types.ObjectId, required: true },
    learnerObjectId: { type: Schema.Types.ObjectId, required: true },
    requestor: { type: String, required: true },
    paymentDetails: { type: paymentDetailsSchema, required: true },
    features: { type: [commonSchema.featureSchema], required: true },
    status: { type: String, required: true },
    billingCycle: { type: Number, required: true },
    nextBillingDate: { type: Date, required: false },
    cancelledAt: { type: Date, required: false },
    unsubscribedAt: { type: Date, required: false },
    scheduledCancellation: { type: Boolean, required: false },
    revokedAt: { type: Date, required: false },
    country: { type: countrySchema, required: true },
    amountInLocalCurrency: { type: Number, required: true },
    localCurrency: { type: String, required: true, uppercase: true },
    applyDiscount: { type: Boolean, required: true },
    timezone: { type: String, required: true },
    paymentProviderPriceId: { type: String, required: true },
    paymentProviderForPriceId: { type: String, required: true },
    paymentProviderSubscriptionId: { type: String, required: false },
    trackingData: { type: Object, required: true },
    billingModel: { type: String, required: true },
    interval: { type: String, required: false },
    intervalCount: { type: Number, required: false },
    previousAmountInLocalCurrency: { type: Number, required: false },
    previousLocalCurrency: { type: String, required: false },
    planHistory: { type: [planHistorySchema], required: false },
    metadata: { type: metadataSchema, required: false },
    isOnTrial: { type: Boolean, required: false },
    communityReferralCode: { type: String, required: false },
    referrerCommunityObjectId: {
      type: Schema.Types.ObjectId,
      required: false,
    },
    referralRewardTemplateObjectId: {
      type: Schema.Types.ObjectId,
      required: false,
    },
    phoneNumber: { type: String, required: false },
    restrictRevokeCancellation: { type: Boolean, required: false },
  },
  {
    collection: 'community_plan_orders',
    timestamps: true,
  }
);

module.exports = model('community_plan_orders', communityPlanOrderSchema);
