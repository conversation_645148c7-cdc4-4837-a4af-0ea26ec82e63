const { Schema, model } = require('mongoose');
const {
  META_ADS_STATUS_LIST,
} = require('../../services/magicAudience/constants');

const MetaAdsSetsSchema = new Schema(
  {
    campaignId: {
      type: Schema.Types.ObjectId,
      ref: 'MetaAdsCampaigns',
      required: true,
    },
    communityObjectId: {
      type: Schema.Types.ObjectId,
      ref: 'Community',
      required: true,
    },
    fbAdSetId: { type: String },
    optimizationGoal: { type: String },
    targeting: {
      type: Object,
    },
    duration: {
      type: Number,
    },
    billingEvent: { type: String },
    facebookStatus: { type: String },
    errorReason: { type: String }, // for tracking FB status
    errorFacebookReason: { type: String }, // for tracking FB status
    status: { type: String, required: true, enum: META_ADS_STATUS_LIST },
    dailyBudgetInUSD: { type: Number },
    totalBudgetInUSD: { type: Number },
    startTime: { type: Date },
    endTime: { type: Date },
    conversionEvent: {
      type: String,
    },
  },
  {
    collection: 'meta_ads_sets',
    timestamps: true,
  }
);

const MetaAdsSets = model('MetaAdsSets', MetaAdsSetsSchema);
module.exports = MetaAdsSets;
