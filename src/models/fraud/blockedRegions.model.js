const { Schema, model } = require('mongoose');
const { BLOCKED_REGION_TYPES } = require('../../constants/blockedRegions');

const blockedRegionsSchema = new Schema(
  {
    type: {
      type: String,
      enum: Object.values(BLOCKED_REGION_TYPES),
      required: true,
    },
    value: { type: String, required: true },
    reason: { type: String, required: false },
    createdBy: { type: String, required: false },
  },
  {
    collection: 'blocked_regions',
    timestamps: true,
  }
);

module.exports = model('BlockedRegions', blockedRegionsSchema);
