{
  "env": {
    "node": true,
    "es6": true
  },
  "parserOptions": {
    "ecmaVersion": 2020,
    "sourceType": "module"
  },
  "extends": ["airbnb-base", "prettier"],
  "plugins": ["prettier"],
  "settings": {
    "import/resolver": {
      "alias": {
        "map": [
          ["@", "./"],
          ["@src", "./src"],
          ["@models", "./src/models"],
          ["@services", "./src/services"],
          ["@constants", "./src/constants"],
          ["@controllers", "./src/controllers"],
          ["@middlewares", "./src/middlewares"],
          ["@utils", "./src/utils"],
          ["@config", "./src/config"],
          ["@helpers", "./src/helpers"]
        ],
        "extensions": [".js", ".json"]
      }
    }
  },
  "rules": {
    "prettier/prettier": [
      "error",
      {
        "endOfLine": "auto"
      }
    ],
    "eol-last": "off",
    // NOTE: Disabling rules below for ease of unused
    // We'll progressively enable these and refactor as necessary
    // allow comma dangles
    "comma-dangle": "off",
    // allow console logs
    "no-console": "off",
    // allow for functions to return as appropriate
    "consistent-return": "off",
    // prefer destructuring
    "prefer-destructuring": "off",
    // underscore dangling
    "no-underscore-dangle": "off",
    // no ++
    "no-plusplus": "off",
    // spaced comments
    "spaced-comment": "off",
    // unnamed functions
    "func-names": "off",
    "dot-notation": "off",
    "sort-imports": "error",
    "import/order": "error",

    // prefer template
    "prefer-template": "off",
    // useless concat
    "no-useless-concat": "off",
    // no restricted syntax
    "no-restricted-syntax": "off",
    // class methods use this
    "class-methods-use-this": "off"
  }
}
