const {
  revokeCancellation,
} = require('../../../../src/services/plan/index');
const planOrderService = require('../../../../src/services/plan/planOrder.service');
const commonService = require('../../../../src/services/plan/common.service');
const { ForbiddenError } = require('../../../../src/utils/error.util');

// Mock dependencies
jest.mock('../../../../src/services/plan/planOrder.service');
jest.mock('../../../../src/services/plan/common.service');

describe('Plan Service - revokeCancellation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const mockParams = {
    communityObjectId: '507f1f77bcf86cd799439012',
    reason: 'Changed my mind',
    email: '<EMAIL>',
  };

  const mockCancelledPlanOrder = {
    _id: '507f1f77bcf86cd799439011',
    communityObjectId: '507f1f77bcf86cd799439012',
    status: 'CANCELLED',
    cancelledAt: new Date(Date.now() + 86400000), // Tomorrow
  };

  const mockUpdatedPlanOrder = {
    ...mockCancelledPlanOrder,
    status: 'CURRENT',
    revokedAt: new Date(),
    revocationReason: 'Changed my mind',
  };

  it('should successfully revoke a cancelled plan', async () => {
    // Mock manager role validation to pass
    commonService.isManagerRole.mockResolvedValue(true);
    
    // Mock retrieving cancelled plan order
    planOrderService.retrieveCancelledPlanOrder.mockResolvedValue(mockCancelledPlanOrder);
    
    // Mock revoking the plan order
    planOrderService.revokeCancelledPlanOrder.mockResolvedValue(mockUpdatedPlanOrder);

    const result = await revokeCancellation(mockParams);

    // Verify manager role was checked
    expect(commonService.isManagerRole).toHaveBeenCalledWith({
      communityObjectId: mockParams.communityObjectId,
      email: mockParams.email,
    });

    // Verify cancelled plan was retrieved
    expect(planOrderService.retrieveCancelledPlanOrder).toHaveBeenCalledWith({
      communityObjectId: mockParams.communityObjectId,
    });

    // Verify plan order was revoked
    expect(planOrderService.revokeCancelledPlanOrder).toHaveBeenCalledWith(
      mockCancelledPlanOrder,
      mockParams.reason
    );

    // Verify result
    expect(result).toEqual(mockUpdatedPlanOrder);
  });

  it('should throw ForbiddenError when user is not a manager', async () => {
    // Mock manager role validation to fail
    commonService.isManagerRole.mockResolvedValue(false);

    await expect(revokeCancellation(mockParams)).rejects.toThrow(
      new ForbiddenError('Only managers can have access')
    );

    // Verify no further operations were performed
    expect(planOrderService.retrieveCancelledPlanOrder).not.toHaveBeenCalled();
    expect(planOrderService.revokeCancelledPlanOrder).not.toHaveBeenCalled();
  });

  it('should handle revocation without reason', async () => {
    const paramsWithoutReason = {
      communityObjectId: '507f1f77bcf86cd799439012',
      email: '<EMAIL>',
    };

    commonService.isManagerRole.mockResolvedValue(true);
    planOrderService.retrieveCancelledPlanOrder.mockResolvedValue(mockCancelledPlanOrder);
    planOrderService.revokeCancelledPlanOrder.mockResolvedValue({
      ...mockUpdatedPlanOrder,
      revocationReason: null,
    });

    const result = await revokeCancellation(paramsWithoutReason);

    expect(planOrderService.revokeCancelledPlanOrder).toHaveBeenCalledWith(
      mockCancelledPlanOrder,
      undefined
    );
    expect(result.revocationReason).toBeNull();
  });

  it('should run validation and retrieval in parallel', async () => {
    // Create promises that we can control
    const isManagerRolePromise = new Promise((resolve) => {
      setTimeout(() => resolve(true), 10);
    });
    const retrieveCancelledPlanOrderPromise = new Promise((resolve) => {
      setTimeout(() => resolve(mockCancelledPlanOrder), 10);
    });

    commonService.isManagerRole.mockReturnValue(isManagerRolePromise);
    planOrderService.retrieveCancelledPlanOrder.mockReturnValue(retrieveCancelledPlanOrderPromise);
    planOrderService.revokeCancelledPlanOrder.mockResolvedValue(mockUpdatedPlanOrder);

    const startTime = Date.now();
    await revokeCancellation(mockParams);
    const endTime = Date.now();

    // Both operations should run in parallel, so total time should be ~10ms, not ~20ms
    expect(endTime - startTime).toBeLessThan(20);
    
    // Verify both were called
    expect(commonService.isManagerRole).toHaveBeenCalled();
    expect(planOrderService.retrieveCancelledPlanOrder).toHaveBeenCalled();
  });

  it('should propagate errors from retrieveCancelledPlanOrder', async () => {
    const error = new Error('No cancelled plan found');
    
    commonService.isManagerRole.mockResolvedValue(true);
    planOrderService.retrieveCancelledPlanOrder.mockRejectedValue(error);

    await expect(revokeCancellation(mockParams)).rejects.toThrow(error);

    expect(planOrderService.revokeCancelledPlanOrder).not.toHaveBeenCalled();
  });

  it('should propagate errors from revokeCancelledPlanOrder', async () => {
    const error = new Error('Payment provider error');
    
    commonService.isManagerRole.mockResolvedValue(true);
    planOrderService.retrieveCancelledPlanOrder.mockResolvedValue(mockCancelledPlanOrder);
    planOrderService.revokeCancelledPlanOrder.mockRejectedValue(error);

    await expect(revokeCancellation(mockParams)).rejects.toThrow(error);
  });

  it('should handle different email formats', async () => {
    const paramsWithDifferentEmail = {
      ...mockParams,
      email: '<EMAIL>', // Uppercase email
    };

    commonService.isManagerRole.mockResolvedValue(true);
    planOrderService.retrieveCancelledPlanOrder.mockResolvedValue(mockCancelledPlanOrder);
    planOrderService.revokeCancelledPlanOrder.mockResolvedValue(mockUpdatedPlanOrder);

    await revokeCancellation(paramsWithDifferentEmail);

    expect(commonService.isManagerRole).toHaveBeenCalledWith({
      communityObjectId: mockParams.communityObjectId,
      email: '<EMAIL>',
    });
  });
});