const controller = require('../../../../../src/api/v1/plan/controller');
const service = require('../../../../../src/services/plan');
const schema = require('../../../../../src/api/v1/plan/schema');

// Mock dependencies
jest.mock('../../../../../src/services/plan');
jest.mock('../../../../../src/api/v1/plan/schema');

describe('Plan Controller - revokeCancellation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should successfully process revoke cancellation request', async () => {
    const mockReq = {
      user: {
        email: '<EMAIL>',
      },
      body: {
        communityObjectId: '507f1f77bcf86cd799439011',
        reason: 'Changed my mind',
      },
    };

    const mockCastData = {
      communityObjectId: '507f1f77bcf86cd799439011',
      reason: 'Changed my mind',
    };

    const mockServiceResult = {
      _id: '507f1f77bcf86cd799439012',
      communityObjectId: '507f1f77bcf86cd799439011',
      status: 'CURRENT',
      revokedAt: new Date(),
      revocationReason: 'Changed my mind',
    };

    // Mock schema validation
    schema.revokeCancellationSchema.cast = jest.fn().mockReturnValue(mockCastData);

    // Mock service call
    service.revokeCancellation.mockResolvedValue(mockServiceResult);

    const result = await controller.revokeCancellation(mockReq);

    // Verify schema validation was called
    expect(schema.revokeCancellationSchema.cast).toHaveBeenCalledWith(mockReq.body);

    // Verify service was called with correct parameters
    expect(service.revokeCancellation).toHaveBeenCalledWith({
      communityObjectId: mockCastData.communityObjectId,
      reason: mockCastData.reason,
      email: mockReq.user.email,
    });

    // Verify result
    expect(result).toEqual(mockServiceResult);
  });

  it('should handle request without reason', async () => {
    const mockReq = {
      user: {
        email: '<EMAIL>',
      },
      body: {
        communityObjectId: '507f1f77bcf86cd799439011',
      },
    };

    const mockCastData = {
      communityObjectId: '507f1f77bcf86cd799439011',
    };

    const mockServiceResult = {
      _id: '507f1f77bcf86cd799439012',
      communityObjectId: '507f1f77bcf86cd799439011',
      status: 'CURRENT',
      revokedAt: new Date(),
      revocationReason: null,
    };

    schema.revokeCancellationSchema.cast = jest.fn().mockReturnValue(mockCastData);
    service.revokeCancellation.mockResolvedValue(mockServiceResult);

    const result = await controller.revokeCancellation(mockReq);

    expect(service.revokeCancellation).toHaveBeenCalledWith({
      communityObjectId: mockCastData.communityObjectId,
      reason: undefined,
      email: mockReq.user.email,
    });

    expect(result).toEqual(mockServiceResult);
  });

  it('should pass through service errors', async () => {
    const mockReq = {
      user: {
        email: '<EMAIL>',
      },
      body: {
        communityObjectId: '507f1f77bcf86cd799439011',
      },
    };

    const mockCastData = {
      communityObjectId: '507f1f77bcf86cd799439011',
    };

    const serviceError = new Error('No cancelled plan found');

    schema.revokeCancellationSchema.cast = jest.fn().mockReturnValue(mockCastData);
    service.revokeCancellation.mockRejectedValue(serviceError);

    await expect(controller.revokeCancellation(mockReq)).rejects.toThrow(serviceError);
  });

  it('should handle schema validation errors', async () => {
    const mockReq = {
      user: {
        email: '<EMAIL>',
      },
      body: {
        communityObjectId: 'invalid-id',
      },
    };

    const validationError = new Error('Invalid community ID format');

    schema.revokeCancellationSchema.cast = jest.fn().mockImplementation(() => {
      throw validationError;
    });

    await expect(controller.revokeCancellation(mockReq)).rejects.toThrow(validationError);

    expect(service.revokeCancellation).not.toHaveBeenCalled();
  });

  it('should use email from req.user', async () => {
    const mockReq = {
      user: {
        email: '<EMAIL>',
      },
      body: {
        communityObjectId: '507f1f77bcf86cd799439011',
        reason: 'Test reason',
      },
    };

    const mockCastData = {
      communityObjectId: '507f1f77bcf86cd799439011',
      reason: 'Test reason',
    };

    schema.revokeCancellationSchema.cast = jest.fn().mockReturnValue(mockCastData);
    service.revokeCancellation.mockResolvedValue({});

    await controller.revokeCancellation(mockReq);

    expect(service.revokeCancellation).toHaveBeenCalledWith({
      communityObjectId: mockCastData.communityObjectId,
      reason: mockCastData.reason,
      email: '<EMAIL>',
    });
  });

  it('should handle trimmed values from schema', async () => {
    const mockReq = {
      user: {
        email: '<EMAIL>',
      },
      body: {
        communityObjectId: '  507f1f77bcf86cd799439011  ',
        reason: '  Trimmed reason  ',
      },
    };

    const mockCastData = {
      communityObjectId: '507f1f77bcf86cd799439011',
      reason: 'Trimmed reason',
    };

    schema.revokeCancellationSchema.cast = jest.fn().mockReturnValue(mockCastData);
    service.revokeCancellation.mockResolvedValue({});

    await controller.revokeCancellation(mockReq);

    expect(service.revokeCancellation).toHaveBeenCalledWith({
      communityObjectId: '507f1f77bcf86cd799439011',
      reason: 'Trimmed reason',
      email: mockReq.user.email,
    });
  });
});