const { revokeCancellationSchema } = require('../../../../../src/api/v1/plan/schema');

describe('Plan API Schema - revokeCancellationSchema', () => {
  describe('valid requests', () => {
    it('should validate request with only required fields', async () => {
      const validData = {
        communityObjectId: '507f1f77bcf86cd799439011',
      };

      const result = await revokeCancellationSchema.validate(validData);
      expect(result).toEqual(validData);
    });

    it('should validate request with all fields', async () => {
      const validData = {
        communityObjectId: '507f1f77bcf86cd799439011',
        reason: 'Changed my mind about cancelling',
      };

      const result = await revokeCancellationSchema.validate(validData);
      expect(result).toEqual(validData);
    });

    it('should trim whitespace from fields', async () => {
      const dataWithWhitespace = {
        communityObjectId: '  507f1f77bcf86cd799439011  ',
        reason: '  Changed my mind  ',
      };

      const result = await revokeCancellationSchema.validate(dataWithWhitespace);
      expect(result).toEqual({
        communityObjectId: '507f1f77bcf86cd799439011',
        reason: 'Changed my mind',
      });
    });

    it('should accept empty string reason', async () => {
      const validData = {
        communityObjectId: '507f1f77bcf86cd799439011',
        reason: '',
      };

      const result = await revokeCancellationSchema.validate(validData);
      expect(result).toEqual(validData);
    });

    it('should accept maximum length reason', async () => {
      const validData = {
        communityObjectId: '507f1f77bcf86cd799439011',
        reason: 'a'.repeat(500),
      };

      const result = await revokeCancellationSchema.validate(validData);
      expect(result).toEqual(validData);
    });
  });

  describe('invalid requests', () => {
    it('should reject request without communityObjectId', async () => {
      const invalidData = {
        reason: 'Some reason',
      };

      await expect(revokeCancellationSchema.validate(invalidData))
        .rejects.toThrow('Community ID is required');
    });

    it('should reject empty communityObjectId', async () => {
      const invalidData = {
        communityObjectId: '',
      };

      await expect(revokeCancellationSchema.validate(invalidData))
        .rejects.toThrow('Community ID is required');
    });

    it('should reject whitespace-only communityObjectId', async () => {
      const invalidData = {
        communityObjectId: '   ',
      };

      await expect(revokeCancellationSchema.validate(invalidData))
        .rejects.toThrow('Community ID is required');
    });

    it('should reject invalid ObjectId format', async () => {
      const testCases = [
        { communityObjectId: 'invalid-id' },
        { communityObjectId: '12345' },
        { communityObjectId: '507f1f77bcf86cd79943901' }, // Too short
        { communityObjectId: '507f1f77bcf86cd7994390111' }, // Too long
        { communityObjectId: '507f1f77bcf86cd79943901g' }, // Invalid character
        { communityObjectId: 'ZZZZZZZZZZZZZZZZZZZZZZZZ' }, // Invalid hex
      ];

      for (const invalidData of testCases) {
        await expect(revokeCancellationSchema.validate(invalidData))
          .rejects.toThrow('Invalid community ID format');
      }
    });

    it('should reject reason longer than 500 characters', async () => {
      const invalidData = {
        communityObjectId: '507f1f77bcf86cd799439011',
        reason: 'a'.repeat(501),
      };

      await expect(revokeCancellationSchema.validate(invalidData))
        .rejects.toThrow('Reason cannot exceed 500 characters');
    });

    it('should coerce non-string reason to string', async () => {
      const testCases = [
        { communityObjectId: '507f1f77bcf86cd799439011', reason: 123 },
        { communityObjectId: '507f1f77bcf86cd799439011', reason: true },
      ];

      for (const invalidData of testCases) {
        const result = await revokeCancellationSchema.validate(invalidData);
        expect(typeof result.reason).toBe('string');
      }
    });

    it('should strip additional fields', async () => {
      const invalidData = {
        communityObjectId: '507f1f77bcf86cd799439011',
        reason: 'Valid reason',
        extraField: 'should not be here',
      };

      const result = await revokeCancellationSchema.validate(invalidData, { stripUnknown: true });
      expect(result).not.toHaveProperty('extraField');
      expect(result).toEqual({
        communityObjectId: '507f1f77bcf86cd799439011',
        reason: 'Valid reason',
      });
    });
  });

  describe('edge cases', () => {
    it('should reject null reason', async () => {
      const dataWithNull = {
        communityObjectId: '507f1f77bcf86cd799439011',
        reason: null,
      };

      await expect(revokeCancellationSchema.validate(dataWithNull))
        .rejects.toThrow('reason must be a `string` type');
    });

    it('should handle undefined reason', async () => {
      const dataWithUndefined = {
        communityObjectId: '507f1f77bcf86cd799439011',
        reason: undefined,
      };

      const result = await revokeCancellationSchema.validate(dataWithUndefined);
      expect(result).toEqual({
        communityObjectId: '507f1f77bcf86cd799439011',
      });
    });

    it('should handle mixed case ObjectId', async () => {
      const validData = {
        communityObjectId: '507F1f77BCf86Cd799439011',
      };

      const result = await revokeCancellationSchema.validate(validData);
      expect(result).toEqual(validData);
    });
  });
});